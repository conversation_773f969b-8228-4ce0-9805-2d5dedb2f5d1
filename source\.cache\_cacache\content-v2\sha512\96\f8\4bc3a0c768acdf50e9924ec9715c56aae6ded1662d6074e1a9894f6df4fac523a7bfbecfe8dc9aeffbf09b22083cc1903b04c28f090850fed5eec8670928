{"_id": "buffer-crc32", "_rev": "38-38f73f88b2f8305c0a317c6eec8edf4c", "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "dist-tags": {"latest": "1.0.0", "next": "1.0.0-RC9"}, "versions": {"0.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A CRC32 algorithm that works with buffers and is cool with unicode.", "version": "0.1.0", "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "_id": "buffer-crc32@0.1.0", "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "aaf375a949dea411fa2b39b41e808f9543224e3f", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.1.0.tgz", "integrity": "sha512-J9S+od1040fGFKwBnbOF2/vcHRCH9V+vLJXeZb4CM9C+sytqpiseIL8L+iP1nfcrGTMMt8xmN15d5yE2IFaoPg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE0ci38Wj1lrh2Y/oMLpo2OXRi6wAytQMwBiXC7C1ZX7AiEA1VbI+ZF+43txe0S/jYwe9a10x2osmVslsoLzWsmMqrM="}]}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.1.1", "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "./node_modules/.bin/tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "_id": "buffer-crc32@0.1.1", "_engineSupported": true, "_npmVersion": "1.1.16", "_nodeVersion": "v0.8.1", "_defaultsLoaded": true, "dist": {"shasum": "7e110dc9953908ab7c32acdc70c9f945b1cbc526", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.1.1.tgz", "integrity": "sha512-UsRYbAdoR39JogmdI9AeZFQrmf0aqdPOzNLh5NR7GBdGNwxXW4eKqUvgZ+B9ImebovILBxiqSnoX9BfNRsh9bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIIKbNc3MQNSh3DOcrHmvL9eGXnAngB9b6KxODpzjc7AIgGGqYLNf76iZo1Wx2K+blpz8jePUqC8vyjZBWTUuwdhw="}]}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.0", "contributors": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "./node_modules/.bin/tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "_id": "buffer-crc32@0.2.0", "dist": {"shasum": "bdd04c663eb5cad999bd272dfcf2ea51a2ab0a3e", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.0.tgz", "integrity": "sha512-8snwO26R3OgEyNJ4Oc2C7m2DUXtffP5BmTlpfCxIZwGsR0Wi3gFvIDbi697+FLaIbjZPiyvm0JX++hyXDI0sUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDChpLoaLx8XkD3qyCWkQPnUKefEArsNd2sDmTO+Iel8wIgWx3pyhawfrIPH09mgaluyn3I5AN1tF9DmzFi+wEi3f4="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.1", "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "./node_modules/.bin/tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "_id": "buffer-crc32@0.2.1", "dist": {"shasum": "be3e5382fc02b6d6324956ac1af98aa98b08534c", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.1.tgz", "integrity": "sha512-vMfBIRp/wjlpueSz7Sb0OmO7C5SH58SSmbsT8G4D48YfO/Zgbr29xNXMpZVSC14ujVJfrZZH1Bl+kXYRQPuvfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHGN7zeTxURNWzXjKp8afqokoW0kHxRok2eCHBpmMGuHAiAWU7eVbVOvtw/rH9xMz50L3hp9NAeRaujTEWv/d2FmCQ=="}]}, "_npmVersion": "1.1.65", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.3", "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "gitHead": "f06dc30bccbad685af30666268c6fd2670805998", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.3", "_shasum": "bb54519e95d107cbd2400e76d0cab1467336d921", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "bb54519e95d107cbd2400e76d0cab1467336d921", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.3.tgz", "integrity": "sha512-HLvoSqq1z8fJEcT1lUlJZ4OJaXJZ1wsWm0+fBxkz9Bdf/WphA4Da7FtGUguNNyEXL4WB0hNMTaWmdFRFPy8YOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwDB10TJ2KMiwMorOfxKhOYHrTk7A2vcCQBfa7aqlErgIhAPuwYXGU+oBnqeLZVZtQOYTeWxfUqovoducDqcTKkLG4"}]}, "directories": {}}, "0.2.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.4", "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "gitHead": "1906667eedde5fdb5c9c98f91b743518c2d7469c", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.4", "_shasum": "f729c4c5d2eb39ba49f29422175cc20b0bf1b96f", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "f729c4c5d2eb39ba49f29422175cc20b0bf1b96f", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.4.tgz", "integrity": "sha512-Dr28Q4S/T2WHIzV3WL/zss+CseFOneIGKaUueTzBJ8TQi9GQxagO+XqWec50UirVTWHItD+F9JUdh93epn50GA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAKBL9DjHXaalpN2d4EWX5qC23IQO+VyQm+Jq0XeR58pAiEAlKcR1+A6nn78OuYJ3/7dxVON2/GKVr1mj1QtugtHEaQ="}]}, "directories": {}}, "0.2.5": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.5", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "gitHead": "beb976670f2ea6414e4cce4764d0213e5f9d7cbc", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.5", "_shasum": "db003ac2671e62ebd6ece78ea2c2e1b405736e91", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.33", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "db003ac2671e62ebd6ece78ea2c2e1b405736e91", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.5.tgz", "integrity": "sha512-ll3l0azxuhOdPet7vXuD1kHQ7BY0XQuO37AE8Qf0oRNbD+cg6Dgh6K3u6n+ZXGtHjgYNjl8hJryxQFiS6W6+PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbdqpiKNjR73dSNc+1/utBITu5RZUUtPZbvK/uFAOMAgIgLsZpAnrmhwPNiYFYKi+6F/8moF817ts3P5m6rT80T9Q="}]}, "directories": {}}, "0.2.6": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.6", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "gitHead": "16fce71a69a5a2dd7b095b55496e11831c5e6e01", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.6", "_shasum": "612b318074fc6c4c30504b297247a1f91641253b", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "612b318074fc6c4c30504b297247a1f91641253b", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.6.tgz", "integrity": "sha512-E0F/I32k7cWYjjmQ8+iZ6yhwDHgixoD778eZ7TvXiZATkE6nXMcXTFNeeO2VVV8DzFAH9ZAgJY46/3uAzs/3Bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAFk9Q3OXRDH03/a1+pLgoipNJ1038vFhII1fWuwMQwCAiEAziH7ftu82LAAXZdukW3spNy1tU4vnqtqI1nzbKSSDrU="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.6.tgz_1479485867929_0.5437697607558221"}, "directories": {}}, "0.2.7": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.7", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "gitHead": "70b8acfea031bf3c60a61d8e74c393ccd8417ac1", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.7", "_shasum": "1b9152ced5bc28eced319a29f53ca2f366a61585", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "1b9152ced5bc28eced319a29f53ca2f366a61585", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.7.tgz", "integrity": "sha512-eOIJXSjOW3pVT+1imZCYT5Fz+GgojKBBTDHQxOhLS5dPiGANwEf1/P8A5ZITjNL7K/5NngMAh/V3dIO5L0esHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG47zEWsqX6KZWx+15WYysp2OIcImGfT2F9e9F3LU7AqAiB9wY2fuNDd3augD0v1vx4DvB7gMwxQe1o9nQZwE9i6ig=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.7.tgz_1479748032437_0.43503195815719664"}, "directories": {}}, "0.2.8": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.8", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "3753329fd18f96631a14d12aede70494a130f2e4", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.8", "_shasum": "f5de1a797f1e1db8d4b1f5a0d7b499965b516cd6", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "f5de1a797f1e1db8d4b1f5a0d7b499965b516cd6", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.8.tgz", "integrity": "sha512-bop+hqeSbTDjYPsdjzmZDqLFSUEhLOAbeSWS3Fwi0InmhuYdBu9NwvAw9ctAVe7TEhrF+FVtRqZedutctycusA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjD4gqSD/OIpDu423cesbH35cTYwXCxUf6OHu1mdiC3AiAmzT9AP5BUyRvb3DcfC3EGCLDksAKXh7niPGwIQauQnw=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.8.tgz_1479748295648_0.4060637867078185"}, "directories": {}}, "0.2.9": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.9", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "gitHead": "bcf3edb304d0932dbdcd7f161a99f06957fe3fd1", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.9", "_shasum": "3b09f0738e981546a3dcb36589b7918cba7bb65d", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "3b09f0738e981546a3dcb36589b7918cba7bb65d", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.9.tgz", "integrity": "sha512-hVYET3vmmT6Ox9++TvZTmJQfSxAEnLx9acqH9hqKT7mMV6QJd8Ta1S0zfSXXUjIIGemsnJFbU+BoeZmIElkCUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYOhWe3teL+ClaSmFhkFxotfTdqO6cu46t8G2RklU11wIhAJjdvaXhWzZk+mkJDi2CMhsoPSpvENCYoKilcuZN4k/x"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.9.tgz_1479753671598_0.5003868564963341"}, "directories": {}}, "0.2.10": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bjb.io"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.10", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "gitHead": "79e536bd43e1bebb0e4d89c085822e87bcafb722", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.10", "_shasum": "17a84bcefec579b634f5da3b92f75a6a4d5db565", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "17a84bcefec579b634f5da3b92f75a6a4d5db565", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.10.tgz", "integrity": "sha512-2JV80AbjZWcqqxThVUtnVgXGWprS0GzmjOiNN0yL87rzlvj2suShBOYlYb9UqZUJ+T/al3m2kCG7lLbBL+DhIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXgFUuD2c4DqLcbR4qpyiuOMcrBFkDhicC0Pi7Lkuf5AiEAlu6U6FZ82L55r9hpb5HU1NydpfFeKpYHrIdasGYRP48="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.10.tgz_1479754279084_0.9421022920869291"}, "directories": {}}, "0.2.11": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.11", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "gitHead": "6ed04b8cfea47374d20eddb6e864a8fa194f2621", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.11", "_shasum": "4d3903772a1f1c7f28d9c72a4127a638338e2ed5", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "4d3903772a1f1c7f28d9c72a4127a638338e2ed5", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.11.tgz", "integrity": "sha512-WyGqptiHXKMpxJabAZkfZok+g2ZjgU4gbagel9btPwdIqKbcNBi2g2a5+VxCo2OUIlS/Tb7fS0iP1TwPz6R6Xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuHvp/yTSKCG1LI09iQiVERQAtRekwysxnhQrES/bBPQIhAIGyHe0lfjvPKo0YhvYWcc/vXGoJm1XEU+i8r79HFoVA"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.11.tgz_1479763691716_0.7032775871921331"}, "directories": {}}, "0.2.12": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.12", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": ">=6.0.0"}, "license": "MIT", "files": ["index.js"], "gitHead": "fd2e84fdf4fe43388722ad0cbc31e89f2bfda535", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.12", "_shasum": "4d5df19d1d49227b84e9a7ca8940c738448e0879", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "4d5df19d1d49227b84e9a7ca8940c738448e0879", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.12.tgz", "integrity": "sha512-4m5kWWM4ptjT0liu5RYiSav4H8c6P239cYN9zZv4oiN3P5aMJfJDLvD25MBJ8krIWlvE/nI6KqmiIcW98VrEtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWRpBadYZO8WeO9lpR5y3FCwdmQi/MqptXq2zkFejZuwIhAPiH9q7QWixTJFdPUmElGP905KJQkGDGSDXPGZVXRizl"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.12.tgz_1479766357312_0.3505246720742434"}, "directories": {}}, "0.2.13": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.13", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "files": ["index.js"], "gitHead": "e9b66c7356f4ea6f7c2f89d51160a1ef6117849f", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_id": "buffer-crc32@0.2.13", "_shasum": "0d333e3f00eac50aa1454abd30ef8c2a5d9a7242", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "brianloveswords", "email": "<EMAIL>"}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}], "dist": {"shasum": "0d333e3f00eac50aa1454abd30ef8c2a5d9a7242", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICRPfOR/yOeLkfLBMEJZkx44iT+mxS9dbyITt9TNyXnAAiASx9YpL1mGxqfrbima80cFHGmlncIf30VSBs3msuBOEg=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-crc32-0.2.13.tgz_1479778107033_0.1442455758806318"}, "directories": {}}, "1.0.0-RC1": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC1", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "_id": "buffer-crc32@1.0.0-RC1", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-YCIsbMEI9NYGv5YV4MS5E7CnYnKEpNRBr1ZuWKF3o8GIZnae2KDPzbtCNbbFRjQU4YljMDyfsqPJoZU7kpJYXg==", "shasum": "0dd52cdf907869ba67d69738ad99dfcb243cb126", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC1.tgz", "fileCount": 5, "unpackedSize": 13313, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuruo8DZvGqwhZQUX5eMAYBb25U54woRunpXm9U9qpTwIhANgI2b+ycSq5wrphuqFHc5ytgS8xgviNj0cFr8/X7/IU"}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC1_1706916503977_0.6517348121567454"}, "_hasShrinkwrap": false}, "1.0.0-RC2": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC2", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "_id": "buffer-crc32@1.0.0-RC2", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "types": "./dist/index.d.ts", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-ysyhe1S2bNB0k9v2tE8i/+cJ9jym6jYgNym+i8srankcxKAwY/1DSC6LTvVu3UsgM4idoW2wQsl/zDYa0P2ibQ==", "shasum": "7a4aa2580b28adc4e98fbc1eb79fe784975cc665", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC2.tgz", "fileCount": 8, "unpackedSize": 27615, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCED+azC+rL32zIdBOuBjqgAd/Mr4XwOKMkKOP31CjuAQIhAPoFw3WLZJ+wREkCKKmAIvnjJ57aMljNGHHrFIpPR++L"}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC2_1706916761735_0.23582097806846036"}, "_hasShrinkwrap": false}, "1.0.0-RC3": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC3", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./types": {"import": "./dist/index.d.mts", "require": "./dist/index.d.cts"}}, "main": "./dist/index.cjs", "types": "./dist/index.d.cts", "_id": "buffer-crc32@1.0.0-RC3", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-jDMu5PuuzJmvHpDbAmrdZcK6LFWDF0TveVBoS0oTi/s3AOs+9ei1b5nyYo7uwqauMz78GNu8pxHKFypiMPDbZg==", "shasum": "54f6f520c6cb3a8e7cc055c3b58a72db6ded2070", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC3.tgz", "fileCount": 9, "unpackedSize": 20212, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE7U8gdwZqjO3S56VNic7grqrcLeFGNX/rHNY4QhABVJAiBeMW3g3rZPZ8hPxQTVD0fOCL1pOH7vIFN/3z3rgdKUQw=="}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC3_1706917333429_0.5334103667271359"}, "_hasShrinkwrap": false}, "1.0.0-RC4": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC4", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./types": {"import": "./index.d.ts", "require": "./index.d.ts"}}, "main": "./dist/index.cjs", "types": "./index.d.ts", "_id": "buffer-crc32@1.0.0-RC4", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-nkEdwPkNudvsFqfItkkH9lFeqsvG+KTv+ZAijUFzTwPldSwNGg0sJurg8Xy/qGktaYSb+C4GF4ViBUojIKtMog==", "shasum": "ec13f8dadbb9af15e45b73e7f3f82837933f4202", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC4.tgz", "fileCount": 6, "unpackedSize": 15259, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBTcm/OxzPOG4wzULAxcAqO2rRgnCRfCcwf+4dnk4/3gIhAJkW5qRRyHNqwvEfrDx4F9fVeXE1z8WVFdxpLZ1NvTp2"}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC4_1706917453971_0.8111580156661269"}, "_hasShrinkwrap": false}, "1.0.0-RC5": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC5", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "types": "./index.d.ts", "_id": "buffer-crc32@1.0.0-RC5", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-0zID+ESAWMX26sa6ulD/CEwWsUUZly4y/ZlQCtIldrJzLNq/VZ5Rsa3h2jIubI9k+GLacSInCi7TFO3HkWmBlA==", "shasum": "7bdefc106b8265e0efc2be0883a7f791f918717b", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC5.tgz", "fileCount": 6, "unpackedSize": 15171, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZOnAYsi+2qbMNrY9h3sUStXGurmZF8ghALwFQpQhBUgIhANrjfvUCt6vBOJeU9/OHzazUJJVTtkJxkhamHxWlp6Hy"}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC5_1706917546892_0.07498775466262919"}, "_hasShrinkwrap": false}, "1.0.0-RC7": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC7", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0 && npx cpy-cli index.d.ts dist --rename=index.d.cts && npx cpy-cli index.d.ts dist --rename=index.d.mts", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./types": {"import": "./dist/index.d.mts", "require": "./dist/index.d.cts"}}, "main": "./dist/index.cjs", "types": "./dist/index.d.cts", "_id": "buffer-crc32@1.0.0-RC7", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-GEWsYS2lyak2u3ltXmlF+jkSBQ/Ik1Q7rN2n6PzoDia+BYt2wCwRCFDCALH6UauPEZKAhlHi0aAwdqJn10re7w==", "shasum": "25213579c299a1e046e39eac0f7cbfa01969da4f", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC7.tgz", "fileCount": 7, "unpackedSize": 17122, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaJYxHUbgpFZyffLDn8jJzOLeHgDmYnNvw0+Z/Cs5oegIhAIoUXmHhOebevGpLmxWcRy64wfQrOIPo93EuRuhJolcF"}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC7_1706918246290_0.4032307779579041"}, "_hasShrinkwrap": false}, "1.0.0-RC8": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC8", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0 && npx cpy-cli index.d.ts dist --rename=index.d.cts && npx cpy-cli index.d.ts dist --rename=index.d.mts", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./types": {"import": "./dist/index.d.mts", "require": "./dist/index.d.cts"}}, "main": "./dist/index.cjs", "types": "./index.d.ts", "_id": "buffer-crc32@1.0.0-RC8", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-rWKHXJ77/Sal4ga9kQ+K1zdD3Uo6JCux99//JaJgdjKm4Im2azcWgkTRUPqQEec3EE4tcjKflIYVfYx9NoiATQ==", "shasum": "41c3aa00df7effff95ea896f5ebf2b41eb3919af", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC8.tgz", "fileCount": 8, "unpackedSize": 18893, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVUF2v+DqKWRkrhGl2tS4fbkquvX2RzbMUr908Qn9g9QIgA5VQ6ayeqMb1smQKHCKqnmyo0A/gYuhw4wxCamnBcMA="}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC8_1706918326221_0.2029735301484017"}, "_hasShrinkwrap": false}, "1.0.0-RC9": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0-RC9", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0 && npx cpy-cli index.d.ts dist --rename=index.d.cts && npx cpy-cli index.d.ts dist --rename=index.d.mts", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "types": "./index.d.ts", "_id": "buffer-crc32@1.0.0-RC9", "readme": "# buffer-crc32\n\n[![Build Status](https://secure.travis-ci.org/brianloveswords/buffer-crc32.png?branch=master)](http://travis-ci.org/brianloveswords/buffer-crc32)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nvar crc32 = require('buffer-crc32');\n// works with buffers\nvar buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nvar partialCrc = crc32('hey');\nvar partialCrc = crc32(' ', partialCrc);\nvar partialCrc = crc32('sup', partialCrc);\nvar partialCrc = crc32(' ', partialCrc);\nvar finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "readmeFilename": "README.md", "gitHead": "78060e0713b17c28972b46180957ca1c180eac99", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-nGfOWS48QJzrG2IgBurHGrUfZi9F3f5j8AuJViub6Z0qD9i+pw0FRhTDLqJ87ibbuckWdeAKpi4lxlhHjZNJ4g==", "shasum": "dd06101eb6d21def8d54ee21592497cbad20d5fc", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0-RC9.tgz", "fileCount": 8, "unpackedSize": 18793, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbWg5vEULsrVhjWLiy1J7R/RfMYkE/eTw7TGeWw51pUAIga9KrSQQAmPQgAK2mKnE3Q0IxL4/+OeRly6wiuOLu8hQ="}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0-RC9_1706918399444_0.6232004938034224"}, "_hasShrinkwrap": false}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "1.0.0", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "scripts": {"test": "tap tests/*.test.js --reporter classic", "build": "npx unbuild@2.0.0 && npx cpy-cli index.d.ts dist --rename=index.d.cts && npx cpy-cli index.d.ts dist --rename=index.d.mts", "prepublishOnly": "npm run build", "format": "prettier --write --log-level warn \"**/*.{json,md,js}\""}, "dependencies": {}, "devDependencies": {"prettier": "^3.2.4", "tap": "~11.1.5"}, "optionalDependencies": {}, "engines": {"node": ">=8.0.0"}, "license": "MIT", "type": "commonjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "types": "./index.d.ts", "_id": "buffer-crc32@1.0.0", "gitHead": "fc79b0d9e490dee637d02107b93b88dc9c26dc69", "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "_nodeVersion": "21.5.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-Db1SbgBS/fg/392AblrMJk97KggmvYhr4pB5ZIMTWtaivCPMWLkmb7m21cJvpvgK+J3nsU2CmmixNBZx4vFj/w==", "shasum": "a10993b9055081d55304bd9feb4a072de179f405", "tarball": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-1.0.0.tgz", "fileCount": 8, "unpackedSize": 16801, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAS5BF7lsiWoArjVzkPrFL0Q4o/XWUXrowh7gftpNAlZAiEAsiNlrHjW+mGahCFFAb8ffFoIT85Zg1oUaf5Fz57nMVI="}]}, "_npmUser": {"name": "kibertoad", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-crc32_1.0.0_1707242286713_0.9201286026324527"}, "_hasShrinkwrap": false}}, "readme": "# buffer-crc32\n\n[![NPM Version](https://img.shields.io/npm/v/buffer-crc32.svg)](https://npmjs.org/package/buffer-crc32)\n[![Build Status](https://github.com/brianloveswords/buffer-crc32/actions/workflows/ci.yml/badge.svg)](https://github.com/brianloveswords/buffer-crc32/actions/workflows/ci.yml)\n\ncrc32 that works with binary data and fancy character sets, outputs\nbuffer, signed or unsigned data and has tests.\n\nDerived from the sample CRC implementation in the PNG specification: http://www.w3.org/TR/PNG/#D-CRCAppendix\n\nThis package requires Node 8+ to work.\n\n# install\n\n```\nnpm install buffer-crc32\n```\n\n# example\n\n```js\nconst crc32 = require('buffer-crc32');\n// works with buffers\nconst buf = Buffer([0x00, 0x73, 0x75, 0x70, 0x20, 0x62, 0x72, 0x6f, 0x00]);\ncrc32(buf); // -> <Buffer 94 5a ab 4a>\n\n// has convenience methods for getting signed or unsigned ints\ncrc32.signed(buf); // -> -**********\ncrc32.unsigned(buf); // -> **********\n\n// will cast to buffer if given a string, so you can\n// directly use foreign characters safely\ncrc32('自動販売機'); // -> <Buffer cb 03 1a c5>\n\n// and works in append mode too\nconst partialCrc = crc32('hey');\nconst partialCrc = crc32(' ', partialCrc);\nconst partialCrc = crc32('sup', partialCrc);\nconst partialCrc = crc32(' ', partialCrc);\nconst finalCrc = crc32('bros', partialCrc); // -> <Buffer 47 fa 55 70>\n```\n\n# tests\n\nThis was tested against the output of zlib's crc32 method. You can run\nthe tests with`npm test` (requires tap)\n\n# see also\n\nhttps://github.com/alexgorbatchev/node-crc, `crc.buffer.crc32` also\nsupports buffer inputs and return unsigned ints (thanks @tjholowaychuk).\n\n# license\n\nMIT/X11\n", "maintainers": [{"name": "brianloveswords", "email": "<EMAIL>"}, {"name": "kibertoad", "email": "<EMAIL>"}], "time": {"modified": "2024-02-06T17:58:07.060Z", "created": "2012-07-11T17:53:46.260Z", "0.1.0": "2012-07-11T17:53:46.688Z", "0.1.1": "2012-07-11T18:52:29.048Z", "0.2.0": "2013-01-09T16:54:40.660Z", "0.2.1": "2013-01-09T17:04:09.173Z", "0.2.3": "2014-06-17T14:37:36.527Z", "0.2.4": "2014-11-26T15:20:39.348Z", "0.2.5": "2014-12-07T19:19:44.403Z", "0.2.6": "2016-11-18T16:17:49.845Z", "0.2.7": "2016-11-21T17:07:13.025Z", "0.2.8": "2016-11-21T17:11:37.544Z", "0.2.9": "2016-11-21T18:41:12.530Z", "0.2.10": "2016-11-21T18:51:19.620Z", "0.2.11": "2016-11-21T21:28:13.830Z", "0.2.12": "2016-11-21T22:12:37.901Z", "0.2.13": "2016-11-22T01:28:28.865Z", "1.0.0-RC1": "2024-02-02T23:28:24.179Z", "1.0.0-RC2": "2024-02-02T23:32:41.927Z", "1.0.0-RC3": "2024-02-02T23:42:13.613Z", "1.0.0-RC4": "2024-02-02T23:44:14.123Z", "1.0.0-RC5": "2024-02-02T23:45:47.116Z", "1.0.0-RC7": "2024-02-02T23:57:26.474Z", "1.0.0-RC8": "2024-02-02T23:58:46.409Z", "1.0.0-RC9": "2024-02-02T23:59:59.609Z", "1.0.0": "2024-02-06T17:58:06.898Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "homepage": "https://github.com/brianloveswords/buffer-crc32", "contributors": [{"name": "<PERSON>"}], "bugs": {"url": "https://github.com/brianloveswords/buffer-crc32/issues"}, "readmeFilename": "README.md", "license": "MIT", "users": {"heineiuo": true, "faraoman": true, "flumpus-dev": true}}