{"_id": "progress", "_rev": "195-3c79e358f9a508deca65aa386c884f41", "name": "progress", "dist-tags": {"latest": "2.0.3"}, "versions": {"0.0.1": {"name": "progress", "version": "0.0.1", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@0.0.1", "dist": {"shasum": "5ddf9238fbc9d237e1fe5f1ff7939c14b0615c3f", "tarball": "https://registry.npmjs.org/progress/-/progress-0.0.1.tgz", "integrity": "sha512-yCxWNqIqqVBaB5FP9QpZdl3XZTPJ30kRQbaWLeyW0X01zqkOCStcYDbiwxtQM7JOupRca3LBMZgYKieW0sc15A==", "signatures": [{"sig": "MEYCIQCS+VF4BP/M4riE9bwATYpAG4AqARQ6R7AmpwnSpZvqwAIhAJ4MTQIz70h+cW3fo/p7NgeW2DqKcA9iPay24V4h5lCG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "0.4.x"}, "_npmVersion": "0.3.18", "description": "Flexible ascii progress bar", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {}, "_defaultsLoaded": true, "_engineSupported": true}, "0.0.2": {"name": "progress", "version": "0.0.2", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@0.0.2", "dist": {"shasum": "8ff162e1eb8fd121d4d10126302825a45db22057", "tarball": "https://registry.npmjs.org/progress/-/progress-0.0.2.tgz", "integrity": "sha512-QGTlLoiSqf7keGjZYPbFo83qB4VL/UM9UPILFtqI81TUg5SADCo+/sRmJOcHFDFZrGla+GxY+wW8wZDQV42AVQ==", "signatures": [{"sig": "MEUCIFCOLDixnUnmlZdXadJIssOWb8SUMI1h53eDXao40vkNAiEAuNgG7Mmcki2nvEL3+ER/5jKmvqMk0dUuKcxTYNS5Xrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "0.4.x"}, "_npmVersion": "0.3.18", "description": "Flexible ascii progress bar", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {}, "_defaultsLoaded": true, "_engineSupported": true}, "0.0.3": {"name": "progress", "version": "0.0.3", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@0.0.3", "dist": {"shasum": "75629307e033e34e592bd217950aea39ddd0d899", "tarball": "https://registry.npmjs.org/progress/-/progress-0.0.3.tgz", "integrity": "sha512-XNEZeP2ahv0lluTozr0cFI3J/NVY1ygix3116iI2A6mGYqWGXZ9iLqFhFvSkp8oWTD1Latn4IAXeIBPyZweHXg==", "signatures": [{"sig": "MEYCIQDOGe8kFmxXjx4hXrCPXIIVMOddDEJREvcD6meV8++mOgIhAPtqDBruqeHfboSUjsIQqoAs0yMb3+il93pIp6soxuk0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "files": [""], "engines": {"node": "0.4.x"}, "_npmVersion": "0.3.18", "description": "Flexible ascii progress bar", "directories": {"lib": "./lib"}, "_nodeVersion": "v0.4.6", "dependencies": {}, "_defaultsLoaded": true, "_engineSupported": true}, "0.0.4": {"name": "progress", "version": "0.0.4", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@0.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dc20375e77236ba009c0ab5c6fa4f6d92f7408cf", "tarball": "https://registry.npmjs.org/progress/-/progress-0.0.4.tgz", "integrity": "sha512-D91pHw+l8w46yr1/UNAdjXRbDu4bKxR4DZQV3yosuX7osMR54SNewCfhg2LWqjb1Q1cCUSz0nJ15WDFG1w5BIQ==", "signatures": [{"sig": "MEQCIGbJbuAZQVpatG+1HKCEtd8neka/LiAe9Bkjl+F2qCXjAiBEF7VkVBFdJOYPUaPZfExn9dJ5Qw0RO9kgkS+Y7fqcdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.104", "description": "Flexible ascii progress bar", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "0.0.5": {"name": "progress", "version": "0.0.5", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@0.0.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "397e5af42596d81ef1641d40728826908c60ca90", "tarball": "https://registry.npmjs.org/progress/-/progress-0.0.5.tgz", "integrity": "sha512-bX9dFfpd+CsvwDUGpr+vR1xlmEJYNueFDX0PesPZfP4Yle/a26U+hYMwicFO77gdJGh5IuJfQYcq32q9TP+Oag==", "signatures": [{"sig": "MEUCIQCsNY0az8r54rZMgG0fYpu7YrjVXwP/+BfMwpTRPzmPSgIgcqJ+NOHGK2wh6TkjGoTwD1FhqgcNKxHjFk5Ex6CegT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">=0.4.0"}, "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "0.1.0": {"name": "progress", "version": "0.1.0", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dbec0c99fbc2f7b9623e133163be6361d5e9e447", "tarball": "https://registry.npmjs.org/progress/-/progress-0.1.0.tgz", "integrity": "sha512-BO9u0nR/2r+YDyMCEPbodE2iW5xt5eF4C+NaPAKGWx6+vftH+ROUDvI88eELayOgN4bG9w0hH4HGrb5ayLezrg==", "signatures": [{"sig": "MEUCIF9igjW9ejPJ/ZYIJsYatxnI/tWmGkFHuxRhL8+VjvG0AiEAy9Tg1tlhaEwHLt+oxTGu/TmIkznoHu4eHGa404/+fBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.61", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.0.0": {"name": "progress", "version": "1.0.0", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ac997b60bce2619321d69080fed5014fff002b3", "tarball": "https://registry.npmjs.org/progress/-/progress-1.0.0.tgz", "integrity": "sha512-IA5j7+TjuayhjJu6e6LCZlFxvkvV3/y5MYkqcsw4N39bZtAO8elba/JDr4NyaKRJ3suVfsRUYsbQFmg3l4cCcw==", "signatures": [{"sig": "MEUCIQDjU+3GHvlHO58RxISs8Uz0DgswlIKH9xMxdeYAsRiRWQIgL6GDEwkBJdm/EAcQHoUN2KcQ6X6PqnKVnTkjcKo63XU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.2.14", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.0.1": {"name": "progress", "version": "1.0.1", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5bbef5d227896754f40ada2c38c3146336140a62", "tarball": "https://registry.npmjs.org/progress/-/progress-1.0.1.tgz", "integrity": "sha512-o+hMs3rCYDQsx+niisl+D5cZ3gnKdbHsY9Cr2UIWX7qBz4ZTuKf6xJrPMA+3GQclieIx3DGGYrDhQ6vl2Jp6IQ==", "signatures": [{"sig": "MEUCIQDERgZXnd3UYUj+loFuISK9NmWJSTU6CNPaKuj1MHu3qAIgYqIW64v11wPBiAxZFXkG3n+jyNNAQHHkdnT2mf5ER8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.2.30", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.0": {"name": "progress", "version": "1.1.0", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "dcc90b9c7b727ae496d51cf98940998c9ed1bc1a", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.0.tgz", "integrity": "sha512-iCCPMFqmRbdXmR+lt1x7lM0R+jS9dg1q6uwkdVJHcNqRPpU2OAz5f/QFGDMHKVQC8ELXIGjYj2cQZWGA7IzdkQ==", "signatures": [{"sig": "MEUCICLDlN2gPhRMLqpf5Ku+IS4WzX7Jsc1Ykn129ta11JWpAiEAjqlVQYQS1LYPblIVBYEKueaaSViVGHxJ0TZbzrtPie0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "prezjordan", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.2.25", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.2": {"name": "progress", "version": "1.1.2", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "87fdbc7c76a784020897b5e9665554b05fc58cd1", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.2.tgz", "integrity": "sha512-zdfeCQ3yhy2r53MrO1jx/14CWGQRlfai0OFB7I8ZYCj7fTjAxhapkeVnHNgymY/sgbDAHZ9dh8m3Dd/WjlLPOg==", "signatures": [{"sig": "MEQCIBZOWqIAykVH4UmVo2yRYQns60QyxQHz0puVsZycL5pgAiAo7uBoDUS1vMkwuTJA7izmQCn70omphAjTTf7CDvoFZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "hallas", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.3.11", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.3": {"name": "progress", "version": "1.1.3", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "42f89c5fc3b6f0408a0bdd68993b174f96aababf", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.3.tgz", "integrity": "sha512-2+64ACnkw86JBS2YEvAETSmQhbCC6yWMd729xuQAN64PvYorJpyEijSp1yUD4BaLFDFiEn7xBLjBK9gCkX0dXw==", "signatures": [{"sig": "MEYCIQDQ1Rlh/GPT810zStefOqN1g+FJPj2uMdktBm0zJ7P9+AIhAJ2bg/3N8zkypLGKsFM7l7d1tM3u+IAEfvzJHJwdeG1f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "prezjordan", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.3.8", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.4": {"name": "progress", "version": "1.1.4", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "789f57691b88b826a439bc52dc9620245d60255b", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.4.tgz", "integrity": "sha512-8pLDavd48F/BOXzTBwt7dQHXgxIbEAqWXxlOWat+re1sTc6cV8mEk9pguG74dYLX3Q5S2GV/2SYEOl/nHqaDOw==", "signatures": [{"sig": "MEQCIGFyZP9qYpJNOAc8erV4WNXt3O0YHYml5w0k4PoahkIbAiBZEe15JJEN250x/pCrLsyJ7rLVNTwY5lIE8+wZk5mkyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "prezjordan", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.3.8", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.5": {"name": "progress", "version": "1.1.5", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.5", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "0954b25aa36132e7b264c67c388f073919b387d0", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.5.tgz", "integrity": "sha512-oKNGi4rXlK+6DBPqtW5HfOoOuIDqzO56Rvi5mRJpOdpbSbPfuhi1aMlZ6EjLTz/VeendIQVe2RAHDvFjlLu8Qw==", "signatures": [{"sig": "MEYCIQCLtDaHia24feXD52rqzwT6PSK+7Nf3Yavc90DLDZ0GxwIhAMU4Hw66RE/anzX1x7KwGe24a2IKxL0sb4kWmO3tzj+U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "hallas", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.3.22", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.6": {"name": "progress", "version": "1.1.6", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.6", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "bfe28cbc5e02f010cf02ddb2619d7e981571fe18", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.6.tgz", "integrity": "sha512-1P4N3Dt43505+Zq/i7tslBb6KsL/A/Jw8iyn4FC7hHEY5ioPtbhHn4V2rFnqu9xABSayyGOg3OZta1QR1WKjdQ==", "signatures": [{"sig": "MEUCIDMevEmXs9h3DJUPbSfOOHOTOfy0GOg2edqDNnwUzWRFAiEA1ZDlcumvfCJJkrexT108Z3x/l+H6BjB1zTw567wIjR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "engines": {"node": ">=0.4.0"}, "_npmUser": {"name": "hallas", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.4.3", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.7": {"name": "progress", "version": "1.1.7", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.7", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "0c739597be1cceb68e9a66d4a70fd92705aa975b", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.7.tgz", "integrity": "sha512-BNlCn8hCdqOytNFbFPe5f2pT2Qhnag4MaVY9xdokFoIp1W3tAM/0uBU0WX1qW1HmqXsBOgmSCaxMzfY5Yk3CWA==", "signatures": [{"sig": "MEQCIF1IXAXz1DqdWuNdMum+dA/ceDyLDXJp+yuVcQZZoBMQAiBro41Vwomo5jME1GGIMZi4ge4iXLdgkf9I0aUhVOkXyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "0c739597be1cceb68e9a66d4a70fd92705aa975b", "engines": {"node": ">=0.4.0"}, "gitHead": "8c957a77e5c89e21de3e3ea1b8297b9e407860c4", "scripts": {}, "_npmUser": {"name": "prezjordan", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.4.14", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "1.1.8": {"name": "progress", "version": "1.1.8", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "progress@1.1.8", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "e260c78f6161cdd9b0e56cc3e0a85de17c7a57be", "tarball": "https://registry.npmjs.org/progress/-/progress-1.1.8.tgz", "integrity": "sha512-UdA8mJ4weIkUBO224tIarHzuHs4HuYiJvsuGT7j/SPQiUJVjYvNDBIPa0hAorduOfjGohB/qHWRa/lrrWX/mXw==", "signatures": [{"sig": "MEUCIAxjgOR6G0o+XfFSyeeVzn7us4Nbb6kWzK07WnqrzB+8AiEAsq74qnhDUd7EUQz1SEifeEZyeK1h2UDzDqIAxU9meu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "_from": ".", "_shasum": "e260c78f6161cdd9b0e56cc3e0a85de17c7a57be", "engines": {"node": ">=0.4.0"}, "gitHead": "6b9524c0d07df9555d20ae95c65918020c50e3e2", "scripts": {}, "_npmUser": {"name": "prezjordan", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress", "type": "git"}, "_npmVersion": "1.4.14", "description": "Flexible ascii progress bar", "directories": {}, "dependencies": {}}, "2.0.0": {"name": "progress", "version": "2.0.0", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "progress@2.0.0", "maintainers": [{"name": "hallas", "email": "<EMAIL>"}, {"name": "prezjordan", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "thejameskyle", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress#readme", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "8a1be366bf8fc23db2bd23f10c6fe920b4389d1f", "tarball": "https://registry.npmjs.org/progress/-/progress-2.0.0.tgz", "integrity": "sha512-TRNLrLfTyjKMs865PwLv6WM5TTMRvzqcZTkKaMVd0ooNM0fnMM8aEp0/7IpnGo295TAiI13Ju30zBZK0rdWZUg==", "signatures": [{"sig": "MEUCIE1lRF/1cdrhYy3xrTpOzw+LG304erVhbK9VygcjJpezAiEA+J/IADhZMt7WkWIFm5WYn/NVLwf/21On5qNdl036oPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "8a1be366bf8fc23db2bd23f10c6fe920b4389d1f", "engines": {"node": ">=0.4.0"}, "gitHead": "d84326ed9ab7720592b6bbc9c108849cd2a79908", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Flexible ascii progress bar", "directories": {}, "_nodeVersion": "6.9.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/progress-2.0.0.tgz_1491323693801_0.8384695542044938", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.1": {"name": "progress", "version": "2.0.1", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "progress@2.0.1", "maintainers": [{"name": "prezjordan", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "thejameskyle", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress#readme", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "c9242169342b1c29d275889c95734621b1952e31", "tarball": "https://registry.npmjs.org/progress/-/progress-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-OE+a6vzqazc+K6LxJrX5UPyKFvGnL5CYmq2jFGNIBWHpc4QyE49/YOumcrpQFJpfejmvRtbJzgO1zPmMCqlbBg==", "signatures": [{"sig": "MEQCIDpiBo25Huamb8h/I18Mzmtdi8JqlVevF+udkp+f5o4OAiBESY6HYMBNZ0q1349MKiQAg9ENyaUJcXO7FDq6MjXMZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxvJHCRA9TVsSAnZWagAAPssP/10oq00anQynzlXLfmm1\n+pQ4d32+K4a8AoTmVFfahPLnmu97B19/3pH70jPlSc9Np+Fcbh3/e9D/WPHe\ntEgaBt2Ik3r9iVmGh+y7Rkmx6FfY9fCTF9eCYyeHtth2sYZemv2qPnFZx250\nWu3XU0MIA2fWCjBNYb7goP2sR11qqecIAqc8EBU2xcsYJFjDQ2eCZCLSo6gZ\nwbrDcMce0/hUzXD1i6yx9x+wGAc1ni65QjLg4VkC3J3XJX5uKbf3J6tAr+6m\ndO+k+okkZlkLlvfOzo4dMe1trvr0pYKyLfCHLic1d6k73DlleCPuLd+RMwjt\nmoyVsaDdRZqUTEXkB3XP3M9Af1E/aTQLXKm9RPBtnw/3Ay6gvC21fjq7HSQ9\n6+XzH8ZqHg53/UgS1RRiU9t9z2BKk40uNP3WQC4j60MDuI6+aFAVZrkqcFXU\nrQY79xk5kJwl7argfiTyr3s332i+/lOwCjTVc3kYlQEc5czX/wT61m6Vgz26\nNPZD/KJZ6ttH4nYOiV23UC0oN7p2N8jdbKkXpNHJIrZPbULhqG3g8I8CENZl\n0IqjJRxdPQYfeNx9m9Nr241OV7rTZRWojBKg/ZFlshOAstmlKNtQ1Ehpjb9s\n+w/fNre0acyXnUd43oCoPViFJZACQgvJkyHUgJjFamZA05ajGn0iPBKzOhwA\nlCXx\r\n=9Jmf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">=0.4.0"}, "gitHead": "4951391515bf6fa5e794b59175e6c80b100d9fac", "_npmUser": {"name": "turbopope", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Flexible ascii progress bar", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/progress_2.0.1_1539764807268_0.0941383289795259", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "progress", "version": "2.0.2", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "progress@2.0.2", "maintainers": [{"name": "prezjordan", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "thejameskyle", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress#readme", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "db9476f916bcc9ebe8a04efb22b18b0740376c61", "tarball": "https://registry.npmjs.org/progress/-/progress-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-/OLz5F9beZUWwSHZDreXgap1XShX6W+DCHQCqwCF7uZ88s6uTlD2cR3JBE77SegCmNtb1Idst+NfmwcdU6KVhw==", "signatures": [{"sig": "MEUCIQDlptKPKgDk1G43WuIjDZKRJfP/9liGgLMDdB06j8nerAIgO5cSgXKYbz9znFvk9gWyphAayGOPk0xre6HCa5OUZwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBPTtCRA9TVsSAnZWagAALWYP/A54Wrf7gMcre6aYPMfQ\nrEuVzJdi2DS3X7+hwiRwgdM60j0ThUF/PYzfgWQn6YfVTCLeRmhpD9Pm/nPi\nOFeM9BU4BbuWjnU06UWUSPmirmJpCKUP7hZwFrtLrpIaYcW51RVruUAa/cIp\nXCcIkDxbSg/orB83rqbY+7Ha6M3mWVZIygnsEiAzxykMxALOq7qAAaLrEVjP\nSenQG7d2SoMT3929aQBmtPtVFu9UzWtW6ofMV0ec6lukWCs+JLEC4DkdP+OM\naceCDPdPB+o9KEtDOKHDf26VskdiH5Z/b8eX0rnwICHFndWppBpzrbVKtRJo\nRm00QPSD19Lw4spyjyu9CZwY8v6hydRxNiPScnXywYmakj5PwPnh0o+CWCyP\n+IW6dxsaJfnQn6vsSkG040QqDhfs+E1RXltMjJDp1ROMp16p8OE4BGyji7B5\nVv68iIlWSaYytNgogtl3xTZlBx1Tv10B0WsLNYDiYOsa4alWLeY+AqkVNty0\nRRtLrSgy+8XfVXSw0rdWYjMVarwnhHjlJZbGzbJfE3fDgFFF5uCTJEhWNvjt\nRVwToAsp7YiHvfwteM1U7NDfsubJQQH1KreHfvJwfJYtokkx08I+0cVAiH/W\nB1O8FWSlpea6cHWZ/OnWUUnfy3b2rOCU2003rtPSonuSF1XewYWJ4bXNhoCo\n9fs1\r\n=aM5k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">=0.4.0"}, "gitHead": "5a754fa7b35d4c06963db576fe14e3bce28db3f4", "_npmUser": {"name": "turbopope", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Flexible ascii progress bar", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/progress_2.0.2_1543828716887_0.5888166196271871", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "progress", "version": "2.0.3", "keywords": ["cli", "progress"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "progress@2.0.3", "maintainers": [{"name": "prezjordan", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "thejameskyle", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/node-progress#readme", "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "dist": {"shasum": "7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8", "tarball": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "signatures": [{"sig": "MEUCIFl+XBke0PTP1bDHncIujlhMpFb7w2lOEuaNE5tWZRmKAiEA8t+wAcV3dxkkOqdFFKjINnHSLraMTAuSAu8CcFcrOaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCAOWCRA9TVsSAnZWagAAltQP/R68Xyeu82R5z0PYQ+T7\n1jLDwRPs+lwxzzDl95xnkxVZ6qCY28M7elmNTHihudoVvYYYwyFgeHst9d3I\nxTtLr/sWjxQMA2Pmr1mOm2KNkxgs+SJWmLLIkPzetVmSAqP/jXuEg22EGfXP\nfdtTE6s7nd8Hhhn7NcDL1a6biHu/ilSX6hbYGfSoIAMTTKZPKdbzOyK9ZJwP\nnN8IeDXj9Z+aWmSnHKuKOKKPGV8apAKMte5Zhbn39fQqr2pbnKsORd0PvQwf\n+t1HeUXmKAI92+0+GOiGUekCyyIaZ9cA8563EVLvN7cHNz5INfS0d6ksvxIg\nA1nDz/yl0alfiQm2hmn+OmsV/t0JseMLRiblVLrBpzhsm8eSeWf/2eSyxc6z\n8IQbmovhJELDv+w4gZVYqllgENXTkP9PX66XUL7KfbbfzEiOT05BExkQPGi9\n6DWvvRpy9h2RRsdA8lkfe/J/Dc0o5956vW83okSr6uALRcNjBND7PgzTtE8u\nbNYyl3h+SNXInsTDabmpFy8OhPxRrDApWQ3cLzW6dzkiDuhK/rmEz1zcaqpN\n7eOdBA+Nxsf1DM4Iri5D0ZWnHA4Fw5TIvWkh/bgSNszgxL/o2SskikIXNQP1\njQq/4oiZRqM2+xVFeSGilDqCdHAHvIJ0kKNeoFGLafV1rJjJp+LPyUvl1vZP\nTwpZ\r\n=O+vn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">=0.4.0"}, "gitHead": "0790207ef077cbfb7ebde24a1dd9895ebf4643e1", "_npmUser": {"name": "turbopope", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/node-progress.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Flexible ascii progress bar", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/progress_2.0.3_1544029077906_0.911644159343294", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-04-20T20:06:39.604Z", "modified": "2025-06-03T10:46:21.914Z", "0.0.1": "2011-04-20T20:06:40.071Z", "0.0.2": "2011-04-21T02:35:36.302Z", "0.0.3": "2011-04-21T02:43:40.646Z", "0.0.4": "2011-11-14T23:14:51.251Z", "0.0.5": "2012-08-07T11:37:18.427Z", "0.1.0": "2012-09-19T20:06:06.685Z", "1.0.0": "2013-06-19T00:19:27.767Z", "1.0.1": "2013-09-07T03:48:39.957Z", "1.1.0": "2013-09-18T00:03:55.826Z", "1.1.2": "2013-10-17T07:30:40.869Z", "1.1.3": "2013-12-31T17:16:02.034Z", "1.1.4": "2014-03-13T03:57:28.247Z", "1.1.5": "2014-03-25T17:11:36.503Z", "1.1.6": "2014-06-16T22:29:02.281Z", "1.1.7": "2014-07-01T02:14:39.785Z", "1.1.8": "2014-08-10T04:06:16.388Z", "2.0.0": "2017-04-04T16:34:55.528Z", "2.0.1": "2018-10-17T08:26:47.452Z", "2.0.2": "2018-12-03T09:18:37.004Z", "2.0.3": "2018-12-05T16:57:58.058Z"}, "bugs": {"url": "https://github.com/visionmedia/node-progress/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/visionmedia/node-progress#readme", "keywords": ["cli", "progress"], "repository": {"url": "git://github.com/visionmedia/node-progress.git", "type": "git"}, "description": "Flexible ascii progress bar", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Jordan Scales", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"email": "<EMAIL>", "name": "thebigredgeek"}, {"email": "<EMAIL>", "name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "thejameskyle"}, {"email": "<EMAIL>", "name": "prezjordan"}, {"email": "<EMAIL>", "name": "turbopope"}], "readme": "Flexible ascii progress bar.\n\n## Installation\n\n```bash\n$ npm install progress\n```\n\n## Usage\n\nFirst we create a `ProgressBar`, giving it a format string\nas well as the `total`, telling the progress bar when it will\nbe considered complete. After that all we need to do is `tick()` appropriately.\n\n```javascript\nvar ProgressBar = require('progress');\n\nvar bar = new ProgressBar(':bar', { total: 10 });\nvar timer = setInterval(function () {\n  bar.tick();\n  if (bar.complete) {\n    console.log('\\ncomplete\\n');\n    clearInterval(timer);\n  }\n}, 100);\n```\n\n### Options\n\nThese are keys in the options object you can pass to the progress bar along with\n`total` as seen in the example above.\n\n- `curr` current completed index\n- `total` total number of ticks to complete\n- `width` the displayed width of the progress bar defaulting to total\n- `stream` the output stream defaulting to stderr\n- `head` head character defaulting to complete character\n- `complete` completion character defaulting to \"=\"\n- `incomplete` incomplete character defaulting to \"-\"\n- `renderThrottle` minimum time between updates in milliseconds defaulting to 16\n- `clear` option to clear the bar on completion defaulting to false\n- `callback` optional function to call when the progress bar completes\n\n### Tokens\n\nThese are tokens you can use in the format of your progress bar.\n\n- `:bar` the progress bar itself\n- `:current` current tick number\n- `:total` total ticks\n- `:elapsed` time elapsed in seconds\n- `:percent` completion percentage\n- `:eta` estimated completion time in seconds\n- `:rate` rate of ticks per second\n\n### Custom Tokens\n\nYou can define custom tokens by adding a `{'name': value}` object parameter to your method (`tick()`, `update()`, etc.) calls.\n\n```javascript\nvar bar = new ProgressBar(':current: :token1 :token2', { total: 3 })\nbar.tick({\n  'token1': \"Hello\",\n  'token2': \"World!\\n\"\n})\nbar.tick(2, {\n  'token1': \"Goodbye\",\n  'token2': \"World!\"\n})\n```\nThe above example would result in the output below.\n\n```\n1: Hello World!\n3: Goodbye World!\n```\n\n## Examples\n\n### Download\n\nIn our download example each tick has a variable influence, so we pass the chunk\nlength which adjusts the progress bar appropriately relative to the total\nlength.\n\n```javascript\nvar ProgressBar = require('progress');\nvar https = require('https');\n\nvar req = https.request({\n  host: 'download.github.com',\n  port: 443,\n  path: '/visionmedia-node-jscoverage-0d4608a.zip'\n});\n\nreq.on('response', function(res){\n  var len = parseInt(res.headers['content-length'], 10);\n\n  console.log();\n  var bar = new ProgressBar('  downloading [:bar] :rate/bps :percent :etas', {\n    complete: '=',\n    incomplete: ' ',\n    width: 20,\n    total: len\n  });\n\n  res.on('data', function (chunk) {\n    bar.tick(chunk.length);\n  });\n\n  res.on('end', function () {\n    console.log('\\n');\n  });\n});\n\nreq.end();\n```\n\nThe above example result in a progress bar like the one below.\n\n```\ndownloading [=====             ] 39/bps 29% 3.7s\n```\n\n### Interrupt\n\nTo display a message during progress bar execution, use `interrupt()`\n```javascript\nvar ProgressBar = require('progress');\n\nvar bar = new ProgressBar(':bar :current/:total', { total: 10 });\nvar timer = setInterval(function () {\n  bar.tick();\n  if (bar.complete) {\n    clearInterval(timer);\n  } else if (bar.curr === 5) {\n      bar.interrupt('this message appears above the progress bar\\ncurrent progress is ' + bar.curr + '/' + bar.total);\n  }\n}, 1000);\n```\n\nYou can see more examples in the `examples` folder.\n\n## License\n\nMIT\n", "readmeFilename": "Readme.md", "users": {"nyx": true, "rdm": true, "ari7": true, "j3kz": true, "jmal": true, "n1kk": true, "neo1": true, "nuer": true, "usex": true, "z164": true, "arefm": true, "mutoo": true, "zoxon": true, "ackhub": true, "akarem": true, "bojand": true, "dankle": true, "dotnil": true, "emyann": true, "glebec": true, "ishman": true, "kagawa": true, "lestad": true, "m0dred": true, "mrbgit": true, "shriek": true, "tayden": true, "tcrowe": true, "wickie": true, "yuch4n": true, "ziflex": true, "antanst": true, "baldore": true, "ferrari": true, "gztomas": true, "jcowgar": true, "kreozot": true, "lachriz": true, "liunian": true, "morewry": true, "nichoth": true, "quzhi78": true, "timelot": true, "tooyond": true, "tyr_asd": true, "yanghcc": true, "zcoding": true, "26medias": true, "ahvonenj": true, "akinjide": true, "amazonov": true, "azazdeaz": true, "cable023": true, "jklassen": true, "joeyblue": true, "kenlimmj": true, "leejefon": true, "leodutra": true, "liwenyao": true, "nicocube": true, "onheiron": true, "pddivine": true, "qddegtya": true, "rochejul": true, "toledano": true, "wujianfu": true, "xueboren": true, "zhouanbo": true, "abuelwafa": true, "antixrist": true, "azusa0127": true, "erincinci": true, "flftfqwxf": true, "gavinning": true, "gochomugo": true, "heartnett": true, "hemstreet": true, "madsummer": true, "mastayoda": true, "mjurincic": true, "ptallen63": true, "quatrodev": true, "sasquatch": true, "sqrtthree": true, "steel1990": true, "ukrbublik": true, "visual.io": true, "yuxiaoyan": true, "adamdscott": true, "aitorllj93": true, "axelrindle": true, "brunocalou": true, "davidbraun": true, "f124275809": true, "fattypanda": true, "jessaustin": true, "jkrusinski": true, "namhyun-gu": true, "quocnguyen": true, "rocket0191": true, "sbruchmann": true, "shuoshubao": true, "ahmed-dinar": true, "brainmurder": true, "codedungeon": true, "craigpatten": true, "django_wong": true, "flumpus-dev": true, "galenandrew": true, "huangxinxin": true, "jonaswebdev": true, "kodekracker": true, "louxiaojian": true, "micaelsouza": true, "ryoikarashi": true, "wangnan0610": true, "justintormey": true, "matiasmarani": true, "reecegoddard": true, "spacegeek224": true, "beaugunderson": true, "codyschindler": true, "johnsmithcoder": true, "joshua.marquez": true, "bohdantkachenko": true, "recursion_excursion": true}}