{"_id": "proxy-agent", "_rev": "50-947fa3e8221c17704c45472c6c366ca4", "name": "proxy-agent", "dist-tags": {"latest": "6.5.0"}, "versions": {"0.0.1": {"name": "proxy-agent", "version": "0.0.1", "keywords": ["http", "https", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@0.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "5b9bbb5187074488c705753a5ad31459ab78b0e2", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-0.0.1.tgz", "integrity": "sha512-cGaQBjWmdg6jkcIM6PRBp6Kx9JB8lbxe8i2+rIbs5m6QUNjqueeAQHWaaCa5hvlRPaHTsrvdk6gEvZdGosYUIQ==", "signatures": [{"sig": "MEUCIEHy2OOGTiX4HFXGpb4mu4v9FAUZIcaV9N/5IIzSqZzaAiEA+El/wVrKDouN77edcKgzznR4AuOg8LsVp80dssDAzUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "dependencies": {"lru-cache": "~2.3.1", "http-proxy-agent": "0", "https-proxy-agent": "0", "socks-proxy-agent": "0"}, "devDependencies": {"mocha": "~1.12.0", "superagent": "~0.15.4"}}, "0.0.2": {"name": "proxy-agent", "version": "0.0.2", "keywords": ["http", "https", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@0.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "3446d0c5cfe3ec589d4c68a831e37cab82e9c70e", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-0.0.2.tgz", "integrity": "sha512-d/ePObjwS5IIaGIB5LiNXYWQ/STHCn31s/2k93Ks9mPhmbAt6YzHX39ppCN27hOlZhKRbcwtmaB6iJIl28iHAA==", "signatures": [{"sig": "MEYCIQCXYX79FRSKqPGRyv0HNPKRQdo4Xn+KVuwkYvJJFK/ufAIhALaW7jZfq1pGnKxm1I+7OiKhNEKQKeVWqYSf/goL6EXP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "dependencies": {"lru-cache": "~2.3.1", "http-proxy-agent": "0", "https-proxy-agent": "0", "socks-proxy-agent": "0"}, "devDependencies": {"mocha": "~1.12.0", "superagent": "~0.15.4"}}, "1.0.0": {"name": "proxy-agent", "version": "1.0.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@1.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "435b85c48bbe75fc59f57961d03d0d7749e44c41", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-1.0.0.tgz", "integrity": "sha512-FkeMKhjY3ovqWltMKSqFipwRKsQpJHBIzyM0T+g8Cs20eORG3YeMrlbTQ8yCdXOVuqp4QmBHDfDnCZm3YJIkuQ==", "signatures": [{"sig": "MEUCIFyLeONOEz0GGxZSv1JUszuP78kDYk4w5AfNt14POnShAiEAvdwV1wTwLAWSIbG8MFW2s1rkNsWRCVrE36ICh0Ey4Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "dependencies": {"lru-cache": "~2.3.1", "http-proxy-agent": "0", "https-proxy-agent": "0", "socks-proxy-agent": "0"}, "devDependencies": {"mocha": "~1.12.0", "superagent": "~0.15.4"}}, "1.1.0": {"name": "proxy-agent", "version": "1.1.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@1.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "e23fd209bcbaa3e6743206f4e5fef0b765c380a6", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-1.1.0.tgz", "integrity": "sha512-pvxoTnm408irYcaY6ihrwwJ4mcRhHfgKZhUfgGv0+yojLPt4S6isdhjZiwnRov03qCyMHLAJlE6GFeji9Gwxag==", "signatures": [{"sig": "MEUCIE0mW/FG12GffKIOMooXDH1iHARudgr2GeN8Ta/L+jCWAiEAnRE0jcEsKyfMDkIcmYQBK7Wjr8VFMKtM6P5ihWGXucA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "dependencies": {"lru-cache": "~2.5.0", "pac-proxy-agent": "0", "http-proxy-agent": "0", "https-proxy-agent": "0", "socks-proxy-agent": "0"}, "devDependencies": {"mocha": "~1.12.0", "superagent": "~0.15.4"}}, "1.1.1": {"name": "proxy-agent", "version": "1.1.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@1.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "fcb1eef5e58965c995f938f029d729fc81858b95", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-1.1.1.tgz", "integrity": "sha512-25H5ZU12cl82fjFJmlq8iYy9gHjw4jdNmJQBbdztwcpPl4URsepnlTZUqAXKwrxwtPXg4z6NbrIiB7e3arxVUw==", "signatures": [{"sig": "MEUCIQClrgjw+1FdjZ38P8Dz6j2VBJ9IX8fhLelq7gfeG2LNqwIgOG/C2OP0S111WbVqAOg3uaw6ZV2O84xvKfcAaQEr1Zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "fcb1eef5e58965c995f938f029d729fc81858b95", "gitHead": "c29e8c0a40fe965a579cc43494fd1088bf91ea3c", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"lru-cache": "~2.5.0", "pac-proxy-agent": "0", "http-proxy-agent": "0", "https-proxy-agent": "0", "socks-proxy-agent": "1"}, "devDependencies": {"mocha": "~2.1.0"}}, "2.0.0": {"name": "proxy-agent", "version": "2.0.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@2.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "57eb5347aa805d74ec681cb25649dba39c933499", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-2.0.0.tgz", "integrity": "sha512-KAJqqQk7BZ/2aWcQ6aVLrA3NzTGNt69HBBFYnqTCy93DbtLSkXJZseFmpBzGI3+aon4B4rkAFxWJwzcb1cvCgA==", "signatures": [{"sig": "MEQCIFO51yybPsCXr0rqBBdPyA/SvHyYlu1o4JWcTQHZQzunAiBCvJhqK4KDfrOKrTFj372JPjbNo4InXSXBkd5YCpM4dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "57eb5347aa805d74ec681cb25649dba39c933499", "gitHead": "f437275e85d069898d0ae1d9642849f1878583c7", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"debug": "2", "extend": "3", "lru-cache": "~2.6.5", "agent-base": "2", "pac-proxy-agent": "1", "http-proxy-agent": "1", "https-proxy-agent": "1", "socks-proxy-agent": "2"}, "devDependencies": {"mocha": "2", "proxy": "0.2.3", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}}, "2.1.0": {"name": "proxy-agent", "version": "2.1.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@2.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "a3a2b3866debfeb79bb791f345dc9bc876e7ff86", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-2.1.0.tgz", "integrity": "sha512-I23qaUnXmU/ItpXWQcMj9wMcZQTXnJNI7nakSR+q95Iht8H0+w3dCgTJdfnOQqOCX1FZwKLSgurCyEt11LM6OA==", "signatures": [{"sig": "MEUCIQD/jo5Te/xH8RsSZ7lcfPk9zJETr3zedkD6KmqTtxSLzgIgZ/Ho6xHx4+4TJpEtKZPIQbP7509Y7YTTh46mAtRD7yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "1bdfc4ad7a5d54ac9530b9f1c37be08338b5cdff", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"debug": "2", "extend": "3", "lru-cache": "~2.6.5", "agent-base": "2", "pac-proxy-agent": "^2.0.0", "http-proxy-agent": "1", "https-proxy-agent": "1", "socks-proxy-agent": "2"}, "devDependencies": {"mocha": "2", "proxy": "0.2.3", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent-2.1.0.tgz_1500576220000_0.9494226421229541", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "proxy-agent", "version": "2.2.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@2.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "e853cd8400013562d23c8dc9e1deaf9b0b0a153a", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-2.2.0.tgz", "integrity": "sha512-cmWjNB7/5pVrYAFAt+6ppLyUAWd4LhWw47hkUISXHAieM5jT2PWjhh1dbpHUEX3lJhWjAqdNGrW8RnUFfLCU9w==", "signatures": [{"sig": "MEQCIEi8yCNnS0H6RgfaZ5ZLcNemK1kbkUE5uVFQ6hCTIGnXAiBfKy2Q6lVRSBiVW13ayjYEsA4DNXd8eKeCqEjy/0ijNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "c007ea11f1eec4b0f5c203f3935bbb2b4a750631", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"debug": "^2.6.8", "lru-cache": "^2.6.5", "agent-base": "^4.2.0", "pac-proxy-agent": "^2.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^3.0.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "0.2.3", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent-2.2.0.tgz_1516060456390_0.5609507695771754", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "proxy-agent", "version": "2.3.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@2.3.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "5d6508c91717faf99c21ee750cc813ba49af1229", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-2.3.0.tgz", "fileCount": 8, "integrity": "sha512-o5N/sUb0SmzqsMMiCvk/jdAjWqFVF0boP/IBOPyKwYw1fSlLz/D1lcKxwqxpVs+sCKS7SPhTjk4qMaKbE98E1Q==", "signatures": [{"sig": "MEYCIQCZKJYOF/TrH8xJ5MFf1jbNN5e4BNUYhH7t7MKvcVabQgIhAMYzcawHay6ZSbVZW7aRVeejJnyxEopHxA9frs9mHWsA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23543}, "main": "index.js", "gitHead": "85c9c2e1d0e6ab0a86b9485a76ca22e2b8be9fa9", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "lru-cache": "^4.1.2", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^2.0.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_2.3.0_1523472793149_0.8466469035509034", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "proxy-agent", "version": "2.3.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@2.3.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "3d49d863d46cf5f37ca8394848346ea02373eac6", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-2.3.1.tgz", "fileCount": 8, "integrity": "sha512-C<PERSON><PERSON>uhC1jVtm8KJYFTS2ZRO71VCBx3QSA92So/e6NrY6GoJonkx3Irnk4047EsCcswczwqAekRj3s8qLRGahSKg==", "signatures": [{"sig": "MEYCIQCs45QdeApyZvHlVP4vgus3rBQ+lLRN17ArMCGfD3pahQIhAI/GSKF/rjOv1JuqBO+1wHIHXgRhXAWtqFFKIOE4V6Vh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23658}, "main": "index.js", "gitHead": "8f1cfc864515e12df0957d0d520e218f4580f724", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "lru-cache": "^4.1.2", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^2.0.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_2.3.1_1523553129857_0.0005078183832063488", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "proxy-agent", "version": "3.0.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@3.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "f6768e202889b2285d39906d3a94768416f8f713", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-g6n6vnk8fRf705ShN+FEXFG/SDJaW++lSs0d9KaJh4uBWW/wi7en4Cpo5VYQW3SZzAE121lhB/KLQrbURoubZw==", "signatures": [{"sig": "MEUCIQDzsoOU37Or2zDanqiYlgC/TSG/PWtWFZpKu/It+NIVSgIgb2KKTR3152p6pI6tIhqHPq2ElI+mWMqFiWqbMRQz69Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23788}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "65fcf7af73fb1324c7e29ff7dee63b962951b457", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "lru-cache": "^4.1.2", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^2.0.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_3.0.0_1523553352511_0.9312493651639702", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "proxy-agent", "version": "3.0.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@3.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "4fb7b61b1476d0fe8e3a3384d90e2460bbded3f9", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-mAZexaz9ZxQhYPWfAjzlrloEjW+JHiBFryE4AJXFDTnaXfmH/FKqC1swTRKuEPbHWz02flQNXFOyDUF7zfEG6A==", "signatures": [{"sig": "MEYCIQDrUw88iiHo6gTYQzoSwT5yjix8C7O86SQP+i8+ewRhLwIhAIMMHOpIH4NK5Eifw3rd7B3A05D1d4QbQt752MsT0cE8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRldwCRA9TVsSAnZWagAA5OEQAIxf0VzwwzLBun6NtypD\nsCBqKitrtwOBkGmtAEwwa4jXtpMk5RcARsuGgq+R56jwpobZVJhFkj6iotGQ\nUpNtBYarCYuUkZsR+3UG2l9lCNibamWNS/YfgHvnOI0JdhZeWEI4+oL/O+l2\nLzqjy4zPszvjNHn/fJF+pdxIXMCw572PQ4nnyMNoiU6t614+2QbouK8QLLp4\nNHjvga+XaqEKr1NNbJER70EYC9AHiOyMoWBjYcxW3EZEfpef7DErz4AtmoZ7\niH9qFpFdGCWDRnzv2vl0L6lOJ/WGVI1R6N05RhrqnqxjHRN/q0hqwHhZnmBU\nK7bEOObnMaaykxScvIJihFzDqAXSVxAK3D0S1WfyBrvom62FjfmND6GK78yi\nn0v59VYv02ZdxZHRp2AzXXmwNGvqHAYFkXIzNlsJlWnMPosXchTv8vAC40li\nrRpdkXQY65YJpaJ4HDaBPjxyAOt1sfPcabx9dH12TOashPKM4RerQWRM74aQ\n1FP90K88KVTantz0nJbx8Qozv6EFCjU3vWTXH70ciUT9e7hx76sz05yXZ57g\npXfQAl1Sd/IO0Ymb9wAg2c124mRz9/5My3uiOpElrg7IE7B1TzW7XSWOUwu5\ndtRCxm///7bAp7UimqCn7AV8AmISaxlbhA+tcScjiWjmLOeLw1I1Tq4E8jvg\nX1Dr\r\n=XRDj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "d392bc78e0815e958b74c6264e800b1a94a21b17", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0", "lru-cache": "^4.1.2", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^2.0.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_3.0.1_1531336560671_0.0822589773718394", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "proxy-agent", "version": "3.0.3", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@3.0.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "1c1a33db60ef5f2e9e35b876fd63c2bc681c611d", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-3.0.3.tgz", "fileCount": 8, "integrity": "sha512-PXVVVuH9tiQuxQltFJVSnXWuDtNr+8aNBP6XVDDCDiUuDN8eRCm+ii4/mFWmXWEA0w8jjJSlePa4LXlM4jIzNA==", "signatures": [{"sig": "MEQCIH0riHCRVk5HV5+LDLD5FYPpiDuApdtrjNLK3rQf9x8WAiAvhjGR6rcZiJCjVOibpNOdUver70aTUd6T3i5nVN8NrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblyDPCRA9TVsSAnZWagAAhlwP/01LGFiC3WVPLvQI7vY7\nM5UNwJKfIVJpXQF/MLrdgf1A3nYWnEhJJCDADJVliROo0V7vJp7xzec62N5J\npGfltZZy0eLKSk/aTbPQFHQUpwbjihKTbNMJQnFdHIsnpF2SARNl/RiDfaSj\nXgMiiA/p3ntdqlRgeSGK//HJyQ7/be9LZya1lQHJevVrIkHS1A8j1lRsXStY\nCyFfg01ps0UTP3W1Ha0k6tUzQIXCS4Kskshtr9E/da7swa3VrG+8tzNSwVqV\n05864rKrFC2nBkvu42rDvxGMOcRMPr7d4Z+20HJhog28qcBlrk1zYjhF6hIr\nyABTZ6iPj/yZCk6OIJq/6tNNBA2mBSkNFoeX4BIPB7Frby2IS4cpOht3K72t\nuz31S1ePTvUH0s7Q5Fyk2/8O7Y0yiqUOn3sD1bi6sktwn7buH4dxJMbXQpY6\nCigfHWi9m3kyKsfPPYj8oJCC3jnxNXPa7VxcjuCeJKy6m/QcQwjtBndX+AHA\nGMYyJ3NuMnZBS/6IXcibVEb0YjEIOaORH3l9NolxUvqcANrJ6r/RgrDowwHQ\nLdHWiqBZQI/n2/bSiTtajbECtsUWPjAXXYImqAmwVxQ74JAlVlinijyMMzyS\nEpHbdXZnkniALexZUkf0vhzOLGoV5iMqC4OPPsx4T2yyH2heAUM6GuZ31eGh\nJ4QU\r\n=IRXE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "bfe9ec5adf0a3e92000763069dea2ef2c5a16a9d", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0", "lru-cache": "^4.1.2", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^3.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_3.0.3_1536630990687_0.677388764472937", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "proxy-agent", "version": "3.1.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@3.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "3cf86ee911c94874de4359f37efd9de25157c113", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-3.1.0.tgz", "fileCount": 9, "integrity": "sha512-IkbZL4ClW3wwBL/ABFD2zJ8iP84CY0uKMvBPk/OceQe/cEjrxzN1pMHsLwhbzUoRhG9QbSxYC+Z7LBkTiBNvrA==", "signatures": [{"sig": "MEUCIDDYED0UryNl59K+u4QVZuEiPGHNbuil8+Tsdg3ePiXnAiEA+VvZIJINzQwd7Rn7jqd+Q7ns0QH2KRrzs/Fc01wg05w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckb2ICRA9TVsSAnZWagAAS1IP/0S9g6M57x9kKtmQIv+A\n3WyEGBIPb31Az4sqmPhEHMc17k5A4MjOTvFW5BQiQkL0NVi39yXLRyCacXoA\nmX/4swWBXYUDwNp817xBzAS494ZnpWRp5yDdpvG0UqgyAs4Tw0GJKN1ohg58\n1YdcFFVfF4+WPTviGaiXL7ypKzN3S0qCdHwDFWOQAGln6khiRq5v5IWFrrew\n4OzdKnqIptuuM/qscDvOIAYDh3eedzTWtb7T80OtoNv/85EZZnFy/bBi7mLA\n02gR97q0XoQ00e1zHGIxqJ8ZV4WmKnXpeUj4lcFEZMsf3RDcABldnTIKOpwg\nLmugpEwKxXN2beTId8dMdM1+e/cCN4NRw5XXF/8bgppN2FqaxF7a8s+qFoHZ\n8tQ4Lc8x7KQ8E2idY/0reDA2AIfQFYlWgfzXjgZ1NzNWc+BG0yvebEL1vEuD\nyXCZQd24T/35vxIlSk5JOccc4tWD3/eHis8B0BxSyaWvsJzepYwBWd9w9RJp\nlNDQd4DJDQmRVOh4/8FKwmjvq+QokOlu+Rp4ZVxKIzRTRx3mBJJP/hOf26Eu\nlBEorO07PnWaTsKaWdhMJkS047DkxXdgR+C5PNQf2OWbk8hEeyDlEPdJnXMa\nhPx0QwCG/k0y1lHbNDvZDgwtPTUJnfBdKO+U43EQIfaPE+dmq/KkhOhs8DFa\nAe4s\r\n=qM/h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "10ff67b88644261a01dc68bcf7bc59f210601459", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"debug": "^3.1.0", "lru-cache": "^4.1.2", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^3.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0", "@types/agent-base": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_3.1.0_1553055111695_0.7062021843711836", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "proxy-agent", "version": "3.1.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@3.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "7e04e06bf36afa624a1540be247b47c970bd3014", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-3.1.1.tgz", "fileCount": 9, "integrity": "sha512-WudaR0eTsDx33O3EJE16PjBRZWcX8GqCEeERw1W3hZJgH/F2a46g7jty6UGty6NeJ4CKQy8ds2CJPMiyeqaTvw==", "signatures": [{"sig": "MEUCIQDBFrxnX4g0YJ9vZjlurWJW+x6Km7BOJJP2TQOj58GF2wIge856b4nxYUiOyPQIGlsFftzQrnMXcsKTbFlTJ3PTefI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrl+HCRA9TVsSAnZWagAASV4P/25NtgSpxtwfmkXHZ5Id\nQMc1nxklENYENGVLCfEYLdWbn9c1K7iksTzNlbK5H/LPsMFA/lJKREVS0ZgM\nL9eHIXvsbSuDDRTlgTBOnfevQe4k7aJLTgSYAdUJq0LR4Y2onFhNQZCStNpy\nLvZnlhHPCDVwdH58ywhYTePzG/cBhpr3T/lZj04qJ3EEaNcD8x7oWy+QubU6\nkH4QgssiZlEVl6NC29HrjknMkewmaP6QDwCNCF1fZTa7gDbrxTP35jyIoVe2\na0LvQ3XmCb3E1nkzMgWSXLf+JIaCcZyNjyh6MqCqPkucvdLxKBBjFttalACM\nKitZhpz3nUsyYgerbVNhbL+/6Pr1CnzXqomVTFy1bkOcxcKR1Msf5gAA8Nhr\nLM4ekmevOGJDDcFbPwYbxlVtTkm4JsY7iFNWNLrDXAly2pF0HHbFsZ0ZYMCi\nRINiYdPgNGzXd/7ayhFjCd/6PY1IY7hSyHjNPoqsSya5MY6xjnebgP9oAqdc\n77YncQDbolHK6P/gFHd68hzhlq/HFBFeEMTrA0Ui/SYjfXlJgajYspodXZoJ\nuYcHCHIw827SsdhMTur18jgUIZpBNCXQkQ3hjSBUtazJerlpzgH4eKOZ1qJT\nu6tLRqGcky32gc6NuHLn14jHAT9ZV02x48PuNWMgeNmwALzu7nYJMGT0wmHa\nFgjP\r\n=PP5H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "4af6f5e38a41680f97238639804014abb04910ec", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"debug": "4", "lru-cache": "^5.1.1", "agent-base": "^4.2.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^3.0.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^3.0.0", "socks-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.1", "proxy": "^1.0.1", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0", "@types/agent-base": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_3.1.1_1571708806878_0.7136547046786179", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "proxy-agent", "version": "4.0.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@4.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "a92976af3fbc7d846f2e850e2ac5ac6ca3fb74c7", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-8P0Y2SkwvKjiGU1IkEfYuTteioMIDFxPL4/j49zzt5Mz3pG1KO+mIrDG1qH0PQUHTTczjwGcYl+EzfXiFj5vUQ==", "signatures": [{"sig": "MEYCIQCtG09iek9JdxPod3C33J6Glq7/uMqLGfl4QpQQUrR3iwIhAJWVfZs6k1N7qF0wsgofi/xzh0te/UybKzHLleQT9/z6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffj8FCRA9TVsSAnZWagAAdrkP/i2lUhp75Z23lFjbaJop\n9vnyTb72KIDsvthtHkC9NboFkKn40RiC9nBfqjIaNxkaYPU75sPp8mdq9rFz\n2uFvyb9jdjMIcv309rg+hLSL3pf3nrgtOz66AvVVf14fezqNjck82uKMpLLW\nm/EnQ9qsfBnaQUkM2c7DP6hIkbo8gTnOsNjYPGFRT08to7Hh4DSo8inR76bj\njIvxC2iQ8jVusUdqnX/AZUIFhKNJCWIl1Dnia6aOG/iibx/IU5Q1dYGg77JM\nUtjzX2ZL4dY6JUg2kNSBplDuydiCiDNE2wDV6zGhbkAB7mmPLw1b8hjaiea6\no4mmWlYQ+/6N1vTlLF749nPTfJZz5B7kjMyPAA5f66S5svl5TfIh3WJZ2MXr\n7zXDBxwB324OLr/yWGNVFLdizCFjKGugVJ6w7ooZR4OKdQzmTLCmfy6rFteD\nR1jHxS2mUnypJzc7V+E/Qlt3hTAiZhUKgbACfiajYyvVMcLJSdHTQ8XeObYI\no8pPiwCTnhKihI+RjAFna6T+YdMWzIGTiIJd6SrhBQMJnelnPeL5cEBWHkAE\nl2TxlSA90qAuVdwsOTzv0tlICXkuanwYBnoiKvaknRzHN3PnEKDzFMCMa3SE\nK4Nx3q7wIcmqK0ayxmVK+rzLRWgA+DZfCbqEEvzr1jjJkKBnxSRwi60kqk2u\nUbZd\r\n=imEr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "b9fcf37d50371482dd98c88ce82737d4d38aa3a0", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "12.18.4", "dependencies": {"debug": "4", "lru-cache": "^5.1.1", "agent-base": "^6.0.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^4.1.0", "http-proxy-agent": "^4.0.0", "https-proxy-agent": "^5.0.0", "socks-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.1", "proxy": "^1.0.1", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0", "@types/agent-base": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_4.0.0_1602109189257_0.8926140647152327", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "proxy-agent", "version": "4.0.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@4.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "326c3250776c7044cd19655ccbfadf2e065a045c", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-4.0.1.tgz", "fileCount": 9, "integrity": "sha512-ODnQnW2jc/FUVwHHuaZEfN5otg/fMbvMxz9nMSUQfJ9JU7q2SZvSULSsjLloVgJOiv9yhc8GlNMKc4GkFmcVEA==", "signatures": [{"sig": "MEUCIG6mug/75nK6tokw2Iu2MSKDZknvU7NGKx+vzzsHaJbfAiEAzghkxsiqNEaurhoeZaUE5lvaB9SsJNoggIkLfkjW5To=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/OLaCRA9TVsSAnZWagAAKDoQAIAm5H+Rvr462Fmi1Zyl\nRW7D2IFtawtzm6miLN3F2adnbgwbyIlNiILngKkf9UTZ+H8LlmKRS2fEAksu\n4Q5nrafjLCsE2MzD1zgOJ0kAhslPS9oXUnywiMhIaVPNh++TsUXSgEbbjQXB\nsJlgaxM2zKFpZEMygdwmduFfaLW/Gunn2YV8VmC4ndq37A4+cs/7YJtgM2Tk\nY8jwEr6taIQuyuvOwr+PbQZd8DPWNK3Rp1HXCWFPwEhNgSdhbQm69xX0w3+s\n8tvwDGxvF6OnnxiB4bx4Nt9DKu2TzAEnXTwfjvXYk+ofs7FF6SS6fUQyYi3Z\nOcBow6KwUZhhofhqGEEjtIPbJeNBqKLoK2gd3Zicg202l7l0NkH5aeW+NBle\nSQwgpQhNCBjeCjyo2Za6URt/aUG28rcCJuxWon3DQeTAWVy88CFoVcu8uJwA\nSipno+PVWDIysJZT9RmqNggXs0oxjKCnSqP63d6wOQy4yENqtAvBw2pHltAV\ntkopzlk/BnssApCb4SnsyPDZFM6ldpTN3zgdag5UvSqTyV/BCbmzaugPa4MJ\nKODp6XIKIcY3fxlqDGgrJMidgxLTcGwdlqbOUPSwgeX8gauiE9wn91Mgjo4l\nYbTlvxU0DkEM7QAo5QdlAsqWVFFZs4URjg7oVyEEUxMAJttZCEPaZPGf7/bl\npLhl\r\n=1Tnx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "13b1544515d7a9724ec25a41d31e33be1a4db04a", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "12.20.0", "dependencies": {"debug": "4", "lru-cache": "^5.1.1", "agent-base": "^6.0.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^4.1.0", "http-proxy-agent": "^4.0.0", "https-proxy-agent": "^5.0.0", "socks-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.1", "proxy": "^1.0.1", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0", "@types/agent-base": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_4.0.1_1610408666357_0.1256997435619236", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "proxy-agent", "version": "5.0.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy", "cache"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@5.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-proxy-agent/issues"}, "dist": {"shasum": "d31405c10d6e8431fde96cba7a0c027ce01d633b", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-5.0.0.tgz", "fileCount": 9, "integrity": "sha512-gkH7BkvLVkSfX9Dk27W6TyNOWWZWRilRfk1XxGNWOYJ2TuedAv1yFpCaU9QSBmBe716XOTNpYNOzhysyw8xn7g==", "signatures": [{"sig": "MEUCICEymqScyCcXm/jz4+/l3F7IbC0vwVlYRvyh7c+kpEerAiEAvKvlZFwdCZSP/HSdEECBKAtXxXvqtUD/tf0+wlUJ3Yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7zldCRA9TVsSAnZWagAAhh8P/0RVosBefBkeXsdmCuHJ\nG5a8xOAI2hWn2HciIt6a7wyKhQ4KPbQbgHntQ3+BoGtIfIkMwNtPy0MMzHem\n47o9k9ZHEizPCWUAL5ZIReejQsBaHJoUwXzo+aivjkqB24GxT73zUJK8cXxV\noqKQjqLZQJianGw+mk/jp+rMfJK8HChI2hmA9acrfW8GkrFIhFNFLdk5TOyD\nuwdZzmmyotHk7dKBctCzgeuAZjYTalifWxuq46h540B2J7b0sBhHyAScLDul\n41YyoYITJFubomlXkPYa3A5p7zDYlnSUrsA8p7dP8CYA5DXpaD5pvHwlZgth\n4ABpnw40ijsj3SHrzzhcCM94UmYRY5bstqKvfWbXfDtVmhAKMhH2kEAA2m5i\nRATJz2oonHkr1deowSpw69Y+nNXE8r2UDdKttDf5uQEzsVvXOj72EbkBIDh6\nhputHxEMoBPIX3wD/7ex6iLcyMAK7xIEPY2VraASx2o5OfNlAEkw88p0QuM/\nd5ZYsI5W+vXot68pFScQ30nCupfwH9etyfRGyDNUUFfTkrbqbVz4mmhKOcGh\nfaJAvFTNzWuQZNBLk3sD7vKRjUFuIBo9cZFmKjum/KAO/DrPcAx0SR9bqZ5R\nldRZ2WeEJHRAz5oKo3UTx16I8kM5oLkWceroRTpX+8gDt2I/QT603KyMKIJ3\nzHss\r\n=S+m2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">= 8"}, "gitHead": "3f4571097d22c9b9ac399d5b060c36b6e2caa993", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-proxy-agent.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "14.17.3", "dependencies": {"debug": "4", "lru-cache": "^5.1.1", "agent-base": "^6.0.0", "proxy-from-env": "^1.0.0", "pac-proxy-agent": "^5.0.0", "http-proxy-agent": "^4.0.0", "https-proxy-agent": "^5.0.0", "socks-proxy-agent": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.1", "proxy": "^1.0.1", "socksv5": "0.0.6", "stream-to-buffer": "0.1.0", "@types/agent-base": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_5.0.0_1626290525131_0.4764037199089788", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "proxy-agent", "version": "6.0.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "bf472ff253bfe9cf180b1e03ec7870299bd8b267", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-3NXhU3GYHB9QtW7TNOjuevlwqgPAyJoWef8BN3VBODUiAv9Z0FSuCw22DcCPGJwVXXQ6U+urKD7j7e3oBg3Qew==", "signatures": [{"sig": "MEYCIQCxIStHck8XYQs5WQrx3linSEzCRbOcg4fLRov8/kTP0wIhAJElxNqINF+YlhE8enG8Hg7orQBWxm4Y72rsauc34rho", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzlA/+N4fWIdAHTcxZ6ACdLzxxAqG4uo+ycsLdIvTW0WpGFrV0oA6G\r\ngOvG+X9U9bv7/a8TxJpGtjA03Z/Qie/goE2b+WwigLSEryYrx0Se9x3OEm74\r\nfvmacK0epKjEH2OcPruFm8HY77ASwqdJFzAcUW9LlSApbR3L6IRLGdQhyKTX\r\nFoW78n0Y3xo6moErNLO7NdvAjLKXJmFHzB9YNun+7HWRr0K/VRhBnnDvHz64\r\n7WG3Liu6Y+0Pe5E5iqpJ7RAj3ulaOb2SHs45hJ5Qumedbal40i+ibNdGpAqk\r\nBeCpTVab1waH4SePIVjEEfmc6rhQyemhCoqiWj1dxt5yYhkK9fvr6ifuQDuT\r\nSdGnQVXI68JVlvO+V/2ZI0j+mBFC+R9KVUeWO0x8XO3ulJ0i1t9D/JUu8qkT\r\nyQUFHje5sApCGcY9UTNzGrPhndm+RA/iwJuRnyqz75Mhlk2SXR/60Psm2tp/\r\n49Ap5AxOWWO56B7TjDb0uSQ9zS1Kw6zuaq3jh4AE/U96HmCsaaMgrPOpWRx6\r\niziGojCTu3QIlWJPx3WAJ65YpYEZVKMp59bpz8UpjTB+fsgQ7lXSPbMBhqVq\r\nEjGqKRMb5f/IAPm0AaqwaQmTiYpqZUYNAfUwunbSbmgx9k7Zy/oTf3i/X5Tj\r\nUWfDWHdvpoCt+4BE58+uMGy0+IXkT7B/wTM=\r\n=3/q/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/08b1e85cc17b73638793d4e793a4b85e/proxy-agent-6.0.0.tgz", "_integrity": "sha512-3NXhU3GYHB9QtW7TNOjuevlwqgPAyJoWef8BN3VBODUiAv9Z0FSuCw22DcCPGJwVXXQ6U+urKD7j7e3oBg3Qew==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.6.4", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.0", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.0", "http-proxy-agent": "^6.0.0", "https-proxy-agent": "^6.0.0", "socks-proxy-agent": "^8.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.0.0_1683232399609_0.3048611394924352", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "proxy-agent", "version": "6.1.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "f521bea0d32af3ac548700cac3968d80e3ca45a3", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.1.0.tgz", "fileCount": 7, "integrity": "sha512-nDAWiG4h1HIVlsK2xxdDKvR7ELlYQSbNnTrWpd7SNHg2ECOcU+b+VojGxqcXk7UhSTJojhBHUeX8P/zs91JwaQ==", "signatures": [{"sig": "MEYCIQDYzsgpylO/Wg0FjNE+pTANxXKIB0ODJ/FKP7hIW6fokwIhALtVDuCVz/scZkf/I5Q1tHrEuFDglm41j5p9NAhKWgZ8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVByDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxVQ/8CyHiPTIDl5mGfScorP4dvHIVY3DcXjsE4/jwRDZCGzkEqayI\r\n+xPkCFjRMnleEvXR6462Oa59ZfknbF0IIvonIj0ai137UfbdPTrQKQpyPNWI\r\nUnMn5BsmkoLTJbNZv8BBWmt6EVu2d32p/k/xRcI+mRUUeOCKG+fTWlo4dNcS\r\n2yj54WZv8NDfrvaA580Yq1y/Nx7J5boRpi6VK+sO78xhYTR1MxL0aSai/mNl\r\npLOJmCle7D94U8ELVuYhAQEOUZa1xmacGK1/aI9uWkvC1725jehSBxv7ywRo\r\n4KteKINVjWN/db2dJWpYrZQ8URwc4iq5uWUIIcLtjP+I1iRTbTI+eJMJwsAW\r\nX9MvKHN+jVyYlWmVFClxSowHbrDAsOkm81RsfWqkfz7wMyAA7A2/+ZsmJ5/r\r\nd5BxLDVa9koj7YUNV/Ilpr3VlIjvt0DenBYssd/RlYpVfRCyyt3UaRn9QIIM\r\nOPgiQ72rBToHSTNcT438okua6BpiVRd1M5qm8j92zOU1knMaoCTnnzCUtkQW\r\nm+jCQfDkkSjJRJ5Hl//FuUH3/btJwME2dK4zD8adMTR5v0OiFHO9d/zvGx0M\r\nJbmT1gCk0oSKoB8E5Hr2+v0bhhXB0Y1CMRNKkYGBjfqcbfaTTrJp82ck1RCA\r\nSDe5PifxKd5MbIYKxlPtLb8pCNvLzhvxavs=\r\n=Ahuh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.1.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/c510e08bed1e29c0c64ad0aa2d42a237/proxy-agent-6.1.0.tgz", "_integrity": "sha512-nDAWiG4h1HIVlsK2xxdDKvR7ELlYQSbNnTrWpd7SNHg2ECOcU+b+VojGxqcXk7UhSTJojhBHUeX8P/zs91JwaQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.6.4", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.0", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.0", "http-proxy-agent": "^6.0.0", "https-proxy-agent": "^6.0.0", "socks-proxy-agent": "^8.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.1.0_1683233923318_0.7742404711550455", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "proxy-agent", "version": "6.1.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "7062d773936cff6f19100ebf95c0aa26cc6c0c0f", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.1.1.tgz", "fileCount": 7, "integrity": "sha512-zlPFyvAulI8LC8+oXqJI3ygVHnEThwQcCjlzrmOM2esDTlDh3cLnDS9hC6ncXj1sra19JTos1SC3MpOYdE5PZQ==", "signatures": [{"sig": "MEUCIQDls9xSywlh7+IZmB2e+rIGwVnvxuld6f2rfp4A6HF7nwIgM80kz/+Zce1SLyzo4yq0zHVJnhf+h104oiuuNFK/o+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVC1UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOWQ//U39h4ElNfnr96lyQAgwZ4svNw2gyo+3w+AR/UlGxpJr7VAO4\r\nciYBVKA7iVdrVyJK9qicwA0Wkt/KTSx5xUWx2Pr52bcoVOFvytzhuWF1YTvb\r\nn+ggvqsNrzlbKaCCtXOo2CMOjrG1sZZHo5FwsI8o6+IdwoolIG2b8O1hk6hL\r\nB2EFfTRypKaF1JfCIh5fMKDrmulCNBTn1/V1hqvTXZuJWGTfAU3LxSf+IRhf\r\nIdtPi6jNwMLObGsjPZ+zr+Rc/EW6wooOzATLdAa8maa++VvnEmEfwpoQWqk4\r\ntU2S8OEbJfdvpDMP8I7CiDXNgULbkZAd4I3lMUA3zxJHOsLvz6IsDdqz9KHp\r\nhFCgFHBxTPFIQM0i1bNP/+hTR2Cz/myeQVrLn0muHfCuSjbxREBGfFKoK00U\r\nGzEx7MDYnjzRfTb5+MlMtmwvh1nwJHoCUBkvgd0UwRUZneNXHLTGX1y22mje\r\ns0Jd+6iYwk8cPEwTWOeGzc9Uz+giFkYIcfIVoNiJPlyBzzWwqd5X4P7DYWlT\r\ntydFqPoRsa+cwUshKf/CP7RPQUHpOFWNmH5WLXdL1F/DySCe0SRnScklGq3S\r\nXXdvPE/9wnjaPgBz2y5mnCWDAnzRTz3bVrEnxI4za5nj6T1AkiuAwGdvQFEe\r\nsB6cYIwz1pbDMf2kwA0C4wAAfkPi/ebq7S0=\r\n=h1xn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.1.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/7b193a03e1ba6015039f74976869eb95/proxy-agent-6.1.1.tgz", "_integrity": "sha512-zlPFyvAulI8LC8+oXqJI3ygVHnEThwQcCjlzrmOM2esDTlDh3cLnDS9hC6ncXj1sra19JTos1SC3MpOYdE5PZQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.6.4", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.0", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.0", "http-proxy-agent": "^6.0.0", "https-proxy-agent": "^6.0.0", "socks-proxy-agent": "^8.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.1.1_1683238228153_0.6164756286865025", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "proxy-agent", "version": "6.1.2", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.1.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "b784daccefb0ca7f1b0627048fff75165b26d998", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.1.2.tgz", "fileCount": 7, "integrity": "sha512-h4zy3UcwJ4GgkjoQQzpipTZfW4SMSN1CilTA2yZZsrvP7oOhxd85EpsN/lliCBCUYUonoBB3Z32RYifL039X4w==", "signatures": [{"sig": "MEUCIBENhWNz78aDb9aboqjYrj07U/i8hl3xjO1NFk51+LaQAiEAxZ/o0lsmC7ih+tolC1m3B/0F3U9btLn6qulCv/U0vXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22269}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.1.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/3932c348e415d6d7671edce00e8dce4c/proxy-agent-6.1.2.tgz", "_integrity": "sha512-h4zy3UcwJ4GgkjoQQzpipTZfW4SMSN1CilTA2yZZsrvP7oOhxd85EpsN/lliCBCUYUonoBB3Z32RYifL039X4w==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.6.4", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.1", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.1", "http-proxy-agent": "^6.0.1", "https-proxy-agent": "^6.1.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.1.2_1683324250251_0.2665588487372419", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "proxy-agent", "version": "6.2.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "03c9d5fb85c6c80f1b27d101f11f4f364b7838ed", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.2.0.tgz", "fileCount": 7, "integrity": "sha512-g3rBHXPhEa0Z1nxZkirj0+US1SCcA67SnjpxbdZf7BloLdULEUCzbQozsq+wFwhmMeZegeZISDZjPFN/Ct9DaQ==", "signatures": [{"sig": "MEUCIQCF7Sjdujh8lW90RGrMtcL0TejoNTUjrQ15TOlIRFzJiQIgZEPOcb71GNZ52np8iQeAqwLs/IWVhhCodXafRkMmu9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22947}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.2.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/03ecf7a32c4342e59e5a500a6daff053/proxy-agent-6.2.0.tgz", "_integrity": "sha512-g3rBHXPhEa0Z1nxZkirj0+US1SCcA67SnjpxbdZf7BloLdULEUCzbQozsq+wFwhmMeZegeZISDZjPFN/Ct9DaQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.6.4", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.1", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.2", "http-proxy-agent": "^6.0.1", "https-proxy-agent": "^6.1.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.2.0_1683623020727_0.0076394555148917576", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "proxy-agent", "version": "6.2.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.2.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "062df6609a4012fd1c108974865599b61e77abde", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.2.1.tgz", "fileCount": 7, "integrity": "sha512-OIbBKlRAT+ycCm6wAYIzMwPejzRtjy8F3QiDX0eKOA3e4pe3U9F/IvzcHP42bmgQxVv97juG+J8/gx+JIeCX/Q==", "signatures": [{"sig": "MEYCIQDzYK+c2f1Mf9KUCbtTTN8opGpJnDsn01vE6qDT7P91/gIhAJHILTTGJDgAgbAmBCk+N8JR0b0+nbPcu5mRqtJutft0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23591}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.2.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/16e9df37db55e6ec5487bde2be695230/proxy-agent-6.2.1.tgz", "_integrity": "sha512-OIbBKlRAT+ycCm6wAYIzMwPejzRtjy8F3QiDX0eKOA3e4pe3U9F/IvzcHP42bmgQxVv97juG+J8/gx+JIeCX/Q==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.6.6", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.2", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.3", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^8.13.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^8.5.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.2.1_1684974179996_0.8072385717649886", "host": "s3://npm-registry-packages"}}, "6.2.2": {"name": "proxy-agent", "version": "6.2.2", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.2.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "12224685c5791ab4a2f558c18f3e6ba50b4ebacf", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.2.2.tgz", "fileCount": 7, "integrity": "sha512-wPQ+zf4bFG3wtqX9L8xNEK6vfOmaZABbpN2NslLLSlbfTKbUL7X1LqwpPVdbsbloAFvtWAmnVhJQ3vkagxKUTA==", "signatures": [{"sig": "MEQCIEy4pBl558iC1KMcQzjrQtkt3I78OVtohR6RIsPDqSZEAiBLk7B4stNOJDCVdctElNIhd4Y3zPaIJ3l74Pvso8z+2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23591}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.2.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/66afc8e11972be3fb5de60fd9823a73f/proxy-agent-6.2.2.tgz", "_integrity": "sha512-wPQ+zf4bFG3wtqX9L8xNEK6vfOmaZABbpN2NslLLSlbfTKbUL7X1LqwpPVdbsbloAFvtWAmnVhJQ3vkagxKUTA==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.7.2", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.2", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^6.0.4", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^8.13.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^8.5.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.2.2_1689276869378_0.7441760301488005", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "proxy-agent", "version": "6.3.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.3.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "72f7bb20eb06049db79f7f86c49342c34f9ba08d", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.3.0.tgz", "fileCount": 7, "integrity": "sha512-0LdR757eTj/JfuU7TL2YCuAZnxWXu3tkJbg4Oq3geW/qFNT/32T0sp2HnZ9O0lMR4q3vwAt0+xCA8SR0WAD0og==", "signatures": [{"sig": "MEUCIQD2OGgNZn3QG3Z4H3ts4aFaYvM5vWoG2UC/8jmoOQ9WbgIgK7Kh2+wU/bY4LOciIgYngZEdgAQIxYkeEQPTWm8QXU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23591}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.3.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/5da2ac529bc3175215a8c22c97f53096/proxy-agent-6.3.0.tgz", "_integrity": "sha512-0LdR757eTj/JfuU7TL2YCuAZnxWXu3tkJbg4Oq3geW/qFNT/32T0sp2HnZ9O0lMR4q3vwAt0+xCA8SR0WAD0og==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.7.2", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.2", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^7.0.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^8.13.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^8.5.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.3.0_1689671065309_0.6990282135053487", "host": "s3://npm-registry-packages"}}, "6.3.1": {"name": "proxy-agent", "version": "6.3.1", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.3.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "40e7b230552cf44fd23ffaf7c59024b692612687", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.3.1.tgz", "fileCount": 7, "integrity": "sha512-Rb5RVBy1iyqOtNl15Cw/llpeLH8bsb37gM1FUfKQ+Wck6xHlbAhWGUFiTRHtkjqGTA5pSHz6+0hrPW/oECihPQ==", "signatures": [{"sig": "MEYCIQDkqoGwaPyswCUnRSmgwIeL8xk8Gv9+moc7Q/+VeUjTSgIhAK0w+i2IKnZm2QO0ucK8uPrfHCVCtkrwWh+yvnPfcfFM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23688}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.3.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/28f129aa929e20dc9137f7a26a6be7af/proxy-agent-6.3.1.tgz", "_integrity": "sha512-Rb5RVBy1iyqOtNl15Cw/llpeLH8bsb37gM1FUfKQ+Wck6xHlbAhWGUFiTRHtkjqGTA5pSHz6+0hrPW/oECihPQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "9.8.0", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.2", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^7.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "socks-proxy-agent": "^8.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^8.13.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^8.5.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.3.1_1693814988523_0.9117206591458455", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "proxy-agent", "version": "6.4.0", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "proxy-agent@6.4.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "b4e2dd51dee2b377748aef8d45604c2d7608652d", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.4.0.tgz", "fileCount": 8, "integrity": "sha512-u0piLU+nCOHMgGjRbimiXmA9kM/L9EHh3zL81xCdp7m+Y2pHIsnmbdDoEDoAz5geaonNR6q6+yOPQs6n4T6sBQ==", "signatures": [{"sig": "MEQCIGuz8rnwJYX+Q0It8iIZTFIiV1kA9NSKUsmxOGhF0ZSjAiAlJbdWaZFva2lRTZAipgrdLICmomaLNO8zy8nisegT9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23721}, "main": "./dist/index.js", "_from": "file:proxy-agent-6.4.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/68500d4233105dfb0098f8e230c66e22/proxy-agent-6.4.0.tgz", "_integrity": "sha512-u0piLU+nCOHMgGjRbimiXmA9kM/L9EHh3zL81xCdp7m+Y2pHIsnmbdDoEDoAz5geaonNR6q6+yOPQs6n4T6sBQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/proxy-agent"}, "_npmVersion": "10.2.4", "description": "Maps proxy protocols to `http.Agent` implementations", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"debug": "^4.3.4", "lru-cache": "^7.14.1", "agent-base": "^7.0.2", "proxy-from-env": "^1.1.0", "pac-proxy-agent": "^7.0.1", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.3", "socks-proxy-agent": "^8.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^8.13.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^8.5.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/agent-base": "^4.2.0", "@types/proxy-from-env": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proxy-agent_6.4.0_1707762283453_0.20840057388768596", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "proxy-agent", "version": "6.5.0", "description": "Maps proxy protocols to `http.Agent` implementations", "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/proxy-agent"}, "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.6", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.1.0", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.5"}, "devDependencies": {"@types/agent-base": "^4.2.0", "@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/proxy-from-env": "^1.0.1", "@types/ws": "^8.5.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^8.13.0", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "proxy-agent@6.5.0", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-TmatMXdr2KlRiA2CyDu8GqR8EjahTG3aY3nXjdzFyoZbmB8hrBsTyMezhULIXKnC0jpfjlmiZ3+EaCzoInSu/A==", "_resolved": "/tmp/80e243a0d64687d8603e2a0f197f2e0b/proxy-agent-6.5.0.tgz", "_from": "file:proxy-agent-6.5.0.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-TmatMXdr2KlRiA2CyDu8GqR8EjahTG3aY3nXjdzFyoZbmB8hrBsTyMezhULIXKnC0jpfjlmiZ3+EaCzoInSu/A==", "shasum": "9e49acba8e4ee234aacb539f89ed9c23d02f232d", "tarball": "https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.5.0.tgz", "fileCount": 8, "unpackedSize": 24670, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFRtu6FfHiJc2miLdgs+RfEUZFMA4YFoT0wUSnmiNiF2AiAvWiikhWwlcCPIhDHqO/w3WdXSv0gWienVvSZPSiOVgw=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/proxy-agent_6.5.0_1733542335207_0.07007393420257535"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-11-21T01:02:52.160Z", "modified": "2024-12-07T03:32:15.561Z", "0.0.1": "2013-11-21T01:02:53.177Z", "0.0.2": "2013-11-21T06:59:13.052Z", "1.0.0": "2013-11-22T02:23:13.897Z", "1.1.0": "2014-01-13T06:34:27.679Z", "1.1.1": "2015-07-01T18:49:11.051Z", "2.0.0": "2015-07-15T22:05:38.169Z", "2.1.0": "2017-07-20T18:43:40.120Z", "2.2.0": "2018-01-15T23:54:16.527Z", "2.3.0": "2018-04-11T18:53:13.221Z", "2.3.1": "2018-04-12T17:12:10.076Z", "3.0.0": "2018-04-12T17:15:52.573Z", "3.0.1": "2018-07-11T19:16:00.742Z", "3.0.3": "2018-09-11T01:56:30.772Z", "3.1.0": "2019-03-20T04:11:51.883Z", "3.1.1": "2019-10-22T01:46:47.061Z", "4.0.0": "2020-10-07T22:19:49.406Z", "4.0.1": "2021-01-11T23:44:26.445Z", "5.0.0": "2021-07-14T19:22:05.252Z", "6.0.0": "2023-05-04T20:33:19.782Z", "6.1.0": "2023-05-04T20:58:43.526Z", "6.1.1": "2023-05-04T22:10:28.301Z", "6.1.2": "2023-05-05T22:04:10.420Z", "6.2.0": "2023-05-09T09:03:40.860Z", "6.2.1": "2023-05-25T00:23:00.204Z", "6.2.2": "2023-07-13T19:34:29.543Z", "6.3.0": "2023-07-18T09:04:25.444Z", "6.3.1": "2023-09-04T08:09:48.741Z", "6.4.0": "2024-02-12T18:24:43.646Z", "6.5.0": "2024-12-07T03:32:15.395Z"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["http", "https", "socks", "agent", "mapping", "proxy"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/proxy-agent"}, "description": "Maps proxy protocols to `http.Agent` implementations", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "readme": "proxy-agent\n===========\n### Maps proxy protocols to `http.Agent` implementations\n\nThis module provides an `http.Agent` implementation which automatically uses\nproxy servers based off of the various proxy-related environment variables\n(`HTTP_PROXY`, `HTTPS_PROXY` and `NO_PROXY` among others).\n\nWhich proxy is used for each HTTP request is determined by the\n[`proxy-from-env`](https://www.npmjs.com/package/proxy-from-env) module, so\ncheck its documentation for instructions on configuring your environment variables.\n\nAn LRU cache is used so that `http.Agent` instances are transparently re-used for\nsubsequent HTTP requests to the same proxy server.\n\nThe currently implemented protocol mappings are listed in the table below:\n\n\n| Protocol   | Proxy Agent for `http` requests | Proxy Agent for `https` requests | Example\n|:----------:|:-------------------------------:|:--------------------------------:|:--------:\n| `http`     | [http-proxy-agent][]            | [https-proxy-agent][]            | `http://proxy-server-over-tcp.com:3128`\n| `https`    | [http-proxy-agent][]            | [https-proxy-agent][]            | `https://proxy-server-over-tls.com:3129`\n| `socks(v5)`| [socks-proxy-agent][]           | [socks-proxy-agent][]            | `socks://username:<EMAIL>:9050` (username & password are optional)\n| `socks5`   | [socks-proxy-agent][]           | [socks-proxy-agent][]            | `socks5://username:<EMAIL>:9050` (username & password are optional)\n| `socks4`   | [socks-proxy-agent][]           | [socks-proxy-agent][]            | `socks4://some-socks-proxy.com:9050`\n| `pac-*`    | [pac-proxy-agent][]             | [pac-proxy-agent][]              | `pac+http://www.example.com/proxy.pac`\n\nExample\n-------\n\n```ts\nimport * as https from 'https';\nimport { ProxyAgent } from 'proxy-agent';\n\n// The correct proxy `Agent` implementation to use will be determined\n// via the `http_proxy` / `https_proxy` / `no_proxy` / etc. env vars\nconst agent = new ProxyAgent();\n\n// The rest works just like any other normal HTTP request\nhttps.get('https://jsonip.com', { agent }, (res) => {\n  console.log(res.statusCode, res.headers);\n  res.pipe(process.stdout);\n});\n```\n\n\nAPI\n---\n\n### new ProxyAgent(options?: ProxyAgentOptions)\n\nCreates an `http.Agent` instance which relies on the various proxy-related\nenvironment variables. An LRU cache is used, so the same `http.Agent` instance\nwill be returned if identical args are passed in.\n\n[http-proxy-agent]: ../http-proxy-agent\n[https-proxy-agent]: ../https-proxy-agent\n[socks-proxy-agent]: ../socks-proxy-agent\n[pac-proxy-agent]: ../pac-proxy-agent\n", "readmeFilename": "README.md", "users": {"phris": true, "bojand": true, "itskdk": true, "tsxuehu": true, "gotvitch": true, "zuojiang": true, "antixrist": true, "complexcarb": true, "flumpus-dev": true, "ganeshkbhat": true}}