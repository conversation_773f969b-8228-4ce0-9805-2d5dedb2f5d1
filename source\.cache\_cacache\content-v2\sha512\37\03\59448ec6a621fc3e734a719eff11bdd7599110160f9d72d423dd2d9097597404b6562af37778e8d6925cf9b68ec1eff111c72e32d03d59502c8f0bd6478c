{"_id": "semver", "_rev": "442-84e84c3cc698f0602ebc68da04ca2acb", "name": "semver", "dist-tags": {"legacy": "5.7.2", "latest-6": "6.3.1", "latest": "7.7.2"}, "versions": {"1.0.0": {"name": "semver", "version": "1.0.0", "_id": "semver@1.0.0", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "11f18a0c08ed21c988fc2b0257f1951969816615", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.0.tgz", "integrity": "sha512-3l/lKKm8i8qr6QUjadwmuzJa56Jzo4qfKxBcDi38YZdrHdDl0dmcJnHM9tozk45jTGN3wLWvTE570ywWiKTyTw==", "signatures": [{"sig": "MEUCIQCvpsAf8+xghdm1wUtdahwz2W9a+ynAizf2jyrn0WJlqQIgCupAQTwREapD5ILhR74svN7w/DbWDwXZpzpBkfsnrIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver", "files": [""], "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "_npmVersion": "0.3.0-beta", "description": "The semantic version parser used by npm.", "directories": {"bin": "./bin"}, "_nodeVersion": "v0.4.0", "_defaultsLoaded": true, "_engineSupported": true}, "1.0.1": {"name": "semver", "version": "1.0.1", "_id": "semver@1.0.1", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "93b90b9a3e00c7a143f2e49f6e2b32fd72237cdb", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.1.tgz", "integrity": "sha512-I1MWjDW0QWmfI0ctueHeQuBDh2I3joLhSnOkYf20UBV1esvoxdNV1WgUNsPW0LpZ+Zq5O9nbbfACWsXs8kZQmg==", "signatures": [{"sig": "MEYCIQC0ld9MD4rbXzP93M92D0ATb0SifkmCVzzRkHsaw4S1wwIhAK9cCuQlU0GAedpakKd0H8HzWnO+mjb+bN+2nTPFjzM2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "files": [""], "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "0.3.0-8", "description": "The semantic version parser used by npm.", "directories": {"bin": "./bin"}, "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "_engineSupported": true}, "1.0.2": {"name": "semver", "version": "1.0.2", "_id": "semver@1.0.2", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "57e4e1460c0f1abc2c2c6273457abc04e309706c", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.2.tgz", "integrity": "sha512-pR1479ERuU15GZJ9uWDvfkE7GgBpgS1RJVjJxgidz4ExvhFUMFYKspUYCrsqnN5JyZvLHHM4DaIMqE8Z21gIyg==", "signatures": [{"sig": "MEQCIBVSRFRQXY87k1bNpa+01lze+4eudUfNbCrfzoyW6sbtAiBC7foc+tn2wcRndLolUiTS68UaG3UNJK9vcR7FVNpeRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.0rc3", "description": "The semantic version parser used by npm.", "directories": {"bin": "./bin"}, "_nodeVersion": "v0.5.0-pre", "_defaultsLoaded": true, "_engineSupported": true}, "1.0.3": {"name": "semver", "version": "1.0.3", "_id": "semver@1.0.3", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "453f40adadf8ce23ff4eb937972c6a007d52ef0d", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.3.tgz", "integrity": "sha512-n3g1ulgVjIVSa5IJ/zjNsbqEaLkL1AwVqBBY1JDP/kar37uL7HEuWK4tG7qR/IjhcHbcWUaGkW2ioLH61jOffg==", "signatures": [{"sig": "MEUCIQDQq4382AP3vv1Yp7/yUJzK5Ufgzlsvgfd9Mx4mCUFIZAIgNS8e16nbFiVy2G3OO9inb+lOqBZeLjERpMEdI3NLhA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.1rc9", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.4": {"name": "semver", "version": "1.0.4", "_id": "semver@1.0.4", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "41c0da40706d0defe763998281fc616a2a5d1e46", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.4.tgz", "integrity": "sha512-2QU4IpdkKSBoWbWKU0w3aGvlWyY4CvzFPiDs1bBivfKnomaXzF5fx0+BceFFDmPr4p2fUCeip2PSnid2+utiIA==", "signatures": [{"sig": "MEUCIAYe0E5/51K1hGXxy6ZNc6CUM/uOJHWITN4mhPZY5qRfAiEArTBXN67h6prnTePtCOUe+rwiyeLQc6qqgfxr6QBBENw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.1rc9", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x"}, "_engineSupported": true}, "1.0.5": {"name": "semver", "version": "1.0.5", "_id": "semver@1.0.5", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "7abca9337d64408ec2d42ffd974858c04dca3bdb", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.5.tgz", "integrity": "sha512-Z7h3RyOZVszoXynP9IU7j088RvIoi3NivBWfH7ZdjDQ3XPFShNU4udjLM0kxh0Afnm12P+RAGAqHNJXBpJOqpw==", "signatures": [{"sig": "MEYCIQC6TVS/bQTN+dwyy0xGama7sP5Wo/KS7NCN4z+xS2hl0AIhAPwROvp3nGl3yFu6FBz9gGhjIWtuhhjQhXKra1CaAijs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.4.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x"}, "_engineSupported": true}, "1.0.6": {"name": "semver", "version": "1.0.6", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.6", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "697b6bff4b3eca86f35dc037c9ab2f1eb7af1a9e", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.6.tgz", "integrity": "sha512-UgpYQpAv0dXBvjIZtInaqKhpIGGL3d2ezKkBn/+Lefd5mDiQ7ibbrw5/KewvEgrWSIZYPssxzBnsB19OBni4KA==", "signatures": [{"sig": "MEYCIQDkkhxF/qJFMrYp9y4KwDPx2vdDoeVHkze1+RQZBmhlaQIhAPSaSCsyfs6z414jwnDd4ATlVJqiZbBm1aTya1XF7DU8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.6", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.4.8-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x"}, "_engineSupported": true}, "1.0.7": {"name": "semver", "version": "1.0.7", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.7", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "668e127e81e81e0954d25a6d2c1cb20a1538b2e3", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.7.tgz", "integrity": "sha512-t8LmYpE71rsrHgd7e9n3KBWJfF/IlzJpKBkVT7KN8kJj/muyb1YCd2QMw3/tIpkexzvC6yamodMYVP32X7+NdQ==", "signatures": [{"sig": "MEUCIECNuaArN5SZLWqe065UfUXJKETC0tUzikGPPDym7a3nAiEAx42T4NxqgoPKm5fRw8qmsVeIbRNzkw9aA3L0Wv7wPh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "node semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.13", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.5.0-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/semver/1.0.7/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x"}, "_engineSupported": true}, "1.0.8": {"name": "semver", "version": "1.0.8", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.8", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "8a78a9bfad863a0660683c33c91f08b6cd2cfa98", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.8.tgz", "integrity": "sha512-EO1NC3/lkkaQt4P6THqDPMaOT67hyXdwtTSLZu7puHADe2cZY1e53ILdA/1iEkdu4T3cMVht962xsxm64zl1UQ==", "signatures": [{"sig": "MEUCIQCN7exWwJEPrGP/HkW5qltU/+fTkeXmszxUaMpW/+uB1gIgeygnmtt0cgWhIIjkw2eIxH8DXbsTt6xm96IMfZjynlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.15", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.4.9-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/semver/1.0.8/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true}, "1.0.9": {"name": "semver", "version": "1.0.9", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.9", "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "d053046aea88e25b488ecbc0a8c9deda03db8a9c", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.9.tgz", "integrity": "sha512-2fPgYNmGmaejvD2obMSb52JTSJQVxm2KPP/Ckdx+CHsFUbM0kRe554jWvJUyh+oLctIFv2gDTKHxhg13tFsKzQ==", "signatures": [{"sig": "MEQCICuKvsNapy2JiAIFs7kBwLfoxMkLEMpBTXecF3B5G2CaAiByI2518eHtueWaVtvjOXcWNlxD5gSPTSXAXQqSU946SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap semver.js"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.18", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.4.10-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/semver/1.0.9/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true}, "1.0.10": {"name": "semver", "version": "1.0.10", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "cfa5a85d95888c75b4a9275bda7491568d8cfd20", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.10.tgz", "integrity": "sha512-01AAWjDhmaOBfwtahjC41n6Yc2MPOsnSTMmbS+Y5YCtDtBud9hPeg+hqSw+PmfL5bSK/qMrGWBCXUlRiyuQrxA==", "signatures": [{"sig": "MEQCIBDmnPMXmom0H4Ixz0WmNMIaYEz5J2BW/5gwdjTjxGsqAiAPYsfnxHIeu1jXsVxphatd3itHNM8ykRHfqbsPgqVH0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap semver.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.92", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.5.9-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true}, "1.0.11": {"name": "semver", "version": "1.0.11", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "1bd01c550d477cbf9a839b02269c4011ce147992", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.11.tgz", "integrity": "sha512-NG2tk8v8aDLiJ82lFMDAmAfNLOYlf+qEwrmpUCcf+413uDlh2DPR5Uo5Y41FPjbcFOoXOTCLGSSYF1szV1W3rQ==", "signatures": [{"sig": "MEQCIGxaSdE316nO3t+cFW2ENAXMpCIEh1kYKq3nDHA6cb7wAiBVQ8mJHJk5RtDtVhTSeek7dML4T6eJ8Mt4/f9MxFNRBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.105", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.6.1-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true}, "1.0.12": {"name": "semver", "version": "1.0.12", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.12", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "4686f056e5894a9cba708adeabc2c49dada90778", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.12.tgz", "integrity": "sha512-j<PERSON><PERSON>bCSCJPFUNPP8L8JPyEWcLG5t391s39mrCnbFZI5EC1QX6wcBRICOy+AghzdjDT/AN1pxXYAsyoTx2OB3zJg==", "signatures": [{"sig": "MEUCIQCXK6gwCRKHqqKCN46F3MPgvnGoitwsxWBtgjhMgWjx8wIgZJoyLu91oN07BBBgmgWoKBL9ODoWk49MoI7dxrVdddU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.6.2-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true}, "1.0.13": {"name": "semver", "version": "1.0.13", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.13", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "9512ce1392105e72a0b739b27f39e0242913d07e", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.13.tgz", "integrity": "sha512-qDRTcyNWqdabnjccyHRgJKraxVBICKe6EDZo4gSHNjMJuG9znreLCQOetjpbr1YJxwTiAMR/lYxIBDN68c1brQ==", "signatures": [{"sig": "MEUCIQDQX07jOL5/EXuB9jGXbyyNrjst0UsC9LN0MbFdh3aaugIgUT0fQB8xbtqtVaz3aCgyyq5/xRVaA6NSmcEOM1oaj94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.1.0-beta-7", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.6.7-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true}, "1.0.14": {"name": "semver", "version": "1.0.14", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.0.14", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "cac5e2d55a6fbf958cb220ae844045071c78f676", "tarball": "https://registry.npmjs.org/semver/-/semver-1.0.14.tgz", "integrity": "sha512-edb8Hl6pnVrKQauQHTqQkRlpZB5RZ/pEe2ir3C3Ztdst0qIayag31dSLsxexLRe80NiWkCffTF5MB7XrGydhSQ==", "signatures": [{"sig": "MEQCICloZzNYG5Iz/ZH2ZXVZcDrSSRElEcZnNXyDLaqGhl4eAiA8U3pyOUTvmO2uXSKKnArorRip4Cxi1/XCc8Pl7KLY0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "engines": {"node": "*"}, "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.1.22", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "v0.7.9", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"tap": "0.x >=0.0.4"}, "_engineSupported": true, "optionalDependencies": {}}, "1.1.0": {"name": "semver", "version": "1.1.0", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "da9b9c837e31550a7c928622bc2381de7dd7a53e", "tarball": "https://registry.npmjs.org/semver/-/semver-1.1.0.tgz", "integrity": "sha512-dWhAIeZiiKLiSijlIE2ebLDc6TmKb1xccwAmObQQS3DozfWiH56YXxi/9gh5lH0xU83pXK+CZVrnovwCShfjWw==", "signatures": [{"sig": "MEUCIQDVdIaYf/D3qO7/qvyyqRddcb2qxUlfRezVL/vIdWMa4AIgY2HC1cX6XyIRyzc6aQ/t1G1nGY0Seraib/6En5uket8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4"}}, "1.1.1": {"name": "semver", "version": "1.1.1", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "de7faba203f7cbb59ac16da64198df0c6cebca30", "tarball": "https://registry.npmjs.org/semver/-/semver-1.1.1.tgz", "integrity": "sha512-vC25KyajeTKWZJM41EVAexRb3UBIn4rN2JrBHrvxaTdJ4pN8N9mzUruYzYVJ6gqIrAUg6BntTEKblYnIxl/QKA==", "signatures": [{"sig": "MEQCIFKA60HFpqg3Y9JyLCQWiZVUt/Aek3Kv8hkPBRx+MyekAiAzNATr2/LuH9FDMgUi5I7bn8ul5MsdjEfXPd7wJRUC2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4"}}, "1.1.2": {"name": "semver", "version": "1.1.2", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "f6c851dcb2776b0aa7af2294dadaf6ce20de897e", "tarball": "https://registry.npmjs.org/semver/-/semver-1.1.2.tgz", "integrity": "sha512-sP5HF+okbIPRQUJ3kvn99FWkZDapbRspyXfUh+zZT5VBi1GZnmSLa3LeXVphWyiLN3OPGycS8zW5p0Oq1RGfhg==", "signatures": [{"sig": "MEQCIEoB6xr26a/SOTyFCK7RVUHQyB7JDaeY74WzN6cOjr1hAiBqNp2e1EXeAzN7Hzgvu6eUt29NeeVP0ammX97D2kL8Ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.1.70", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4"}}, "1.1.3": {"name": "semver", "version": "1.1.3", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "a0f06f2fb23b64ef9c0ff714fa079082e0532633", "tarball": "https://registry.npmjs.org/semver/-/semver-1.1.3.tgz", "integrity": "sha512-kqR5VYrim9OOJyEXjQAWfKsE974sr190VrFmkWA841O1dDENc+8OUus69XQaWk6xjaiyHu0L4HOqixBJvir8eQ==", "signatures": [{"sig": "MEUCIQCfo9JCLAHz4bA02AVYtHFC0MaCH+XgjbiFlkLTxbDNRQIgHESqQBIQ4VgojA3LKx45LAv9/lz0UvEHRw4TggB4LDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.8", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4"}}, "1.1.4": {"name": "semver", "version": "1.1.4", "license": {"url": "https://github.com/isaacs/semver/raw/master/LICENSE", "type": "MIT"}, "_id": "semver@1.1.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "2e5a4e72bab03472cc97f72753b4508912ef5540", "tarball": "https://registry.npmjs.org/semver/-/semver-1.1.4.tgz", "integrity": "sha512-9causpLEkYDrfTz7cprleLz9dnlb0oKsKRHl33K92wJmXLhVc2dGlrQGJT/sjtLOAyuoQZl+ClI77+lnvzPSKg==", "signatures": [{"sig": "MEQCIEMwtoP8YLVEG3We88HW1licfcNLQPwSQkGWV0Y4GLeXAiA6lcbGt0Clzjiw9t95ofyLZio5+jQ0dF6TmNKUqcsp/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.12", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4"}}, "2.0.0-alpha": {"name": "semver", "version": "2.0.0-alpha", "license": "BSD", "_id": "semver@2.0.0-alpha", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "979de555a1bae1c781cc6b2907b576b59067706e", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.0-alpha.tgz", "integrity": "sha512-sgLPsnfuNgIRa+1GafPpm97jb6gvrMjl2XiQ5MVXPb0BkWN7H4J4+SNTrpIz4ksARMJ7wAGkIeICV77mcP1VYA==", "signatures": [{"sig": "MEUCIQCb6nvHsBVVmBKGFh9HPVTly5B/msPYfimhYTo9IcDMywIgPqaXgsu4nBv5xg0RIekeM848zAdyfCxAdGWTxQSzWDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "scripts": {"test": "tap test.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4"}}, "2.0.0-beta": {"name": "semver", "version": "2.0.0-beta", "license": "BSD", "_id": "semver@2.0.0-beta", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "5c3585c4eaa97879cc07de06aa6e75a44f5b249f", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.0-beta.tgz", "integrity": "sha512-/NYzYBm/4twt8UsOjfSUX0Lx1G5zm1LjpwAhJd2ChXGIZcQ2UqjqApBWsyR5QhVWc6HWFnRoN2jUT2nmVn8IUQ==", "signatures": [{"sig": "MEUCIQCZ84sSnX5OqE2bJqSw1RJ3BxQTZHDLMGunEkSXfMAHdQIgClh6bm9wzmzlBFq+g33FzNQLkRUhOLh0bdbGYSq7lT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.1": {"name": "semver", "version": "2.0.1", "license": "BSD", "_id": "semver@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "92741b2bd09d9c53695cf116cd6de32cae925976", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.1.tgz", "integrity": "sha512-PtxXFO3N7+Hq3ZgjZ3kfX/d/rK7p+KzOXbRu1kHxiW7hmFKwOBkMAPy2v6iic9Dg1YcHrsoYFfwJfUgcm+hIcA==", "signatures": [{"sig": "MEUCIQC43YMPI1ho2VYv+jyHgsaoCI1L4HnsMooPEUdpNGeOlAIgVpPmaYcB7grC0pZ652JKnEUvq3QXcPq0xxuu79zYrU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.2": {"name": "semver", "version": "2.0.2", "license": "BSD", "_id": "semver@2.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "6697b743dafc38a9ae24d4f0183eb53f460b662a", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.2.tgz", "integrity": "sha512-LaEXhHQiAkH+cJc34ZfpejQ+/edqRjAgD2FFe3g+K6X0sDlhqnP2tZU0WZ8ZXJJcjzwRJsSVg9YHoqyRcvmrmw==", "signatures": [{"sig": "MEUCIGUcP7bkewg0HmJZfwQKDjG3m3rXMiTp0p6gdtoIFxl9AiEA7fDRDAWJ/S2HPxifFl5XWqn5NXdod3WngoUtvtgi6zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.3": {"name": "semver", "version": "2.0.3", "license": "BSD", "_id": "semver@2.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "1778ae3b7d5f5f457a48b164320bf6e29c8e8fe1", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.3.tgz", "integrity": "sha512-wAekqPFJmElFS/TKrb616ViPmLd4E0BvTp5nKKqeqV7lzPVVTIgadTNhUtbhWoe3Nps8q3OcziJ1CLIgoGanwg==", "signatures": [{"sig": "MEUCIF7Lh1CN+ECIEd3tpx/3gyIymXOD+DIksuja23TbXc9tAiEAvx1tMZu0h+hDXplgkOQVnad9k0c3ozDHHjSgzBxUwng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "bash build.sh"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.4": {"name": "semver", "version": "2.0.4", "license": "BSD", "_id": "semver@2.0.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "1bb8e25f9a445b55f2d2ea5bc06742790b6a5ba7", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.4.tgz", "integrity": "sha512-Bk18XYmfDuxL2HsfqaeBPaD7RlgY4cvZEAJFEaFw5iIxN7fXkg20h3XFrgg1xpDgQc3ieUTm0MspjK+Uf52MUw==", "signatures": [{"sig": "MEUCIBprmMc9NMZ0APcFsh8yDw00AbFAGriwlJse2wcL7AXPAiEAmwD41W5T0JLX6TUiq0vBL6xeavyH8rgmcGby5vVDCC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.5": {"name": "semver", "version": "2.0.5", "license": "BSD", "_id": "semver@2.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "601aebc1dbeedac222bb7e7b8882454f0c2a24c7", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.5.tgz", "integrity": "sha512-ryGDg2K1/aWIKCjs9dM8aojyvYYYGt4haET1Q0jKuqkLCBj7eDeLfhChH4XzJRi7cSTiMi49WGibqRsELPPeIw==", "signatures": [{"sig": "MEYCIQDo08a4WUysxYkqiycgNomFPeu7u5PEUHyl/UHaUtZHrwIhAIp8ihF6fqhMeqo3/lNpLlY5x+VGRonkfRKNcejYTUsf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.6": {"name": "semver", "version": "2.0.6", "license": "BSD", "_id": "semver@2.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "3febe96c89813fc299d8e67cbcd685101e07335a", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.6.tgz", "integrity": "sha512-IS1QUisbrNNjOsF0RKuhQ+DUbOYjs+tFxDI3corPjGkWIXTJrbIB4zSVRnVyIiVZc2jpN1YHi8D+zCtfm8teqQ==", "signatures": [{"sig": "MEQCIGma1A2/JserHJd6PdcR7X4pkzpWAzZzuCYl4xG3A+txAiA2WxI29CrZduEV1hM6B668KpVySFy894+vyU0TLAXREQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.7": {"name": "semver", "version": "2.0.7", "license": "BSD", "_id": "semver@2.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "7815c1a7ef647604646ecdabc95b90dfeb39174f", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.7.tgz", "integrity": "sha512-iqPKEvInsF/uu8BmoCWGzhdY5QctPNndIIqYyU3VSgXh5cwU+oSR7zzj/CPhLYsaA+6hRqjNQN2RSLtg/i+6EQ==", "signatures": [{"sig": "MEUCICY82aPoGz3EEotFy06WXDaHxhMXjqwm8KNmokIM1GebAiEA6KlxaZPP2awkexazmI1udTvHrScsa/Ps8BoMJciHs1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.8": {"name": "semver", "version": "2.0.8", "license": "BSD", "_id": "semver@2.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "f5c28ba4a6d56bd1d9dbe34aed288d69366a73c6", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.8.tgz", "integrity": "sha512-tdvBVFDfQJ9GoManbsTAqDs69WEfz/HYFqHCWDkWfeX2gxLS2lFIDBsAjiUWoyc1z8wYdItbgvDsYF779ise5Q==", "signatures": [{"sig": "MEQCIHke5YSoKiFy/R4qokeItrqu6ENy1qAYogxgoY2cirEYAiBXnJ9kcnOlSZpMHDjXvs/NJBSxZcfjuH3AYtR0GTQQGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.0", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.9": {"name": "semver", "version": "2.0.9", "license": "BSD", "_id": "semver@2.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "3c76b0f216bd62a95f5f03e9ec298da548632403", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.9.tgz", "integrity": "sha512-uQ8oVqP1MRM8HpwN9KrY1YqpnMPMNEGUWLwC8aXuZBDoXs7nvEfBUYDPVzaaJafoDXv0aNYjdbG057N53i9zUA==", "signatures": [{"sig": "MEUCIHm0YPoUREL6h5zvc5CgiFywxTEpIDBH/xk0AeLMqYc4AiEAr5UtPrILUvLOG2ErQPKNY78J2/GztQMhXtOJLqGTSas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.1", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.10": {"name": "semver", "version": "2.0.10", "license": "BSD", "_id": "semver@2.0.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "e3199263b1e9f1913dbc91efb4af559e8e4d3d31", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.10.tgz", "integrity": "sha512-b7SPVdVdJ4vAP/Vaz3A0IFujeGT3zG9ivfuJqeSCSE3o4j+UfoIbOpuTHlzBin+4hN9jGhyBCtpj8ILyXXPSDw==", "signatures": [{"sig": "MEQCIGQRw18qWrK4zVoqJ5T5gU1GuiTm8SbkTfog0fK7tTIfAiBzfR5hK3wpenz8VisSDKrvzGsnGjykBO7dtBaXSnkpWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.0.11": {"name": "semver", "version": "2.0.11", "license": "BSD", "_id": "semver@2.0.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "f51f07d03fa5af79beb537fc067a7e141786cced", "tarball": "https://registry.npmjs.org/semver/-/semver-2.0.11.tgz", "integrity": "sha512-LllH2+bpxApwHikoDHLcxJRZO1iYdtECXcvtReaV4UEf3EiR2HhoyiS24Xq+S38lDGvI0t4ZifK8h6pHVB3oXA==", "signatures": [{"sig": "MEUCIFnwRMTwq7R1onTanECAiSY+q2erX0AELn9bioFkbbJbAiEAh8oGec0JX6lnq+HgrlVkOMaZ6qefF0gH6WiD+l7Ukcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.1.0": {"name": "semver", "version": "2.1.0", "license": "BSD", "_id": "semver@2.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "356294a90690b698774d62cf35d7c91f983e728a", "tarball": "https://registry.npmjs.org/semver/-/semver-2.1.0.tgz", "integrity": "sha512-c72GsIRwIoxL+mBGH33w2dwcX2tqO0I/Snh8jjYpOpktGoobMnu0dYT+2wl6Uvo8zLcvWsB4gP6HHLRsrbZcng==", "signatures": [{"sig": "MEQCICIA6I01elu4L29YWzQUla4SHEU6Hok62tcQaMjBUdWRAiAS420lfbmXj8GdNO5NrxyeZRS5WiEJhmwHs75NZw27cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.6", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.2.0": {"name": "semver", "version": "2.2.0", "license": "BSD", "_id": "semver@2.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "290ec979d731b3dc6c08a15dbcffdeae420f4473", "tarball": "https://registry.npmjs.org/semver/-/semver-2.2.0.tgz", "integrity": "sha512-P487vwoC/P9XUUaAQBciHmNNXF0sHxXJL/zlV0HxrAax0nMqdBQAl4QHOr1aXCIUo0kAtDYDJ9qDbqjuoSLS3w==", "signatures": [{"sig": "MEUCIQCA2TCj4JqZLDZ9nhcgqewahunXMLsqwmDZdoX0uUXW7wIgQrpTLx7MniIq0ArgATzgLOvHFR7onUQWDG2khDRGLBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.2.1": {"name": "semver", "version": "2.2.1", "license": "BSD", "_id": "semver@2.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "7941182b3ffcc580bff1c17942acdf7951c0d213", "tarball": "https://registry.npmjs.org/semver/-/semver-2.2.1.tgz", "integrity": "sha512-zM5SE887Z8Ixx9cGaFnu9Wd8xr0RFwixASZcvUh2QGnf/1uxYmyetDzhzkEdDKipmZPq/JTB0gLo1Sg59LXkQQ==", "signatures": [{"sig": "MEQCID4rlh0PiaC+RntVXcyaXZTiUyifetg+59a7715hwBr7AiBwRdkrry9UOx3QPzqzfBgRvsWsLPwp8e/Os+ZePp+dPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.3.12", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.3.0": {"name": "semver", "version": "2.3.0", "license": "BSD", "_id": "semver@2.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "d31b2903ebe2a1806c05b8e763916a7183108a15", "tarball": "https://registry.npmjs.org/semver/-/semver-2.3.0.tgz", "integrity": "sha512-QD4DH+D12a+3WeHAT7TOpe24YjL11i+vkW4PdY52KkvfZkams42ncME5afs/UgAzYzqDXWS668ulm+KrrTo9+g==", "signatures": [{"sig": "MEQCIBCwne3Hzp+wYaSMCIAeKax8YQ76tic62HqHyFFBWJ7yAiAMDCKS94YbZPR706eMjYVIJJujxBtSG4BiCwC0q3l/Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "d31b2903ebe2a1806c05b8e763916a7183108a15", "browser": "semver.browser.js", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.4.10", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.3.1": {"name": "semver", "version": "2.3.1", "license": "BSD", "_id": "semver@2.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "6f65ee7d1aed753cdf9dda70e5631a3fb42a5bee", "tarball": "https://registry.npmjs.org/semver/-/semver-2.3.1.tgz", "integrity": "sha512-o+rwSUCokxzPIMBWGG96NGBo2O3QuUuWagXMxTSRME1hovdWXpM4AVUcnqXt2I16bSBJZTuiUwZrSDb8VIz8Qw==", "signatures": [{"sig": "MEUCIALBR78iPb6/jOfjVRT3HzW0RaZBf/nEuvfcPGVKmo5PAiEAq62bO66XOMCQodr4Y9owF3pLEYf/R/aVwoKbzb1nWuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "6f65ee7d1aed753cdf9dda70e5631a3fb42a5bee", "browser": "semver.browser.js", "gitHead": "e5b259a784b79895853aff1c6d5e23b07bdd4664", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.4.16", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "2.3.2": {"name": "semver", "version": "2.3.2", "license": "BSD", "_id": "semver@2.3.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "b9848f25d6cf36333073ec9ef8856d42f1233e52", "tarball": "https://registry.npmjs.org/semver/-/semver-2.3.2.tgz", "integrity": "sha512-abLdIKCosKfpnmhS52NCTjO4RiLspDfsn37prjzGrp9im5DPJOgh82Os92vtwGh6XdQryKI/7SREZnV+aqiXrA==", "signatures": [{"sig": "MEUCIQC0y0FqzC9nrRJFVsZzJPAAUef4QHfdnvRv3p8yNzM2RQIgYOC3J3+547/yw7NKW2Ne42ev3CGwgpbmsK61QUufFww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "b9848f25d6cf36333073ec9ef8856d42f1233e52", "browser": "semver.browser.js", "gitHead": "87bcf749b18fd0ce32b1808f60a98eacecd84689", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-4", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "3.0.0": {"name": "semver", "version": "3.0.0", "license": "BSD", "_id": "semver@3.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "994f634b9535a36b07fde690faa811a9ed35b4f3", "tarball": "https://registry.npmjs.org/semver/-/semver-3.0.0.tgz", "integrity": "sha512-UXaRocpOaX/BQohXM5bIL4Xx0W4IIiuzesMItEx5oe7Xt5SZ7TXmjIwO1+0rRGhMGJ8o4HxjFD0+eXg/ox53sg==", "signatures": [{"sig": "MEYCIQDgMnlAGcaPaRhWzBilSWhcJyQCdRBjyFoWHADwPdIeQgIhAInx8EHwdNX85pbuOizo6DYWiwpzV4E6ZuiNairMsu+x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "994f634b9535a36b07fde690faa811a9ed35b4f3", "browser": "semver.browser.js", "gitHead": "e077f4e33e21b280a5cf6688d850dabf5f6e48e2", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.0.0-alpha-5", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "3.0.1": {"name": "semver", "version": "3.0.1", "license": "BSD", "_id": "semver@3.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "720ac012515a252f91fb0dd2e99a56a70d6cf078", "tarball": "https://registry.npmjs.org/semver/-/semver-3.0.1.tgz", "integrity": "sha512-MrF9mHWFtD/0eV4t3IheoXnGWTdw17axm5xqzOWyPsOMVnTtRAZT6uwPwslQXH5SsiaBLiMuu8NX8DtXWZfDwg==", "signatures": [{"sig": "MEYCIQDCuehbXtW7QexW3FEsIBCSuZhRg1ePjhLPCMDFJp/7AwIhAJRH6slc2TyApfBBlri5liqLcmThSpHgvWavBm4YkbV0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "720ac012515a252f91fb0dd2e99a56a70d6cf078", "browser": "semver.browser.js", "gitHead": "4b24aeb54dd23560f53b0df01e64e5f229e6172f", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.0.0-alpha-5", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.0.0": {"name": "semver", "version": "4.0.0", "license": "BSD", "_id": "semver@4.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "7be868416a5e669923a8e3af8bafa5faf62a151a", "tarball": "https://registry.npmjs.org/semver/-/semver-4.0.0.tgz", "integrity": "sha512-P1vQUCJKfUsGNh9i8HW9Y/EPxYS4ccD8Lez1yp8/yOsO/NlPodHINwZsF68jLK0NU+xfWVFRe95Dfhj3H2ORzg==", "signatures": [{"sig": "MEUCIAKsRA4XdpVsGDg0A0pqYtGxYP5sPbmh85MoEiwTwwRzAiEAovw2GFKFl+tOsfArKvFHj4Ox3uEpR02TyQCDDhY3n3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "7be868416a5e669923a8e3af8bafa5faf62a151a", "browser": "semver.browser.js", "gitHead": "f71a46b52f5d413aff1cb3afa7d2f940b23ab1a0", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.0.0-beta.3", "description": "The semantic version parser used by npm.", "directories": {}, "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.0.2": {"name": "semver", "version": "4.0.2", "license": "BSD", "_id": "semver@4.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "13f7293ca7d42886123963ca0b947a03f83f60c3", "tarball": "https://registry.npmjs.org/semver/-/semver-4.0.2.tgz", "integrity": "sha512-GqDeHKVae+gifcdysWn/oQTzhNdvkj/qYKON49ChzAzYBu3A6rKMJs5AS4AZW5xHbdWKWJ2oUj1yiaL2F+jg/Q==", "signatures": [{"sig": "MEUCIHje2JYh5eziIH28WUKk6yrlm2ubi04iU1ROZDUSV7b4AiEAkOdaH7wj6pCnvqR5nCJKE6PQUrHtw6XirlPTkXr4iFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "13f7293ca7d42886123963ca0b947a03f83f60c3", "browser": "semver.browser.js", "gitHead": "078061b03e7e10202f9d03fe447b528202cd7a06", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.1.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.10.31", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.0.3": {"name": "semver", "version": "4.0.3", "license": "BSD", "_id": "semver@4.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "f79c9ba670efccc029d98a5017def64b0ce1644e", "tarball": "https://registry.npmjs.org/semver/-/semver-4.0.3.tgz", "integrity": "sha512-89AI+k269YvAjOOeeK23fk2JQX3Uoh2efNO7hye1Rn1E+/K3R4sP0IK6v0yylDRXGIzu2qaKAPl4ltOzYhwUkA==", "signatures": [{"sig": "MEYCIQCRnLuKNQP9716ussdHM6xXmKrZ9i8X2S7FtiuI2gmZqAIhAMtwFNceqB1N3QPX5A9I281wZ4+4/kTder+k8VMRhjif", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "f79c9ba670efccc029d98a5017def64b0ce1644e", "browser": "semver.browser.js", "gitHead": "58c971461ade78bca6c1970109de4dc66cc2c13b", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.1.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.10.31", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.1.0": {"name": "semver", "version": "4.1.0", "license": "BSD", "_id": "semver@4.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "bc80a9ff68532814362cc3cfda3c7b75ed9c321c", "tarball": "https://registry.npmjs.org/semver/-/semver-4.1.0.tgz", "integrity": "sha512-lLkkQcdd/nO1WKpCh2rljlJ17truE0Bs2x+Nor41/yKwnYeHtyOQJqA97NP4zkez3+gJ1Uh5rUqiEOYgOBMXbw==", "signatures": [{"sig": "MEUCIECiGc6Jlw5C2vntDvck12LgLC7k3hF9y8YjD33TMjdMAiEAjS2iDkDPBHRcs0PI7tX3uZi6oFvCH3Kx8wr2o+RCllw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "bc80a9ff68532814362cc3cfda3c7b75ed9c321c", "browser": "semver.browser.js", "gitHead": "f8db569b9fd00788d14064aaf81854ed81e1337a", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.1.3", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.10.31", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.1.1": {"name": "semver", "version": "4.1.1", "license": "BSD", "_id": "semver@4.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "8d63e2e90df847e626d48ae068cd65786b0ed3d3", "tarball": "https://registry.npmjs.org/semver/-/semver-4.1.1.tgz", "integrity": "sha512-fGVhAtp6zIx5Q2AHR3xu/8tUyPx0osOuL7kvJvX/S5b4rE85KazmMZnt+Mfhq8p9AOlz0POVqiTg1URFT46xkQ==", "signatures": [{"sig": "MEUCIF1CEnuz94qUIXtEtYlxP3j5pfH0iBfsJOCdCz+GqMNZAiEAx7Hv6aVliOzPDROHGT6PQ1phatPEq7CmrPFEI4z3V/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "8d63e2e90df847e626d48ae068cd65786b0ed3d3", "browser": "semver.browser.js", "gitHead": "f43cb35c96b05e33442e75b68c689cc026bf5ced", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.1.14", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.10.34", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.2.0": {"name": "semver", "version": "4.2.0", "license": "BSD", "_id": "semver@4.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "a571fd4adbe974fe32bd9cb4c5e249606f498423", "tarball": "https://registry.npmjs.org/semver/-/semver-4.2.0.tgz", "integrity": "sha512-w3jtCMLFhroT8G0VFOyFKX/Xla6aYgvIH1vou5vpQlQH9i6X0UIA60lwhwtBls/dtlZKZjCBAIMQ8Y/ROcTz1w==", "signatures": [{"sig": "MEYCIQDtsSG+bzPS34VxiPTpaPnZmnXfXgT4inGEmB38ckkWHgIhAMVa6vzL3dHjBPcULfCBVEYP09rOd3OWMSTelcpAAE6t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "a571fd4adbe974fe32bd9cb4c5e249606f498423", "browser": "semver.browser.js", "gitHead": "f353d3337dd9bef990b6873e281342260b4e63ae", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.1.14", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.2.1": {"name": "semver", "version": "4.2.1", "license": "BSD", "_id": "semver@4.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "70828f545f40f49ffab91fef09c3cd3257937142", "tarball": "https://registry.npmjs.org/semver/-/semver-4.2.1.tgz", "integrity": "sha512-3+aGL3H9fBl8UJKDqxMnrsGI3SYD44/KDyCHXo5/sJKDsH4SsXmTo5K9B6LorKd7cxRTVVoDTAhvXtq2+/zGgw==", "signatures": [{"sig": "MEYCIQDiKbkKI+pqBquFi2NcD2uekBFHTQfS1yuvXKRoLenEfAIhANJMXeF0nmUu4Doa0c6tjyRQJoiEkvNkN2kRSedynSXr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "70828f545f40f49ffab91fef09c3cd3257937142", "browser": "semver.browser.js", "gitHead": "bdfb19555ee0f2f46ca6694931ca476d8b8c35af", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.2.2": {"name": "semver", "version": "4.2.2", "license": "BSD", "_id": "semver@4.2.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "a6aa6ac6a63c0dc7aff7ea48d5455ae2b93a3062", "tarball": "https://registry.npmjs.org/semver/-/semver-4.2.2.tgz", "integrity": "sha512-xg+0bAChxDcgXAZ9yJJ/I4xQm1CZaFr0hQ7E2HdCMnQbT2hUBidtCzCiPB0oWmc53UNDIBgNwNBdiDZG2Sx67Q==", "signatures": [{"sig": "MEYCIQCHxJkU7u1ZNzlRwgN0ZnX83wzqOJOVwVxnnzL0G6HeewIhAPc56uXyRBj/Y3Bo5XoB75S9dN96hPsNCXn2CJUZs8t8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "a6aa6ac6a63c0dc7aff7ea48d5455ae2b93a3062", "browser": "semver.browser.js", "gitHead": "d2806e62a28290c0bb4b552b741029baf9829226", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "0.12.0", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.3.0": {"name": "semver", "version": "4.3.0", "license": "BSD", "_id": "semver@4.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-semver", "bugs": {"url": "https://github.com/isaacs/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "3757ceed2b91afefe0ba2c3b6bda49c688b0257a", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.0.tgz", "integrity": "sha512-RH9n+cmtBU68yyLhsqAjrMVrF7uhmbe5+vWMVtVklTcO5k/dN5lne6tHdz2l8SjcoZKvZfb1fOyG48hp01fcDQ==", "signatures": [{"sig": "MEUCIGoBfwCHecP5oSeXREs/BAQfXXRDmyEFZH4GnJzWFm1GAiEA1Xwy+3hQ+1NWju7g7499IBlIgbJCZa/eH7d+0Px1G6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "3757ceed2b91afefe0ba2c3b6bda49c688b0257a", "browser": "semver.browser.js", "gitHead": "12c0304de19c3d01ae2524b70592e9c49a76ff9d", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-semver.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "1.1.0", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.3.1": {"name": "semver", "version": "4.3.1", "license": "BSD", "_id": "semver@4.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "beb0129575b95f76110b29af08d370fd9eeb34bf", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.1.tgz", "integrity": "sha512-XCegw2pJ735ut1ZyhSQ2Mm8W0Z/qkci+JjD2pIXNNFZXUMRlO93qNhSw8LBorDyEYbtiVa5FyodGbZ2k0w851g==", "signatures": [{"sig": "MEUCIQD9Th/mvhddNk6iNCKKn58xOymSXPapiT8eszCy1N5AMQIgeE8NErzh2th5GUPWVA9MWzt16kCqFzBoOXduQ2//6XU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "beb0129575b95f76110b29af08d370fd9eeb34bf", "browser": "semver.browser.js", "gitHead": "fa9be2b231666f7485e832f84d2fe99afc033e22", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "2.6.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "1.1.0", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.3.2": {"name": "semver", "version": "4.3.2", "license": "BSD", "_id": "semver@4.3.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "c7a07158a80bedd052355b770d82d6640f803be7", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.2.tgz", "integrity": "sha512-VyFUffiBx8hABJ9HYSTXLRwyZtdDHMzMtFmID1aiNAD2BZppBmJm0Hqw3p2jkgxP9BNt1pQ9RnC49P0EcXf6cA==", "signatures": [{"sig": "MEUCIEXkLP5Be2Hh67FqyZG2E8F/gNIIhz8UyKPR32ZLSoczAiEAnq0DHgMvEpFr7pfaK2iCMSkfKrGoRNNPe5U2BhvPpbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "c7a07158a80bedd052355b770d82d6640f803be7", "browser": "semver.browser.js", "gitHead": "22e583cc12d21b80bd7175b64ebe55890aa34e46", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.3.3": {"name": "semver", "version": "4.3.3", "license": "BSD", "_id": "semver@4.3.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "15466b61220bc371cd8f0e666a9f785329ea8228", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.3.tgz", "integrity": "sha512-jXRt0450HFhUqjxdOchxCfajQsS9NjXEe+NxorJNyHXef2t9lmbuq2gDPFs5107LEYrU2RZDg9zGzcMjeng0Fw==", "signatures": [{"sig": "MEUCIQCrPFhcvZFoJxMrEM2mRV5lYvIsg4GOgKwA5lMvQwtdIQIgCSRs3nMUWne6Z8NfN4ZmL+XljD6EywsWe+kQkr4l9J4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "15466b61220bc371cd8f0e666a9f785329ea8228", "browser": "semver.browser.js", "gitHead": "bb32a43bdfa7223e4c450d181e5a2184b00f24d4", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "1.4.2", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.3.4": {"name": "semver", "version": "4.3.4", "license": "ISC", "_id": "semver@4.3.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "bf43a1aae304de040e12a13f84200ca7aeab7589", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.4.tgz", "integrity": "sha512-Rx2ytq2YiTsK/aNQH01L6Geg1RKOobu8F/5zs80kFeA0qFhSiQlavwve2gAQqEHKqiRV9GV/USNirxQqMtrTNg==", "signatures": [{"sig": "MEUCIEJaVnPvXg0Twa9rUmCAZR8DOq4sUhTgoMQMuyqLl7fPAiEA93+7Glsdx0fArbdtVhj/82lhDKgKdwk3loWW+mKsMsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "bf43a1aae304de040e12a13f84200ca7aeab7589", "browser": "semver.browser.js", "gitHead": "d7d791dc9d321cb5f3211e39ce8857f6476922f9", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "2.0.0", "devDependencies": {"tap": "0.x >=0.0.4", "uglify-js": "~2.3.6"}}, "4.3.5": {"name": "semver", "version": "4.3.5", "license": "ISC", "_id": "semver@4.3.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "c20865a8bb8e1b6ac958a390c8e835538fa0c707", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.5.tgz", "integrity": "sha512-xnlAURpjiUoauD8XiuSz2hH1At8J/01YZkDCO3hDlXPugZoKU4XNhdng3nfpTHNafuJ4D5DvJeEONcANBYPx3Q==", "signatures": [{"sig": "MEUCIQDGLtcRhpl9JqKH4F7uQQXssERKQ2pBIgdsZq5KiSixJwIgC6zPpKacedzSGMak3/2A4qjuJPBDxBV91UJZC836yWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "c20865a8bb8e1b6ac958a390c8e835538fa0c707", "browser": "semver.browser.js", "gitHead": "75bb9e0692b562f296b0a353a7934e0510727566", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}}, "4.3.6": {"name": "semver", "version": "4.3.6", "license": "ISC", "_id": "semver@4.3.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "min": "semver.min.js", "dist": {"shasum": "300bc6e0e86374f7ba61068b5b1ecd57fc6532da", "tarball": "https://registry.npmjs.org/semver/-/semver-4.3.6.tgz", "integrity": "sha512-IrpJ+yoG4EOH8DFWuVg+8H1kW1Oaof0Wxe7cPcXW3x9BjkN/eVo54F15LyqemnDIUYskQWr9qvl/RihmSy6+xQ==", "signatures": [{"sig": "MEQCIBvtZMpBK9knoGp2TGcNFFbp+BSG9+Yynqu0tnUyaEDCAiBUZJuTa+PodhVsSOk27KsyQKQl1TRVZxWTEdHzUBkQ0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "300bc6e0e86374f7ba61068b5b1ecd57fc6532da", "browser": "semver.browser.js", "gitHead": "63c48296ca5da3ba6a88c743bb8c92effc789811", "scripts": {"test": "tap test/*.js", "prepublish": "make"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "2.0.1", "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}}, "5.0.0": {"name": "semver", "version": "5.0.0", "license": "ISC", "_id": "semver@5.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "f96fd0f81ea71ec131aceac26725cef2a255dc01", "tarball": "https://registry.npmjs.org/semver/-/semver-5.0.0.tgz", "integrity": "sha512-z/rlLJBd9rXGzbWqnPw1zBkcn2hqGPQ+I95tJIBbyqMKnX9E+J4DqPvIJxxQHldsIxEG/Z60TVRwwJcjl11IeQ==", "signatures": [{"sig": "MEYCIQCOltskKTGHeSnehiw4UkIabqwdY9Xrbvmy5wQdfFLiAwIhAI5PoFzbLJMP9V1mka5qMRT3PvwIUWUbqQ9uZQslXcsH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "f96fd0f81ea71ec131aceac26725cef2a255dc01", "gitHead": "01ac00c45efa423894b2da5b043ce6190c96ae96", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}}, "5.0.1": {"name": "semver", "version": "5.0.1", "license": "ISC", "_id": "semver@5.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "9fb3f4004f900d83c47968fe42f7583e05832cc9", "tarball": "https://registry.npmjs.org/semver/-/semver-5.0.1.tgz", "integrity": "sha512-Ne6/HdGZvvpXBdjW3o8J0pvxC2jnmVNBK7MKkMgsOBfrsIdTXfA5x+H9DUbQ2xzyvnLv0A0v9x8R4B40xNZIRQ==", "signatures": [{"sig": "MEUCIQCvUUR9zz4lpfNZViOdTtzOi8Sy4IWzGNg0ISMxpOzHjgIgPqr13rRgqB7aGLbXWK/qRzwEe9HHnP6k6tKp4oOCZSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "9fb3f4004f900d83c47968fe42f7583e05832cc9", "gitHead": "3408896f115cdb241684fb81f85abb0d2ecc27e9", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.1.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "2.2.1", "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}}, "5.0.2": {"name": "semver", "version": "5.0.2", "license": "ISC", "_id": "semver@5.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "19041bd286619344116c60bcc011a3a4cb4a14ef", "tarball": "https://registry.npmjs.org/semver/-/semver-5.0.2.tgz", "integrity": "sha512-VAVUsO7VKPfjjzkxcn02KJA0FxiaAx3xUFcF88QXXdRKuHpBsdGJnY51o5cfML2QUZPcghgnKQBamjD1N9SMUQ==", "signatures": [{"sig": "MEUCIQDuXYS2zymve0u5OYdbPlzqCNVrA0THPL1gX95EVL1TawIgHF2T/2+tnRtLw6E5ccztFBqn7zz4mJsnzajTFeZE+ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "19041bd286619344116c60bcc011a3a4cb4a14ef", "gitHead": "df967e1ad6251d0433b0398a93756142a423a528", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^1.2.0", "uglify-js": "~2.3.6"}}, "5.0.3": {"name": "semver", "version": "5.0.3", "license": "ISC", "_id": "semver@5.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "77466de589cd5d3c95f138aa78bc569a3cb5d27a", "tarball": "https://registry.npmjs.org/semver/-/semver-5.0.3.tgz", "integrity": "sha512-5OkOBiw69xqmxOFIXwXsiY1HlE+om8nNptg1ZIf95fzcnfgOv2fLm7pmmGbRJsjJIqPpW5Kwy4wpDBTz5wQlUw==", "signatures": [{"sig": "MEYCIQD7yYc0vAKUfa4irI6Rrg46K8imnfHyc883MJ3UpNOL3AIhAOa0OIAmuklS3c7SjvpgOMCQ5zJ/OVEIOqZI9FNdVZvZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "77466de589cd5d3c95f138aa78bc569a3cb5d27a", "gitHead": "5f89ecbe78145ad0b501cf6279f602a23c89738d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^1.3.4"}}, "5.1.0": {"name": "semver", "version": "5.1.0", "license": "ISC", "_id": "semver@5.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "85f2cf8550465c4df000cf7d86f6b054106ab9e5", "tarball": "https://registry.npmjs.org/semver/-/semver-5.1.0.tgz", "integrity": "sha512-sfKXKhcz5XVyfUZa2V4RbjK0xjOJCMLNF9H4p4v0UCo9wNHM/lH9RDuyDbGEtxWLMDlPBc8xI7AbbVLKXty+rQ==", "signatures": [{"sig": "MEUCIQDrHij4DDgmlmTPtA9l7bSYrbqoGDQ01X69wsbgThGDEAIgTtokIOlNUNTgkstc4tC79AGuFT1Pa0sAJjs5bLtEIRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "85f2cf8550465c4df000cf7d86f6b054106ab9e5", "gitHead": "8e33a30e62e40e4983d1c5f55e794331b861aadc", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "4.0.0", "devDependencies": {"tap": "^2.0.0"}}, "5.1.1": {"name": "semver", "version": "5.1.1", "license": "ISC", "_id": "semver@5.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "a3292a373e6f3e0798da0b20641b9a9c5bc47e19", "tarball": "https://registry.npmjs.org/semver/-/semver-5.1.1.tgz", "integrity": "sha512-bNx9Zdbi1OUN62PbKeG4IgGG8YILX/nkHJ0NQEBwg5FmX8qTJfqhYd3reqkm0DxHCC8nkazb6UjNiBSHCBWVtA==", "signatures": [{"sig": "MEUCIQCfxXSOYRQZ6D6t3P0DL8DKSm+d+mg1UGH/4L9QwNBD4wIgdtyIYuuhORK/yDdpZIHMEWJER20y32PoVPcHUQZKrHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "_shasum": "a3292a373e6f3e0798da0b20641b9a9c5bc47e19", "gitHead": "ad1d3658a1b5749c38b9d21280c629f4fa2fee54", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.10.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "4.4.4", "devDependencies": {"tap": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver-5.1.1.tgz_1466704850953_0.017174890032038093", "host": "packages-12-west.internal.npmjs.com"}}, "5.2.0": {"name": "semver", "version": "5.2.0", "license": "ISC", "_id": "semver@5.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "281995b80c1448209415ddbc4cf50c269cef55c5", "tarball": "https://registry.npmjs.org/semver/-/semver-5.2.0.tgz", "integrity": "sha512-+vNx/U181x07/dDbDtughakdpvn2eINSlw2EL+lPQZnQUmDTesiWjzH/dp95mxld9qP9D1sD+x71YLO4WURAeg==", "signatures": [{"sig": "MEYCIQDs6Vu7T1aJdIaqTs0j7u38248DGVhOsgdnxFNp6eo2NAIhALygXWnN20SyiiodeLW96Sg3xZjIKe/nYfODwwwf3zBA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "files": ["bin", "range.bnf", "semver.js"], "_shasum": "281995b80c1448209415ddbc4cf50c269cef55c5", "gitHead": "f7fef36765c53ebe237bf415c3ea002f24aa5621", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.10.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "4.4.4", "devDependencies": {"tap": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver-5.2.0.tgz_1467136841238_0.2250258030835539", "host": "packages-12-west.internal.npmjs.com"}}, "5.3.0": {"name": "semver", "version": "5.3.0", "license": "ISC", "_id": "semver@5.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f", "tarball": "https://registry.npmjs.org/semver/-/semver-5.3.0.tgz", "integrity": "sha512-mfmm3/H9+67MCVix1h+IXTpDwL6710LyHuk7+cWC9T1mE0qz4iHhh6r4hU2wrIT9iTsAAC2XQRvfblL028cpLw==", "signatures": [{"sig": "MEQCIAok1x5L2mDUdSqV7Ym4Jdd8AtHTLR/wI72bCWMJg5j3AiAwSpY4n5y1e2JjHeAL2g7LSygRs1yzsVRH1sgTlFBaIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "_from": ".", "files": ["bin", "range.bnf", "semver.js"], "_shasum": "9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f", "gitHead": "d21444a0658224b152ce54965d02dbe0856afb84", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "4.4.4", "devDependencies": {"tap": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver-5.3.0.tgz_1468515166602_0.9155273644719273", "host": "packages-12-west.internal.npmjs.com"}}, "5.4.0": {"name": "semver", "version": "5.4.0", "license": "ISC", "_id": "semver@5.4.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "4b753f9bc8dc4c0b30cf460124ed17ae65444ae8", "tarball": "https://registry.npmjs.org/semver/-/semver-5.4.0.tgz", "integrity": "sha512-TBZ1MavfXEY92Ohe3vwQbXSSIUy7HRHuSayvV84i9/+BHzHxYZxtnam2FEdIMvkri17UmUD2iz5KzWI4MQpEyQ==", "signatures": [{"sig": "MEYCIQDjd76j2Kgdy4adkvhIsSp+lwbhKdPRfg9W7q1jWKxuigIhAJTlCCqliJ9pDZiQ+tzEPAZm8CetpiC0pWjSwrP9xnEs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "files": ["bin", "range.bnf", "semver.js"], "gitHead": "e1c49c8dea7e75f0f341b98260098731e7f12519", "scripts": {"test": "tap test/*.js --cov -J"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "8.2.1", "devDependencies": {"tap": "^10.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver-5.4.0.tgz_1500914373430_0.7377612616401166", "host": "s3://npm-registry-packages"}}, "5.4.1": {"name": "semver", "version": "5.4.1", "license": "ISC", "_id": "semver@5.4.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "e059c09d8571f0540823733433505d3a2f00b18e", "tarball": "https://registry.npmjs.org/semver/-/semver-5.4.1.tgz", "integrity": "sha512-WfG/X9+oATh81XtllIo/I8gOiY9EXRdv1cQdyykeXK17YcUW3EXUAi2To4pcH6nZtJPr7ZOpM5OMyWJZm+8Rsg==", "signatures": [{"sig": "MEQCICGJIfKBfoO/4UJYeoufB6IE5Ng6NtJIacgYPYui3MhhAiBnFlr28SDykeDg+yx4j7PFgkZ2JgbjKKJNweLtSPFR9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "files": ["bin", "range.bnf", "semver.js"], "gitHead": "0877c942a6af00edcda5c16fdd934684e1b20a1c", "scripts": {"test": "tap test/*.js --cov -J"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "8.2.1", "devDependencies": {"tap": "^10.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver-5.4.1.tgz_1500922107643_0.5125251261051744", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "semver", "version": "5.5.0", "license": "ISC", "_id": "semver@5.5.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "dc4bbc7a6ca9d916dee5d43516f0092b58f7b8ab", "tarball": "https://registry.npmjs.org/semver/-/semver-5.5.0.tgz", "integrity": "sha512-4SJ3dm0WAwWy/NVeioZh5AntkdJoWKxHxcmyP622fOkgHa4z3R0TdBJICINyaSDE6uNwVc8gZr+ZinwZAH4xIA==", "signatures": [{"sig": "MEQCICX2Rkd0RwqzHKYcJgntbPyklQQ4OQf53jsRziJCrMKoAiBdiriirSSJmTq0gkjaWItHjndfr6si1upBK9rP1gMN9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "semver.js", "files": ["bin", "range.bnf", "semver.js"], "gitHead": "44cbc8482ac4f0f8d2de0abb7f8808056d2d55f9", "scripts": {"test": "tap test/*.js --cov -J"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"tap": "^10.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver-5.5.0.tgz_1516130879707_0.30317740654572845", "host": "s3://npm-registry-packages"}}, "5.5.1": {"name": "semver", "version": "5.5.1", "license": "ISC", "_id": "semver@5.5.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "7dfdd8814bdb7cabc7be0fb1d734cfb66c940477", "tarball": "https://registry.npmjs.org/semver/-/semver-5.5.1.tgz", "fileCount": 6, "integrity": "sha512-PqpAxfrEhlSUWge8dwIp4tZnQ25DIOthpiaHNIthsjEFQD6EvqUKUDM7L8O2rShkFccYo1VjJR0coWfNkCubRw==", "signatures": [{"sig": "MEQCIElCuedrSTWZX5T5nVy10XlO2ZfPag+ZaMR3Lfwqj/HSAiBJB9xDBlGpsggAISG82DqShYfxkWWRK/UqBbGan+eTKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdzGjCRA9TVsSAnZWagAA9dkP/13iStlVBKH4BSsU2Lo6\n8ZPM5pOX5+ZLmF6cEsn16YEm+NVYXvYmw5Hy0Mcosgf4Wi6GZzKYup+t00D3\nkd5A9X5E0e+4l/bxmF6i/3qkRcxxXSUHjpLLw3wOc71H840Zdfmh0aT7+Foy\noVFgvAgJMueVsA0fgyks3q1SpssM3Gk1rsUDCLjIfLVViqfrdi952pI+Qwe0\n3hju+N7P8evmA5w1VwogT9uexMAQ61mlsibZMDUyPs9Bky6gMKcY/m3H9MNO\nlw5EoiQLid3Ca+AExrP+011dSAs+XE1tVo4pkzsSeAVMMe/xiH+FtFthetsv\nymDbzoVFCjSjlFJIeo4Ct3zS0Tsr3tJZmgx6BAGf/RGpMQUEUeuPUDLEHQ33\n3VQSBz9ANIrTfRmPep1iJHUN9odtfOAGEuZsFYdeP1m8Vf/5zcML5BAeqtVk\n/HVT76llGP44utxK6MeasJYJctHPT/E0ITf/ILhliATQgr6v/+UTVITtAJo1\nCnTc8GCzUdtxw+lqxDnQf9PJ2sHvfPwTshHiPnAFORRhWnFJCJV59j62yR4G\n4Miam6QnGcqe8BkgZ8w4yRC9L/hc3eezPrMcecTWaqxDNO67vE8GywxL5Xh5\nE0NqHNoabkwH2G0Gr+BgKeyi6tyBeoKIdrBFyRe8CnnELs2rcjs39b1TQ1je\nC/sz\r\n=OhAx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "files": ["bin", "range.bnf", "semver.js"], "gitHead": "89bfa00a24b93cb7d10b6a89486e1f927837952f", "scripts": {"test": "tap test/*.js --cov -J"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.3.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "10.0.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_5.5.1_1534538146584_0.02094243650440908", "host": "s3://npm-registry-packages"}}, "5.6.0": {"name": "semver", "version": "5.6.0", "license": "ISC", "_id": "semver@5.6.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "dist": {"shasum": "7e74256fbaa49c75aa7c7a205cc22799cac80004", "tarball": "https://registry.npmjs.org/semver/-/semver-5.6.0.tgz", "fileCount": 6, "integrity": "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg==", "signatures": [{"sig": "MEYCIQCPrdnmdzjousP5bvQSbIZ2lBWr9s8Pmuwvjg9ixU5XngIhAJKNdwdY73U6fBGpP8cZZ/233duXswUGrRZsGD9TPdN7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvpC6CRA9TVsSAnZWagAApbMP/3bk+fOFL1FLwS924EDm\nrwE9nR9Knp9+J6Gme1UH1HvMkNm92/ZSvrbMaW5Uzw3RhRJOEjdtDpQFpJ/N\n7SFdFf4RmrZKThr4EuVVi/Iuy2MwIpZ3UUbrDYWV1kTwfMeW6mDWqcNUOOIk\nHLW9icN0oyvbB4LfPwhr3SFDKlTi6TlyZseQHFGndSbw2/mS9wdLHaa6bz0x\nB85SW5PVPw7Y83RdzdPz59vrNmVytY0pEsepLR7IQkdyB+YU7cIxR7gLbzkY\nUV5F6FyvvOcrfEIiRZgprhH5a2RnYZ8Id/3/ca08yN1Q65SsjztKq4Bh7+JI\nO/pjNjRWKLYsRom9l4q4iAFhdy6fFHlqkUkM1Yy5jFmQNjSk5RnpuRrDUUXV\ntFbWGkEccYrXby5WOq1JZxJ/MfiUpxg22qlO3AcTHbKddhFZvYh/cQFIohV4\nbmV7TeWoodJ7KaF6MVhnwda1AWTBZTNSqrCmsmDSZZwNb6NQhVSrbkLXDbgu\ntN4RhRPjOE1mFYjuGJuN32oSdceOCT9x61Lr0uucw8JHZldVIe0o63Qb6/4t\ntl+wzR3JfbdkB1vGVo8oXm8KoghMhFwmpD+RKg4hPxJhxQTXRy4OUsSkU5+l\nRRK9JHTyBGXbpcyeVLtPAGYdsl42sYG9+lSqGI3at/HKDcJrnzp3KJ4fXcUB\nlkPD\r\n=LpzA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "46727afbe21b8d14641a0d1c4c7ee58bd053f922", "scripts": {"test": "tap test/*.js --cov -J"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "10.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_5.6.0_1539215545199_0.6768223257800898", "host": "s3://npm-registry-packages"}}, "5.7.0": {"name": "semver", "version": "5.7.0", "license": "ISC", "_id": "semver@5.7.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "790a7cf6fea5459bac96110b29b60412dc8ff96b", "tarball": "https://registry.npmjs.org/semver/-/semver-5.7.0.tgz", "fileCount": 7, "integrity": "sha512-Ya52jSX2u7QKghxeoFGpLwCtGlt7j0oY9DYb5apt9nPlJ42ID+ulTXESnt/qAQcoSERyZ5sl3LDIOw0nAn/5DA==", "signatures": [{"sig": "MEQCIF7/5kMIrUuUaQWsnQdFaFdpWXXXymVSPzkidm9E0dEVAiAKv2Va382R1Iun9y+M4+eDdcpo5IlJ7DN6wTnGtdo8dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmrT8CRA9TVsSAnZWagAARMMQAI415wi9qxabS351a5Wf\nFD71A8Ss13BZZJ/R1R6kGsSrGbM9d0cjrikxGnVc0nTyyG9cu4MZRQTQiAPn\norBKEUk7xtYzXXAseaQAJX3g1/iX+aGkZBEo14yABXfv615Xnmroe5yQ3m6h\n0HkHxXjdxRwBb2WEwNDCNrvmzTwxvkGg32Rqu+Ow+fbsnubgc7+q12ooq9lD\nscxvAqhMuDcHRFHEFoQFUpW5YO+N7/cY7GEdNkJnsDbd+yCfnJOJSUqDF0rz\nHiv+j9CkwiRMkliimBW1KXRiYz2S/HjEuOcfg+MwUk7l3pFIZH2cTyYoQBQC\n+nR241WcuWBX+BwOFmbvHiMWPcJGvYK8r9Ql7Pswzjxerbt/VIOiWAEFlVX1\nOaopM8d+exP8EEurYBFT1OF0FSD5LOjV96b7B3SuvlrooDVU4lKdF+wXWsPr\nkHR01hULTV/JG4BTvblo5T7x74lJ8tG5vIW/CQQXLDeAgVlHgJNdrmnX+nB4\nwBcWDw/bZYJ1AV3Zzb3VJKbSjw4GAgo8OevlGrAmyp8T7IhZzpX+V/BQs1V1\nW7AiEVUmptY89GWCBc5MPc+YwEeg+ti0LfLGcn15+CFFImIY1taytkuGpCMw\nA2+lsjXrW9+7RtUInZFjbFxsh/q1HRFng+C8dRhBBpPOmbHGRBUUtClHU9V4\nkbZh\r\n=hoZW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "8055dda0aee91372e3bfc47754a62f40e8a63b98", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "11.11.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^13.0.0-rc.18"}, "_npmOperationalInternal": {"tmp": "tmp/semver_5.7.0_1553642746999_0.7735603320650997", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "semver", "version": "6.0.0", "license": "ISC", "_id": "semver@6.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "05e359ee571e5ad7ed641a6eec1e547ba52dea65", "tarball": "https://registry.npmjs.org/semver/-/semver-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-0UewU+9rFapKFnlbirLi3byoOuhrSsli/z/ihNnvM24vgF+8sNBiI1LZPBSH9wJKUwaUbw+s3hToDLCXkrghrQ==", "signatures": [{"sig": "MEQCIGzF0UT7ClPVdoGF1mjr/FNof+X/7AKnZX3tXXslMaXIAiB8SSTHw8yRR9PchcSl1pDTO47mt0c6j8DONwdhB6F3QA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmrX+CRA9TVsSAnZWagAAoRwP/0YCCBFIfY3upqagUByQ\niSSXpONHBXU1EFnjbfTBfLyFWQR7P5O2fdSuGRLDBKKkT4S9w0cAsPAXGiPD\nHhRILVd+8JlnJl4W8bfVSC/DLZySWkUnJzkCcje6goCJDfM1FU8k8DUg2iQi\np2+4u1C5An2ALUNT15IbKye7bpHXnTkJppfyp65d3adcbpz29o1lnb5EvgkX\nnVSxRDla/JNl4e3bTUMINV//iCEd1M7J1em2rQ0jIuZo0sKiuVOprEtXd7OZ\n3UiOOnTOyl+qjw8ivfHvbvXY3FHtIWEKnBPkWZq7uI3Vdv6EKwb1/8xmnMMT\nqYfkanQ9ONh7TYWUOhgNLiJDYu1IJP2sabu/JC2LqYbZ8BCeuzNUatwoelxk\nGXexUfYE8DkdaeFprl8UJlPFScuXIf/zgu0uzmpodabp0K27j/y1A9jGpUC7\ntD0ehK11H90rGsbqXxHJUW4q87edMspLdk7pJFqE9qXTqovHN4gY9I4Mcan/\nzGuEViraahDlUxyMxUaQZ6dGBL6FG7M0viPC65J4yQrlkVl/BGL3ccHhEB3R\n8V2cszwskVjwdLXeYSoalaOtWg/MkoN+6ZkobVYrJcirws7i/y3OiXpUsofu\nNroA7knyguQt1bU6XTk78/T3/ihZFROSmP5sQkxRvjjdXC4MDXMgjlxzjrzf\nmWlI\r\n=KdhG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "5fb517b2906a0763518e1941a3f4a163956a81d3", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "11.11.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^13.0.0-rc.18"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.0.0_1553643005349_0.774072845857031", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "semver", "version": "6.1.0", "license": "ISC", "_id": "semver@6.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "e95dc415d45ecf03f2f9f83b264a6b11f49c0cca", "tarball": "https://registry.npmjs.org/semver/-/semver-6.1.0.tgz", "fileCount": 7, "integrity": "sha512-kCqEOOHoBcFs/2Ccuk4Xarm/KiWRSLEX9CAZF8xkJ6ZPlIoTZ8V5f7J16vYLJqDbR7KrxTJpR2lqjIEm2Qx9cQ==", "signatures": [{"sig": "MEUCIQCmQ8x3Rqyn7q3fBp47jsUTJU8Fr83zgOH9NSDSXrMb4QIgIcUMphOKl1T34LmAPAhiLi6NMEqHmFH9+QDUSo/Ajs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5btRCRA9TVsSAnZWagAAgsEP/0Y2HJZpOn2psrflNRGf\nLxDBJgcXjtw7uvqhTfJ9FaNtSLO+7hRMf/DmysNlEOTvrHyxIbjuNQIxXE0+\nTwhQjUXinbVEPhojcuHpyfVHf/3Hpw6Y6w20njrSQSakIZ1LR0Ps2WRpHElI\nBeCUL2XztGkhBFI8X2sLbIapDb47lCsLz1yWQNu180y9y9ec2FId2UXZAhXX\nmzQRquZANcCli2up7fWssJgIGIWnhZopgOlUjSOtKHPf2whk115naEqiQQyy\niaGhGvtxPqZXQ0aADQJIHNIfG+PAweKdUa6/hocSf83EPMcbB3jNEQ3hvvxp\n0lyUy4l+M3TAWplMDBxb1OjZG0d/PM3ct6t3K0j5HeLBG20jDnNTkEHGTUpd\n3YveaIT6RiVYDYpNLJOE0EJeiB/57FZLZEZHM/2iOL3YN0kjANio/bMWJr44\nC0CX+KqOLUOYjlyFixWdQswgS4Rv0GiMjql54y2T3nrqmneQvs+BWjJ97H9v\n7RMkmRD8xdic7E7VkWD+uCGAEN/57Od4vCpAvcDfdgriEcIlBodItOgc+P9n\nBd1XIhiDf0VFFKI2Lnc3WOZ51yyBtQWmX/Wxl09VSqo9yv52PwHUMPVBIgY4\nr+he+TmSDHSHFIiL3AO8O62iuNJrZ4lljAE+/AmFDT85RMmCu5mQcBd/l/On\nKMRV\r\n=Ffwf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "0aea9ca2d3c4bc879f5a0f582d16026ee51b4495", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.1.0_1558559568989_0.41700709355019305", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "semver", "version": "6.1.1", "license": "ISC", "_id": "semver@6.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "53f53da9b30b2103cd4f15eab3a18ecbcb210c9b", "tarball": "https://registry.npmjs.org/semver/-/semver-6.1.1.tgz", "fileCount": 7, "integrity": "sha512-rWYq2e5iYW+fFe/oPPtYJxYgjBm8sC4rmoGdUOgBB7VnwKt6HrL793l2voH1UlsyYZpJ4g0wfjnTEO1s1NP2eQ==", "signatures": [{"sig": "MEUCIQCYWTSGuBxPBgXJCS16bjuBYbDa+KzHFu9Av5lO75O4cAIgMCCNGpZ58PUQR94sqVMXLa8yBQUHeiKo5UrHdrqlQ7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7WycCRA9TVsSAnZWagAA8IcP/0R30XsRrUZQUA9MbxFV\niuIoMVb6ZsFMTbHRSPGmOKEfr5OOEwwhKZbpzqD97z8h0uxZB8yntwnjadaB\nJD/POnEdbX9QSzp9t1D9iEJTEOk5PPcQgwacEcQSa1R1m1QjAjeN+kQqrWq5\n1tRFxnPHOr2b+XYwkuH4tIkQgKEeBnAHT5+gFt2h4RVcTzS7hM5Z0sExgx3Z\nw2tFCCZcCtoReKM7ZgbCWk5HSA3ORD09bSJLKOEvOwVIqdfNvsh85/30ab3X\nos5vWEg6++twniVIuW+fLBa9Suyb6f/8LwPrVITustjWbUkakQZELs14K2dh\nIPmtRYMJe3Mv6WQhYxjftyBRHSn5rqybuFNY7Khscm4WM3YwexOhFZ6LCIj5\nt7b1y2l645LR2+tw2xv4Vg8w50xRWZdD/lM/b/lAAQCDc4nQ77HkBHT7fV+j\nrPwxheQJ3RLzPzNXFefJuPl9zjXw4Kxc85UDc83IQEM9wGqiyX5MSal5BcbL\nYWAJk0/fORhrEE49vaTlMepkiAwa6+SxMqnp/Yu1bIOqYPZhUxPaBTZNZDRU\nm3OL0c+rkS6GvaJ3U6wVqG9xBIRb3kWOO45llv5WNhXDnObsnfz2zMz8ciDE\nJAGmnQdOaYtEWAG05mWBSaY+n2WrwkE+u3+mf6ndNvkCt/lAWwjYH+FPC4oi\nkEvU\r\n=fBQu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "0e3bcedfb19e2f7ef64b9eb0a0f1554ed7d94be0", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.2.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.1.1_1559063708250_0.6141590731812676", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "semver", "version": "6.1.2", "license": "ISC", "_id": "semver@6.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "079960381376a3db62eb2edc8a3bfb10c7cfe318", "tarball": "https://registry.npmjs.org/semver/-/semver-6.1.2.tgz", "fileCount": 7, "integrity": "sha512-z4PqiCpomGtWj8633oeAdXm1Kn1W++3T8epkZYnwiVgIYIJ0QHszhInYSJTYxebByQH7KVCEAn8R9duzZW2PhQ==", "signatures": [{"sig": "MEUCID6u7TGOShE0QBljEM8KOkaCVXy1fDpDb10k33GaJBUZAiEA9ycw1Pxh0Hb51NXObo/w2y9Ea5zSOYt1QmrYafl2j7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64286, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdECvTCRA9TVsSAnZWagAAUVwP/06WUedeMoHIjvX+ItO7\nXTCuNar3748Ue6x6zYIztkPCSENP8+wI/l1ctBEu6xOG0RKzXTes8wl+GHFL\nhUvImiKlu6SklcTIkNPpBfCJqYlZp5ANECPEq4zpPWQ0cynYC6NAHE7lU42p\nkEwpvHVeIM5FJlfqcKQhrFtpVXPSvTDPbvsg0+fQTUpOZ1M+iDhBTj074qvu\nfrRkeDVSZVpppOPYng6GgiAq9Pl5+snma0kJEtVQeTyx1Q7p5z52urv+9f+z\n3hSPi//fGhANj2ohoNng+hFNKVAAFgbLPzHrPKZYoIkwd+3Le5D1zNdPeggp\n4miRfPLWSSrOBvCQFXDVbyb6d2Zm2OEj2L+yGUr2Uk4SnAZ6kzz/UJm9msnh\n2k35zppLUCrjZ8UPkmbBrg91TF/WjpaXyzU1EUYHwbtx2JRS8HijXLVbmwxq\nmWFdfF3fQ02Z1VW7beFPBudYhGzMUz2DtBc5TL+iupIV1cWEnIVcPT3MQuBb\nOPvD+CXcRdzlieAPOKpvYpcbcD1CI+OeYD6AOl/P6nky6qpoNC4p9XwXHQYw\n8VsBpcq0xVP5bFKnr+T6KsH8ySvdXgKm/PgxPOf4qarHkKG5ZUgN83O9dwZU\nL3JdG+12MgKoQRjg8+oHHK87R9Lmdn6w3m80Y2ANMzUG3lI2L1Bvzxq58WSc\nZHco\r\n=M2gi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "7ba4563de94e473817c7b8606f564359e78fa8ea", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.3.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.1.2_1561340883124_0.7463086402417924", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "semver", "version": "6.1.3", "license": "ISC", "_id": "semver@6.1.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "ef997a1a024f67dd48a7f155df88bb7b5c6c3fc7", "tarball": "https://registry.npmjs.org/semver/-/semver-6.1.3.tgz", "fileCount": 7, "integrity": "sha512-aymF+56WJJMyXQHcd4hlK4N75rwj5RQpfW8ePlQnJsTYOBLlLbcIErR/G1s9SkIvKBqOudR3KAx4wEqP+F1hNQ==", "signatures": [{"sig": "MEUCIBkXf1PxW5D7KC6es9r4qMv/IBlcijz7bKIDUVzSGZcaAiEAvH19WTdbwVLpGvn/MpZdEcd8cEc0L2bkElKPZl60054=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGZ9NCRA9TVsSAnZWagAAcqkQAKCXi2se8vhb+QCyDmY+\nqo4/fLtzOYy5EliORLmQXvDKEIzI0pcaEW/Fg/dL+uEyBStyfbPj/h+XKjjg\nggc2/IrxFoBpz0FkrobRPXpQO6L2+DDUS5Ch93RaN4fUP+SzLajs30ZE7aJs\n0xZVb4ey7nhguroCStfVTgClMXAPTeWNb8ZOLxBRNQ6F4d8cOqvZLfreIt3z\nOry6a0vAxI4tft4/Ps97XIXWs1NV7vTBq5A2RBa7mEjYaVnM+wAi89DCXdrs\n+cMkZoCiOVi1uvp3hUditzDgZXec667VDY+19Q3ELJ1EUCkVdrklLT05X5ay\nY/z0H2G/4/OA1bn/yEFZQ/jMYIg8K3ZLa2N++laluCjrHhOJmVO/0SStx9+d\nvF23vdnTNdOPWxyho9OJabVVjjFqOYs+vkE65t/QhKcMyUX2O/a4EBc+Rpwm\nXzWy+CXf/aesXqpYpKQiTPLVzW20NZ25YzLn0iJcApRvlTbAQNTais9uLMNI\nCtgmnzSnK06Ue6O702HsBkrOpm8mJDTYnV15+fvcRIap4sCTbegStBMO3u8n\n9pTME9iJ82CZ2zvVyPnG1cks+vfGrcLyijnIDnfK7xiDl+oncD7zVmDdYxSj\nqAqmgiilbo+gGjDMue3zSY6/JFwg+xheUHFfBpgLovk7VyOWdz2DCpWqnqNU\n7xyb\r\n=2ohr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "3dc88f3b3d1563aa92bca3b60d739b214937ca27", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.9.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.1.3_1561960268574_0.9939105883443704", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "semver", "version": "6.2.0", "license": "ISC", "_id": "semver@6.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver.js"}, "tap": {"check-coverage": true}, "dist": {"shasum": "4d813d9590aaf8a9192693d6c85b9344de5901db", "tarball": "https://registry.npmjs.org/semver/-/semver-6.2.0.tgz", "fileCount": 8, "integrity": "sha512-jdFC1VdUGT/2Scgbimf7FSx9iJLXoqfglSF+gJeuNWVpiE37OIbc1jywR/GJyFdz3mnkz2/id0L0J/cr0izR5A==", "signatures": [{"sig": "MEUCIF+dPiUayq4Bqd5swr0vTWXzT5LLiPzu7eUkGcA/G9KNAiEA/CHGKIYeUpUu6ByEq4Ez2dOPvnpSGSUV7sRyJjbeagI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGpFACRA9TVsSAnZWagAA0AsP/igqD2dT8oa7I6GXeoYh\nB4Q4NVEA7rU5la2rA9Pd0OFZJ3xmZoUwkCvN0HOdH33DxCcgV5v9ZExdMm1a\nW+lhz0ByojoX8xiRbLHtycS0FQElmsJzEWLfQziUyKy5whu7QQQ1jK0gKPka\nLvGt2o5DOAusE4cQIQMNPE7OQYIHRmgKfFp13UciJd1FC38LKENGbWsYBFPa\n5M2d4oB/odg0b5NNe+ufaAr0wIy+OFVE3z7I8bP4RD9Kvm0vCoGCpaVS2nxY\nbogq1F4nwhTnOPK9/3p/AUXUP7gK5A0MtzYHuteYPKtUX+/amvVN2k9eAV4Z\n+hQN5E1kkTs/YDg4561y1vS+ukSBY2J1NCnEI9fMZ/RVm2p4RZ5g5u+frxeO\nWdt2lIJ2CGC+Amwaf0+koHIyUeDfIlWE9paB6iBCcRBQuXxGjNdE/TUFQaYn\nBDlAj9q4efBLe9KVdIvSME3C7ztR0z9ouyule4jSviFqyAygKNem+inaeUs1\nVwvvG5fYtk5sx8F4f0Ns3TUyC3BjrTv3I1U6Eq8IaEc1jJft1OAl5fgcUqYe\nvbVT9I4TYo9uWeuABL1Vfgwl/eoXvE9ZxrXspO9rST7PRij//fYD2tYeJ6tT\nyIkavdL+N4WMc5nXrYhXkR8rqZYR0O0i9xhulHdFJ6DFkdCdjyc8Ei1j/ILD\ncFUf\r\n=YZPi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "ce6190e2b681700dcc5d7309fe8eda99941f712d", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.10.0-next.0", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.2.0_1562022207205_0.5907925634442714", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "semver", "version": "6.3.0", "license": "ISC", "_id": "semver@6.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver.js"}, "tap": {"check-coverage": true}, "dist": {"shasum": "ee0a64c8af5e8ceea67687b133761e1becbd1d3d", "tarball": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "fileCount": 7, "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "signatures": [{"sig": "MEYCIQD9/QMBP50EQ2ZsHNXgfAAx/r+86rC/s1iLWAbHLtp9+AIhAM80OrwwhN+gYfAL46tSaNJy2afhXLUNPZDpO7eZ2R34", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdN18nCRA9TVsSAnZWagAAw0AQAKBp2ynlIw1BNM6KtKKE\ndfzCM5T+p/uNU6uuHeHVNAtEaiaR49Yj02cZwyebsGABvo3shL91zUN4wHVR\nsnCDodrZ0MMUtSishHbUa+qnD5PzT0wfli2VgCHMHmMikt6ILdpGM50o2edX\ntEnMztR+NIHQ5URFR9gNyPn1zrNZ6axT1exx+Xw0We0dVb7jUqUh3xN3F3b+\n06tD10xoh64Lny3FZx+GoIgV/8XIFTPSPu7qw4xNpXuW08NR8a/5A6+AKFZe\nOAKFuXtMLoQVhb5Qu0grw2NrfBcQbo6YI8J+N+7KnE0dDGUJh6LQ29VRhNam\nTz/XR0g/TB7JQcZAFtVi/OUTcoYHnSLDYuImvlzTJTjvtmODBUVoMOBAaoxr\ncVeGUFjRUu5i8LmNkKvfQvCWZYACD/u5o8nFpv13aC9gKsIlfPc2SygHJ7/N\nG6EjJPXSFP9/VbB1hH7JRzy5e8ztbbRvxl/tJDIOj/wFKaL0mF9tuI/VvCtl\nbA6i3W8dhQORgfmJ1Y6HZSQtIu+mmE0FNcNiaFsnpU+Az5onPWHPhKwWfzri\naCH/KgIf8c9JQrgovaPKaADhao6RaWKh6ucmuCp9s+4OnPmSoIYKdIPTFHer\nBRAddBXV6dsPGj9tN4bfGTtISSQA0t/3Gvk5fqErENZafAKHLLQY7bIJtloe\nDy19\r\n=NlB3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "0eeceecfba490d136eb3ccae3a8dc118a28565a0", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.3.0_1563909926455_0.245174701596915", "host": "s3://npm-registry-packages"}}, "5.7.1": {"name": "semver", "version": "5.7.1", "license": "ISC", "_id": "semver@5.7.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "./bin/semver"}, "tap": {"check-coverage": true}, "dist": {"shasum": "a954f931aeba508d307bbf069eff0c01c96116f7", "tarball": "https://registry.npmjs.org/semver/-/semver-5.7.1.tgz", "fileCount": 7, "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ==", "signatures": [{"sig": "MEUCIEIpmCEaNSpS83o3nMtcyfFbcZyEj7QACzHU6yrAPe7fAiEAkZd5wQb+jQzQccfpF8xQv/9MHmCdqelbYNUWNjF1DfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUZOfCRA9TVsSAnZWagAALOoQAIryFFr3APbQy7JtFRVQ\nZWyRM6KerD0BuRsiDj8Z+krefX6/DJ4CghE3P3dYwSyJI3irUOLblY2Je6S3\nQPnQLOdT2R0uC/mOnZ5xfu5ok88KkXwc2UQdsot+u+FCMerbv+XPHnOi2T+Q\nhYYuOP26jX74MdZJr5LrXZsBppEeypVCGEi/k5B+L0AM2EPBVzrhfl7+OjqT\nIRao2JvxOBpF6D6p1Q+x48yGcPGWH5qnSeaXFBnH7lzJD64IJPb5c5oA57AX\ndy1NFbQ1bFLZ++7RwQ4dsZlC614/58fCrasdepTQkFxKGv6Glz0TxdrsEqyE\nRPuP0on337QcRwNRB7buoVkBE1gNTc3x9yisJRNBMzfOaPiEg1rQdnN9pr8o\nOvespmkE2SbTGU5zJA3cy7O/4IAK5epBzsWuqLSnA4aOXEb1zlmVW4Q7pSAY\nYXE1G2OB/LMMCcs947/6PR78q9sa7+Hw6nqg0GV4lrJhCFVYezRVCsY7GNay\nJ/GzPB/PaffK1fzLUG1eg1USItnh2QmDnnF2fYpqIN96IgaJ4YN3mlOzLnM8\nJ+/p1cyHeGZI1gT8HVq3XOZXgsl/gtF4zTyfwx2YNnM9E8FCKttKF9AYHH9p\n9EpwbiSSMREq4B19wt0Uy88NxZDgqHcz+MVgUNREWopxOXnD5Ka1M7TIxV3z\nkEbC\r\n=hYf4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "semver.js", "gitHead": "c83c18cf84f9ccaea3431c929bb285fd168c01e4", "scripts": {"test": "tap", "preversion": "npm test", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "12.6.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^13.0.0-rc.18"}, "_npmOperationalInternal": {"tmp": "tmp/semver_5.7.1_1565627294887_0.7867378282056998", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "semver", "version": "7.0.0", "license": "ISC", "_id": "semver@7.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "5f3ca35761e47e05b206c6daff2cf814f0316b8e", "tarball": "https://registry.npmjs.org/semver/-/semver-7.0.0.tgz", "fileCount": 48, "integrity": "sha512-+GB6zVA9LWh6zovYQLALHwv5rb2PHGlJi3lfiqIHxR0uuwCgefcOJc59v9fv1w8GbStwxuuqqAjI9NMAOOgq1A==", "signatures": [{"sig": "MEQCIBDvxyP24ch9FyHnNtwHpAPU/nipoKwzHm0z6TmI7m23AiBnevnc/hnsy4t08g8gKjOmNqhSOX14fVMHguJS76+5Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9TnXCRA9TVsSAnZWagAAZJUP/RwScknE8TPmm+2oKnU4\n5vKc7OFGQgETATjNhJmYiEoU+yi6QXvxWeER0djTvSR1b1WQTT/ad4WeZ48g\nW1u0egl0ctUhnMXqlQPA4pB3fCSvebKKWfLQZp1ElHnppGNlY3BYy7MNSY2+\nuF5lO8KOhTlrowjswOpFu6nqKxAhEsLg4NSwliHZL1iU9z+ozE9+9X3yjKFC\no7J+dxbw3As0C9doVR0q6sO/6Q9SQqMRcX25kvNUVLMb0EEX6KPZJlkBjV0D\nXC7VE5q3a6IMGMWeDCDFC1WG94mrMLrUpltriqqVnuDUl/WUs5mdXCWxpa+R\nODinRVD95RA2a/BgXLUEMBBcaC1YAY0BRy51cPzJ4b5o12zb6tbyGUdyYv3H\nr7zAzSz/hHr6VrVJbxV7deo56s4NjnEgs8qRGykvILskY8CTUE1xz1LfvUoo\ny22jD/KbtxY3kc/DVhAw7tlIRr+RXVtMU4fYGyXFOaZMjIdlGFvQRRc5wAsd\nKHPyn2cl204xS84nYf0UZ2d1Nx7mTUv7BW7W6/9dhPrut4Yy23JYFQGaX6yU\nhpDwz1KocHZb/ayvLCuE6F4USPFb/7CvV6a+/yt/2ISZYbYtPF46J1B+hhkr\nEeSnaexXgatoFANnR5TLWH0SYmqcavKzmhxGnd63hy0qhLLetIIrDpDBSuAx\njyob\r\n=GXHR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "f56505b1c08856a7e6139f6ee5d4580f5f2feed8", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.0.0_1576352214659_0.7765955148506742", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "semver", "version": "7.1.0", "license": "ISC", "_id": "semver@7.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "e5870a302d6929a86a967c346e699e19154e38e0", "tarball": "https://registry.npmjs.org/semver/-/semver-7.1.0.tgz", "fileCount": 48, "integrity": "sha512-4P8Vc43MxQL6UKqSiEnf0jZNYx545R9W1HwXP6p65paPp86AUJiafZ8XG81hAbcldKMCUIbeykUTVYG19LB7Cw==", "signatures": [{"sig": "MEUCIDvPHSex7UmRErZwIDv5dS1IKKMWzcSKjJytT73ZNysoAiEA2vTj/LL/0Nr93fGGuw+nWZC8ocmvCQIEHYmT1bAmGtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+C9RCRA9TVsSAnZWagAA5rsP/1UdsGU+xP2QPmsjtY//\nzh/+x1qV+MND5YOtQNGx03Q4ar14i1VRbha5MEO7WscK7W4v4joTXSBkGj31\nNmHuWs1Q7gQ+rchl4/ONr0qrp1GnSxzXcKvQGWB9HFINfj2EaZveJCNSvXp/\nlb2USJdumtqsF3sChr+u2BpIvvB+5IXKFZIg3TS1CMrQ7CrxcxUVOPiNW64c\nmqZBiZfU6hLrbrBvRfqkUSI9KfXsXPkliSU2+uf1uPHuDC34WV1BcJ+GwIVY\nWK90dxSOf1HpJG79FINJ/jKA/X1gi+ThoDizLMDsUgeUca/OtgOoAhy+msF+\nsxR7vymNKnX37uCc/9We7fZLOKHepvtOBl/axDh2gQAwE+Mh1Iw5czQMQS9U\nthLMPQBRiGyR4KzYNo4Ayal1iT5yO56+9LK+6zESOGbkTj/WF7yO/7CnrX/1\nPEXsRdfEi9f0IbgkEvSpXMG1FvA0mGvubDqDkcAMxfAh4uaC0W8Hh7TMz6M2\nN9oc+iIOTp/UT2jZTitSzF6KXHIfAeuES+DMmGVx5moBJv8uTlI1qCSVorpr\nyK8xuJRRPxiuqDaGpD7QPObyjlko3jqRMfRc79I3U0tWr8fArgjSnyboQ6L8\nVPa4bTy+uQILoRJdZj6X7KyOB97TQK3hImNx0qlWmR7Mh9vf68BSHJsHNH0K\nOKhP\r\n=wceY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "e663d38c2d3f77bfe8c9cae9770c409aa434c713", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.1.0_1576546128722_0.7082040504653042", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "semver", "version": "7.1.1", "license": "ISC", "_id": "semver@7.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "29104598a197d6cbe4733eeecbe968f7b43a9667", "tarball": "https://registry.npmjs.org/semver/-/semver-7.1.1.tgz", "fileCount": 49, "integrity": "sha512-WfuG+fl6eh3eZ2qAf6goB7nhiCd7NPXhmyFxigB/TOkQyeLP8w8GsVehvtGNtnNmyboz4TgeK40B1Kbql/8c5A==", "signatures": [{"sig": "MEUCIQCA95Ah0ndMKjK9EL2hZugJFTFKp1otkKCLW8dNJZbTAQIgTXAeg74h3OuOhKcGhwBdNcxV4UNCgtFcVv2wtm3oS90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+Qi9CRA9TVsSAnZWagAA/eUP/0pL5O+0HEZjbnXjmYZk\ndz6kfVHV4zXnNSxcLhGxIzs7H8J7bzWCu+7AT84dDW1KbYryVxwxUI5Aha7Q\nDeYBRnpG3cCYpH6KBe4CattmWT9iTAo8CVO8bIAYPFRa9vwzYLh2Z9MS57Z9\nwXpk4ED6W36fZrnDcwUc5cOXAthFwc8Im+OFuBotzWPrDRyDZVjFquXzpZ9t\npMadH3wVwUXEz0StuXYUSQWgYTnuA16KLSBXZWm8ofqYPMNpRTNj6+CJWLyd\najqKGhdpF+TMrFBedd/N1x0EMOUyPCYtSyIDehOy8xz16ZEFVDUOxx/Uub/E\ngvNV0qWvzcBc7rCJh0ofa/ZmRJuHY7ClvBTMRBKOAYNBYABxCncmGgvc9zD6\n+omakN7gggfjD0IThWkDi+x6uUVU38kKUnXjcfrVK3bL8HiSQdciuKHMGDAc\n/eG+4emkuA1muphWQzN7T5y1U2DIHIvXSwpWoSrx1F1jlBm+9Pg1RMlsklpC\nnBeCO6KnzIrr6FkWU7P3etUE/Er3a7708fZ294S0QhgRtXPEvINbsMuxMoLW\no5uzB2vpuUtWVW7oDaDHFOFMDrxej2IFTzT9XLFo7mSlHsUefOXE3rGr8zcD\nr3MTn7IA97ArS9PvNPurCbWp0XKFGbLBpmAT87Jx2xw6ABI3g4s1ErPxOKOz\n06ED\r\n=707S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "bb36c98d71d5760d730abba71c68bc324035dd36", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.1.1_1576601788850_0.008654434026552194", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "semver", "version": "7.1.2", "license": "ISC", "_id": "semver@7.1.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "847bae5bce68c5d08889824f02667199b70e3d87", "tarball": "https://registry.npmjs.org/semver/-/semver-7.1.2.tgz", "fileCount": 49, "integrity": "sha512-BJs9T/H8sEVHbeigqzIEo57Iu/3DG6c4QoqTfbQB3BPA4zgzAomh/Fk9E7QtjWQ8mx2dgA9YCfSF4y9k9bHNpQ==", "signatures": [{"sig": "MEQCIFOZ/pYtIP/WyniDl97d31/Vl7kT+RI2FZmtTzINpx+4AiAbu69om1MI2Ltw+vmLLYmnGIbP6AtmDQ5aCskKPhnHwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeM4MJCRA9TVsSAnZWagAARlAP/2M5Ze5j/Tg/QTsPtBNQ\n7nlYq4d6mokFesaKOOWUqZ20P48FvySgUyMIEMhocI6JT2ctYnD+WgZ1Ht8C\ncUXUdg7dRf4opzaGaebYmrmco2b9FRP4n3EfaZ4WTLw+LvzVcxKAUuQ17c1m\nooLmdj0EMtSVn6frFhO7TDUtGCbmpatUa1GQfUThnuY1HWrYnS50jCM6x4cs\n36Xc3pqRcrUSu61QjYw0/l6kxrghxkrc9kRIFGmERplqDBu32NHeHplY2QV6\npuaqmG7TvjNq3hKHKlmzh/GMeWKOJtHCDpem0J255DDSEoup750DN4LbX2Fq\n8Eq/5a5epC0a/fq7TZqwKdPi1583QaW6kAHxzurhCrV/gHqdjhnmMB+gqZs5\n9LtgL0xUte/DLvtXKZuUX+FBD73zdjurEAzLqybhPMNLJzuXjNWP+msH1C8f\nb04fcc2eb5/Nw1dASX02atYXv4X+H9G9FX1l4JxzW8qpsfSYML2tL8NPWHyR\nSGhhMb50Bjqqf5fHc0hmOt4FtJQ4m82RnbE6vspXdmDwlNjoy+VbJpFDBPqj\nY3BSDv3A171OvE9HDylT+z3K5d+53sjhCyK//XaJdhmIFb6gJawFPc/CqwEu\ngp5jSnPQ/hfs/sfyvZ8dLpDxB9M+8ON4OpIlnkTpzrD+v6/N8/N+mOKEmgy+\nLod2\r\n=sWdq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "8f4d96d7816c296d311eef101588a3809170ea2b", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "14.0.0-pre", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.1.2_1580434184945_0.2853223384213779", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "semver", "version": "7.1.3", "license": "ISC", "_id": "semver@7.1.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "e4345ce73071c53f336445cfc19efb1c311df2a6", "tarball": "https://registry.npmjs.org/semver/-/semver-7.1.3.tgz", "fileCount": 49, "integrity": "sha512-ekM0zfiA9SCBlsKa2X1hxyxiI4L3B6EbVJkkdgQXnSEEaHlGdvyodMruTiulSRWMMB4NeIuYNMC9rTKTz97GxA==", "signatures": [{"sig": "MEYCIQC7w4pmARfVvQwW0X0859TIMhaHkWbtI73ZyIq3dTZj/gIhAN0mypHx/lpZV0XAsGpkYeoXqMdiiWptEl2IXBM6nef3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQyJ9CRA9TVsSAnZWagAAJHUP/09jA/fHrBZb8HLVTnTD\nuvPri2IuUTLWk5k+90yZrhzPXpO+0qpMDqBqx0x6uWIPj2z04Au2azu7RmOF\nNu99X56Zyt54IM0mHxkaLzRC8OnEbRV9nDESu7QfU7/5J/qK7gt6JyaIpW5Q\nMTc7GAQye1JaTyCMIrdh0X4/XsUs/UBcv6XFTry8eEbnHI2HUphWQg8i8YAj\nhTKJcbwGTWkeCPCBk6zpukwOtgGpfQUBXryQRQ4PCTIo1rvFpLDz1NHbmtQP\nTK4QYTK5pmKBad355PIU5gm+8RnAoMW6oSPfrL1KQeeQqnfGC6UwUeTIdQbc\nuqOiiAxi7roY1uQwSOc3ZL/aMAY2Bb4PpmrhgE+1m/fiUugconPEv+NvLvD3\nxjJrmoxSzyjXJzXlcNkq6niff5T2HEHLueZ86PZ5JhF1PIMjQ+dshjfpSc0r\nQj0imDPAoBwRzL8RNr5Od4f2PFJxNL2UBnszGS15KQnhiNv1VJDVOgBohKub\nQmLfrxC7rb7Ij867aYf4LX/xEpeSpfKEYGfX0o6Sl8JRDMJubMPHTTRnz9aH\nGvbsBJDIE2v1UwF6++cjV7mr80iXWSDK1b1if0VoCsEds1V4cvSmqRcveS11\nMzcsw/3AAAIlb9rzKzqosFfzgQCCeDVM7hphgOzWtxtXVRB6aAsNtJhJtL1a\nJH9h\r\n=CH+b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6e7982f23a0f2a378dad80de6a9acb435154e652", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.7.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.1.3_1581458045178_0.7603358869317778", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "semver", "version": "7.2.0", "license": "ISC", "_id": "semver@7.2.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "e4c8e6ca40b6b953175194ae89b628afdc0a20e1", "tarball": "https://registry.npmjs.org/semver/-/semver-7.2.0.tgz", "fileCount": 51, "integrity": "sha512-VOQxUCRgttu7mxuvAgselSlok1XoOXju6XSJdTBo8+8RsvnPwKXEZtZVKvzlNigT1mXH2fDupcT8oX8Vw1Vm2w==", "signatures": [{"sig": "MEUCIHQ7kDsnr3kWN/hAQBPyT9uXriThN9VIIL7OIAYZNgjLAiEAx2nPnV9PF/oJ0gJC83acCeFQuFyFkT0iMEw7eBEoig8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei7zqCRA9TVsSAnZWagAAkgQP/1tsaxpjTJitNXMYHEp9\nkr4C+ErLeUeark58A6GrXi/db+XyjT1ukLbTqJuiMYXyWQHby6LGbIuatjmm\nIipjH1DsWutZGCgglTzhLWTKXv1XWbJ4ya/iMgq7EsanwQUpSOearQZRFhok\nwCi874fWVsYG6Q0DlJBZftGZ7gSJkOG6gdhrim6QZogWJBxfwju+iNzN96KR\ns7KW5raelbcTkRKUTBnkK3JY0/PZSG2iysn11TfSggOL+oacbsKPL1vcxy/X\nV/3urAIJMsfVvktFdbTNYw6BrpL0rBgrje9VVJVd6jki75cNV3Lut7Qk1coi\ngpJ0xe2QyVXWdlWGfgbEVG4Hxm6YQuviRtIr1xZ+W2CESin5i5jcsTBiqpKd\n32jf6lQJNdYglKLAJFfw4oUFl1YHh2PzWEfICUtNsnZ0u1Pc/Hy4jnSY+ev0\n24jQ6iKDqhX5Ggrg8kisIq5Gvg1yR/DPEv+tr5oraVar0EI4wMvMeyLcg4MP\nxhcSKEpCxWVsBi9Q3Ap7vYcs3BlJULRTR+B/GoA3LP2xgd+Q6XRmX512nJyj\nPNQnvbnpMTVTElXjfl1SDfNkknA6j3TrgBjVLl6tSIjESYuhYWg7iBjcdZTj\nczhZ2dWUiT2vXXUj1jKJGqoPpmYEHJr1uhyPsjId/G+So0BsmF+KZDxWG8pf\nzzdu\r\n=x9XN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "c6581a8b6bf6dac430a30eb6be60ed0e06c22f74", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.2.0_1586216169599_0.0732386597934036", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "semver", "version": "7.2.1", "license": "ISC", "_id": "semver@7.2.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "d997aa36bdbb00b501ae4ac4c7d17e9f7a587ae5", "tarball": "https://registry.npmjs.org/semver/-/semver-7.2.1.tgz", "fileCount": 50, "integrity": "sha512-aHhm1pD02jXXkyIpq25qBZjr3CQgg8KST8uX0OWXch3xE6jw+1bfbWnCjzMwojsTquroUmKFHNzU6x26mEiRxw==", "signatures": [{"sig": "MEUCIHTdHjXuzTNIyaxxhMyHpZPFvVl4Tgom+vL+uHBsz5e2AiEAx0q1gpiJooh+oa50pb7EDX/Q5tbDMhpXpktMVqQH4BQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei71ICRA9TVsSAnZWagAAnT4QAKEguwNc3dASBfx2pBs3\nrQdvnu4WDjg0U8e9qdbq2/+xPAo5XgT21GYdn026CY62HdSYm8IipU3klW27\nkua+d650povuPCqFBzCrA8zD+kpkayZF6B2LrZzOYU+UzPnWQX5bcIFhuQyo\n2OimQf3jUpW5fL4roexFXeqBrgHbky1uTEBZOC92DCJ0Bd8Oi7N+6lw7Dta/\nmuPONUAICBWkumcMfyw4FCquKyb1oIgZAcTmJTgOwlTwhqZ13JItMBUll/yI\ngLnNP06f1J8fYVLO2aDd4qZQx2WrZqUr9CfhY3aRDnehmNdt6igXUAvZ/ZoL\nYqqDFQjJBNB0aAH3lgEDlqEsLd+VMGMbiPUddCtu1tgI8m8yAeFZn51S6SIQ\nTo5DEcr9+WwFgA1kQSJVmfhhc0BcedBFJn8GyGpSSodUlz/bgDg+aBfcBsdN\nywz8M4UBf01KYLih2lVaOkUhKSskc5kxTAgpbKhsR+5t2pu/vS/KNCYIL9T3\n7gEgqyjRPKguebpyziVQXxfvMGpoUUlFWkg+vziptQz0zdxeGJW05ivFxalV\nJ8bR5BWDfWea3sE3/geI+UHPwOivcOIyHmIo/JvkrtJtHKxEGCKPV2E3oSM3\nTcYAoqxKVY9B77gTYCgQV5zJhmfMgmhw4BVUNwpUVtyr+XyNQkw+2hQPu8Bu\n+BbB\r\n=4m2a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "dfe658fd611ccbf6703b1c9315f9ad8cb29db1bb", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.2.1_1586216264072_0.03363624901937112", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "semver", "version": "7.2.2", "license": "ISC", "_id": "semver@7.2.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "d01432d74ed3010a20ffaf909d63a691520521cd", "tarball": "https://registry.npmjs.org/semver/-/semver-7.2.2.tgz", "fileCount": 50, "integrity": "sha512-Zo84u6o2PebMSK3zjJ6Zp5wi8VnQZnEaCP13Ul/lt1ANsLACxnJxq4EEm1PY94/por1Hm9+7xpIswdS5AkieMA==", "signatures": [{"sig": "MEUCIQCc2l8m+YYhZrF08JX4wheeary/AtFXQeY0z4Y6Jpq11QIgXumuUx8Rd3eXd9FAO/lfN5XdeyLPwpok9JSa8eqZTvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekJhNCRA9TVsSAnZWagAAQEMQAIvvL5GmSpQXuOj/ZEo1\n0trlO4l6UcdbmQwoMA6t1HeYv0jPug9WV11eh7SdlO+Rn7U49sMIV+Lpt+B9\neY/Z7tpyaXq4W1t3a3EOwux/oa8YfzN7hfhDa+QHjpc4O7JAKJWw1q8Fz3Wm\nwRYh/z75Nc2g2XxIxEXlh4qCJLHj0vXd0RDDx0/2uMV3zp47FTyFjCHHufVs\nYnRO8osqSbIouI/TGti1dEgnVsUjhzpv8l4/STCnzASLBVLJGceb8Bd4/zNO\nqN7V5pkbdbls6+42RIEYNuVxmVfUxcrRCEClBzYpJDtAqxq5aChmOvNHacTh\nM9M2v5ZZJib4PO4UL/6knArR+AtlMTsecfHfWkSOlFyDpvcBtp6ZnfVp0M9r\n2OeRekjqX+mDT/WmRjoFFV0wIUm2VMfMFguPZLhXUiLUPgHMJYIbtJIleaCq\nBKGlFrtqVUSblogdVGCKxVcRojBDjdVw0/6hh99yz7E5djkv6dmH5LfA1Scw\ne9TFoF4YQETlwpP1bJOQpABHvUybJTAi0hHsn6HITYSmaIIN3XZHQ0tgkUhg\nee5C6/O45EmON3sIk6MrDwMolDMa5RTizhOdGRma1dtSwT5VdVhXRmGSDsdP\n74SK1h7OtKrPWGTo3buE+wsgHlW10TpYvljGfCP7W5n6TfOFwPdk1+dTHdPs\ncIpo\r\n=tKmI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "5d0dcdac5daeef368b73b9b67d1aa6f554315e2b", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.2.2_1586534476820_0.045995279100802255", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "semver", "version": "7.2.3", "license": "ISC", "_id": "semver@7.2.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "3641217233c6382173c76bf2c7ecd1e1c16b0d8a", "tarball": "https://registry.npmjs.org/semver/-/semver-7.2.3.tgz", "fileCount": 50, "integrity": "sha512-utbW9Z7ZxVvwiIWkdOMLOR9G/NFXh2aRucghkVrEMJWuC++r3lCkBC3LwqBinyHzGMAJxY5tn6VakZGHObq5ig==", "signatures": [{"sig": "MEUCIQDgpCZB5ru0xKUMKXCKmIX1HYZcBm7+uEA9ow9YZMGiRwIgHYFsLEf8YG4kphdXD61IChUR5hsfuuYykH1Gsb3FIuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelLAFCRA9TVsSAnZWagAAZ/EQAJ5yOX6QZ4KUutwEUcXK\nhl9apWf8EO227V7Mfc7k1oTLGVwvhdd0uwRCKPCjd21g/hFfx9/2ozynyhVt\nVTDOZNztBzlNeSwHu/XtVFrfE0vJkpNt7/bNRnCk0jKmMsJyjAEWSrdHazPm\na9qmFSFMWVMjzr5J/lqRmzSwoRBtaKRnK0Kq5BaWY74/MaMnVZ/GdWdimgF+\nwNaqtzJIgszdSM25+dfzmQsVzA9cTA+nhH9Sa7kl9QtxZnyn+xIsa5nsfkDt\nEp9GQQTEg2bLWBFBODXnMo/k3N+CARYgvcvrnp4df3dGq/0D8ZJbr2/ESSFV\nWGVM+j9ANfWi67vfjsDaQj11pylZiYTCeRaTSbiVtxN4R4N0jn2qfB4Rx7FJ\nc8IxzROV3c+gJkWr6PCGFDDvz0MahU0wJS7Pt7UiPSyy1huiQ3Tqs0aO/4uK\n7o5Yqsu3wz4/CuC2c4j2P7iAg6a6Cp05FJAG6lk1eO+FTVhLGqANNL4HPvOO\nvxrjAZWPHx6YxHHm+ZYZsBmsCevi71VlY+/hGzhAtJhZpUvWQrE+XCikqpLl\nHbfJBXfGL0GQFDhqwjPrgTd+1S3lV1Wig0za3VYfPHA2MJUW14h6CV3RShsl\nfluLkF+Cu1yRwzJekMBualtEj6XW+vto3XE8vZZiOLwHPBdO6bo3P5+CsGSy\noXNX\r\n=Bzl9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "45b14954eac049a1d2824fb5543753e53192216a", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.2.3_1586802692959_0.21135831995159804", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "semver", "version": "7.3.0", "license": "ISC", "_id": "semver@7.3.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "91f7c70ec944a63e5dc7a74cde2da375d8e0853c", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.0.tgz", "fileCount": 51, "integrity": "sha512-uyvgU/igkrMgNHwLgXvlpD9jEADbJhB0+JXSywoO47JgJ6c16iau9F9cjtc/E5o0PoqRYTiTIAPRKaYe84z6eQ==", "signatures": [{"sig": "MEQCIBNp1mWs5ZRFRo8KyB5/LShU7Br5UThIsLHopJQ3fOIuAiAjF/Ac2Zg2pSZfPaJ+SkogIc8CjNusHZEEoByUede9/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelQ0dCRA9TVsSAnZWagAAFssP/24rlTDHxzHVxbCLJ5Ll\nienCuGoUb/leXiA6nE9xZdYUxytvxt1zgbI6PMoQRQC05p3UXhHzplPX4z7O\nVhe7AI5SpLhasSo1EkQT0eiTYT8wOnZ3oU4IK892GXjhNW2rETKu/KzyEfOD\nuOjUYaYnyOwkypbpuDd/W2Z1lThf44bv76GUVHSOcBs3FxrDzPyK/FhtRary\nYrk7cbEv0mowLsyl44Ae9JnIac/jvJWSoOoE7q5vUS3JGE66f58r1ENSVhOq\naWzh934D9lCHpBvYkVETX6j1texIyAKUaMViSLmht/iDQ7v56usxAAt/pCmb\nC24lvfgV64ZZNfxELFblT5hfyhfID+f3VuDnXty87NfQmLUMEjSXCqgDLXaH\nbkxhESvdt9wLOA4HSicDszVv99H3EYaJbqm+onljsrWyjbSEt/alOpZ9Fiew\n4GUk125mfmIHkIlTNVCJ18SWjkP7fWvMWKzZIuzLWOsjYAL3z2xLhiZ0HlNP\nW8Wx2rNexrGgN2jH3Iij2TxknpvmIg+UmpIL/MLrJKuWibSzi1+An98yiwDv\nK4iYrSBxQ1ocucQNta/CqKRLz9auN3cRQ0+yB25T8uLY/7Wm7bP1VHCVkoYe\nDGgkm3CVuOY7oEoN1BqtN18eK8eicv+DQayk/evMMxpZIeTF3DtJydnbb59b\nWHnP\r\n=uqCJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "92bccf1d0950c9bd136f58886036e8c1921cd9a1", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.0_1586826524726_0.19048944441720184", "host": "s3://npm-registry-packages"}}, "7.3.1": {"name": "semver", "version": "7.3.1", "license": "ISC", "_id": "semver@7.3.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "8fbf3298570e73df7733f1b89e7f08e653cf1b8f", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.1.tgz", "fileCount": 51, "integrity": "sha512-r7sBEClKDfDFhPQfIk2D+EF74SS1j4uyfto50/KWQRzQj8IeKD7wqLbwkqb33289rtqQZoKzID5WOavkJ63DGg==", "signatures": [{"sig": "MEUCIBhdKV8Jz2tgF/6GEuCW85wdq6rBsW2oQvax6QFNjFl+AiEA7Rx9868fQRQEKBxu6shNk9qbmyXezIRXMHTMJyhTELA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelesoCRA9TVsSAnZWagAAQigQAJmWwyqPvF5L7PEFcW4A\nfHRyQw0U2G6QLI1/+hSGV39zq4D3Vs/6x1PoApXeJ49JZM8/43JXM7xYF5Eu\nto4/2s2hiO4l2aGsFHk4Le5iC9Plo3jp4TXceXmyanNzIoZT6Co51DPxqzDs\nA45TxTBFP2RMRhjPCHlDp20vEv2HZGB9TF8kt0S/JXG5tYkroL6L0HgyGSzf\noYz5xE6P7IznN7yMgnW8Lylcs3FzVs3JiG0Dg04kbsClyq4woFkvWnrtgW70\nbEUw7q3TBvimhojKTUvsHwt2uapf+mKn82bAClwDE3AAKwxSjrRsswnI/0VK\ngDVPTMF4X+5FtO6dRx7jb4r+YRn1pRh8rvWqsgzMavMxiHxay6IeBx6HzsDk\nkIdMNzUTyIz+YXo9fWFHOMToOOyBdXG1WcWiw9+4tV3aOn6YqA/z0zCvzppD\nl2mOJwlMethFeowMrb3gCX3iIkGpdtAwuQpGwHt5XHOGny6PxSZ3ywCRfqim\n5fydPf/ExWCVf8mYclhTugJgazn8YOIIB5vvFohdMQp4Yt5WuWWglOfKQluD\n2mYPQCGwRIzRLpYOckgxxLH7pKtO7uHv3kqrDYcYc86hldEBijEIyplkChL8\nnBHEgoP3Dg1dG+QaOqP/TIFRoP8BstX8x4KKpivdqsDb8IySPTIFfkcGPOc2\nOwy0\r\n=HZ2R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b97044b0de1a771bff151c40695fd7e340b3a09c", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.1_1586883367892_0.4603661322871977", "host": "s3://npm-registry-packages"}}, "7.3.2": {"name": "semver", "version": "7.3.2", "license": "ISC", "_id": "semver@7.3.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "604962b052b81ed0786aae84389ffba70ffd3938", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.2.tgz", "fileCount": 51, "integrity": "sha512-<PERSON><PERSON><PERSON>32TeeambH6UrhtShmF7CRDqhL6/5XpPNp2DuRH6+9QLw/orhp72j87v8Qa1ScDkvrrBNpZcDejAirJmfXQ==", "signatures": [{"sig": "MEQCIHnewBW56miRC9SeBHOhoCYEzwX6/+r3iwe0KsAU7S+WAiBf8qd/vn7yQd4MibBC7VN/mvAnk25lk0NrZ4ehjSVunA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelfZACRA9TVsSAnZWagAAgg0P/A2mGuEyc/dX9rlX3CBL\nHtzCIbwuHCUIkzdA4DOKjNRswKzPtXo9UeqqUGkXY7McRhTuFSjoXVdbTf7M\nMKm4Qb9HG3664+TvZ8wvtY9mXWfgC+V4FsBhiCm5r86ZsLvYVwaDg55ClYBT\nc5EyXV5F6/OhI7PUw+wY0B7X2A2lWcx8j1aTYG3Nd4GLAcJ58NY8Y0SeA1/w\nRkdyJr9g5G0/U0VcufUJBSEVqikg56C3BzrPRMhmTyNqYShCozhO5NnNlLfS\nt3n1n+7isMPnKb30UAJaeUAQdICqm8J9x8FgpiIv4wcvZb8qEZwXbII9TaJu\nJaHdeGUmfKALrZA3ArBJrm0OCajsNJspR+tGiZPGCLSMiZJrfnkF1dP1KcVO\naKlkHvFIglW2zvNeuclem+uiuB/0dONR9erUECgf75tY9kkORiEoT0lwptTF\nyXSs/XSW+/GQdpi2D6e4Q3sePbuWtPDdzlTYLWad7s5yn8bVrXvafljeFiKU\nIY1kjJgcuLFexHNPTVY68qZK+lK2l3/bbMnLvW9a0UNu/RFwI29/yEeOgfFW\nl9My7e5WwJSp6u8U+L5e2O1lD9k7Yq2CFsUy8Nztf6QURp0IAOd5KOQeKrEZ\nCIf+MlJXvR0/X6IwqYEGcJKcEQpZi59uwVP9KOWPmNSwk3q2QoM+pc9yjB57\n4PLX\r\n=NXIW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "ce978f9a58b71d22a7c303432c9a5135510e01be", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.2_1586886208300_0.6137271223938625", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "semver", "version": "7.3.3", "license": "ISC", "_id": "semver@7.3.3", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "a6cef9257e40f4b8a78cd97e106d215f8a3ebdc9", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.3.tgz", "fileCount": 52, "integrity": "sha512-kBGrn+sE2tyi6f4c9aFrrYRSTTF5yNOEVRBCdpcgykFp3jt2ZGlBwzIwWER9J9HZnQa9IF1TrR8Xy2UU+eaUhQ==", "signatures": [{"sig": "MEQCIBlfTCqRYKNgS0pDZN5BvL5AuAxsHyHywEZYXqhzQhapAiBX61XAkWLIU2IoB9d9+WWNh5hMePb3CVFo/obuoYS99g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxpm+CRA9TVsSAnZWagAAju4P/1m7tcQnyyzmUjycgHno\nH8RYkEm0DZEePHf/altpa1kpRF94QfVXOep3dJiz3zH9FHIjsoEDQWMI1bkW\nzI9z7ItWeepg8CnIZxr1aQJqPy8S4sKV47SgC+GinOIVPdqsJCIrLhD9ADWf\n5i/o10jLDbSW4SSI78YK9n4H0RHqQCZFcbT1Ir9iQorh/J/9ecip1Ut8oZSx\nuCoK9S+jvLq0XWi5kN6y5MUDzE41QetLNnWVuIbYAOvW8PPzv5HBX1+KWEra\nQ12eNdZ+1kQij+RSlUXvsBsRtaNnZoOO8Waxy2tWFgy9132OKmbd1jDEOPqG\nk2oxbtiu1b0n5NUYIiu8AaH6NW9FloyGcqzOH3zsI6LKiJ1N3NknVptZLVAA\n8MXYzhKELw4uLCCRTmMxtRIqRYm9H9Fvoe3mFLbOIvAorO4DtJKXV3p8fNt3\nkYXlpo8mJaccV6qZIXP/bTO5BgfDt5HeotwMLAOu0PtsaiuxWj+Yr1yadU9e\npN6/t/ijt3zcNEo74HMlD4Ld3UhB4xzb07iO04t4ceCZ0+lokTDPTDi87Agc\nWERccemir9QGPKlTHFwwJGFfAJjdxt3SK5jKHs6uVqhJb8WqD4VCoKt9oV19\nyuepdm3SttmxicdLWNovStoEtnflbyxz680VL6GHEJjNSexWAbh/7b+SZd64\nJvRe\r\n=zBtA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "984a8d5f2d403f90ca95c201e9ba061ac96ca3fc", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "7.0.12", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {"lru-cache": "^4.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.3_1606851005696_0.7698350137108039", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "semver", "version": "7.3.4", "license": "ISC", "_id": "semver@7.3.4", "maintainers": [{"name": "nlf", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "27aaa7d2e4ca76452f98d3add093a72c943edc97", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.4.tgz", "fileCount": 52, "integrity": "sha512-tCfb2WLjqFAtXn4KEdxIhalnRtoKFN7nAwj0B3ZXCbQloV2tq5eDbcTmT68JJD3nRJq24/XgxtQKFIpQdtvmVw==", "signatures": [{"sig": "MEUCID4Jfadv6z7dqWfVoD9MIrlDwq5jhDi6kcIkglVaLl3wAiEA8+UmZhF6VeSX3ggfjodp0os/2nEGMzMzl1yKCS0LZqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxqRSCRA9TVsSAnZWagAAnUoP/AptW1ht7QRFkAT38G04\nCNgOEblIfwYRC+HOb1lIrlCXqh3EVko/6Jadb6fk8/2NmZ93PKSp/al+zkBH\niFSYI4uzNwZG09F4M5t76u/FStTuAA8TcIfkht/E5FEPw5G6xN5uRZhIYJ2r\nIatDpWRkR8o5kQ/kMHak2FBHGlHoIbxk96a3jb3vBYjrhLjyR4M4DD/xM+sH\noomBXAD/sUVt48vEwHrhPmKlgUH4n2UZe2QbKhva/gwEBtQ8LHeyzJG9sjg5\ngA24U8+LEnul56FDOwJHjMIIUPhr1u8Vs9d72edxOCIvvoUqrpDDToA6uB4O\nacF7PYoyumroIih4OuYVq7rE1XHcIaa82+ua5Xx4KbklWhN5Zp6UbKKSi1LN\nBI9vd63w+GwVFrEDZulPIZ+Qx52k4SvMmQS0y/4y1/6poBjfxQX043/Gj94h\nQNH1d1H3mrceoxghnaXelRnoRj8FjTLkNBOyJDF7re76vahpwK+lTjVNFeEz\nQRRegAh8ODac+HXIxdAQ0VHUXT+CHuPWli36p1Z/ABF1eRlAoDoxeDaVjjkt\nibd1A7wVBlT0riHEoejjwUW8LwfO+6MQyQpPF16Xl9MpiFlfarE/AxbPuBdU\nlNJQ+SdrH84GylNnj5UnJN2aeDKBtWRNKizW2uMHWPOlkTsgzZ+yBTc59O5E\nvYcV\r\n=8dWH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "093b40f8a7cb67946527b739fe8f8974c888e2a0", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "7.0.12", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.4_1606853713862_0.733630657601954", "host": "s3://npm-registry-packages"}}, "7.3.5": {"name": "semver", "version": "7.3.5", "license": "ISC", "_id": "semver@7.3.5", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "0b621c879348d8998e4b0e4be94b3f12e6018ef7", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.5.tgz", "fileCount": 52, "integrity": "sha512-PoeGJYh8HK4BTO/a9Tf6ZG3veo/A7ZVsYrSA6J8ny9nb3B1VrpkuN+z9OE5wfE5p6H4LchYZsegiQgbJD94ZFQ==", "signatures": [{"sig": "MEQCICf1SCGs/6eJ/YJWwFg5D9R304s2M8H1dJUw4qylFVfvAiAwlCGfYUtl6WoRINHJAsXpttA/BrqOeTmPFzsTPvIrwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88244, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWUZxCRA9TVsSAnZWagAAKCcP/2yi0PxM+y/lOwz+p5MM\nin3w++rvLrBbsPGqHexm1BeOdxJc3lieiblulisCazfy2r/e/3LAlO5f12UL\n2Ed0VLBTGRAVg69uSAv6xwXVhHNUFIg5HPe6/+czbnW2hAL5Nzp6K+YlVVg0\nzAZYoqGzmOLrS6r8+G6Au2jCv+BkmsWfTGkGK8QG9J6GUEBwNOInThEYY+JL\noRpdx0PoWXUNXBQVHmiNB1SvZEnVhorERdQpk4rLn37LrhbcphIvhd6exBwm\n7TqxrwZdXYkQfTwSGdiYofidzDX8G3J7V8aby/PN7gmAarXegpmdJK+npmFS\nzQJnp1KpnB3cR/5kfD6F6MTNlBO8lcYPO4eh48gnTU44BAKww3zsToJw3GCH\ny7H51qKSeVpUgdM2BBhBJZs+J6OUXJfU22hcHxIDGU5E7NQJioDsLUZFHRQV\neP0yo+9L4Ct/N0YH7iPUpyWinyd2zjhLb8QNSC/lUkf+zcvuHUBY+DGu8QTu\nqwgp1WDRd/ypDt2s5LPgClDmL0cB+pkHnZMGWFRJoOQYf0JwDpWIqKP6mNXq\n6bEzgdZ9OHU76PHX55QlF3eyKD7N5DAHbW2/cErzzmDXKkeoDTne83hMIqfF\nXv6Q9Z5U9fiF39gsoZR26owNQ0qlzaDbjlN8hGpNq/fwjzTFafu7QJ13ha6q\n3kTc\r\n=2ZLt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "e79ac3a450e8bb504e78b8159e3efc70895699b8", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "7.6.3", "description": "The semantic version parser used by npm.", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.7"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.5_1616463472660_0.6244304507667418", "host": "s3://npm-registry-packages"}}, "7.3.6": {"name": "semver", "version": "7.3.6", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.3.6", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "5d73886fb9c0c6602e79440b97165c29581cbb2b", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.6.tgz", "fileCount": 51, "integrity": "sha512-HZWqcgwLsjaX1HBD31msI/rXktuIhS+lWvdE4kN9z+8IVT4Itc7vqU2WvYsyD6/sjYCt4dEKH/m1M3dwI9CC5w==", "signatures": [{"sig": "MEYCIQC0mR8aBDXEQrqMXJH4NgCScNwpLCzGX/rNgmqCdkAPaQIhAPmriqoVLJxT75raIgPjQJPeJKr4CUaw/8apB7slN30t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTcFNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqASBAAi5vCBGxg2XXI1DTgenEXg7yCvhft4Q5rBo+hND1ZkAcZGksQ\r\nlejC6/ndxYxjDNQbc/D3f/xUonG0WpyLTTPPNCe9+jP1svbx8YT76Z8lybbi\r\nFICSyZfiF2m16xJ1YdCrUTHyomM9mQtepcOwc+xpT2MOg+zm3fhpTG5cBNbR\r\n2K4pyrSxbx2o+zCtPvhznUIlrY4o+bZj62RC87wd2wqH8AVjP1HE6o+7excO\r\njnyWNbR01uH2lj5hJMyvzXKdOq61RnTlieYgaMiDErRTJPduf4BfiWgMR+tb\r\n6IpIOV8A2JFb5fYY9hwc71Fu/vT5Y7hJ6Pr30kmcy5i7KMT7nWa+sHy9D4Ei\r\nIkA4guz430p2EP3LTQ4rInoPn9ZujFmYfPQ5ZSGsYMK78U/J9MONp6u2fdBw\r\nNoL02nX/4TwJRkZhdtqZG92Xu6wAQu2rSMrlPUxNmCggjFH3GSzAHEIWEtMf\r\nSK7nOuVznf5g5q+19BF5JS1dd7fiy/SvLXsUzm8T3z2WHdedSOTntBTM/VsZ\r\n0uqsNqNIgwaCmDhRqOi2lHykMQBeYNu/OnBnAJ9m8l+8Q9n3h/zOwbDG2HBl\r\nnqsTjC59QCqcseSLWM/+rmoeqqyeDMBNpspVmRA4HmeZPe7DRiqRQprppofr\r\nCgI4xBKoPT52KbkdAaL1P1aLFiqHsS+7qSM=\r\n=f01D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": "^10.0.0 || ^12.0.0 || ^14.0.0 || >=16.0.0"}, "gitHead": "1ea0fe261851fed16e507410c08b40a2b91a1d1e", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"version": "3.2.2", "distPaths": ["bin/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "dependencies": {"lru-cache": "^7.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.6_1649262925411_0.9717734085396461", "host": "s3://npm-registry-packages"}}, "7.3.7": {"name": "semver", "version": "7.3.7", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.3.7", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "12c5b649afdbf9049707796e22a4028814ce523f", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.7.tgz", "fileCount": 51, "integrity": "sha512-QlYTucUYOews+WeEujDoEGziz4K6c47V/Bd+LjSSYcA94p+DmINdf7ncaUinThfvZyu13lN9OY1XDxt8C0Tw0g==", "signatures": [{"sig": "MEQCIHK6xa/t9j6mwcOiHg8i6GpUlmBq/dtzLAcOUroqDrJUAiAtwDesc7AH66h7RJNH9HDYjRkSlTsVGEKiab5bnN66Ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVbZBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1yQ//eGzrJTVQAT1CMLLJGLDs5Y/T83535qrINHJ5JW8PiKq9WPHM\r\nbe8Ti8L45Onw8CdAyElaKJB3bufCebGKy+qFm8vU5VGKiwe4UNSuhVK2QZFz\r\nFxb1a7NT/nvXxCtRtAeXm2BSkIhdawMLbATGp6Ljc1TTWTomqrYndyCVspSg\r\nVqTBgHvU0szXZlNTmtekPO1X+i/dRqOAblLBxKmbGsF77B5bxRpPfTgt/fj6\r\n7ol0+r1C0NCw5NOkkKGlcEVUFhO+Z3Hv4KhIYqfRuDarPYxDJi9XMZ2hvqmB\r\nNb2N3WtaAF2WK7rUxp+er0tUM0kfvNL4EuZDb2zLA7tBKj5CLdpUeb/yF3JJ\r\n+qikPaXrj2lROE4dqqXEvcEYDe0JDZ6NyL4c6W6FHSzelzsRhr3xA8SkrLqA\r\nYQs+YYDQr6/upSg6swHT+8RG/AfkmmhkP+MrSj3LDtwlb+h2tOQxzUHVKKLg\r\n2d/arVjZzOThKLyaLUZk+JPNb/jUR9NiQ/tbJZaRS7T/VkBNPYXQ42Pql5eU\r\njxfskGPv5ngu2VPWZuFCQ2FsMlAgxMTXptKKk0qU4/cjGiMlJR+dIffvvsF0\r\n6HRZOKE3ist+/Kr7E1kDOj15pmE6vPHMVX3p85Hm3FNuk8P/GM2wkfGzwMf1\r\nyB9kCZ+JWBGgLbbksXucyAkiz/hiLkftfWQ=\r\n=wgB4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "7a2d69c294571bb71c53ccf8104edb3937bc28b2", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "version": "3.3.2", "distPaths": ["bin/", "classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "3.3.2", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.7_1649784384799_0.18837178049597436", "host": "s3://npm-registry-packages"}}, "7.3.8": {"name": "semver", "version": "7.3.8", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.3.8", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "07a78feafb3f7b32347d725e33de7e2a2df67798", "tarball": "https://registry.npmjs.org/semver/-/semver-7.3.8.tgz", "fileCount": 51, "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "signatures": [{"sig": "MEYCIQCfMbIqJguiHPwmmbY/zHraBPuMAOlCG7rCwn8kvfM1vgIhAOAbX3CN/MUAGw9zRjP1q8r26neb8J2gYkklnnC4jnqi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPIw/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr99Q//bEVkJFE4JZ+XsxdxrFiT1xnwBV0+vIMlWX5757NRSf5bM2b8\r\nRrElOYrivTuPRdRVU/ic7yDvzuFIGfqA6sN/x6coWcBrSaltBsDBVTvTlPYu\r\nvjhsSUFVdqumaCEhbVD/87dQPz4qW80RVsrBVAXRo3Gf4ZYMzrZSyKfymhxG\r\nHnyubfSZTbdntSHoVvNNIy2xBQpsvgLhqEXHGsHFjC9NDsVAzmpo1a+iRSMi\r\nmqtnE6YsjFn6BszTeS5K3ZCBOQwDaYUdrAAU3WfO3kruJRkz7lqJ/Dj8UCEn\r\n9J3+t8dAAC0DPFNeEvohoGtBLJSO8kogNY5XFokT3ds2hSd97kQEDHm56xHS\r\nWaSlO66DNqLzfDuel+OEadc1JNAmyQeaLRwaCvJhhF8BdVRG3EVmlojAe+fQ\r\nTDnA9mkUXN8F2kM9Ixnf4Wg0eflUcg+SsIqgyie05hBm9GaJ9fd4r9AJ3S/s\r\nM3vZ8yTrxwcatcnF8+Zl6FaRe35r89Gn80c76FtL+bPytMlidTimLZLqbeEm\r\nbcDHf2bUi0sRWD2BXYxGUAFGDr/FipjPC/jG7lw04TnISWBlzYJTiJrm4jvV\r\nfv1KgtyB9P9W1XMElI8MKqJQnNIm9vCR5/R0o8Z93QXmVe+SIGsDfIAulM/W\r\neORiAoO5f/qVcnGmC/bF9kDGHSqtvNxfLMs=\r\n=/c9Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "dc0fe202faaf19a545ce5eeab3480be84180a082", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.0.0-pre.3", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"content": "./scripts", "engines": ">=10", "version": "4.4.4", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.4.4", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.3.8_1664912447716_0.647142154438872", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "semver", "version": "7.4.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.4.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "8481c92feffc531ab1e012a8ffc15bdd3a0f4318", "tarball": "https://registry.npmjs.org/semver/-/semver-7.4.0.tgz", "fileCount": 51, "integrity": "sha512-RgOxM8Mw+7Zus0+zcLEUn8+JfoLpj/huFTItQy2hsM4khuC1HYRDp0cU482Ewn/Fcy6bCjufD8vAj7voC66KQw==", "signatures": [{"sig": "MEUCID+AHRIawycC+CL7jn6IeG8qI0YTzXhH/DGZ17RN919WAiEArBUyfYL/BbnT0yTtL0lY3MMt1i/8BKCVmvK+sD8fepQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNIZaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYLA//V71EN9qQnvlXt4hyb5Hr7WghnBJV//K9elHIYf6pRBfvhJH+\r\ntQamY/yjdJuOjVAv98aUYkbab8PzH0N1ed5GjIlstPi4y6jMybKNW5EYYJEi\r\nWiFnxlyo0iJdTSnJu6Ud2xjNGzz12NMNTWqEsZ1Zl+VBO1fzDDv7ccInmsyH\r\nP7tr04fywpgwOGbMDgwpdEMCxDyC+ra3x2v7ZBQl8UgyfMktoYOJMKpHCMQG\r\n7Od9DW4Z/+PliXe9umzswKHHJxZzk0iyFBoBAkQeEo6gHe2q1YDCU4IOMRMr\r\n9wGD7D4hiif+QkgDuGbRQptyLSl6GXnw6PheUjao8thR7hg40CyTq96NRAW/\r\nuNLb0I4SkQmmMH+V9iIbieHdXIHSCiWDdMRfPdYtTqlXJYJmTuNdxAUnwR9t\r\nfEZ1wM8xxK6z97ZPgiCNOKHPsL4AHtIglCSPltXw+AfSUvFu3SfoB3cb00PH\r\n5S+WnRgSEuRO0FShqFEG0WHrX4X9rZgtd08bpHGRBhymkYl+r5U7nS32AyDy\r\nxZ4QsoNblkZDCkhWxHdL5lg5fMvIsnwtPYnehLOxOZE5gLhnZHzl6+GSDhjN\r\npV5JSy9YdnykoJypDGG0OyE2ro4a3SjMellh2UKxXmT1sjUGpjcPJH2l4ChY\r\niCrqkalZScXZtjhMT12b1m+snN1n7HwJq2w=\r\n=y35w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "82aa7f62617d445f578584543565329a62343aab", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "npmSpec": "8", "publish": "true", "version": "4.13.0", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.15.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.13.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.4.0_1681163866102_0.5153635697004932", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "semver", "version": "7.5.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.5.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "ed8c5dc8efb6c629c88b23d41dc9bf40c1d96cd0", "tarball": "https://registry.npmjs.org/semver/-/semver-7.5.0.tgz", "fileCount": 51, "integrity": "sha512-+XC0AD/R7Q2mPSRuy2Id0+CGTZ98+8f+KvwirxOKIEyid+XSx6HbC63p+O4IndTHuX5Z+JxQ0TghCkO5Cg/2HA==", "signatures": [{"sig": "MEYCIQDt07E5UJuRwXPOEi2hbEuVDLVZG3Pf9OMnpX48BNkovAIhAI9nWUYPRzvhBwQjGhrkaVWjLEDlnUGLInCY6j9khlNu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPYBwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSbxAAkuGiIuyA9EhYLW+KXPEY13ovIYhhJPKzhiL4VU22qt4OV+dm\r\nMwfWUoO4D3uQdpnbBnS5RHFI9Y3414A4AUNMUjlnsORbJJycIeHm82/3WJtT\r\nD3K5QtKtGhdMVOIHy9VlahtEj0hJvxiirv6Ly7xnlFP6cw1Xoo8uivD9wXPt\r\n30qSQyYUxAJNG/199y6Homa7oAFJW0ctu7iTZfHpGtrh5cwOBYTxSDDKtkZl\r\nP8W+Ip+1OPkHn1HdBGlSaMJSzuJtYWUbYG415KJf6n/QIOWp8zTnfg1RTeTJ\r\nrctDzU8WGdoC3cvLkuv8e50NvmbPNI8KNHgDqV5UhcEOy1t4LCnWcBntLXXJ\r\nGBhIk85AA4pG31euM6dyyoVFuQBoq6nbhdy859+YE40DypSiT8v1V4yq38Y0\r\n6syo+j4/yPCKrVxDfiePupltJIe5g9nwSoKWU7AWMQ1DREq9KqI2EaYUygYF\r\ndL+7uHTr8ALEaBZfZ4LJ3EwTAhhcrrUSrmImNVVicnHjQWAv3VtLFV8MisN6\r\neqMTJwpPhtWi8C6s2kdM3HHsNf6eEt5IRs1HYr6t41kAIUF+bjtVTG9GnUB0\r\nHK+y98OTS461JUEQlPULbSxbYRwmv9Ka7NIdYPg9Zmw4efZyRW5Ize5cYfv5\r\nIXRWQBbGo/8ovhjW5NSsM2WnaeVT1Et2R+4=\r\n=c4yY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "5b02ad7163a3ddcbcadf499e4f6195d6f2226dce", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "npmSpec": "8", "publish": "true", "version": "4.13.0", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.15.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.13.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.5.0_1681752176355_0.39230078049183925", "host": "s3://npm-registry-packages"}}, "7.5.1": {"name": "semver", "version": "7.5.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.5.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "c90c4d631cf74720e46b21c1d37ea07edfab91ec", "tarball": "https://registry.npmjs.org/semver/-/semver-7.5.1.tgz", "fileCount": 51, "integrity": "sha512-Wvss5ivl8TMRZXXESstBA4uR5iXgEN/VC5/sOcuXdVLzcdkz4HWetIoRfG5gb5X+ij/G9rw9YoGn3QoQ8OCSpw==", "signatures": [{"sig": "MEYCIQDg9wLVr9b1ltyYFc61qa+HZlOkWATcmpBZwf324Wp9xwIhAMntmIqywp/Cuz/Gmf2LCC5STJSAWiGOgUWxu3trPi+Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 91379}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "aa016a67162c195938f7873ea29a73dac47ff9ba", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "npmSpec": "8", "publish": "true", "version": "4.14.1", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.5.1_1683909581518_0.26769160533187386", "host": "s3://npm-registry-packages"}}, "7.5.2": {"name": "semver", "version": "7.5.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.5.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "5b851e66d1be07c1cdaf37dfc856f543325a2beb", "tarball": "https://registry.npmjs.org/semver/-/semver-7.5.2.tgz", "fileCount": 51, "integrity": "sha512-SoftuTROv/cRjCze/scjGyiDtcUyxw1rgYQSZY7XTmtR5hX+dm76iDbTH8TkLPHCQmlbQVSSbNZCPM2hb0knnQ==", "signatures": [{"sig": "MEYCIQDmFfzANuN7yDnbcJkGbVoNdXIriQXp3gdOfpbBYCmXPQIhAKwIaEvIBBPoUBP7ubl66810YwU3KZGZja0PxNUIOpy0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 92605}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "e7b78de06eb14a7fa2075cedf9f167040d8d31af", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "npmSpec": "8", "publish": "true", "version": "4.15.1", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.5.2_1686860771824_0.57606870088476", "host": "s3://npm-registry-packages"}}, "7.5.3": {"name": "semver", "version": "7.5.3", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.5.3", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "161ce8c2c6b4b3bdca6caadc9fa3317a4c4fe88e", "tarball": "https://registry.npmjs.org/semver/-/semver-7.5.3.tgz", "fileCount": 51, "integrity": "sha512-QBlUtyVk/5EeHbi7X0fw6liDZc7BBmEaSYn01fMU1OUYbf6GPsbTtd8WmnqbI20SeycoHSeiybkE/q1Q+qlThQ==", "signatures": [{"sig": "MEYCIQD0fBOo0eTChhzJ+ngR/HOI/HIn/FBMJ1cSleP7UUHpGgIhAM2H6NINrINo+1jE5UweaX31/tPunAlyaczNsd7rrbq6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 93390}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "7fdf1ef223826b428d7f8aaf906e9eeefa9469f9", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "npmSpec": "8", "publish": "true", "version": "4.15.1", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.5.3_1687470799532_0.2805096124485813", "host": "s3://npm-registry-packages"}}, "7.5.4": {"name": "semver", "version": "7.5.4", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.5.4", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "483986ec4ed38e1c6c48c34894a9182dbff68a6e", "tarball": "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz", "fileCount": 51, "integrity": "sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==", "signatures": [{"sig": "MEQCICledieqn36Ququ16KUtspwRpndZ1cor5Bn8AL0istatAiA6nTSWUF4M/o1UcAnaSEaoEwNxbIEvOfE94WXVBFla7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.5.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 93401}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "36cd334708ec1f85a71445622fb1864bceee0f4e", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "npmSpec": "8", "publish": "true", "version": "4.17.0", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "ciVersions": ["10.0.0", "10.x", "12.x", "14.x", "16.x", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.1", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.17.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.5.4_1688764232427_0.41544901656519095", "host": "s3://npm-registry-packages"}}, "5.7.2": {"name": "semver", "version": "5.7.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@5.7.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver"}, "dist": {"shasum": "48d55db737c3287cd4835e17fa13feace1c41ef8", "tarball": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "fileCount": 6, "integrity": "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==", "signatures": [{"sig": "MEUCIA1FkZlK+BP8dzoMUahZunoDStleso00k4b8Mnt/73xDAiEA9Fa9ZlmGHmvqmXmPvnAkueEb8/gl8D27TKon4US4m+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63315}, "main": "semver.js", "gitHead": "63169c1d87a1f36eb35022a3c6fcaf7ba6954055", "scripts": {"lint": "echo linting disabled", "snap": "tap test/ --100 --timeout=30", "test": "tap test/ --100 --timeout=30", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"content": "./scripts/template-oss", "version": "4.17.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^12.7.0", "@npmcli/template-oss": "4.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_5.7.2_1689019066913_0.7461531805384485", "host": "s3://npm-registry-packages"}}, "6.3.1": {"name": "semver", "version": "6.3.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@6.3.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "dist": {"shasum": "556d2ef8689146e46dcea4bfdd095f3434dffcb4", "tarball": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "fileCount": 6, "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "signatures": [{"sig": "MEUCIQCPu+5SJS1ygK7jDAPKWWXoKPfkubt2xDbbpmCZnRoCHwIgAm7TDcdNEL196wlziooSvSxaEIQmW42yauM55KOgKwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68343}, "main": "semver.js", "gitHead": "b717044e57bd132c7e5aa50e9af9a03f10d4655a", "scripts": {"lint": "echo linting disabled", "snap": "tap test/ --100 --timeout=30", "test": "tap test/ --100 --timeout=30", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"content": "./scripts/template-oss", "version": "4.17.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^12.7.0", "@npmcli/template-oss": "4.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_6.3.1_1689028721173_0.39493960745374723", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "semver", "version": "7.6.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.6.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "1a46a4db4bffcccd97b743b5005c8325f23d4e2d", "tarball": "https://registry.npmjs.org/semver/-/semver-7.6.0.tgz", "fileCount": 51, "integrity": "sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==", "signatures": [{"sig": "MEUCIQDNwnW9kHxzw5D4hq/5k8jay1Xp6PrsP+zdldgIrenJrAIgINOiu9gTWDQrcXN/KUnnkLYuBWuSSHZXZsItjsV3czs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 94244}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "377f709718053a477ed717089c4403c4fec332a1", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "10.4.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.21.3", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.11.0", "dependencies": {"lru-cache": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.6.0_1707152811382_0.682335914387501", "host": "s3://npm-registry-packages"}}, "7.6.1": {"name": "semver", "version": "7.6.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.6.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "60bfe090bf907a25aa8119a72b9f90ef7ca281b2", "tarball": "https://registry.npmjs.org/semver/-/semver-7.6.1.tgz", "fileCount": 52, "integrity": "sha512-f/vbBsu+fOiYt+lmwZV0rVwJScl46HppnOA1ZvIuBWKOTlllpyJ3bfVax76/OrhCH38dyxoDIA8K7uB963IYgA==", "signatures": [{"sig": "MEQCIDJkY8/BXAHjS+R851ucquRGdIm0QC+GqiwwsvGENwj1AiByhonw2VM5P0ud5qqlmv8tD+K5HTGZ7ihm/zD1ZCSGgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 95504}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "d777418116aeaecca9842b7621dd0ac1a92100bc", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.22.0", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.6.1_1715097748632_0.42046359595446403", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "semver", "version": "7.6.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.6.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "1e3b34759f896e8f14d6134732ce798aeb0c6e13", "tarball": "https://registry.npmjs.org/semver/-/semver-7.6.2.tgz", "fileCount": 52, "integrity": "sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==", "signatures": [{"sig": "MEYCIQD6/pwdiEu5Ip6DyQ8rwsJ13wLppdOMIDtJOClcLbK+nwIhAMgA4McQIu/+mVtitzpO97NIKFlLAt+8ABL6dnMipilm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 95424}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "eb1380b1ecd74f6572831294d55ef4537dfe1a2a", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.22.0", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.6.2_1715270569842_0.9942888461775998", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "semver", "version": "7.6.3", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.6.3", "maintainers": [{"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "980f7b5550bc175fb4dc09403085627f9eb33143", "tarball": "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz", "fileCount": 52, "integrity": "sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==", "signatures": [{"sig": "MEUCIAqB4ybgf5pFTkoWqIEDsWUkavZuelVqZrDd52CZ13OoAiEA+SBgG8aqumWI14kRLo9B6g3jiWzJt1jDtPpgfn0p/iw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.6.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 95824}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "0a12d6c7debb1dc82d8645c770e77c47bac5e1ea", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.22.0", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.4.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.6.3_1721168838877_0.9241625569831082", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "semver", "version": "7.7.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.7.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "9c6fe61d0c6f9fa9e26575162ee5a9180361b09c", "tarball": "https://registry.npmjs.org/semver/-/semver-7.7.0.tgz", "fileCount": 52, "integrity": "sha512-DrfFnPzblFmNrIZzg5RzHegbiRWg7KMR7btwi2yjHwx06zsUbO5g613sVwEV7FTwmzJu+Io0lJe2GJ3LxqpvBQ==", "signatures": [{"sig": "MEQCIA1RRcJDXsTva4cdPL5DN5VXGfalYYYzXMkC+256FkEkAiAGUxt+/oj9B4GtMJT/VfdKSGhIJThbb7gNwi4sb107cg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96558}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "2cfcbb5021059d0b6642a77400efb4b51133bd75", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "11.0.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.23.4", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.7.0_1738170881440_0.41305976524258514", "host": "s3://npm-registry-packages-npm-production"}}, "7.7.1": {"name": "semver", "version": "7.7.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.7.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "abd5098d82b18c6c81f6074ff2647fd3e7220c9f", "tarball": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "fileCount": 52, "integrity": "sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==", "signatures": [{"sig": "MEUCIQDI9SVHcoBbYQUqWYqedRBye+SM2pOj6BV9jBPpUOBszQIgUMGFgxbiwWvdfdCfce17m7VbWiRVZe+heIdtM0GanmI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.7.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 96674}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "30c438bb46c74f319aa8783f96d233ebf5f4a90d", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "11.1.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.23.4", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.13.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.23.4", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.7.1_1738619172328_0.9405645367047419", "host": "s3://npm-registry-packages-npm-production"}}, "7.7.2": {"name": "semver", "version": "7.7.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "semver@7.7.2", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "owlstronaut", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-semver#readme", "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bin": {"semver": "bin/semver.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "timeout": 30, "coverage-map": "map.js"}, "dist": {"shasum": "67d99fdcd35cec21e6f8b87a7fd515a33f982b58", "tarball": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "fileCount": 52, "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "signatures": [{"sig": "MEQCIBWgpqrE5Kqf9YKjIum5n0B6fPDkhrQ2Uf5DKmopfaS5AiBcu38UXAKlgwXKCiOnWMjRjjo4YSVB/6f2YaHE284k7g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/semver@7.7.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 97420}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "281055e7716ef0415a8826972471331989ede58c", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "_npmVersion": "11.3.0", "description": "The semantic version parser used by npm.", "directories": {}, "templateOSS": {"engines": ">=10", "publish": "true", "version": "4.24.3", "distPaths": ["classes/", "functions/", "internal/", "ranges/", "index.js", "preload.js", "range.bnf"], "allowPaths": ["/classes/", "/functions/", "/internal/", "/ranges/", "/index.js", "/preload.js", "/range.bnf", "/benchmarks"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.15.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.0", "benchmark": "^2.1.4", "@npmcli/template-oss": "4.24.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/semver_7.7.2_1747069348186_0.6289037087827105", "host": "s3://npm-registry-packages-npm-production"}}}, "time": {"created": "2011-02-12T00:20:25.690Z", "modified": "2025-05-14T20:04:22.201Z", "1.0.0": "2011-02-12T00:20:26.037Z", "1.0.1": "2011-02-18T17:15:49.775Z", "1.0.2": "2011-03-22T21:27:35.218Z", "1.0.3": "2011-04-19T23:29:13.670Z", "1.0.4": "2011-04-21T07:32:11.512Z", "1.0.5": "2011-05-03T23:11:54.939Z", "1.0.6": "2011-05-21T00:09:47.724Z", "1.0.7": "2011-06-17T16:26:07.324Z", "1.0.8": "2011-06-27T21:58:51.266Z", "1.0.9": "2011-07-20T21:38:13.081Z", "1.0.10": "2011-10-04T01:51:37.206Z", "1.0.11": "2011-11-15T16:40:04.239Z", "1.0.12": "2011-11-18T19:04:02.511Z", "1.0.13": "2011-12-21T17:07:14.144Z", "1.0.14": "2012-05-27T00:47:32.831Z", "1.1.0": "2012-10-02T17:02:34.309Z", "1.1.1": "2012-11-29T00:46:21.597Z", "1.1.2": "2013-01-06T16:25:56.424Z", "1.1.3": "2013-02-06T15:42:39.566Z", "1.1.4": "2013-03-01T18:14:56.811Z", "2.0.0-alpha": "2013-06-15T03:29:33.540Z", "2.0.0-beta": "2013-06-18T00:01:26.679Z", "2.0.1": "2013-06-20T04:42:51.354Z", "2.0.2": "2013-06-20T15:05:58.554Z", "2.0.3": "2013-06-20T15:15:33.034Z", "2.0.4": "2013-06-20T15:33:20.522Z", "2.0.5": "2013-06-20T15:42:04.599Z", "2.0.6": "2013-06-20T18:41:20.343Z", "2.0.7": "2013-06-20T18:57:03.281Z", "2.0.8": "2013-06-24T22:12:37.887Z", "2.0.9": "2013-07-06T03:45:40.578Z", "2.0.10": "2013-07-09T22:39:16.895Z", "2.0.11": "2013-07-24T03:23:19.907Z", "2.1.0": "2013-08-01T23:52:31.371Z", "2.2.0": "2013-10-25T20:02:44.049Z", "2.2.1": "2013-10-28T18:18:10.005Z", "2.3.0": "2014-05-07T01:15:02.092Z", "2.3.1": "2014-06-18T22:48:16.706Z", "2.3.2": "2014-07-22T19:24:50.090Z", "3.0.0": "2014-07-23T21:14:29.806Z", "3.0.1": "2014-07-24T17:24:36.175Z", "4.0.0": "2014-09-11T22:36:27.208Z", "4.0.2": "2014-09-30T23:55:26.916Z", "4.0.3": "2014-10-01T00:18:37.208Z", "4.1.0": "2014-10-16T00:55:35.923Z", "4.1.1": "2014-12-19T12:57:14.981Z", "4.2.0": "2014-12-23T09:42:46.263Z", "4.2.1": "2015-02-10T06:44:26.265Z", "4.2.2": "2015-02-10T06:46:44.370Z", "4.3.0": "2015-02-12T20:08:38.236Z", "4.3.1": "2015-02-24T19:49:50.416Z", "4.3.2": "2015-03-27T01:26:08.892Z", "4.3.3": "2015-03-27T16:56:24.729Z", "4.3.4": "2015-05-05T04:26:05.035Z", "4.3.5": "2015-05-29T22:25:40.918Z", "4.3.6": "2015-06-01T04:16:22.945Z", "5.0.0": "2015-07-11T17:29:40.652Z", "5.0.1": "2015-07-13T20:02:27.516Z", "5.0.2": "2015-09-11T17:09:40.057Z", "5.0.3": "2015-09-11T20:27:31.563Z", "5.1.0": "2015-11-18T23:18:02.918Z", "5.1.1": "2016-06-23T18:00:51.598Z", "5.2.0": "2016-06-28T18:00:41.679Z", "5.3.0": "2016-07-14T16:52:47.104Z", "5.4.0": "2017-07-24T16:39:33.594Z", "5.4.1": "2017-07-24T18:48:27.785Z", "5.5.0": "2018-01-16T19:27:59.818Z", "5.5.1": "2018-08-17T20:35:46.676Z", "5.6.0": "2018-10-10T23:52:25.375Z", "5.7.0": "2019-03-26T23:25:47.130Z", "6.0.0": "2019-03-26T23:30:05.580Z", "6.1.0": "2019-05-22T21:12:49.111Z", "6.1.1": "2019-05-28T17:15:08.376Z", "6.1.2": "2019-06-24T01:48:03.240Z", "6.1.3": "2019-07-01T05:51:08.761Z", "6.2.0": "2019-07-01T23:03:27.604Z", "6.3.0": "2019-07-23T19:25:26.568Z", "5.7.1": "2019-08-12T16:28:15.053Z", "7.0.0": "2019-12-14T19:36:54.748Z", "7.1.0": "2019-12-17T01:28:48.900Z", "7.1.1": "2019-12-17T16:56:29.010Z", "7.1.2": "2020-01-31T01:29:45.224Z", "7.1.3": "2020-02-11T21:54:05.273Z", "7.2.0": "2020-04-06T23:36:09.707Z", "7.2.1": "2020-04-06T23:37:44.278Z", "7.2.2": "2020-04-10T16:01:16.989Z", "7.2.3": "2020-04-13T18:31:33.110Z", "7.3.0": "2020-04-14T01:08:44.962Z", "7.3.1": "2020-04-14T16:56:08.021Z", "7.3.2": "2020-04-14T17:43:28.451Z", "7.3.3": "2020-12-01T19:30:05.865Z", "7.3.4": "2020-12-01T20:15:13.977Z", "7.3.5": "2021-03-23T01:37:52.803Z", "7.3.6": "2022-04-06T16:35:25.625Z", "7.3.7": "2022-04-12T17:26:24.970Z", "7.3.8": "2022-10-04T19:40:47.960Z", "7.4.0": "2023-04-10T21:57:46.268Z", "7.5.0": "2023-04-17T17:22:56.540Z", "7.5.1": "2023-05-12T16:39:41.720Z", "7.5.2": "2023-06-15T20:26:11.975Z", "7.5.3": "2023-06-22T21:53:19.774Z", "7.5.4": "2023-07-07T21:10:32.589Z", "5.7.2": "2023-07-10T19:57:47.111Z", "6.3.1": "2023-07-10T22:38:41.428Z", "7.6.0": "2024-02-05T17:06:51.520Z", "7.6.1": "2024-05-07T16:02:28.840Z", "7.6.2": "2024-05-09T16:02:50.012Z", "7.6.3": "2024-07-16T22:27:19.119Z", "7.7.0": "2025-01-29T17:14:41.608Z", "7.7.1": "2025-02-03T21:46:12.515Z", "7.7.2": "2025-05-12T17:02:28.372Z"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/node-semver#readme", "repository": {"url": "git+https://github.com/npm/node-semver.git", "type": "git"}, "description": "The semantic version parser used by npm.", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "semver(1) -- The semantic versioner for npm\n===========================================\n\n## Install\n\n```bash\nnpm install semver\n````\n\n## Usage\n\nAs a node module:\n\n```js\nconst semver = require('semver')\n\nsemver.valid('1.2.3') // '1.2.3'\nsemver.valid('a.b.c') // null\nsemver.clean('  =v1.2.3   ') // '1.2.3'\nsemver.satisfies('1.2.3', '1.x || >=2.5.0 || 5.0.0 - 7.2.3') // true\nsemver.gt('1.2.3', '9.8.7') // false\nsemver.lt('1.2.3', '9.8.7') // true\nsemver.minVersion('>=1.0.0') // '1.0.0'\nsemver.valid(semver.coerce('v2')) // '2.0.0'\nsemver.valid(semver.coerce('********.3-alpha')) // '42.6.7'\n```\n\nYou can also just load the module for the function that you care about if\nyou'd like to minimize your footprint.\n\n```js\n// load the whole API at once in a single object\nconst semver = require('semver')\n\n// or just load the bits you need\n// all of them listed here, just pick and choose what you want\n\n// classes\nconst SemVer = require('semver/classes/semver')\nconst Comparator = require('semver/classes/comparator')\nconst Range = require('semver/classes/range')\n\n// functions for working with versions\nconst semverParse = require('semver/functions/parse')\nconst semverValid = require('semver/functions/valid')\nconst semverClean = require('semver/functions/clean')\nconst semverInc = require('semver/functions/inc')\nconst semverDiff = require('semver/functions/diff')\nconst semverMajor = require('semver/functions/major')\nconst semverMinor = require('semver/functions/minor')\nconst semverPatch = require('semver/functions/patch')\nconst semverPrerelease = require('semver/functions/prerelease')\nconst semverCompare = require('semver/functions/compare')\nconst semverRcompare = require('semver/functions/rcompare')\nconst semverCompareLoose = require('semver/functions/compare-loose')\nconst semverCompareBuild = require('semver/functions/compare-build')\nconst semverSort = require('semver/functions/sort')\nconst semverRsort = require('semver/functions/rsort')\n\n// low-level comparators between versions\nconst semverGt = require('semver/functions/gt')\nconst semverLt = require('semver/functions/lt')\nconst semverEq = require('semver/functions/eq')\nconst semverNeq = require('semver/functions/neq')\nconst semverGte = require('semver/functions/gte')\nconst semverLte = require('semver/functions/lte')\nconst semverCmp = require('semver/functions/cmp')\nconst semverCoerce = require('semver/functions/coerce')\n\n// working with ranges\nconst semverSatisfies = require('semver/functions/satisfies')\nconst semverMaxSatisfying = require('semver/ranges/max-satisfying')\nconst semverMinSatisfying = require('semver/ranges/min-satisfying')\nconst semverToComparators = require('semver/ranges/to-comparators')\nconst semverMinVersion = require('semver/ranges/min-version')\nconst semverValidRange = require('semver/ranges/valid')\nconst semverOutside = require('semver/ranges/outside')\nconst semverGtr = require('semver/ranges/gtr')\nconst semverLtr = require('semver/ranges/ltr')\nconst semverIntersects = require('semver/ranges/intersects')\nconst semverSimplifyRange = require('semver/ranges/simplify')\nconst semverRangeSubset = require('semver/ranges/subset')\n```\n\nAs a command-line utility:\n\n```\n$ semver -h\n\nA JavaScript implementation of the https://semver.org/ specification\nCopyright Isaac Z. Schlueter\n\nUsage: semver [options] <version> [<version> [...]]\nPrints valid versions sorted by SemVer precedence\n\nOptions:\n-r --range <range>\n        Print versions that match the specified range.\n\n-i --increment [<level>]\n        Increment a version by the specified level.  Level can\n        be one of: major, minor, patch, premajor, preminor,\n        prepatch, prerelease, or release.  Default level is 'patch'.\n        Only one version may be specified.\n\n--preid <identifier>\n        Identifier to be used to prefix premajor, preminor,\n        prepatch or prerelease version increments.\n\n-l --loose\n        Interpret versions and ranges loosely\n\n-n <0|1>\n        This is the base to be used for the prerelease identifier.\n\n-p --include-prerelease\n        Always include prerelease versions in range matching\n\n-c --coerce\n        Coerce a string into SemVer if possible\n        (does not imply --loose)\n\n--rtl\n        Coerce version strings right to left\n\n--ltr\n        Coerce version strings left to right (default)\n\nProgram exits successfully if any valid version satisfies\nall supplied ranges, and prints all satisfying versions.\n\nIf no satisfying versions are found, then exits failure.\n\nVersions are printed in ascending order, so supplying\nmultiple versions to the utility will just sort them.\n```\n\n## Versions\n\nA \"version\" is described by the `v2.0.0` specification found at\n<https://semver.org/>.\n\nA leading `\"=\"` or `\"v\"` character is stripped off and ignored.\nSupport for stripping a leading \"v\" is kept for compatibility with `v1.0.0` of the SemVer\nspecification but should not be used anymore.\n\n## Ranges\n\nA `version range` is a set of `comparators` that specify versions\nthat satisfy the range.\n\nA `comparator` is composed of an `operator` and a `version`.  The set\nof primitive `operators` is:\n\n* `<` Less than\n* `<=` Less than or equal to\n* `>` Greater than\n* `>=` Greater than or equal to\n* `=` Equal.  If no operator is specified, then equality is assumed,\n  so this operator is optional but MAY be included.\n\nFor example, the comparator `>=1.2.7` would match the versions\n`1.2.7`, `1.2.8`, `2.5.3`, and `1.3.9`, but not the versions `1.2.6`\nor `1.1.0`. The comparator `>1` is equivalent to `>=2.0.0` and\nwould match the versions `2.0.0` and `3.1.0`, but not the versions\n`1.0.1` or `1.1.0`.\n\nComparators can be joined by whitespace to form a `comparator set`,\nwhich is satisfied by the **intersection** of all of the comparators\nit includes.\n\nA range is composed of one or more comparator sets, joined by `||`.  A\nversion matches a range if and only if every comparator in at least\none of the `||`-separated comparator sets is satisfied by the version.\n\nFor example, the range `>=1.2.7 <1.3.0` would match the versions\n`1.2.7`, `1.2.8`, and `1.2.99`, but not the versions `1.2.6`, `1.3.0`,\nor `1.1.0`.\n\nThe range `1.2.7 || >=1.2.9 <2.0.0` would match the versions `1.2.7`,\n`1.2.9`, and `1.4.6`, but not the versions `1.2.8` or `2.0.0`.\n\n### Prerelease Tags\n\nIf a version has a prerelease tag (for example, `1.2.3-alpha.3`) then\nit will only be allowed to satisfy comparator sets if at least one\ncomparator with the same `[major, minor, patch]` tuple also has a\nprerelease tag.\n\nFor example, the range `>1.2.3-alpha.3` would be allowed to match the\nversion `1.2.3-alpha.7`, but it would *not* be satisfied by\n`3.4.5-alpha.9`, even though `3.4.5-alpha.9` is technically \"greater\nthan\" `1.2.3-alpha.3` according to the SemVer sort rules.  The version\nrange only accepts prerelease tags on the `1.2.3` version.\nVersion `3.4.5` *would* satisfy the range because it does not have a\nprerelease flag, and `3.4.5` is greater than `1.2.3-alpha.7`.\n\nThe purpose of this behavior is twofold.  First, prerelease versions\nfrequently are updated very quickly, and contain many breaking changes\nthat are (by the author's design) not yet fit for public consumption.\nTherefore, by default, they are excluded from range-matching\nsemantics.\n\nSecond, a user who has opted into using a prerelease version has\nindicated the intent to use *that specific* set of\nalpha/beta/rc versions.  By including a prerelease tag in the range,\nthe user is indicating that they are aware of the risk.  However, it\nis still not appropriate to assume that they have opted into taking a\nsimilar risk on the *next* set of prerelease versions.\n\nNote that this behavior can be suppressed (treating all prerelease\nversions as if they were normal versions, for range-matching)\nby setting the `includePrerelease` flag on the options\nobject to any\n[functions](https://github.com/npm/node-semver#functions) that do\nrange matching.\n\n#### Prerelease Identifiers\n\nThe method `.inc` takes an additional `identifier` string argument that\nwill append the value of the string as a prerelease identifier:\n\n```javascript\nsemver.inc('1.2.3', 'prerelease', 'beta')\n// '1.2.4-beta.0'\n```\n\ncommand-line example:\n\n```bash\n$ semver 1.2.3 -i prerelease --preid beta\n1.2.4-beta.0\n```\n\nWhich then can be used to increment further:\n\n```bash\n$ semver 1.2.4-beta.0 -i prerelease\n1.2.4-beta.1\n```\n\nTo get out of the prerelease phase, use the `release` option:\n\n```bash\n$ semver 1.2.4-beta.1 -i release\n1.2.4\n```\n\n#### Prerelease Identifier Base\n\nThe method `.inc` takes an optional parameter 'identifierBase' string\nthat will let you let your prerelease number as zero-based or one-based.\nSet to `false` to omit the prerelease number altogether.\nIf you do not specify this parameter, it will default to zero-based.\n\n```javascript\nsemver.inc('1.2.3', 'prerelease', 'beta', '1')\n// '1.2.4-beta.1'\n```\n\n```javascript\nsemver.inc('1.2.3', 'prerelease', 'beta', false)\n// '1.2.4-beta'\n```\n\ncommand-line example:\n\n```bash\n$ semver 1.2.3 -i prerelease --preid beta -n 1\n1.2.4-beta.1\n```\n\n```bash\n$ semver 1.2.3 -i prerelease --preid beta -n false\n1.2.4-beta\n```\n\n### Advanced Range Syntax\n\nAdvanced range syntax desugars to primitive comparators in\ndeterministic ways.\n\nAdvanced ranges may be combined in the same way as primitive\ncomparators using white space or `||`.\n\n#### Hyphen Ranges `X.Y.Z - A.B.C`\n\nSpecifies an inclusive set.\n\n* `1.2.3 - 2.3.4` := `>=1.2.3 <=2.3.4`\n\nIf a partial version is provided as the first version in the inclusive\nrange, then the missing pieces are replaced with zeroes.\n\n* `1.2 - 2.3.4` := `>=1.2.0 <=2.3.4`\n\nIf a partial version is provided as the second version in the\ninclusive range, then all versions that start with the supplied parts\nof the tuple are accepted, but nothing that would be greater than the\nprovided tuple parts.\n\n* `1.2.3 - 2.3` := `>=1.2.3 <2.4.0-0`\n* `1.2.3 - 2` := `>=1.2.3 <3.0.0-0`\n\n#### X-Ranges `1.2.x` `1.X` `1.2.*` `*`\n\nAny of `X`, `x`, or `*` may be used to \"stand in\" for one of the\nnumeric values in the `[major, minor, patch]` tuple.\n\n* `*` := `>=0.0.0` (Any non-prerelease version satisfies, unless\n  `includePrerelease` is specified, in which case any version at all\n  satisfies)\n* `1.x` := `>=1.0.0 <2.0.0-0` (Matching major version)\n* `1.2.x` := `>=1.2.0 <1.3.0-0` (Matching major and minor versions)\n\nA partial version range is treated as an X-Range, so the special\ncharacter is in fact optional.\n\n* `\"\"` (empty string) := `*` := `>=0.0.0`\n* `1` := `1.x.x` := `>=1.0.0 <2.0.0-0`\n* `1.2` := `1.2.x` := `>=1.2.0 <1.3.0-0`\n\n#### Tilde Ranges `~1.2.3` `~1.2` `~1`\n\nAllows patch-level changes if a minor version is specified on the\ncomparator.  Allows minor-level changes if not.\n\n* `~1.2.3` := `>=1.2.3 <1.(2+1).0` := `>=1.2.3 <1.3.0-0`\n* `~1.2` := `>=1.2.0 <1.(2+1).0` := `>=1.2.0 <1.3.0-0` (Same as `1.2.x`)\n* `~1` := `>=1.0.0 <(1+1).0.0` := `>=1.0.0 <2.0.0-0` (Same as `1.x`)\n* `~0.2.3` := `>=0.2.3 <0.(2+1).0` := `>=0.2.3 <0.3.0-0`\n* `~0.2` := `>=0.2.0 <0.(2+1).0` := `>=0.2.0 <0.3.0-0` (Same as `0.2.x`)\n* `~0` := `>=0.0.0 <(0+1).0.0` := `>=0.0.0 <1.0.0-0` (Same as `0.x`)\n* `~1.2.3-beta.2` := `>=1.2.3-beta.2 <1.3.0-0` Note that prereleases in\n  the `1.2.3` version will be allowed, if they are greater than or\n  equal to `beta.2`.  So, `1.2.3-beta.4` would be allowed, but\n  `1.2.4-beta.2` would not, because it is a prerelease of a\n  different `[major, minor, patch]` tuple.\n\n#### Caret Ranges `^1.2.3` `^0.2.5` `^0.0.4`\n\nAllows changes that do not modify the left-most non-zero element in the\n`[major, minor, patch]` tuple.  In other words, this allows patch and\nminor updates for versions `1.0.0` and above, patch updates for\nversions `0.X >=0.1.0`, and *no* updates for versions `0.0.X`.\n\nMany authors treat a `0.x` version as if the `x` were the major\n\"breaking-change\" indicator.\n\nCaret ranges are ideal when an author may make breaking changes\nbetween `0.2.4` and `0.3.0` releases, which is a common practice.\nHowever, it presumes that there will *not* be breaking changes between\n`0.2.4` and `0.2.5`.  It allows for changes that are presumed to be\nadditive (but non-breaking), according to commonly observed practices.\n\n* `^1.2.3` := `>=1.2.3 <2.0.0-0`\n* `^0.2.3` := `>=0.2.3 <0.3.0-0`\n* `^0.0.3` := `>=0.0.3 <0.0.4-0`\n* `^1.2.3-beta.2` := `>=1.2.3-beta.2 <2.0.0-0` Note that prereleases in\n  the `1.2.3` version will be allowed, if they are greater than or\n  equal to `beta.2`.  So, `1.2.3-beta.4` would be allowed, but\n  `1.2.4-beta.2` would not, because it is a prerelease of a\n  different `[major, minor, patch]` tuple.\n* `^0.0.3-beta` := `>=0.0.3-beta <0.0.4-0`  Note that prereleases in the\n  `0.0.3` version *only* will be allowed, if they are greater than or\n  equal to `beta`.  So, `0.0.3-pr.2` would be allowed.\n\nWhen parsing caret ranges, a missing `patch` value desugars to the\nnumber `0`, but will allow flexibility within that value, even if the\nmajor and minor versions are both `0`.\n\n* `^1.2.x` := `>=1.2.0 <2.0.0-0`\n* `^0.0.x` := `>=0.0.0 <0.1.0-0`\n* `^0.0` := `>=0.0.0 <0.1.0-0`\n\nA missing `minor` and `patch` values will desugar to zero, but also\nallow flexibility within those values, even if the major version is\nzero.\n\n* `^1.x` := `>=1.0.0 <2.0.0-0`\n* `^0.x` := `>=0.0.0 <1.0.0-0`\n\n### Range Grammar\n\nPutting all this together, here is a Backus-Naur grammar for ranges,\nfor the benefit of parser authors:\n\n```bnf\nrange-set  ::= range ( logical-or range ) *\nlogical-or ::= ( ' ' ) * '||' ( ' ' ) *\nrange      ::= hyphen | simple ( ' ' simple ) * | ''\nhyphen     ::= partial ' - ' partial\nsimple     ::= primitive | partial | tilde | caret\nprimitive  ::= ( '<' | '>' | '>=' | '<=' | '=' ) partial\npartial    ::= xr ( '.' xr ( '.' xr qualifier ? )? )?\nxr         ::= 'x' | 'X' | '*' | nr\nnr         ::= '0' | ['1'-'9'] ( ['0'-'9'] ) *\ntilde      ::= '~' partial\ncaret      ::= '^' partial\nqualifier  ::= ( '-' pre )? ( '+' build )?\npre        ::= parts\nbuild      ::= parts\nparts      ::= part ( '.' part ) *\npart       ::= nr | [-0-9A-Za-z]+\n```\n\n## Functions\n\nAll methods and classes take a final `options` object argument.  All\noptions in this object are `false` by default.  The options supported\nare:\n\n- `loose`: Be more forgiving about not-quite-valid semver strings.\n  (Any resulting output will always be 100% strict compliant, of\n  course.)  For backwards compatibility reasons, if the `options`\n  argument is a boolean value instead of an object, it is interpreted\n  to be the `loose` param.\n- `includePrerelease`: Set to suppress the [default\n  behavior](https://github.com/npm/node-semver#prerelease-tags) of\n  excluding prerelease tagged versions from ranges unless they are\n  explicitly opted into.\n\nStrict-mode Comparators and Ranges will be strict about the SemVer\nstrings that they parse.\n\n* `valid(v)`: Return the parsed version, or null if it's not valid.\n* `inc(v, releaseType, options, identifier, identifierBase)`: \n  Return the version incremented by the release\n  type (`major`, `premajor`, `minor`, `preminor`, `patch`,\n  `prepatch`, `prerelease`, or `release`), or null if it's not valid\n  * `premajor` in one call will bump the version up to the next major\n    version and down to a prerelease of that major version.\n    `preminor`, and `prepatch` work the same way.\n  * If called from a non-prerelease version, `prerelease` will work the\n    same as `prepatch`. It increments the patch version and then makes a\n    prerelease. If the input version is already a prerelease it simply\n    increments it.\n  * `release` will remove any prerelease part of the version.\n  * `identifier` can be used to prefix `premajor`, `preminor`,\n    `prepatch`, or `prerelease` version increments. `identifierBase`\n    is the base to be used for the `prerelease` identifier.\n* `prerelease(v)`: Returns an array of prerelease components, or null\n  if none exist. Example: `prerelease('1.2.3-alpha.1') -> ['alpha', 1]`\n* `major(v)`: Return the major version number.\n* `minor(v)`: Return the minor version number.\n* `patch(v)`: Return the patch version number.\n* `intersects(r1, r2, loose)`: Return true if the two supplied ranges\n  or comparators intersect.\n* `parse(v)`: Attempt to parse a string as a semantic version, returning either\n  a `SemVer` object or `null`.\n\n### Comparison\n\n* `gt(v1, v2)`: `v1 > v2`\n* `gte(v1, v2)`: `v1 >= v2`\n* `lt(v1, v2)`: `v1 < v2`\n* `lte(v1, v2)`: `v1 <= v2`\n* `eq(v1, v2)`: `v1 == v2` This is true if they're logically equivalent,\n  even if they're not the same string.  You already know how to\n  compare strings.\n* `neq(v1, v2)`: `v1 != v2` The opposite of `eq`.\n* `cmp(v1, comparator, v2)`: Pass in a comparison string, and it'll call\n  the corresponding function above.  `\"===\"` and `\"!==\"` do simple\n  string comparison, but are included for completeness.  Throws if an\n  invalid comparison string is provided.\n* `compare(v1, v2)`: Return `0` if `v1 == v2`, or `1` if `v1` is greater, or `-1` if\n  `v2` is greater.  Sorts in ascending order if passed to `Array.sort()`.\n* `rcompare(v1, v2)`: The reverse of `compare`.  Sorts an array of versions\n  in descending order when passed to `Array.sort()`.\n* `compareBuild(v1, v2)`: The same as `compare` but considers `build` when two versions\n  are equal.  Sorts in ascending order if passed to `Array.sort()`.\n* `compareLoose(v1, v2)`: Short for `compare(v1, v2, { loose: true })`.\n* `diff(v1, v2)`: Returns the difference between two versions by the release type\n  (`major`, `premajor`, `minor`, `preminor`, `patch`, `prepatch`, or `prerelease`),\n  or null if the versions are the same.\n\n### Sorting\n\n* `sort(versions)`: Returns a sorted array of versions based on the `compareBuild` \n  function.\n* `rsort(versions)`: The reverse of `sort`. Returns an array of versions based on\n  the `compareBuild` function in descending order.\n\n### Comparators\n\n* `intersects(comparator)`: Return true if the comparators intersect\n\n### Ranges\n\n* `validRange(range)`: Return the valid range or null if it's not valid.\n* `satisfies(version, range)`: Return true if the version satisfies the\n  range.\n* `maxSatisfying(versions, range)`: Return the highest version in the list\n  that satisfies the range, or `null` if none of them do.\n* `minSatisfying(versions, range)`: Return the lowest version in the list\n  that satisfies the range, or `null` if none of them do.\n* `minVersion(range)`: Return the lowest version that can match\n  the given range.\n* `gtr(version, range)`: Return `true` if the version is greater than all the\n  versions possible in the range.\n* `ltr(version, range)`: Return `true` if the version is less than all the\n  versions possible in the range.\n* `outside(version, range, hilo)`: Return true if the version is outside\n  the bounds of the range in either the high or low direction.  The\n  `hilo` argument must be either the string `'>'` or `'<'`.  (This is\n  the function called by `gtr` and `ltr`.)\n* `intersects(range)`: Return true if any of the range comparators intersect.\n* `simplifyRange(versions, range)`: Return a \"simplified\" range that\n  matches the same items in the `versions` list as the range specified.  Note\n  that it does *not* guarantee that it would match the same versions in all\n  cases, only for the set of versions provided.  This is useful when\n  generating ranges by joining together multiple versions with `||`\n  programmatically, to provide the user with something a bit more\n  ergonomic.  If the provided range is shorter in string-length than the\n  generated range, then that is returned.\n* `subset(subRange, superRange)`: Return `true` if the `subRange` range is\n  entirely contained by the `superRange` range.\n\nNote that, since ranges may be non-contiguous, a version might not be\ngreater than a range, less than a range, *or* satisfy a range!  For\nexample, the range `1.2 <1.2.9 || >2.0.0` would have a hole from `1.2.9`\nuntil `2.0.0`, so version `1.2.10` would not be greater than the\nrange (because `2.0.1` satisfies, which is higher), nor less than the\nrange (since `1.2.8` satisfies, which is lower), and it also does not\nsatisfy the range.\n\nIf you want to know if a version satisfies or does not satisfy a\nrange, use the `satisfies(version, range)` function.\n\n### Coercion\n\n* `coerce(version, options)`: Coerces a string to semver if possible\n\nThis aims to provide a very forgiving translation of a non-semver string to\nsemver. It looks for the first digit in a string and consumes all\nremaining characters which satisfy at least a partial semver (e.g., `1`,\n`1.2`, `1.2.3`) up to the max permitted length (256 characters).  Longer\nversions are simply truncated (`4.6.3.9.2-alpha2` becomes `4.6.3`).  All\nsurrounding text is simply ignored (`v3.4 replaces v3.3.1` becomes\n`3.4.0`).  Only text which lacks digits will fail coercion (`version one`\nis not valid).  The maximum length for any semver component considered for\ncoercion is 16 characters; longer components will be ignored\n(`10000000000000000.4.7.4` becomes `4.7.4`).  The maximum value for any\nsemver component is `Number.MAX_SAFE_INTEGER || (2**53 - 1)`; higher value\ncomponents are invalid (`9999999999999999.4.7.4` is likely invalid).\n\nIf the `options.rtl` flag is set, then `coerce` will return the right-most\ncoercible tuple that does not share an ending index with a longer coercible\ntuple.  For example, `1.2.3.4` will return `2.3.4` in rtl mode, not\n`4.0.0`.  `1.2.3/4` will return `4.0.0`, because the `4` is not a part of\nany other overlapping SemVer tuple.\n\nIf the `options.includePrerelease` flag is set, then the `coerce` result will contain\nprerelease and build parts of a version.  For example, `1.2.3.4-rc.1+rev.2`\nwill preserve prerelease `rc.1` and build `rev.2` in the result.\n\n### Clean\n\n* `clean(version)`: Clean a string to be a valid semver if possible\n\nThis will return a cleaned and trimmed semver version. If the provided\nversion is not valid a null will be returned. This does not work for\nranges.\n\nex.\n* `s.clean(' = v 2.1.5foo')`: `null`\n* `s.clean(' = v 2.1.5foo', { loose: true })`: `'2.1.5-foo'`\n* `s.clean(' = v 2.1.5-foo')`: `null`\n* `s.clean(' = v 2.1.5-foo', { loose: true })`: `'2.1.5-foo'`\n* `s.clean('=v2.1.5')`: `'2.1.5'`\n* `s.clean('  =v2.1.5')`: `'2.1.5'`\n* `s.clean('      2.1.5   ')`: `'2.1.5'`\n* `s.clean('~1.0.0')`: `null`\n\n## Constants\n\nAs a convenience, helper constants are exported to provide information about what `node-semver` supports:\n\n### `RELEASE_TYPES`\n\n- major\n- premajor\n- minor\n- preminor\n- patch\n- prepatch\n- prerelease\n\n```\nconst semver = require('semver');\n\nif (semver.RELEASE_TYPES.includes(arbitraryUserInput)) {\n  console.log('This is a valid release type!');\n} else {\n  console.warn('This is NOT a valid release type!');\n}\n```\n\n### `SEMVER_SPEC_VERSION`\n\n2.0.0\n\n```\nconst semver = require('semver');\n\nconsole.log('We are currently using the semver specification version:', semver.SEMVER_SPEC_VERSION);\n```\n\n## Exported Modules\n\n<!--\nTODO: Make sure that all of these items are documented (classes aren't,\neg), and then pull the module name into the documentation for that specific\nthing.\n-->\n\nYou may pull in just the part of this semver utility that you need if you\nare sensitive to packing and tree-shaking concerns.  The main\n`require('semver')` export uses getter functions to lazily load the parts\nof the API that are used.\n\nThe following modules are available:\n\n* `require('semver')`\n* `require('semver/classes')`\n* `require('semver/classes/comparator')`\n* `require('semver/classes/range')`\n* `require('semver/classes/semver')`\n* `require('semver/functions/clean')`\n* `require('semver/functions/cmp')`\n* `require('semver/functions/coerce')`\n* `require('semver/functions/compare')`\n* `require('semver/functions/compare-build')`\n* `require('semver/functions/compare-loose')`\n* `require('semver/functions/diff')`\n* `require('semver/functions/eq')`\n* `require('semver/functions/gt')`\n* `require('semver/functions/gte')`\n* `require('semver/functions/inc')`\n* `require('semver/functions/lt')`\n* `require('semver/functions/lte')`\n* `require('semver/functions/major')`\n* `require('semver/functions/minor')`\n* `require('semver/functions/neq')`\n* `require('semver/functions/parse')`\n* `require('semver/functions/patch')`\n* `require('semver/functions/prerelease')`\n* `require('semver/functions/rcompare')`\n* `require('semver/functions/rsort')`\n* `require('semver/functions/satisfies')`\n* `require('semver/functions/sort')`\n* `require('semver/functions/valid')`\n* `require('semver/ranges/gtr')`\n* `require('semver/ranges/intersects')`\n* `require('semver/ranges/ltr')`\n* `require('semver/ranges/max-satisfying')`\n* `require('semver/ranges/min-satisfying')`\n* `require('semver/ranges/min-version')`\n* `require('semver/ranges/outside')`\n* `require('semver/ranges/simplify')`\n* `require('semver/ranges/subset')`\n* `require('semver/ranges/to-comparators')`\n* `require('semver/ranges/valid')`\n\n", "readmeFilename": "README.md", "users": {"285858315": true, "52u": true, "pid": true, "bcoe": true, "clux": true, "dwqs": true, "glab": true, "jtrh": true, "aaron": true, "akiva": true, "brend": true, "fourq": true, "guria": true, "irnnr": true, "lgh06": true, "mdaha": true, "panlw": true, "pftom": true, "ryanj": true, "seniv": true, "stona": true, "afc163": true, "ajsb85": true, "akarem": true, "amobiz": true, "arttse": true, "buzuli": true, "d-band": true, "daizch": true, "etan22": true, "gabeio": true, "glider": true, "glukki": true, "jimnox": true, "kerwyn": true, "knalli": true, "mpcref": true, "nettee": true, "niccai": true, "nuwaio": true, "penglu": true, "phixid": true, "pstoev": true, "qlqllu": true, "tedyhy": true, "tianyk": true, "tigefa": true, "tomekf": true, "womjoy": true, "yeming": true, "yuch4n": true, "alnafie": true, "anoubis": true, "antanst": true, "asaupup": true, "cueedee": true, "diegohb": true, "dyc5828": true, "eli_yao": true, "itonyyo": true, "kahboom": true, "kiinlam": true, "kontrax": true, "lachriz": true, "liunian": true, "nanxing": true, "nwinant": true, "plusman": true, "rdesoky": true, "sopepos": true, "spanser": true, "tdreitz": true, "xfloops": true, "yanghcc": true, "yokubee": true, "aidenzou": true, "anhulife": true, "bouchezb": true, "dekatron": true, "fakefarm": true, "faraoman": true, "gurunate": true, "hkbarton": true, "nalindak": true, "petersun": true, "pnevares": true, "rochejul": true, "sibawite": true, "space-ed": true, "suemcnab": true, "t0ngt0n9": true, "wkaifang": true, "wuwenbin": true, "xiaobing": true, "xiaochao": true, "xueboren": true, "yashprit": true, "zuojiang": true, "ambdxtrch": true, "bigslycat": true, "cb1kenobi": true, "chriscalo": true, "chrisyipw": true, "edwingeng": true, "fgribreau": true, "guananddu": true, "heartnett": true, "i-erokhin": true, "jhillacre": true, "joaocunha": true, "justjavac": true, "kelerliao": true, "largepuma": true, "ldq-first": true, "lichenhao": true, "mojaray2k": true, "myjustify": true, "nice_body": true, "noitidart": true, "thomblake": true, "tylerhaun": true, "axelrindle": true, "deepanchor": true, "domjtalbot": true, "franksansc": true, "garrickajo": true, "giussa_dan": true, "iainhallam": true, "isaacvitor": true, "kaiquewdev": true, "lijinghust": true, "marco.jahn": true, "maxmaximov": true, "morogasper": true, "mysticatea": true, "pragmadash": true, "princetoad": true, "shuoshubao": true, "simplyianm": true, "a3.ivanenko": true, "aa403210842": true, "adrianorosa": true, "ahmed-dinar": true, "aicisswolff": true, "anitacanita": true, "davidnyhuis": true, "eserozvataf": true, "flumpus-dev": true, "galenandrew": true, "jasonyikuai": true, "karlbateman": true, "louxiaojian": true, "paulsmirnov": true, "shangsinian": true, "soenkekluth": true, "wangnan0610": true, "xinwangwang": true, "battlemidget": true, "bryanburgers": true, "hugojosefson": true, "nickeltobias": true, "shaomingquan": true, "taylorpzreal": true, "tobiasnickel": true, "toby_reynold": true, "ferchoriverar": true, "highlanderkev": true, "jian263994241": true, "josephdavisco": true, "stone_breaker": true, "willwolffmyren": true, "brandonpapworth": true, "sametsisartenep": true}}