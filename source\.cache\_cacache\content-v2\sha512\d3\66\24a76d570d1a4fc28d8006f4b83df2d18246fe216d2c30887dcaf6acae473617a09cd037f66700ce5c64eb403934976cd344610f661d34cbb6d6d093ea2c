{"_id": "@puppeteer/browsers", "_rev": "61-2a8c6bb8794d8b3ce59db4917c41ae0b", "name": "@puppeteer/browsers", "dist-tags": {"latest": "2.10.6"}, "versions": {"0.0.1": {"name": "@puppeteer/browsers", "version": "0.0.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "9e2adff6bf3a3813b656a16a09749f047c9ae7a4", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.0.1.tgz", "fileCount": 106, "integrity": "sha512-aK8cK/avc7J0ExRKn8zb2+B6JP5jRGSJ23xNpmNTPnQiQLy4nn+w5Ut6lwYu8IJPEBoSD3yzzPmDTMZwFief1w==", "signatures": [{"sig": "MEQCIBLtwEtaLHp072zO7EsUV6UuYB5GBJhLTgDdCdIZxqZaAiBjAOXvP+pHX9SMnegzytrrs6Kco3WB5Q+owD6xljZvTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9e/5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrefg/7B8trOk/LOAda/u0zGA9TWGvH/0h2IQOUNLJ6RzGUSpAf49uw\r\nsss7B+pfaB8fB7uMuusN/I5qlI0wp3bT4KS3WkmLeySniBxgWQ1yhkg3Wshp\r\nQZtJPEMvZ3sjd8TvdInO3QTjtAgP+lnUu9E/iL0rwCmaxgF42GMUWOmcaShY\r\neq0GLdpbLWJtyVkFVnROAIltGkSTHv5tu07339PdYHJpKuSBHaXwlLRYgHXy\r\ngetqwQMrz+5TpECplSHX3RBrf6cHLGVrEQ/BqdstXo/8Gt+ifRP9QRjKunBK\r\nqtEjtlCAHEEjnnZd0tcftqWT6wP2EBTUobyqO935koOuPdtm+LUQR3cf3sNc\r\nD3IcPqJMRE+OKRRa6ErC/FwIZ4BrPVoYgZEQsWDcq9uFY6UqnBu93mWFWVKs\r\n0UnhYrBjNdOHi0W7/o0G0BTXtpU2RDxaLLW3jxIoqIhECfJmu8UVjHErB6Q9\r\nSEwFag8mdL92t+95GiP1Cdca4Ou6L1F4UQ9zgR86Ex+crcNQNgGG43WJhP9t\r\naXnhEPt0w2LPDU9EaMPEJNSsJ+7jf1Q1mu1EFiZUmuGzgXtXPaFeC/ohwWhg\r\nN7UVo+APYTV53n/5310ilisXqKzFyemin9Gi6JX5vcVam3uCxLholbowD3bd\r\nPLbS+QmSvPv6PTvxBrLf5xBZJfpRzejI4H0=\r\n=+5ho\r\n-----END PGP SIGNATURE-----\r\n"}, "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "gitHead": "b5f708cf3f08adbe2de7010870b7ca138f26745c", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rimraf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.3.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.0", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.0.1_1677062137128_0.1256787891036799", "host": "s3://npm-registry-packages"}}, "0.0.2": {"name": "@puppeteer/browsers", "version": "0.0.2", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.0.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "c2eab2ec024206ef2a97399d31026cea7917082d", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.0.2.tgz", "fileCount": 106, "integrity": "sha512-InOH8iVk+dNd1C2anlkMqJjFlPCKNfyJ91e6IR6t1oVyREzKNrAjA0gPkhEX7szjr+FUtSSXloWj8ZGr5gdw5g==", "signatures": [{"sig": "MEUCIQC3IakWaptTXBUrE0IY2/r0SjyzPp8UL15Mh/kMRk9vLAIgJEQoZPynXqpnj5+g9dro0x7rKJ++deaG/cVN+hNRQTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9gkfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKuA//QwwH8ZUyqE+AUWaw1byeja/tuVQhs6RklUOUayZKoVeTc4bm\r\nFl0nPHUhzza2+U+9EJwHBKtuQKz3u8ATQm4EUxsOwyeYKSIP/0UmTK5iTxcu\r\nABRyofTMXXl9wVdpz0M/zYI5KKk+s9jwLeUkQfZI9lpbMvZdSGjzVsSBBU2K\r\nqeLOlbKUhmjptFH7eD07KYSJDmtLOGGgnIUF5+8QpY2PWjhamgzn7v0RgfAf\r\nj9gBgJmvnZbBDZK1uI7NdWADl5TPX5PlOGt+ObERUaJY7r+isgEfBONizSf/\r\n3QkJhK3sSNFdN/9p5KQAGODbxngCG2QR00Ub6mV/c0+rETqzsGTGgRbLQp3c\r\nCuTRbdPfpeEaWAij0T/mpEmQ0D7hHqcYL29knDPZq5zFv2IdTaCmj8vjbd38\r\nsZ9LrPdo5zb+cpqUeFVCQQjFa05GemVQ/BH8H7iVu6yK/DVXKAOOgniOklHX\r\nAIXdx1SVPCW01YwIc/+QoTaDU6d0D2HaRge4O6XFtDW5oKrdeAJUBO0oVOYC\r\n1YFSPTAw7+vSXJGioI1YXS92XqiAkLwZYZBhhV7vFdVsPsmwwMW7KTnpsFf/\r\nBu97/CFHZS2ae8Z4hY95RgHGpQay9bOUfnWvQ3f8OCuQIgJOGnOageTe7Ar4\r\ntdXz5YDXbEPmLN4A2vQxWzp9QPelgHjGX4Q=\r\n=c0Jj\r\n-----END PGP SIGNATURE-----\r\n"}, "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/browsers.js lib/esm/browsers.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "gitHead": "dbd41b554f0063c380130e43f1aa65de653a35e6", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rimraf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.3.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.0", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.0.2_1677068575477_0.659901180828564", "host": "s3://npm-registry-packages"}}, "0.0.3": {"name": "@puppeteer/browsers", "version": "0.0.3", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.0.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "c3a9d3f1924b7c5a94c60737579b7d8f83950e67", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.0.3.tgz", "fileCount": 106, "integrity": "sha512-HhVe2BYAgdCpfZtpLeqbPWqDocIIQ6ac2NBcX+gKpccfkEkdOJ+2T0paJAliqQZ56x/Hly7mIsBzEkTgKzXZRA==", "signatures": [{"sig": "MEUCIQCfUq046gGbeBoO+pLhokkQB2K47Bvy75hpuXTfDpD0vQIgc1XzgxfopEpeZVohtjcFNFj8orOsZvVDg2lNXfF3GzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9j8iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYHhAAmLma2Z9+QLBwHUPVIql2Zx7dVi5+5ZP0igGKOmPucOS0LBLw\r\nquH8c87d8tNh8CiySk2Jiv2l0p/z6/0N93wiencTK9j9WQo1NsKZNlpZGiBP\r\ndpyW8GwRDedAriiBgjUREvY3w4mJGoeTjeOJpEqBVYURhs22+xny03b+r0pQ\r\ntO5lBu1iauznPtx/vJKAai6qDt+5kQ0Jes0wgzjyWM4y3YtPefNpAW5cyDCW\r\ncwy9klisd0M4Ff3Uyv1zGYpdtm3mlR0NXTWN9mEM8sj1TsnvlHdwffCqc/h8\r\nHyaFwWFH/cm1SEV0THdLRfd0gCok2wxbM4YkqGdIgGGTFErBzxDIXkjt0Noz\r\nFXUV0wb2cOPdyZV0YDOpva+7XCK2bUMxANRQaxXSgZL7SOczzeWaoaSE6s06\r\nxGHxX/AMOdvb2DD5vqJUVyrMrkM4T4b3CyIvoNrjg8pgUp6OpLaDNG/RbwmI\r\nlXEAjDoCTtivh8vAKJtgwrZNPW7xSItz2TBq8pAAATZzOgL9JltZxwRDVdIC\r\nLwwbL3c4jzPAo2J2kMgH97mdFPZJurFv1LrF0CPyTxyBl4VaJJPSrEX5lyhz\r\nX70jx/DJFfssh6Nv2bRY3w2Fdmm417/dLB7b1YQHeOqKpRHGWjL0j17tqYeo\r\nOezbF2Scaoym6kAOuy5Vg2hXdGF/xamEkQk=\r\n=xkGQ\r\n-----END PGP SIGNATURE-----\r\n"}, "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/browsers.js lib/esm/browsers.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "gitHead": "e7138cf99ddc379a1e805234017cb25641a7aa2c", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rimraf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.3.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.0", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.0.3_1677082402774_0.43806692918257983", "host": "s3://npm-registry-packages"}}, "0.0.4": {"name": "@puppeteer/browsers", "version": "0.0.4", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.0.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "5a04d7c9881481f7ce1c2f5043f0f26f09078227", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.0.4.tgz", "fileCount": 106, "integrity": "sha512-jD1Ib1aMH/fq0CPIg741qGKiyymiZ2KKT93PoXZbmDhBNRqhqP1ScLfhZmLuQCFJMWTZ2qsEu0pnCZVRgb66MA==", "signatures": [{"sig": "MEUCIFfyOb05apsZVqa358k/kuDFmo0026k76gJ2rSQMeF84AiEAnLVEOWWsuatbQNyZt4fCQM7zDaZ4FHHo8HQJ1Lhy5JI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBfEJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqqA//Xc7ZwM0sr0HfAHq7i9gtXK4XdV+/9D+RhnREpTwza/QOMmxQ\r\nClrpQ8uHpWLp27kBHkSlkSO5/GZ5cbD8S+pykIztOVm+v666YrnhD8t1o1Qd\r\n+SudFudOLLinULaoVv9VvivRkkDN/HT2hERkPshAvevmoZCkGzC6ZpUaUh4z\r\nqBx48A3SBY8yEysfTHJ+Dy6Otn1kkq1ae77Mm27a2k8vzAn7K1YemtNTzJHF\r\nt03B2pjRi9m9Px3A+IPucShnCXYS7ZhVNpyezsyQOA9NgSBFipzhmoThhSty\r\nGuQ1MHT+lQu+PN3mfFdYsUOc/6+vZosIoY9V7+Z0HblNnSi7MS3Qa0dKnvEG\r\nTs2ZvqTWekggt/LJdxbg+B/2tfIdLGwfs4aMhljDgHOA38w1DLGzyXCPXjmy\r\nGlGdwJe1/igIe1XKbCS/IdkY/hs+vvBI4g84Tek0gAUJwwrOLArqv3FTIO3s\r\npJeEQKyAqxiDXEjVC1EEf99FqpSJthVFIsqq1MSqGuJQKuWHVQRlgh48WbdV\r\nj4ZnNhzUBcWwHyc9t+bgHOP+VOchK3fTwxwJDyw/pnUIwPypgSawzFZq7fwd\r\nshpWO4k2e8hLS0JF7X/6NCEo8jxbxUj69wxyq41WYdn2k5VLjJSjc4Djdijk\r\nqb32kK8ttOYpiJAOHc1zHpYMZDvakx/m05c=\r\n=PcM7\r\n-----END PGP SIGNATURE-----\r\n"}, "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/browsers.js lib/esm/browsers.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "gitHead": "101fcb994ec1af0c4ea58fe0a0c9b7812cf7a812", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.0.4_1678110984834_0.4388942173304462", "host": "s3://npm-registry-packages"}}, "0.0.5": {"name": "@puppeteer/browsers", "version": "0.0.5", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.0.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "7c009ccce4d960e376501b5c9d2fb6c9cc71e6c0", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.0.5.tgz", "fileCount": 106, "integrity": "sha512-aOgzcFCR1YGD9eVuZY6+/e0E2gALQbX8f1lwicfUbljwtKPEI7ixnnFJOlDFXShnR+FH5zGzIn+dxTNdRewupQ==", "signatures": [{"sig": "MEUCIQCwq/IctWXtP/EdVQlCaU8atMWWT+vHAAKXEeJsboDMLAIgO/JGM7M91IjQJ+qP5y6C0hR1B7JUsU3AV3pjtTraDB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkB19yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotOw/+McPdkjZIg5lMFVci2BA8seVDyZe4XreHMuRGRi/kOSLTC97R\r\n5iFIqd+7wveRAMgDMRtX5RqaMC/PCnQUENiiyQDnQFl/uhyaLTaP1ZvilACj\r\ngIWvYF94wVyZZ9a7vdmGU0yqJIktiPXrkwOrPL9PwBsXBjbKgIGMBIs4e+xt\r\njA9aCheVt9EJd/mGyc/rnUT/I419k3cyQlSLlnZkHqz5Hh5/TDHztNjDuGEL\r\nvGQAnwL0NLd6DhoSc4J1zML3M/18CE+XYDluLiZJJsb55OqhGH2gnHjwAqwt\r\npcVWJ6I0ijRCcWwSDE7pan+5wbg39L0o5vtLRX+ZcoM1YUJuVnaTtLLiJMQh\r\n2A405RiZgJ0GMgRrlPKL6FR+zkDPNXFRAha10PWWYN9ZSPWpmQALeixIcLBO\r\nqkT8zUiQ2H5ZaCJ1WAz0e3QYVru1ID5I+lnfF8aNaUf0i351saovVAecHCea\r\nxmUQs1GjSvch0YqZHCeaKZbs2xVdCJT5M1OlXe5qeUFIXNgHgryr+9KxcixY\r\nZ+PNxP+5Cfy0ioPiOrBBkH/QuV4VIyE3Et2IokTWjcCOYTgsW6+kpecr4N7h\r\nTVQbp7tx2fCMLc4uoSBhV1a3sa8ab8JNKAnzXCQZ1lp17A4MlvTYfSCFjHLV\r\n3Ze9eoSkh3ae5XC3Xq0ncyw1zUwkngak5XE=\r\n=4JwR\r\n-----END PGP SIGNATURE-----\r\n"}, "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/browsers.js lib/esm/browsers.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "gitHead": "7eda506c85c630a31fc97c6027e0598964e18f86", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.0.5_1678204786267_0.913237161120324", "host": "s3://npm-registry-packages"}}, "0.1.0": {"name": "@puppeteer/browsers", "version": "0.1.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "7dca9184e7b4fa82b8491831ad8ef47b02a2d5ab", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.1.0.tgz", "fileCount": 114, "integrity": "sha512-xG8kEl7NJyVSy4b8IBQ6TUjHUUYFpd9n/J6ACcNXtw+qo264JLm7TA23dcMWQWf3LwVh0rJoeIKaDC53DwxmqA==", "signatures": [{"sig": "MEUCIAHMkuW6Uh3ioTERRq7Zd/GEcm/EAfVZpV/8mSEdCeUNAiEAiLahIpG3LCrI1cq/kLsKbPtyWK3AfEyjRTlEyq4A8qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEG/5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEcw/+L28pwORO70C/x7dsyohhr028a4MwA7Gdn9kehal89lLsQ0qy\r\naXMn6nj/5WSYDofbztVHp9fI90m5dRKeE9bAk+BJhdmSN1jkZJ9AlNj3AZOq\r\n5mCJ7oCsK5RyhTN1K6MybxjEHdnvwUqUybnB70/pMyQUPZnM/YatG60ZfeWq\r\nXOHJlLCD+QyP23NmDGl9tnFV93uOP98mUXjezHMj3snYUwqytlKW1BBs/u/Y\r\n+2k9QYHhKrpDIDdsLESuP5EItWFa3aJ14KFIzoWk0DwOKNqPncTlZgUv/EIx\r\nPlAjxD0/8KdAWcTaVOOlpyXWmZpowX1Pjv7VyU2/BAkUVgTZEWgFHoVqfDHf\r\ncYzRepW8N+z5KxmxLrHxjfcmbfJMSrDFDvJMYtVSFTQ9LEwzBnsD1qkru2Ck\r\n6lnayjkUGDd6ztAIrG3m46On5J3BmTe/IeXDGvNQAyopxBvfgVo9Xle+At3F\r\ny56o0XKqQz9BMLhh6Rv/44G+UXhdz0l4cL7CMeZw/RftrWqCnLkN3zkPVfYK\r\nWYk0Wp6eJrt5ADF2bsLickKaj0F1FXLtq5KIjZ0pOX+lCX5IOyDBzVcQoxoU\r\nEgWBMyJUr+7Ym1wMGCnMd1iWeq5FQLygTaucpCCjnSrqhFagsLsBmJKg1GNd\r\nekOlGf46xdf5EWhusIa18GaPo7qX/DgPpT0=\r\n=LMSv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/browsers.js lib/esm/browsers.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "0ead82e29e5dc3fb56d03761c6463720a1e7a644", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "rimraf": "4.4.0", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.1.0_1678798841178_0.3889144152913351", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@puppeteer/browsers", "version": "0.1.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.1.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/browsers.js"}, "dist": {"shasum": "cc380a33bbe3b163fe795a45e653c8a85dca2e57", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.1.1.tgz", "fileCount": 114, "integrity": "sha512-MX1nkPlP3fOblpXAEhhKgz+RaMzAmIw6tDsqEJJl/2THsq/l/vUBEgimVGOLNuRMu86cz0Hw8nr+7k0nPPBFpQ==", "signatures": [{"sig": "MEQCIEDtPtB+b9sf7b97dhcWQ4p7ofwKryN9wbH1eJiReNeQAiAsmWDpyd793XXisRPVi5Bd8ZVlqsiQJSvSHpR9uWlUMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEL4vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0eQ//QSvs4+kLRcdDNiSVZKV0vLOj/Vh+ziMT0yhLXzl6MZhnb0ok\r\nNQu0lbgS/3RE05whp5J/AaFEDUoos/EISMsbfqG4URnkULgVRMDYCbiGGQJH\r\nmDzdBIuJ+R3Ui18jAdDCsetwU0XGiTsbslLTU7p0a5WV12mESMXRQlgmDqt+\r\nH0zpFApNeB+J9r4i+YcjJ/+likFH7s6aYUfQqFKgHgElPbUU9R7b6qq9fFn4\r\nHDV7D7C1KFyFeDo8v+xRDmvpY8SY+I1mE+HVSI3+HR/52jr439fqnaCtesWK\r\nH/3rU4gpUukUFSXXWe4uu5HYsmt6HbSPcWzm8rSY3p+DnokPbI4rAlWIm04P\r\nvS6fylrXunmLuLREbGRlfx0j59R12u0ZBdsK89DDVCquSVTzJSNzzFpPtc5n\r\ncAZgbfnwNlx0VX43InTkZKTG9QDfpXWy6tx+d4f7iI5cMv7tlKyaX1Q33zGQ\r\nz1sAKk06Uu3uD2ZECAfhiIwrqChhJGfYllbv3XGkujiQSpO7QjmIYPMv2OJi\r\nnBMVLS7wnTuoTHjfNhIrOJzBTER9AnnQJhcTY3RPmADE2XVIblxmVKbwexRb\r\nU82UH4XwJ7wUw9BROAdfCyDQFC3y+ScwZnPxCt6d4h38XfinhkMiWrLGtKlf\r\nFu2jV/4nC4RiTyEtviG9NlrTCDK0Aia6/yk=\r\n=WK5w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/browsers.js lib/esm/browsers.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "29aa5c41aea153b26e373e7c4abf85f4e239351b", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "rimraf": "4.4.0", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.1.1_1678818863229_0.5343728351328303", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@puppeteer/browsers", "version": "0.2.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "3729aef57ac8d20fcbee364bea4a55835291e4f5", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.2.0.tgz", "fileCount": 114, "integrity": "sha512-9iTfU3LPuVXS7cjFvWJNZvKHzjInywDE0++ezO0PgajeGNrSFFzGmShpPavCp+xAXdZXLWaLfnd5LK2pIkCUHw==", "signatures": [{"sig": "MEUCIQDI3YtJntDSdCDFdLhBb6vOz1mC4zeSeHo2y2EF2tda+AIgONO6f2wGmTyyB80+jCF9c4iWarf/sptoXNU7RlOG7/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHXpYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp6BAAljsAml+mVEowNb8PChTBPi6LAaqTvX/rtw13MlRbD3AEnymx\r\nE6ZaeTMHHu+mdsDvCzSWaKKrqS5pVLjQx0Qv3BCkmSHrre50kDQCiaq6hA6k\r\nH86TekgVWkJaup0BgjYrtQDkxzJIKkGjOcNEE8+pq+gVUbvDNx5XceUdcd09\r\nYX6OJMmvzSM3JU3DGfvcVz+ltogn5vCwozYE+Zz8RH1s0xfLgL+1TsQYq9kG\r\nlY9isGetD9FjFlnT8QpgrLDwzQcJaHxm/H4tO2Ho+eZOfwC1Tnr4/bVjuEJu\r\nBgZeY9L/3b/qXvZAi8iQRe29StM3y5tX2JdgO8l0Cgcvi8RLT1HWf4ynKU3w\r\nHc9ExSBahZ/Rp1e8bV9T2kgsQim1J7YyrJZ/xhR1zioxlfXuMbaMSHrIXP4z\r\npJAnqZ2KqhI9nvTwWUKxOIMrTtS7+4azppSp6osa/TjjZr97tnXxxBVJO35y\r\nAXGxx+/ySpHuZszJ3uOPry9+LKSbWrkiflYiIRB43DXQxDxKoM66COfWfVou\r\nKuBWSIL6JGVYuTUHYMg7yqy43xnnttBB9Zty9bGf2YfyPXM2jpYrV9CWoGM0\r\n5N4J0cKf1OG+vn7cWzJM8dnran6rqRURpcJrHSOypAkD1N2RxX+ywbSYc981\r\nkcJ3q/SVbC1KrTMHsTYzQyZsO9q/T+VtXao=\r\n=pY65\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "dcfec6dbbd55cd0115606bbc29fa197385cd1f5d", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.2.0_1679653464675_0.8317707686712368", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@puppeteer/browsers", "version": "0.3.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "6139f15cbfc0eac249b817f33d0198840ce9509f", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.3.0.tgz", "fileCount": 122, "integrity": "sha512-/arZ+avSn3SlhgqzbJPCdFWj8/65f5kZrBM3UUkGIJZz3inVMvPTXKfb1A6Ynce4ESkNAvPKlD6Sjpb7tPF/mw==", "signatures": [{"sig": "MEYCIQCQuCXDn3O/mbKeCuHElYIJ0mxH6UtuYX8E+TT4yxhHTQIhAMMvWHtETy+NDPxt58eBfLZuN52XVEjyv4dwqXbPg04s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 292181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIXJVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq67A/8Dah/KaszAWyKZyf6Sm8agWYOPe0NIfzItQ8J/4e+LsE1iQ5T\r\nXd+1z8tRM+cZbRrNlH1ENRL4uOHixwm/13rFTC1idJ6poYQ80S9IaW6psxFf\r\n1I8Q+c78BS8lEMwzD3CPiIp65jGUC6eIFMlArHkquTSSxQLoSR+KUpIGOrrP\r\n3J9oAxzwVGtfkOIE4YFB+GlrJifcAK4Z/IYlQhykYTwKVjDUM2pyCd3JhoPA\r\nEL26XUZLpJHccVcRjT6nToMzXwggIwL1ZX6UjfjOGv/lGT79k3+CSEpixfjl\r\nlb6tgb0q+kVs3h7IbuUnO4xUxHS2aWlDtx99i+69jYzii8JMpHFrOz6sPBaS\r\nlPBVcyxYlyXwGt5fKrqVagrCxmUf/4A22pKJTC2IR1UKfXkhAHtRGyyVcNyE\r\nzrM31TYVmDaD+Lrr2oVeqOgmOA6/jPjOBDR9HwZgf0cN0ofqiJXbodwrLoQG\r\nc6r8f9M5snz8ryLX6HKwZfH5D8buFs0xfaDpWXGwUI0t61P87gFJOtkWmG6P\r\nWjZZtsQWVnniK+7UFBHzM5WM8nyPXcE+smCu4bYTm1b8nGi+moNJRXqkBLMt\r\nD5bIIOOyjPxzhw8NXpXA9N1zM2DgWWdaSqxOt8/NGZo+3wFdMgW5OimwYbN/\r\n8uaS3NfMBGpOoaJ6J9gf8UDUGQS2rTW1WXk=\r\n=Vx3m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "16bf0dc7985e3c0c8a7b02f95386e1444c76a908", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.3.0_1679913557126_0.02901697861064001", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@puppeteer/browsers", "version": "0.3.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.3.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "21d43c49f6f8bb5fa905b322d73c11fca9b515bd", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.3.1.tgz", "fileCount": 122, "integrity": "sha512-WTFVqWY7ipI+CsJRb6vEddYJ/lMl2bHYP5BjFDBM9JMKPjJNT4psXz6DLuJpi3EkG80Q0ef5Azzv3gKu9MvMUg==", "signatures": [{"sig": "MEYCIQD2PKEi4BENKz/QWyrKt5vax4IeMaHeARM2fmVptHbv3AIhALLdN5R0pDSnU0BXkAbrAPzZef1s3634gboTTCs9vXKx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 292977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJB/0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjKBAAj6ThsgOAhl9JeuufUOnYBx7prQoQERI7MXFndFxDvD/zEQa1\r\n6anNhQCdRRnmcYf0b18+Y2U2iik4NUqXuX+n7KbfLTxMsk/cVVS65UaV56kz\r\niVx5LED5XTTs1RrdbpKAyUeoBv/mBRgkL3j5bVFpecYag1e9vvvJTCORLNg8\r\nFWD3fQ57pVv2XlqOBxsg7eWT1LZ4cEcBBQWmKYEir1xS4PJ8oXbbcqzBHXC2\r\nUBlLD0bXtiYhWJoVawXyHGy2OXhBNMGv7KBmOOfYKVnaAunxwTCBk6Ml/2DX\r\nPCgRoeUylFAC2vAfcxbWmzo0Z8PCuo02gM34Nmi80tc3TXnLHuV0BqXb+1T5\r\n/PcUqkfZF8pozUVHJxD1BkQckTgsSgtYVDOL+OpophF4JH05LuTzaYZpUGmq\r\nBspLmj2MbF+SCHhKTD9FWomoMKkt9lJOZijkMc9lC2/xeSC5lQTRo/wZzkhb\r\nZYtWju/4jRY8L7tS9DdZwFmHp1FhdgCrq9v3X7O0VFiA2ka4vzvKl6El3Rzi\r\nLjjav3aNAGbJfaxrT2OIR3tbZDzzpAB2YiChkucADuranl/NZeUc3X+IJnvy\r\njaQPO4zbBDMVi+E+GXD9Ho5kzdnXwZm5otFU4HM5fvRrYdAPf5eItGzPjsF5\r\n1xNoUSvkYtyZ+AVtsHWk5B/ncAeCCgG0TEA=\r\n=RvAt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "8d026042db1044e0720491127e164f801e3c852f", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.3.1_1680089075769_0.5504833618963967", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "@puppeteer/browsers", "version": "0.3.2", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.3.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "1e57690943a574c9d7ce823503e048a1df6c67b9", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.3.2.tgz", "fileCount": 122, "integrity": "sha512-YAwrqy68PskkCDnSsv0afR8l8u90KhodcIFXCage5NLAoeWm0STGmtVugLBp2wc5PnhnCapCKkMjp9fYjvJl2g==", "signatures": [{"sig": "MEQCIE/YrYST7RwsMwBYcOON/6GErKALDTPiEpB+lNsJBVq9AiByMrQxl1Jsg0NuHT7kgmAh857cLcLjnAhNJgQwP0JgFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 293003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKo2EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpr4w//alCxNm0aqSOBz+z7a52Vq5GZsQKKxs+kE6iVl47Ko3e3NxV+\r\nTmqHoJweZ0ii63sU7bBClyvs8bcLHLzKG0yZTiUzTnzCquw4IZfXvw4zcXpY\r\nYBJ7y70oFq20OVg4W1e8wxC8Zb2Yw2gy7RRdPMWvpOc3TzbtOnB7RJmo1yok\r\n38NwetZaE+XU9PdjM4p1NacZz4n1YTtxOuiAjbS2dO/cl7xEIcukrgxr09Lu\r\nXPhYFigu2co6zVAIqa1/5efNVQirZR8OsR2mAolSb9pzU6bUMKGlDyF6du0B\r\nAz4aS1ONMEPXKjH0vBlnyS4IXM7CgHkAZy3reCF8ScFv0fdANrj10wIjIrfO\r\n5Qq6wxApkMFBARs7K8fGLsm3f/N8jxkLgWr45qfRAKmrpfr4UdPTk5wS1cK6\r\njcwwX/QNg9EzRyocy+0558ymFIrojch4l1FR6vkjXB2w+LW4F2iY/QTgMMsH\r\ng7xUjsbeVc3munBRGODN4XwBQYcmRtgboNs2h4nJdP1SV6NsqFo1GvFF+K68\r\naG93ryDtzvSqR4rvsI/fmjNMp2zoS6tWGIs1e/G691/wW8N+q2R2lVqUVfvT\r\nZhN1cuiJZiEEAKq5Igdu9zAafndiaO+DuFVqtQE04DBwk5b51MKA4mNOtFgv\r\n4mYtkp2JkHNvUmyPrI9193lLifr13BJOogg=\r\n=e3fL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "mocha", "dependencies": ["build:test"]}, "build": {"files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js"}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build"]}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "006086bc836e68cc78ede54c5cdabbb1f84005d3", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.3.2_1680510340575_0.09120131300796475", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "@puppeteer/browsers", "version": "0.3.3", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.3.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "6d4aa292ea2e0e17322a4ad8797e88637b853990", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.3.3.tgz", "fileCount": 123, "integrity": "sha512-RonqAKLTDgiYpC6uNc5T9EwPFmbmWmi1Cm8Ak0iukEIIVvDwHn8UG2KK6gdkD1snrVCO9Y9nbpJ9c4v/00ztpw==", "signatures": [{"sig": "MEUCIAc1zJ/zhENZ1D+NelHsMTD77Io0foEOR5vLjHMh64HjAiEAh8bwAXXRvj6NAVnGbJOWEdGe5faF6cCwlK4jCTzHHTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLtwhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjHxAAmDrFCwChIWTeO8FSOaG4MNSEoq1oYjcv55aK4B50XGEycRhk\r\n61XZwto93K77IuuE9Wu8DJxnrZ6jwLyivUBwyfC3PSFJ1YcYjgckx+nzM5MQ\r\nEGlR1lwhMTHj1v2/VR2nnpjqWPUPpgYreWbEgQTsPf1tR6Qu6AF5DptI3/QU\r\nZ34Z8KsMqZjq3e25B+qRc0RaokvraYFPb8KjJU1uZhZs0hBQLjuZqJIustAc\r\nfkZRnsuSmNGLv5m22hY0OJCxkR90l9xBgoI12lAM910VWj4/wrC/E7dP6eVA\r\nDcNkemtaxFUVu/cb5D5rtN7YCYbHTdV65Q0PsjnoJ1Db5XVIQPB7MxMeroTD\r\n6wGEc9ac4d8+/dGIvNRzk2rpBvlGi3NlczfaXV5YCdbUHbqHX0hi+Xr4syO+\r\nxXXkNE4DUspXoyhB0Gedvev6RNt43X9sbZvRW9+bjvzPNS/dmHksC/KgqMTR\r\npCAzjOb5bZnsi7bszv5olmo1i2hB9z+VXlN+5jm5+7p8cbn87GKDLw2Bj5Mw\r\ndfMfQUa7gukSc95DslIy4LlkR4sFybuzKjzHKk6Iq35LW8CkUQzplbzz8ueA\r\nduqGskFVoIsAjEVJbv7QYlPy54pTg2aU3u8usPD7G47ejz2irZ5YCgbcVMUX\r\nPp81ovPeORzHU8cZjv/f0/vLO/kPG5+E/g4=\r\n=4OWB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "c807fe7145adf8d4028dfb2327b1617576c263bb", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.3.3_1680792609233_0.14455960378034227", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "@puppeteer/browsers", "version": "0.4.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.4.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "abaae1eddc9902e1ea441e34171229bd7644ea62", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.4.0.tgz", "fileCount": 131, "integrity": "sha512-3iB5pWn9Sr55PKKwqFWSWjLsTKCOEhKNI+uV3BZesgXuA3IhsX8I3hW0HI+3ksMIPkh2mVYzKSpvgq3oicjG2Q==", "signatures": [{"sig": "MEUCIELnpRqDSxTJIAqvzpUGhIfmkYlvH21LImWLiEvpN8XfAiEAqsX+T/o16GubBKwKIFXvr/TPq2rg7ql6QcRYe3rbADs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334499, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLv3PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmowfg//UDz0xGMzll9IUOtRrvDx7rrUZ0Xh0PMMMlCfADsFGInmP/DC\r\nlthw9kJyiJnGbKoKnxIUOy/v2zbeuqZ+0CJqDs5UWAN40pmylMUh9ArgQ1jp\r\nUqN9uJ5IgcoSgYYrJzilXlQkShaHDUNF+P1deNfVilf9e9gqTuR8udJWxGFa\r\nOBoBeSPxE7FCqcYkHn3NIMW/ybd/HRZ9F5x7GpV2ravs6MqIWkgnh34e0LF8\r\nbjGQFmgr9O8DI13l1mCtZb6URhAu19Zpb1u1+vsoYLVskfZpjOxcACehQna6\r\nHpIi0+9p9RWli3e7suBOb1pql5dkF0NWkdI3MCr665MhsLggpfoZ7/ygavYn\r\nHGZN4DWaSUC8BBWXDWdjvwolcrbIQYOPUndaW2NdBu2TEbTwFh81WQj2lHWc\r\njeyzzrTYAMWbXEEeCmxAxiJg2xsMiOXHm8kvNxlyrfBOGD3DdDGBl9+zKqAL\r\nqkKPOyjcEomUvGHqy7IhigqyQ5J1O4H9VdpOzjnLP0qraPxlGKa8CsUmoso8\r\ncTTpoqbprcVZUgPG33I340R50BdA0fhtAYYsTr19Twi0aWOp5QZ2wwYktf9J\r\n+3BJyL6XOmXMQ7Dqf/ABnzjvwP96YmC+YjY1CxSI/IIfnPg59RUKwJkcdiD1\r\nYz1p4cH15fZ2u61DVTinuxMOdAn2PJdK0ps=\r\n=JM5T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "823a9df4429480f597d45368e763fdb05d1e9210", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.4.0_1680801231701_0.20534786287549145", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "@puppeteer/browsers", "version": "0.4.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.4.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "fae81939adb743420cc2466f3aa37481f7081712", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.4.1.tgz", "fileCount": 131, "integrity": "sha512-4IICvy1McAkT/HyNZHIs7sp8ngBX1dmO0TPQ+FWq9ATQMqI8p+Ulm5A3kS2wYDh5HDHHkYrrETOu6rlj64VuTw==", "signatures": [{"sig": "MEUCIB5014rdQBm1k2RjxPFuRbuAztksLIVCj7r8rESiBaCMAiEA8pIYu7BwKiw6XZiJbo15vQu3LsNne/i+TTGDJhKSVMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkN/W4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVRhAAk1XnwlAGCzV9ZwLo+IiLESQtKrzsNeWBj8/rcPXQdI+JR9v1\r\nbtrOBqAJ9Yjw0kGn1eMXtC+ezFJ9VNydmcweRBrM3ohHfPSPhbc8lJ69410Y\r\n2WI1iNcaOIyCiM4whv4dQi8m6H92bGO+Hfjj9+HrjIB6Bui1aN7bv51J1/9V\r\ng5tqyM+PRucisZyDbYAW65ZHTiGBXo6gNCwtdr2TtcABIrr50g8ZZz388otA\r\n7pBaZ/Pag4SVuPFgZRXR5KtpxAD8IVblR+v1rbPiu8snpp1KVCoExrLIm1Rl\r\nkuxYvgLhvJmOPutwAEVFti+/7L71nOK2DRF46p39DwQeBQYm7wASbC52PjsT\r\ngsKw1e52yt6df4M8m2FiROd8P8vkggfwuGWN3u3aNIqZikfyRmL6b7utDsq1\r\nCIVkjVOi0MRcA2gUWTcPslB6Hg6tAw/78CqnDo1WhnAJquVGUy7ZoyeZNdKq\r\nhqzX8Ui3C7tJ3avOU4xEP6btol+TMKAWHK9BEoOltMRldD3EHEhvgV3ZPVUF\r\nlAquLFnl0nGPNOJk4jJZSgrxhP5MBATwTtOk+/we98JuORycVm9kUF6Ac5C5\r\nnEJQpyOPVn6jg42GzXXevypWujCEXTGx+vHaxfDUWuDgl9LD5r181tB1Ha+j\r\nJd+qiCY+r/eINXa7wcxWQU+whdBCE4Js//s=\r\n=1tQ8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && chmod a+x lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "fe0c74bd5215b9e623c3d3e12c413044440c3af1", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.4.1_1681388984413_0.8688861053740344", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "@puppeteer/browsers", "version": "0.5.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@0.5.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "1a1ee454b84a986b937ca2d93146f25a3fe8b670", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-0.5.0.tgz", "fileCount": 131, "integrity": "sha512-Uw6oB7VvmPRLE4iKsjuOh8zgDabhNX67dzo8U/BB0f9527qx+4eeUs+korU98OhG5C4ubg7ufBgVi63XYwS6TQ==", "signatures": [{"sig": "MEUCIQCyBXes0kBe2yz22CywrAIp6iALesFiK44Vzg3Yzqy3lwIgDoL4OWX7J7Mrfm8gsTndmznTXGJybvayzUlKCt+2ew0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQl9vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+JxAAm3RxRFuIXkkDo9DZWfVAOVgDczNSOwLDcgTE2foCgFExx9K7\r\ncdxVcwHDEtondJg5yQ45iBXQZ4trTGgYVZI0SEVlEOVUwkm9P83Q1TP8M3F+\r\nfz/Pjp4WtkTITlascbk5Dp07C6fdVq8N3iUhVrH1ukVew8aAV0XaFyP5/O5D\r\nlveXteBOlw40IBvc4BvqDSxj+RE1RoupEuaWcIP05I4YYJ1I2JpQ9DbB5St6\r\ndDnp/q4wCwg0B6wcvkon1PJlagw0YC7tdUYBL6gNiUBV6lbrYEgxw+/bZXle\r\nb0txKSr2U6PepYqRppZxqjb8ovN/VjZJblm9PKG9afidGnx3kRWAuO2WFIak\r\nezgglE8PdSfuuG3BXvo5tPBcq1ttulblnN8RPdABqouolyOTFdMkn3yNB2kR\r\n7VcUeTykkQ5/2Wl4/pzA2xgDOUJ95Hca/cmvgtG4oQ7MahE6voYWtyk3ALx9\r\nSAr93uu2MDYAoJAseswqncVrFYXQy2pijtHGnUGKNFoAbTeizHmq2gVcfqqZ\r\nXgWFYiPcWwy90nRJEX0o+MjXSLOkucOnpi1IQFUUDlcf/ab2nDO7Be33c4+X\r\n8cnSWQIMU94xhH2D0w3YnEYo59lBnum8ckoeBVDWLoiiGJ+QO1PYr7E4kXzo\r\nfIUsqwAZgD9vrS2MV/yVij1BhtqxsnIQCIk=\r\n=/BQu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=14.1.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "b64953ef372d98b94c29db4327924a04ef0cc6a3", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_0.5.0_1682071407052_0.4728040052640259", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@puppeteer/browsers", "version": "1.0.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "89de56a718c922857b1d802aac473ebbe1f54d99", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.0.0.tgz", "fileCount": 131, "integrity": "sha512-YKecOIlwH0UsiM9zkKy31DYg11iD8NhOoQ7SQ4oCpwDSd1Ud31WYRoAldbVlVBj9b4hLJIXxn7XSnkH1ta1tpA==", "signatures": [{"sig": "MEUCIA+514Oi5K3OjsHP1rLqY6GBift2cT3RiprYcHiGwjQuAiEApH3B6MYN9/AOEeVQrn/b0Bqi0+sEjbcAudEVWWOrzSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUUE6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN/w/+PvjrezIC4w4/jf3VubsM82Oa2Pjl5nCcbJtKT5/qmqOUMf99\r\nFnORfogBuL3ayJyOOyWo1LFN0U5aAhBBC4aAz579UG76l6ZFS5D63rwCuuHw\r\nr44yqHITfUxZySnb+VmGpqgQv8sTI9K8jEs92i7/G5whWhknFwHBSd4Tvd5I\r\nUVVoYhbVihll1YJ494ueChsx5vHlGIWsMrU4X+Rh+XB1McFHnfywDamkEZ1x\r\n6FlGH3doIG2KVBV97tsgVM7e+e7znqSMAXjWhxndFMpD+Mc9Utzq7acm+PDP\r\nnQSv1wN0a7ZuXOVmvWUsK1eWUYtquJfyXJXNpKRAHxU11AsuOpbK+V1+6E2a\r\nYQZvC+hozLV9e2DRqCJAwiOkcEPn3CdRwG/IlHWk8DEBNRGU/qCIgF2ui1i6\r\nUbgbwkytzRWq9OiaQr7OvBIdb9b6Q4+iHUWhBm2Z6Oc3bk2sqfOYbq3QnUet\r\nTrDjDZuKpOrwkG5Zz/eABms8fuHTjmqODv0znZDCZpQCPMWwdaV7BAZhdN+l\r\nzf9OWLE0EAfOSXCvHexxk2d6sHjiAvRyKVOMyqcXCAmc5jkd200Y44rtn9Mt\r\n4cHw6XX3F2dL+6lYDJhola+j23zU2tm1zvJ1ONE2EgM5cLdvQfw5EeTE4Ng+\r\ng+SEa9HuFdpLpvWrpSvDnBxG4jxqMwZPg6Q=\r\n=5tc7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "61605a0ed929fad97195664b2b769a900c059230", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.0.0_1683046713926_0.9880529602062147", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@puppeteer/browsers", "version": "1.0.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "6c2f6fbc93cb3a587c821c3cfce0e49d99b51dd1", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.0.1.tgz", "fileCount": 131, "integrity": "sha512-9wkYhON9zBgtjYRE3FcokGCfjG25zjzNAYmsHpiWitRZ/4DeT3v125/fCUU66SaPJ4nUsxGNPgpS1TOcQ+8StA==", "signatures": [{"sig": "MEUCIQDUnidcnsiTi0cV9TI5hJDSB84BGRE8WuCPoyBWJVTJ0QIgHzCSroPLM0d/NxkFn2E51qF/JfmDv0/WRlzQQki1S98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339012}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "3a6569e8efb0ac5f4f33bba9209e4816681a22fe", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "http-proxy-agent": "5.0.0", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.0.1_1683286093277_0.04434496483987127", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "@puppeteer/browsers", "version": "1.1.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "72342a0f3d83f34547ea655aaf579b73d1a69249", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.1.0.tgz", "fileCount": 131, "integrity": "sha512-+Nfk52G9cRAKq/V2LEaOlKIMsIuERSofk8hXEy6kMQtjg9Te1iuKymWOR+iKwwzj1RCkZU0fuOoSIclR839MNw==", "signatures": [{"sig": "MEUCIQDFU335nRPcJ1a6oT6egp5TRQclwtOQp1cH2ueEXlXbPwIgTWMeKtk8TMxqi6FqT/gpRtfzpy2KyjGZc0TJq1gA3f4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 345233}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "75a50257e0ec2c93ff26fa58658e8b6ba142aa4a", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "http-proxy-agent": "5.0.0", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.1.0_1683559092529_0.917014293463585", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "@puppeteer/browsers", "version": "1.2.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "bee5ce47a5f55e7d68b884bb288136c5bcccc9aa", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.2.0.tgz", "fileCount": 131, "integrity": "sha512-F2ygRTaNKq2HQQGsvypvy2S/Dg7aqqp2zxv4uolkxxTQvdbYfI0DcLPFNdqenaC+rZX5ldSPs/s39yAPpTVZ0A==", "signatures": [{"sig": "MEUCICL9u43J9cIuNFnH8ITMLQmZuy3dG19DTLL3u3lrPHWIAiEA0+RVS9bupvGtp+i3/PZ7Y63Niihu3eyxyVhlvi+PCJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340425}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "671fa546f117f4f86eb3479109cbc3037a4d5e2e", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "http-proxy-agent": "5.0.0", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.2.0_1683834250689_0.27237356138843105", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "@puppeteer/browsers", "version": "1.3.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "5ad26540ff54e8b8fca8ab50d2da9c60360a21b9", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.3.0.tgz", "fileCount": 131, "integrity": "sha512-an3QdbNPkuU6qpxpbssxAbjRLJcF+eP4L8UqIY3+6n0sbaVxw5pz7PiCLy9g32XEZuoamUlV5ZQPnA6FxvkIHA==", "signatures": [{"sig": "MEYCIQDjho5Gh2buW/Daup5Zq/tJlHZK/FPKTGGDcMG3as8fzQIhAPG+k9Y+jDCJ9Pkg6RKB6FmhuRDTqUH3lWRVukapViVB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347466}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "1e0cb1a164ef178f0013afd99243a01783a0d0c6", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "http-proxy-agent": "5.0.0", "https-proxy-agent": "5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.3.0_1684223050987_0.09070952381362773", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "@puppeteer/browsers", "version": "1.4.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "da760aeb13f09441ed9c7c386e01b1061537c6c2", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.0.tgz", "fileCount": 131, "integrity": "sha512-HiRpoc15NhFwoR1IjN3MkMsqeAfRQKNzbhWVV+0BfvybEhjWSyRNQMC0ohMhkFhzoGnFoS59WlrJCGLPky/89g==", "signatures": [{"sig": "MEUCIFpsIcYFYxMgDHGSo140IWRfeXHEpkSplFy69EvvX6EpAiEA7QhPw4iBOGs7zKPOOFn6V9vF4puTKTX8co0SyK3Tosg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 344964}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.0.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "b1308d1c9577c186d0263a919f1370d43e8f3fb3", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.2.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^14.15.0", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.0_1684990437861_0.2453523234010384", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "@puppeteer/browsers", "version": "1.4.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "9c8ba163c3ef77ae3fc9708ad1f5787263f7290e", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.1.tgz", "fileCount": 131, "integrity": "sha512-H43VosMzywHCcYcgv0GXXopvwnV21Ud9g2aXbPlQUJj1Xcz9V0wBwHeFz6saFhx/3VKisZfI1GEKEOhQCau7Vw==", "signatures": [{"sig": "MEYCIQDQy7HheJtUou7G7ljYCULzpd7+cK8AKwiFpGNBnOf63QIhAMfNazGc/qZT1zSQNoB88HaM7AjriS2+tL/rGqyqmk/u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341948}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "5fc136eec1def87db85dd1e3f2dc01f6e2b12a15", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "2.1.1", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.2.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.1_1685538710638_0.3594108554224784", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "@puppeteer/browsers", "version": "1.4.2", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "c6efa2e664369a2e9e930105cb450fcc71237889", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.2.tgz", "fileCount": 149, "integrity": "sha512-5MLU1RFaJh1Beb9FH6raowtZErcsZ0ojYJvdG3OWXfnc3wZiDAa0PgXU2QOKtbW2S+Z731K/2n3YczGA3KbLbQ==", "signatures": [{"sig": "MEUCIQDs4IxcJhzFH86XSBsX8k4EAylKvXSPZh9idf6fLVv1PgIgMvvXBZI05QEoQdrVa0nD7vz6pTyfk6sdBVgghW7Xxeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417044}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "3b09e956c6e4acd0c20fa3f312d9f6e8b5c019f5", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.2", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.2.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.2_1687331767747_0.3997891579889634", "host": "s3://npm-registry-packages"}}, "1.4.3": {"name": "@puppeteer/browsers", "version": "1.4.3", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "39bfd8bf999d707ed2914b036fa2febac2960985", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.3.tgz", "fileCount": 149, "integrity": "sha512-8Jfkpb8qhPQhMsNBmIY8b6+ic2kvcmHZlyvifmcNKBC5jNZf3MAKq3gryKfmrjFAYFl3naPjiKljPUq5wuolfQ==", "signatures": [{"sig": "MEQCIBLWU186aJKXmaqzdUfQFcaJepG69258bwaIxvPjgJWuAiA47byIIVF1ZBoZCBy3JTFARXBjZMeEBFgrcrARcV4kvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417109}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "74f588f1c1ba38794798c70c965d0ef5c40094c7", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.3", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.2.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.3_1688057630915_0.7320522459850978", "host": "s3://npm-registry-packages"}}, "1.4.4": {"name": "@puppeteer/browsers", "version": "1.4.4", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "5f69836541d247d61b9028a4c2bdb04e9d4112c9", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.4.tgz", "fileCount": 149, "integrity": "sha512-1eRHnLFMJTcIdeAIKFCWjAkUmjZtDlgTzTtK0KC8h+Sw3aJ1B3V7IMFu1S5YL8EmO/U4Vte4ukEpOUPnkqL3jw==", "signatures": [{"sig": "MEQCIAefwlDXPJNQZI+DBoEFkiCqQ0b6wYECTGPGARPevpkiAiA2yiFhAtxWhJlTCqyppYLoiB4rA4nAKDyvZwmgFkZOIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 413896}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "36ebd1cb180c4591135960f85ae3325be61b2fb0", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.2.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.4_1689073994978_0.2822588822955725", "host": "s3://npm-registry-packages"}}, "1.4.5": {"name": "@puppeteer/browsers", "version": "1.4.5", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "e80f46dbc34320744a0c44eeb6d4fe3c60349588", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.5.tgz", "fileCount": 149, "integrity": "sha512-a0gpUa+XlxZHotoOklh99X6RC5R+hQGcVcYOH+oOIEBfQXPp8Z5c765XAu/zhxsjRuAZN4Xx4vZNlwN4wJro2A==", "signatures": [{"sig": "MEUCIDl5aGsgdUfXHM7Yo2JLpWbck1hUrXwaKFN9zYusSierAiEAxKxMe8u/bmKIQz4KIWC87erJQGsj5dAIf6ixq7tOcM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 416952}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "52ef96214b4242cb82debfeab9f781c123f59914", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-from-env": "1.1.0", "unbzip2-stream": "1.4.3", "http-proxy-agent": "7.0.0", "https-proxy-agent": "7.0.1", "socks-proxy-agent": "8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22", "@types/proxy-from-env": "1.0.1"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.5_1689232592300_0.6985499468091361", "host": "s3://npm-registry-packages"}}, "1.4.6": {"name": "@puppeteer/browsers", "version": "1.4.6", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.4.6", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "1f70fd23d5d2ccce9d29b038e5039d7a1049ca77", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.4.6.tgz", "fileCount": 149, "integrity": "sha512-x4BEjr2SjOPowNeiguzjozQbsc6h437ovD/wu+JpaenxVLm3jkgzHY2xOslMTp50HoTvQreMjiexiGQw1sqZlQ==", "signatures": [{"sig": "MEUCIQCM3YdzODrDJy5MpECd9oUEraITCAIWLojyiV9ncvLKmwIgPp2XIHTlQTYwnwn4/k84tTuqfzVswvwcn7Os3x2Mt2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 398177}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "03adf4189e9b0d3867a76c45068352ee6f0e95bf", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.5.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "peerDependencies": {"typescript": ">= 4.7.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.4.6_1689838735381_0.39196074914156553", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@puppeteer/browsers", "version": "1.5.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.5.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "2c445f7e41133d4aa23f776748d70211ea4e98ed", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.5.0.tgz", "fileCount": 149, "integrity": "sha512-za318PweGINh5LnHSph7C4xhs0tmRjCD8EPpzcKlw4nzSPhnULj+LTG3+TGefZvW1ti5gjw2JkdQvQsivBeZlg==", "signatures": [{"sig": "MEUCIAELot691XMr+qp25uB+eGCaAxob32H0DaLDqJD7i3gQAiEAqpu7OxIpsfMaP9LDwVmclF3/IiprUscM2KewbpFAWuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 401957}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "9a814a365644a1e404eb36266a08f579bb2c26ac", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.6.7", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.5.0_1690980749880_0.41693103743688753", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@puppeteer/browsers", "version": "1.5.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.5.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "90e26741467aad32faf78021a7fc02e98e18dd71", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.5.1.tgz", "fileCount": 149, "integrity": "sha512-OY8S5/DIsCSn/jahxw+qhCKa6jYQff6yq1oenzakISLvGcwWpyMzshJ3BIR7iP0vG0RPJjvHRLRxbsWw4kah1w==", "signatures": [{"sig": "MEYCIQCiOjJF54vUeHY0XAnFd8uMsacl9cx9s1jWch+iN7fwcAIhAKqSH8a/oqqmhBl3+FIoDIuZQFEHFMWHfHF27MYfMGGq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402089}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "854d488693b8def9c7be3b7fc8ecdbaa3a8022a2", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.6.7", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.5.1_1691503633042_0.7160085651397123", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "@puppeteer/browsers", "version": "1.6.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.6.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "d52413a7039e40a5ef72fb13fb6505fd87ce842e", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.6.0.tgz", "fileCount": 149, "integrity": "sha512-R2ib8j329427jtKB/qlz0MJbwfJE/6I8ocJLiajsRqJ2PPI8DbjiNzC3lQZeISXEcjOBVhbG2RafN8SlHdcT+A==", "signatures": [{"sig": "MEUCIQDlNNqH/9/17ZYBaEgsjvlsO08ZcKFV9kv3ToBTpNHn5gIgHzC98JP2c8pt34xLK2sf/NTGC3v5I/otbX/tHlTGpo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411864}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "6d7ea9e1057ef14e3ccb9b3e14574ea11ac711bd", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.6.7", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.6.0_1691733613594_0.9383950716736285", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "@puppeteer/browsers", "version": "1.7.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.7.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "714a25ad6963f5478e36004ea7eda254870a4659", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.7.0.tgz", "fileCount": 158, "integrity": "sha512-sl7zI0IkbQGak/+IE3VEEZab5SSOlI5F6558WvzWGC1n3+C722rfewC1ZIkcF9dsoGSsxhsONoseVlNQG4wWvQ==", "signatures": [{"sig": "MEUCIQD8PMxiG4L8UOIPic9486aCr9YhcYyky1TRqfKsbuD7/gIgSy/pHwkFQMEASDUvPPptBnRahuDWuprSEaoLoM7LxVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431908}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "de719dbb86ca4817ed4d71eefb49a3b5f5a56edf", "scripts": {"test": "wireit", "build": "wireit", "clean": "tsc --build --clean && rm -rf lib", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.6.7", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "^16.11.7", "@types/yargs": "17.0.22"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.7.0_1692349526805_0.0296602379469717", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "@puppeteer/browsers", "version": "1.7.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.7.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "04f1e3aec4b87f50a7acc8f64be2149bda014f0a", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.7.1.tgz", "fileCount": 158, "integrity": "sha512-nIb8SOBgDEMFY2iS2MdnUZOg2ikcYchRrBoF+wtdjieRFKR2uGRipHY/oFLo+2N6anDualyClPzGywTHRGrLfw==", "signatures": [{"sig": "MEYCIQDdqwYEMfJlUraQLok6/fYsjfUM3nsRA/a60qEEkAZmpgIhAJworep48W6Gmu6SbFx5LI9TBAD9SZOb6cnvFDElUzgh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431994}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "module": "./lib/esm/main.js", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {".": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}}, "gitHead": "a25527a223c004bcdf7ace436b803ab326df59bf", "scripts": {"test": "wireit", "build": "wireit", "clean": "git clean -Xdf -e '!node_modules' .", "build:docs": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.6.7", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.1", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/yargs": "17.0.22"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.7.1_1694601073464_0.3113799645454638", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "@puppeteer/browsers", "version": "1.8.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.8.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "fb6ee61de15e7f0e67737aea9f9bab1512dbd7d8", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.8.0.tgz", "fileCount": 158, "integrity": "sha512-TkRHIV6k2D8OlUe8RtG+5jgOF/H98Myx0M6AOafC8DdNVOFiBSFa5cpRDtpm8LXOa9sVwe0+e6Q3FC56X/DZfg==", "signatures": [{"sig": "MEUCIDzRzK65INzQcUQo1xr/7A5cKAg+dn6Ct2W6LzA+VQ33AiEAgpK+FdDliMPG04yUwZlddL/xClpC6L/Lf9Ii73dsitI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431727}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "35f9c6d1e699ea37e89ef3bbb7940f5599df4724", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.js", "build:docs": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.8.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.8.0_1697797359782_0.7606906041699453", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "@puppeteer/browsers", "version": "1.9.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.9.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "dfd0aad0bdc039572f1b57648f189525d627b7ff", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.9.0.tgz", "fileCount": 158, "integrity": "sha512-QwguOLy44YBGC8vuPP2nmpX4MUN2FzWbsnvZJtiCzecU3lHmVZkaC1tq6rToi9a200m8RzlVtDyxCS0UIDrxUg==", "signatures": [{"sig": "MEUCIQCGvV7ppXQI8/ZU6uKuE7kKR9fTii1KhQSQM4HHvseuEAIgcM30tJvrIH3ipgy8A5ExomJSJGc++rlOVkIEVKPnAf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 441432}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "8c0ac612607ba48157a7123f52d4fe38188118dc", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.js", "build:docs": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "9.8.1", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.9.0_1701850011627_0.6976616742940944", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "@puppeteer/browsers", "version": "1.9.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@1.9.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "384ee8b09786f0e8f62b1925e4c492424cb549ee", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-1.9.1.tgz", "fileCount": 158, "integrity": "sha512-PuvK6xZzGhKPvlx3fpfdM2kYY3P/hB1URtK8wA7XUJ6prn6pp22zvJHu48th0SGcHL9SutbPHrFuQgfXTFobWA==", "signatures": [{"sig": "MEUCIBLpHkhMMm2Nd+Qef92GuQ0LEwQ3NpsjUVYbIhMAWl7QAiEAwu4gLvtx4QECU3/q8gOa1zXkze5J8qb2EJqIT3/yYsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400865}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && cross-env DEBUG=puppeteer:* mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=16.3.0"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "864012a86d73120750369886880d1a9031e3b993", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.js", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.2.3", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_1.9.1_1704375682784_0.11606984369086848", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@puppeteer/browsers", "version": "2.0.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.0.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "3646d2a465c112eac21510d43b21c828612118f9", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.0.0.tgz", "fileCount": 158, "integrity": "sha512-3PS82/5+tnpEaUWonjAFFvlf35QHF15xqyGd34GBa5oP5EPVfFXRsbSxIGYf1M+vZlqBZ3oxT1kRg9OYhtt8ng==", "signatures": [{"sig": "MEUCIBDPsRt+QU9PQABe/tcm1NDP76T/rTATcj58B9uN7tBKAiEAyyAS7MYQHArdaUZMlgyTBeogaEOHnzw3H13F9gz03cM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400834}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "9fbb9f965311eb2b8b59de6a25817448ac785d51", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.2.3", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "tar-fs": "3.0.4", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.3.1", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.0.0_1707128604280_0.7913200150480957", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@puppeteer/browsers", "version": "2.0.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.0.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "ecae91d06cba83c58ce1e748b050cfc93d6861b2", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.0.1.tgz", "fileCount": 158, "integrity": "sha512-IQj/rJY1MNfZ6Z2ERu+6S0LkIPBSXRGddgmvODqjm1afHy04aJIiWmoohuFtL78SPSlbjpIMuFVfhyqsR5Ng4A==", "signatures": [{"sig": "MEUCICwcJXtsTwzJJEvbF4YZOtONLw46lkSLeIUeOU5ZaIAkAiEAlVc1wFCnwyOjm0OvoNc7yf05/wbLpkP8iocwwVPQgeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400798}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "f6e7cbcadef6c6af6ed3a62206c85d690a6004f0", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.2.3", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "tar-fs": "3.0.5", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.4.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.0.1_1708170319334_0.12994174047668827", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@puppeteer/browsers", "version": "2.1.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.1.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "2683d3c908ecfc9af6b63111b5037679d3cebfd8", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.1.0.tgz", "fileCount": 158, "integrity": "sha512-xloWvocjvryHdUjDam/ZuGMh7zn4Sn3ZAaV4Ah2e2EwEt90N3XphZlSsU3n0VDc1F7kggCjMuH0UuxfPQ5mD9w==", "signatures": [{"sig": "MEYCIQD2tD3yt60cyN/Zc4G51win4BYEtyqSz+JjtL4GlXpmYwIhAKC8ihfBdloemhe8Ym8wD3BFQiwtQukHa91ix7j8dyVn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436334}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "19fe78f797116f3be88c3e59d80895db5c8592aa", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.2.4", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.19.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "semver": "7.6.0", "tar-fs": "3.0.5", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.4.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.1.0_1708589429263_0.39431665765661483", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "@puppeteer/browsers", "version": "2.2.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.2.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "25b5c6d1c93eb91e7086ebc95b767fe7b3ee5ec4", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.2.0.tgz", "fileCount": 158, "integrity": "sha512-MC7LxpcBtdfTbzwARXIkqGZ1Osn3nnZJlm+i0+VqHl72t//Xwl9wICrXT8BwtgC6s1xJNHsxOpvzISUqe92+sw==", "signatures": [{"sig": "MEUCIQChiVB8PaLXjiaGNpH0qz0+QlqH0oUvwrKEjnWBI2pvZgIgUFfwvhE7zXUdqyOwauedp7y/UkYAHGfozNjDg9UXjCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 465770}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "9aef7dc5e5d42086a08f9f27fa1467ec41219201", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.2.4", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.19.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "semver": "7.6.0", "tar-fs": "3.0.5", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.4.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.2.0_1710517373757_0.27020769852778126", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "@puppeteer/browsers", "version": "2.2.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.2.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "c40608b96b10c09a6b2d08ab5ea31dfe1b409455", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.2.1.tgz", "fileCount": 158, "integrity": "sha512-QSXujx4d4ogDamQA8ckkkRieFzDgZEuZuGiey9G7CuDcbnX4iINKWxTPC5Br2AEzY9ICAvcndqgAUFMMKnS/Tw==", "signatures": [{"sig": "MEYCIQDkZGnHWxni/rB+ZNB+k79s99qTKfkw+1APWflD+ZRg+wIhAPJu2uVCMPh8Cf4ijv4c1yKSsY3XmbuT3kcD4ybzze+H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 474774}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "a63b8305acfd79ff73a6a316fc2e819f41613766", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.2.4", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.19.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "semver": "7.6.0", "tar-fs": "3.0.5", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.4.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.2.1_1712323048278_0.5030148628016571", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "@puppeteer/browsers", "version": "2.2.2", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.2.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "c43b00a9808370fec3e548779d81d1e0b972e8bb", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.2.2.tgz", "fileCount": 158, "integrity": "sha512-hZ/JhxPIceWaGSEzUZp83/8M49CoxlkuThfTR7t4AoCu5+ZvJ3vktLm60Otww2TXeROB5igiZ8D9oPQh6ckBVg==", "signatures": [{"sig": "MEUCIQCpiqrbWzWKfeabkXZjFCtC3vrD14YDel7Sbp0hIA4azAIgVr/t9DzmOBUmJr8yYA28PSTQZrVlOcW2zGK2Uw+6NM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 474774}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "e0932e5cce1e1f0754d4f435c68908ba913357a3", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.1", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "semver": "7.6.0", "tar-fs": "3.0.5", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.4.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.2.2_1713164898482_0.5777466435110232", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "@puppeteer/browsers", "version": "2.2.3", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.2.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "ad6b79129c50825e77ddaba082680f4dad0b674e", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.2.3.tgz", "fileCount": 158, "integrity": "sha512-bJ0UBsk0ESOs6RFcLXOt99a3yTDcOKlzfjad+rhFwdaG1Lu/Wzq58GHYCDTlZ9z6mldf4g+NTb+TXEfe0PpnsQ==", "signatures": [{"sig": "MEQCICc7BbcgCBdmiccYXVh3hSko8Tq44WJBsC+BX5Q85sufAiBeum2c2nR/kh9FnWcgaVn1b96dblmxV7/PW/FwO1+//g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 476214}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "feef2a300e2846bd4931f6a0e49117e85b732931", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.5.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"debug": "4.3.4", "yargs": "17.7.2", "semver": "7.6.0", "tar-fs": "3.0.5", "progress": "2.0.3", "extract-zip": "2.0.1", "proxy-agent": "6.4.0", "unbzip2-stream": "1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.2.3_1714040472661_0.6025400852789136", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "@puppeteer/browsers", "version": "2.2.4", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.2.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "4307245d881aa5a79743050be66568bad0f6ffbb", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.2.4.tgz", "fileCount": 158, "integrity": "sha512-BdG2qiI1dn89OTUUsx2GZSpUzW+DRffR1wlMJyKxVHYrhnKoELSDxDd+2XImUkuWPEKk76H5FcM/gPFrEK1Tfw==", "signatures": [{"sig": "MEYCIQCbYL5voT8xP8JLKyn6ZCFiFFP4xCXuxeKvX51XA/bQAwIhAKlX3Ljivzq2yyA3BB8ClY/EvuuP4fk/P6sDWWH44OAr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 476179}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "551cb3d17be241362c4179e7b616d20470114b1b", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.7.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"debug": "^4.3.5", "yargs": "^17.7.2", "semver": "^7.6.2", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.2.4_1721207603621_0.49454581896000205", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "@puppeteer/browsers", "version": "2.3.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.3.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "791ea7d80450fea24eb19fb1d70c367ad4e08cae", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.3.0.tgz", "fileCount": 158, "integrity": "sha512-ioXoq9gPxkss4MYhD+SFaU9p1IHFUX0ILAWFPyjGaBdjLsYAlZw6j1iLA0N/m12uVHLFDfSYNF7EQccjinIMDA==", "signatures": [{"sig": "MEQCIGu96iQFuv7VITyaYy3Njwl9D1DoLa3N4yxUcl9og3L4AiBFI9PpF/VI9Ov9V+yEpKop8D6B2yrHzZV9tsCDJL8CZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487700}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "0dd5012504641511267cf430d534a559f9a66681", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.7.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"debug": "^4.3.5", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.3.0_1721898195408_0.8198087299238317", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "@puppeteer/browsers", "version": "2.3.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.3.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "238200dbdce5c00ae28c8f2a55ac053c3be71668", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.3.1.tgz", "fileCount": 158, "integrity": "sha512-uK7o3hHkK+naEobMSJ+2ySYyXtQkBxIH8Gn4MK9ciePjNV+Pf+PgY/W7iPzn2MTjl3stcYB5AlcTmPYw7AXDwA==", "signatures": [{"sig": "MEQCIAX6DsauyYA70pokbtwz3PeDOqAAL9QB2QxXXXgfkhonAiA1g6nbQx9Bx4Rm5evxrDNWDidGMDwC/CHF74pf8JmeLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487238}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "1e1a63897ff9b726f7b177c33e0169d7ca282d42", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.7.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"debug": "^4.3.6", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.32", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.3.1_1723646973073_0.9020146783466763", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "@puppeteer/browsers", "version": "2.4.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.4.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "a0dd0f4e381e53f509109ae83b891db5972750f5", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.4.0.tgz", "fileCount": 158, "integrity": "sha512-x8J1csfIygOwf6D6qUAZ0ASk3z63zPb7wkNeHRerCMh82qWKUrOgkuP005AJC8lDL6/evtXETGEJVcwykKT4/g==", "signatures": [{"sig": "MEUCIG4aigKnGYVzQJHRiGtgzzp/vP2OBBne/aLvKv8nnqp2AiEAkAhGUSajBMSZjVws54cxx14VRoEHBw1m/rDr0BtaR2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 502794}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "aaf7cfe3497b8f449d05c427306f0d02fc6f2313", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.7.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"debug": "^4.3.6", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.4.0_1725450664264_0.21565417459774228", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "@puppeteer/browsers", "version": "2.4.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.4.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "7afd271199cc920ece2ff25109278be0a3e8a225", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.4.1.tgz", "fileCount": 158, "integrity": "sha512-0kdAbmic3J09I6dT8e9vE2JOCSt13wHCW5x/ly8TSt2bDtuIWe2TgLZZDHdcziw9AVCzflMAXCrVyRIhIs44Ng==", "signatures": [{"sig": "MEUCIFUaPqGnV0bMyS81RLLwegoaPVvkIDMd7OJWiL6sgAX5AiEAzLOee3Wy9yBqgjouay0RLul2tLply08O4hS0tzZQilY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 501175}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "eec80727b24c71d569d62b31fa327f150d23ca44", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.7.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"debug": "^4.3.7", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.4.1_1730723444573_0.461737227506932", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "@puppeteer/browsers", "version": "2.5.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.5.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "7e4f7ba8f04e54f11501b78dc7bcc4033de935d4", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.5.0.tgz", "fileCount": 158, "integrity": "sha512-6TQAc/5uRILE6deixJ1CR8rXyTbzXIXNgO1D0Woi9Bqicz2FV5iKP3BHYEg6o4UATCMcbQQ0jbmeaOkn/HQk2w==", "signatures": [{"sig": "MEYCIQDup/lz1DOoQ4PnIuElqOa9pZqC/toAKSwIIxNECblW4wIhALwd762SkpqR3e7dfr4nZh15yKOXxsphbwvYr2+6ipg4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 503090}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "0c2e549a07f1e712dedd71a41409aac8a5bb9dbd", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.8.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"debug": "^4.3.7", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.5.0_1733247314996_0.8652474702710404", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "@puppeteer/browsers", "version": "2.6.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.6.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "63bc4bb9ee24df2a031b48d57b98d2b64ab87814", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.6.0.tgz", "fileCount": 158, "integrity": "sha512-jESwj3APl78YUWHf28s2EjL0OIxcvl1uLU6Ge68KQ9ZXNsekUcbdr9dCi6vEO8naXS18lWXCV56shVkPStzXSQ==", "signatures": [{"sig": "MEYCIQCU9UElPR6sI+LzD+sW3KuyksIB+MxAMAdUFzAsUgrbMQIhAKapKajmXtSfKk1toszzE3Hqh1p7kj7jlnmvG1wXxSPH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 507478}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "75f76ccb7587d04a64dd96f6f9a29dd22ecb98e7", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.8.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.6.0_1733768303234_0.6331566145471448", "host": "s3://npm-registry-packages-npm-production"}}, "2.6.1": {"name": "@puppeteer/browsers", "version": "2.6.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.6.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "d75aec5010cae377c5e4742bf5e4f62a79c21315", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.6.1.tgz", "fileCount": 158, "integrity": "sha512-aBSREisdsGH890S2rQqK82qmQYU3uFpSH8wcZWHgHzl3LfzsxAKbLNiAG9mO8v1Y0UICBeClICxPJvyr0rcuxg==", "signatures": [{"sig": "MEUCIQDPT4Igj47RryiZDNTAs6UCfU+zAiwFbBxEEytRwyLwngIgb8yKha/597vJsc0I9wbWWhM/Ksh+ll3ZLoRaeUlzIPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 516513}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && tsx ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "tsx ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "193fffd891af063b9a4a6dc61c0872d452c4407b", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.8.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.6.1_1733856641438_0.3607481309057803", "host": "s3://npm-registry-packages-npm-production"}}, "2.7.0": {"name": "@puppeteer/browsers", "version": "2.7.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.7.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "dad70b30458f4e0855b2f402055f408823cc67c5", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.7.0.tgz", "fileCount": 158, "integrity": "sha512-bO61XnTuopsz9kvtfqhVbH6LTM1koxK0IlBR+yuVrM2LB7mk8+5o1w18l5zqd5cs8xlf+ntgambqRqGifMDjog==", "signatures": [{"sig": "MEUCIQDHkj10uoOfi6ww3aDhVbWK6n6ORAuU7TiJ8k8sCWXCvAIgemIU8I8FwE01BVirel3aWWP9U2/MxcF5igMGcIKPvfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518479}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "0b006175eea0c58957715b2c1cd7e56aec5170f1", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.0", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.6.3", "tar-fs": "^3.0.6", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0", "unbzip2-stream": "^1.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7", "@types/unbzip2-stream": "1.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.7.0_1736493562216_0.631438122626587", "host": "s3://npm-registry-packages-npm-production"}}, "2.7.1": {"name": "@puppeteer/browsers", "version": "2.7.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.7.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "6df07e95d8e22239b77599f3ceaef4041b933e62", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.7.1.tgz", "fileCount": 158, "integrity": "sha512-MK7rtm8JjaxPN7Mf1JdZIZKPD2Z+W7osvrC1vjpvfOX1K0awDIHYbNi89f7eotp7eMUn2shWnt03HwVbriXtKQ==", "signatures": [{"sig": "MEUCIBrFnrSJzbJEdxLvxYNP3hXZXioUMaXqsOGEoG/z0k9fAiEAzexZYVj8OSEmb0etoPZAk1rxJqMKdhEjKzTBpqYraSk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 523221}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "721072f04814b60b8e98228d3aa99b906407ba0e", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.13.1", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.0", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.7.1_1738752849488_0.47337989050672813", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.0": {"name": "@puppeteer/browsers", "version": "2.8.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.8.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "9d592933cbefc66c37823770844b8cbac52607dd", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.8.0.tgz", "fileCount": 158, "integrity": "sha512-yTwt2KWRmCQAfhvbCRjebaSX8pV1//I0Y3g+A7f/eS7gf0l4eRJoUCvcYdVtboeU4CTOZQuqYbZNS8aBYb8ROQ==", "signatures": [{"sig": "MEUCIQDhDeWwllcBnZo947IhFGbqqiHwohxRpXs4Cjqqua9+pQIgPkvl4V90NBYOVYtVxRHBuEWdMsKZOm5Y+lNaf5CrQSs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 528300}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "842075f7dafd8853d73552436dcf9130845d80ca", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.8.0_1741250089682_0.4892592951882109", "host": "s3://npm-registry-packages-npm-production"}}, "2.9.0": {"name": "@puppeteer/browsers", "version": "2.9.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.9.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "744a2395b196530d9fffbc64df549689f06bc24e", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.9.0.tgz", "fileCount": 158, "integrity": "sha512-8+xM+cFydYET4X/5/3yZMHs7sjS6c9I6H5I3xJdb6cinzxWUT/I2QVw4avxCQ8QDndwdHkG/FiSZIrCjAbaKvQ==", "signatures": [{"sig": "MEYCIQCiQPom53uTERjiqqADpdfWUIBlUgvehD1pFduK0C7SPgIhAMPWjSgohr5Bi/fmiftZTWi6LTxF6EctuZw6sfEXczYZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 531275}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "db9c7bb5df84e9c5702fc285824ac554d861b7f9", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.0", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.9.0_1743508575715_0.763436208294789", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.0": {"name": "@puppeteer/browsers", "version": "2.10.0", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.10.0", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "a6e55bf85bfcc819e5e8c79f6122cccaa52515a4", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.0.tgz", "fileCount": 158, "integrity": "sha512-HdHF4rny4JCvIcm7V1dpvpctIGqM3/Me255CB44vW7hDG1zYMmcBMjpNqZEDxdCfXGLkx5kP0+Jz5DUS+ukqtA==", "signatures": [{"sig": "MEQCIG80uuVgu2XYSWR2u3M1wcXbe4/sBU+PEhXkr0gceodXAiAsbpc0QcNHn5DWAJQ5N2c+yWfAyff7F+55gddMx+8wow==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 536827}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "45a289e1fb8b6337c5ff336a33345308fe4e9e9a", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.1", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.10.0_1744212554495_0.8369680129013948", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.1": {"name": "@puppeteer/browsers", "version": "2.10.1", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.10.1", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "df0ba78ccaae7a7519580ec0e57fe980026b100c", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.1.tgz", "fileCount": 158, "integrity": "sha512-NgghEvl9fTZdXvxBHMgTlEVrjhDsbOfXgXzoarsV6o/arp2SvOOzZfFlKpxwLHJo7vVULq9Q/Dg76lUddLxgog==", "signatures": [{"sig": "MEQCIA+mp3zlj5gSTNOoiPjnom2eF0E1EesIl1UdlCdyWhieAiA348jdYC6bCWG0ucIF08FCZbzEwOumZMcSmC2ial3QXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 538297}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "61a5536442d8b9141b6961afe0a183b4aabee38a", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.1", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.10.1_1745306957922_0.11664081586090447", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.2": {"name": "@puppeteer/browsers", "version": "2.10.2", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.10.2", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "c2a63cee699c6b5b971b9fcba9095098970f1648", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.2.tgz", "fileCount": 158, "integrity": "sha512-i4Ez+s9oRWQbNjtI/3+jxr7OH508mjAKvza0ekPJem0ZtmsYHP3B5dq62+IaBHKaGCOuqJxXzvFLUhJvQ6jtsQ==", "signatures": [{"sig": "MEQCIEyCTMWBMMzZvDeXbLCe7XYccVCyHd4P7BHukv/foE+6AiAgSROH8N17Z6b+rQC5hxGgXpGkzS+Zi+pExoxX0Hnvpw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 538099}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "98fe5581cb679b3018a296700e635abad7a2c88a", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.1", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.10.2_1745387375702_0.9237796064057449", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.3": {"name": "@puppeteer/browsers", "version": "2.10.3", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.10.3", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "4d89b3520359da6f7016ff96316c2c243be2b3ee", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.3.tgz", "fileCount": 168, "integrity": "sha512-iPpnFpX25gKIVsHsqVjHV+/GzW36xPgsscWkCnrrETndcdxNsXLdCrTwhkCJNR/FGWr122dJUBeyV4niz/j3TA==", "signatures": [{"sig": "MEUCIFhhRGlpILVc4htpaUFrIvV1oZOdEj5pR6Y1UF4hoQwpAiEA5Um0qYAPUpUEwRF+/c1Xwo54ufgW0aU/3/R2BKh+VvQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 540600}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json", "generate:version"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:version": {"clean": "if-file-deleted", "files": ["Herebyfile.mjs"], "output": ["src/generated/version.ts"], "command": "hereby generate:version"}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "5fcbb11a53b883c3688bdb0dc1fe42ccf9263338", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.1", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.10.3_1746174905267_0.32202525814083494", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.4": {"name": "@puppeteer/browsers", "version": "2.10.4", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.10.4", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "9f8923b206f7932a06d32271b14bbea3368b38f2", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.4.tgz", "fileCount": 168, "integrity": "sha512-9DxbZx+XGMNdjBynIs4BRSz+M3iRDeB7qRcAr6UORFLphCIM2x3DXgOucvADiifcqCE4XePFUKcnaAMyGbrDlQ==", "signatures": [{"sig": "MEQCIG/SQ64sO3PokHx+yMhF1fzcxDRhgxa29o/rFEQAokxeAiBoaOF6NRy/9Svjacd6GTuMRw4yMecXxTtICDSzJaSGfg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 540694}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json", "generate:version"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:version": {"clean": "if-file-deleted", "files": ["Herebyfile.mjs"], "output": ["src/generated/version.ts"], "command": "hereby generate:version"}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "9e4619daea05d1990209157107eed68d54ab611d", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"debug": "^4.4.0", "yargs": "^17.7.2", "semver": "^7.7.1", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.1", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.10.4_1746685429321_0.8288541923438566", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.5": {"name": "@puppeteer/browsers", "version": "2.10.5", "keywords": ["puppeteer", "browsers"], "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "_id": "@puppeteer/browsers@2.10.5", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "dist": {"shasum": "dddb8f8716ae6364f6f2d31125e76f311dd4a49d", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.5.tgz", "fileCount": 168, "integrity": "sha512-eifa0o+i8dERnngJwKrfp3dEq7ia5XFyoqB17S4gK8GhsQE4/P8nxOfQSE0zQHxzzLo/cmF+7+ywEQ7wK7Fb+w==", "signatures": [{"sig": "MEUCIEP1qKT+88CZdsjzDN0UH2S4zRZFx4Shn2nwSSVMQ+W0AiEAzNy65aJig5E6M2qdiBJ3oKRU9clP1M4oaC5nXhNlcGk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 540719}, "main": "./lib/cjs/main.js", "type": "commonjs", "types": "./lib/cjs/main.d.ts", "wireit": {"test": {"files": [".mocharc.cjs"], "command": "node tools/downloadTestBrowsers.mjs && mocha", "dependencies": ["build:test"]}, "build": {"clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/**", "!lib/esm/package.json"], "command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "dependencies": ["generate:package-json", "generate:version"]}, "build:docs": {"files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "dependencies": ["build"]}, "build:test": {"files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "command": "tsc -b test/src/tsconfig.json", "dependencies": ["build", "../testserver:build"]}, "generate:version": {"clean": "if-file-deleted", "files": ["Herebyfile.mjs"], "output": ["src/generated/version.ts"], "command": "hereby generate:version"}, "generate:package-json": {"files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"], "command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json"}}, "engines": {"node": ">=18"}, "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "gitHead": "efc1343e24247225428e0602471c57396ac31857", "scripts": {"test": "wireit", "build": "wireit", "clean": "../../tools/clean.mjs", "build:docs": "wireit", "build:test": "wireit"}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/puppeteer/puppeteer.git#main", "type": "git"}, "_npmVersion": "10.9.2", "description": "Download and launch browsers", "directories": {}, "_nodeVersion": "22.15.0", "dependencies": {"debug": "^4.4.1", "yargs": "^17.7.2", "semver": "^7.7.2", "tar-fs": "^3.0.8", "progress": "^2.0.3", "extract-zip": "^2.0.1", "proxy-agent": "^6.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"@types/ws": "8.18.1", "@types/debug": "4.1.12", "@types/yargs": "17.0.33", "@types/tar-fs": "2.0.4", "@types/progress": "2.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/browsers_2.10.5_1747724630981_0.4848572455010096", "host": "s3://npm-registry-packages-npm-production"}}, "2.10.6": {"name": "@puppeteer/browsers", "version": "2.10.6", "description": "Download and launch browsers", "scripts": {"build:docs": "wireit", "build": "wireit", "build:test": "wireit", "clean": "../../tools/clean.mjs", "test": "wireit"}, "type": "commonjs", "bin": {"browsers": "lib/cjs/main-cli.js"}, "main": "./lib/cjs/main.js", "exports": {"import": "./lib/esm/main.js", "require": "./lib/cjs/main.js"}, "wireit": {"build": {"command": "tsc -b && node --experimental-strip-types ../../tools/chmod.ts 755 lib/cjs/main-cli.js lib/esm/main-cli.js", "files": ["src/**/*.ts", "tsconfig.json"], "clean": "if-file-deleted", "output": ["lib/**", "!lib/esm/package.json"], "dependencies": ["generate:package-json", "generate:version"]}, "generate:version": {"command": "hereby generate:version", "clean": "if-file-deleted", "files": ["Herebyfile.mjs"], "output": ["src/generated/version.ts"]}, "generate:package-json": {"command": "node --experimental-strip-types ../../tools/generate_module_package_json.ts lib/esm/package.json", "files": ["../../tools/generate_module_package_json.ts"], "output": ["lib/esm/package.json"]}, "build:docs": {"command": "api-extractor run --local --config \"./api-extractor.docs.json\"", "files": ["api-extractor.docs.json", "lib/esm/main.d.ts", "tsconfig.json"], "dependencies": ["build"]}, "build:test": {"command": "tsc -b test/src/tsconfig.json", "files": ["test/**/*.ts", "test/src/tsconfig.json"], "output": ["test/build/**"], "dependencies": ["build", "../testserver:build"]}, "test": {"command": "node tools/downloadTestBrowsers.mjs && mocha", "files": [".mocharc.cjs"], "dependencies": ["build:test"]}}, "keywords": ["puppeteer", "browsers"], "repository": {"type": "git", "url": "git+https://github.com/puppeteer/puppeteer.git#main"}, "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "engines": {"node": ">=18"}, "dependencies": {"debug": "^4.4.1", "extract-zip": "^2.0.1", "progress": "^2.0.3", "proxy-agent": "^6.5.0", "tar-fs": "^3.1.0", "yargs": "^17.7.2", "semver": "^7.7.2"}, "devDependencies": {"@types/debug": "4.1.12", "@types/progress": "2.0.7", "@types/tar-fs": "2.0.4", "@types/yargs": "17.0.33", "@types/ws": "8.18.1"}, "_id": "@puppeteer/browsers@2.10.6", "gitHead": "3d72c43a2d2a681a44d1f96c3c6432b6a75ac8e5", "types": "./lib/cjs/main.d.ts", "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "_nodeVersion": "22.17.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-pHUn6ZRt39bP3698HFQlu2ZHCkS/lPcpv7fVQcGBSzNNygw171UXAKrCUhy+TEMw4lEttOKDgNpb04hwUAJeiQ==", "shasum": "0b1b5046ec4918a4fd4e4c9383153a80af288bd2", "tarball": "https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.6.tgz", "fileCount": 168, "unpackedSize": 540737, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCBr2UJM4kwECl4LoXk8NOUj+XpEQizot59inSEMYZ3GwIhANenEBqszLO76Oxw/HL60/rQBRmMrwNWMXxExt8CthQ3"}]}, "_npmUser": {"name": "google-wombot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/browsers_2.10.6_1752680221428_0.6569602243505255"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-02-22T10:35:37.011Z", "modified": "2025-07-16T15:37:01.856Z", "0.0.1": "2023-02-22T10:35:37.284Z", "0.0.2": "2023-02-22T12:22:55.683Z", "0.0.3": "2023-02-22T16:13:22.938Z", "0.0.4": "2023-03-06T13:56:24.997Z", "0.0.5": "2023-03-07T15:59:46.442Z", "0.1.0": "2023-03-14T13:00:41.373Z", "0.1.1": "2023-03-14T18:34:23.441Z", "0.2.0": "2023-03-24T10:24:24.845Z", "0.3.0": "2023-03-27T10:39:17.280Z", "0.3.1": "2023-03-29T11:24:35.972Z", "0.3.2": "2023-04-03T08:25:40.729Z", "0.3.3": "2023-04-06T14:50:09.410Z", "0.4.0": "2023-04-06T17:13:51.857Z", "0.4.1": "2023-04-13T12:29:44.517Z", "0.5.0": "2023-04-21T10:03:27.291Z", "1.0.0": "2023-05-02T16:58:34.214Z", "1.0.1": "2023-05-05T11:28:13.498Z", "1.1.0": "2023-05-08T15:18:12.708Z", "1.2.0": "2023-05-11T19:44:10.924Z", "1.3.0": "2023-05-16T07:44:11.242Z", "1.4.0": "2023-05-25T04:53:58.086Z", "1.4.1": "2023-05-31T13:11:50.851Z", "1.4.2": "2023-06-21T07:16:07.947Z", "1.4.3": "2023-06-29T16:53:51.131Z", "1.4.4": "2023-07-11T11:13:15.224Z", "1.4.5": "2023-07-13T07:16:32.578Z", "1.4.6": "2023-07-20T07:38:55.674Z", "1.5.0": "2023-08-02T12:52:30.052Z", "1.5.1": "2023-08-08T14:07:13.227Z", "1.6.0": "2023-08-11T06:00:13.843Z", "1.7.0": "2023-08-18T09:05:27.036Z", "1.7.1": "2023-09-13T10:31:13.850Z", "1.8.0": "2023-10-20T10:22:39.980Z", "1.9.0": "2023-12-06T08:06:51.887Z", "1.9.1": "2024-01-04T13:41:23.039Z", "2.0.0": "2024-02-05T10:23:24.497Z", "2.0.1": "2024-02-17T11:45:19.499Z", "2.1.0": "2024-02-22T08:10:29.514Z", "2.2.0": "2024-03-15T15:42:53.899Z", "2.2.1": "2024-04-05T13:17:28.475Z", "2.2.2": "2024-04-15T07:08:18.669Z", "2.2.3": "2024-04-25T10:21:12.864Z", "2.2.4": "2024-07-17T09:13:23.782Z", "2.3.0": "2024-07-25T09:03:15.652Z", "2.3.1": "2024-08-14T14:49:33.357Z", "2.4.0": "2024-09-04T11:51:04.400Z", "2.4.1": "2024-11-04T12:30:44.752Z", "2.5.0": "2024-12-03T17:35:15.234Z", "2.6.0": "2024-12-09T18:18:23.412Z", "2.6.1": "2024-12-10T18:50:41.670Z", "2.7.0": "2025-01-10T07:19:22.396Z", "2.7.1": "2025-02-05T10:54:09.672Z", "2.8.0": "2025-03-06T08:34:49.868Z", "2.9.0": "2025-04-01T11:56:15.973Z", "2.10.0": "2025-04-09T15:29:14.772Z", "2.10.1": "2025-04-22T07:29:18.091Z", "2.10.2": "2025-04-23T05:49:35.891Z", "2.10.3": "2025-05-02T08:35:05.452Z", "2.10.4": "2025-05-08T06:23:49.887Z", "2.10.5": "2025-05-20T07:03:51.231Z", "2.10.6": "2025-07-16T15:37:01.648Z"}, "bugs": {"url": "https://github.com/puppeteer/puppeteer/issues"}, "author": {"name": "The Chromium Authors"}, "license": "Apache-2.0", "homepage": "https://github.com/puppeteer/puppeteer/tree/main#readme", "keywords": ["puppeteer", "browsers"], "repository": {"type": "git", "url": "git+https://github.com/puppeteer/puppeteer.git#main"}, "description": "Download and launch browsers", "maintainers": [{"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "google-wombot", "email": "<EMAIL>"}], "readme": "# @puppeteer/browsers\n\nManage and launch browsers/drivers from a CLI or programmatically.\n\n## System requirements\n\n- A compatible Node version (see `engines` in `package.json`).\n- For Firefox downloads:\n  - Linux builds: `xz` and `bzip2` utilities are required to unpack `.tar.gz` and `.tar.bz2` archives.\n  - MacOS builds: `hdiutil` is required to unpack `.dmg` archives.\n\n## CLI\n\nUse `npx` to run the CLI:\n\n```bash\n# This will install and run the @puppeteer/browsers package.\n# If it is already installed in the current directory, the installed\n# version will be used.\nnpx @puppeteer/browsers --help\n```\n\nBuilt-in per-command `help` will provide all documentation you need to use the CLI.\n\n```bash\nnpx @puppeteer/browsers --help # help for all commands\nnpx @puppeteer/browsers install --help # help for the install command\nnpx @puppeteer/browsers launch --help # help for the launch command\nnpx @puppeteer/browsers clear --help # help for the clear command\nnpx @puppeteer/browsers list --help # help for the list command\n```\n\nYou can specify the version of the `@puppeteer/browsers` when using\n`npx`:\n\n```bash\n# Always install and use the latest version from the registry.\nnpx @puppeteer/browsers@latest --help\n# Always use a specifc version.\nnpx @puppeteer/browsers@2.4.1 --help\n# Always install the latest version and automatically confirm the installation.\nnpx --yes @puppeteer/browsers@latest --help\n```\n\nTo clear all installed browsers, use the `clear` command:\n\n```bash\nnpx @puppeteer/browsers clear\n```\n\nTo list all installed browsers, use the `list` command:\n\n```bash\nnpx @puppeteer/browsers list\n```\n\nSome example to give an idea of what the CLI looks like (use the `--help` command for more examples):\n\n```sh\n# Download the latest available Chrome for Testing binary corresponding to the Stable channel.\nnpx @puppeteer/browsers install chrome@stable\n\n# Download a specific Chrome for Testing version.\nnpx @puppeteer/browsers install chrome@116.0.5793.0\n\n# Download the latest Chrome for Testing version for the given milestone.\nnpx @puppeteer/browsers install chrome@117\n\n# Download the latest available ChromeDriver version corresponding to the Canary channel.\nnpx @puppeteer/browsers install chromedriver@canary\n\n# Download a specific ChromeDriver version.\nnpx @puppeteer/browsers install chromedriver@116.0.5793.0\n\n# On Ubuntu/Debian and only for Chrome, install the browser and required system dependencies.\n# If the browser version has already been installed, the command\n# will still attempt to install system dependencies.\n# Requires root privileges.\nnpx puppeteer browsers install chrome --install-deps\n```\n\n## Known limitations\n\n1. Launching the system browsers is only possible for Chrome/Chromium.\n\n## API\n\nThe programmatic API allows installing and launching browsers from your code. See the `test` folder for examples on how to use the `install`, `canInstall`, `launch`, `computeExecutablePath`, `computeSystemExecutablePath` and other methods.\n", "readmeFilename": "README.md"}