{"private": true, "type": "module", "name": "kontext", "version": "1.0.0", "license": "SEE LICENSE IN LICENSE", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "description": "A news blog that transforms the latest news into engaging, easy-to-read blog posts using AI.", "repository": {"type": "git", "url": "https://github.com/ShovitDutta/kontext.git"}, "engines": {"node": ">=20.19.4"}, "scripts": {"lint": "next lint", "dev": "next dev --port 3000", "format": "prettier --write .", "start": "next start --port 3000", "dev:turbo": "next dev --port 3000 --turbopack", "clean": "npx rimraf .next .cache node_modules", "build": "npx rimraf .next && npm run format && next build", "db:preview": "tsx src/scripts/db/preview.ts", "db": "npm run db:generate && npm run db:push", "db:push": "drizzle-kit push --force --config=drizzle.config.ts", "db:generate": "drizzle-kit generate --config=drizzle.config.ts", "db:migrate-content": "tsx src/scripts/migrate-content-length.ts", "db:reset": "node src/scripts/reset.js && npx rimraf src/lib/db/migrations", "db:load": "npm run db && tsx src/scripts/db/cron.ts", "db:load:news": "npm run db && tsx src/scripts/db/cron.ts cron/news", "db:load:blog": "npm run db && tsx src/scripts/db/cron.ts cron/blog", "preflight:dev": "npm run clean && npm ci --silent && npm run format && npm run build", "preflight:prod": "npm run clean && npm ci --only=production --silent && npm run format && npm run build", "preflight:clean": "npm run clean && npm ci --verbose && npm run db:reset && npm run db && npm run format && npm run build", "codemod:next-og-import": "npx @next/codemod@canary next-og-import . --force", "codemod:async-params": "npx @next/codemod@canary next-async-request-api . --force", "codemod:built-in-next-font": "npx @next/codemod@canary built-in-next-font . --force", "codemod:name-default-component": "npx @next/codemod@canary name-default-component . --force", "codemod:add-missing-react-import": "npx @next/codemod@canary add-missing-react-import . --force", "codemod:next-image-to-legacy-image": "npx @next/codemod@canary next-image-to-legacy-image . --force", "codemod:metadata-to-viewport-export": "npx @next/codemod@canary metadata-to-viewport-export . --force", "codemod": "npm run codemod:async-params && npm run codemod:next-og-import && npm run codemod:metadata-to-viewport-export && npm run codemod:built-in-next-font && npm run codemod:next-image-to-legacy-image && npm run codemod:add-missing-react-import && npm run codemod:name-default-component"}, "dependencies": {"@auth/drizzle-adapter": "1.10.0", "@google/generative-ai": "0.24.1", "@langchain/core": "0.3.66", "@langchain/google-genai": "0.2.16", "@langchain/langgraph": "0.3.11", "@tailwindcss/postcss": "^4.1.11", "axios": "1.11.0", "date-fns": "^4.1.0", "dotenv": "17.2.1", "drizzle-kit": "0.31.4", "drizzle-orm": "0.44.3", "drizzle-seed": "0.3.1", "framer-motion": "12.23.11", "langchain": "0.3.30", "next": "^15.4.2", "next-auth": "^5.0.0-beta.28", "next-pwa": "5.6.0", "pg": "8.16.3", "postgres": "3.4.7", "react": "^19.1.0", "react-day-picker": "9.8.1", "react-dom": "^19.1.0", "react-icons": "5.5.0", "react-intersection-observer": "9.16.0", "react-markdown": "10.1.0", "react-share": "^5.2.2", "rss-parser": "3.13.0", "sonner": "^2.0.6", "vaul": "^1.1.2", "zod": "4.0.10", "zustand": "5.0.6"}, "devDependencies": {"@tailwindcss/typography": "0.5.16", "@tanstack/react-query-devtools": "5.83.0", "@types/node": "24.1.0", "@types/pg": "8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.20", "eslint": "9.32.0", "eslint-config-next": "15.4.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tsx": "4.20.3", "typescript": "^5"}}