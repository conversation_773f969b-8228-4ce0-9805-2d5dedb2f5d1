{"_id": "get-uri", "_rev": "35-90a0398177f9f8dea661852d6ed40ee0", "name": "get-uri", "dist-tags": {"latest": "6.0.5"}, "versions": {"0.1.0": {"name": "get-uri", "version": "0.1.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@0.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "4c7772b808b05f9307dfe9e5dc2e1153e89ac87a", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-0.1.0.tgz", "integrity": "sha512-PGqCnaZ9E+hUpXNF6hGTTA1tlzE7XZGIMyqKZ1YylrfXNP24sMPg1XEy8QmRL6eSj0ZPNVoD5zHc+hCfFXrbJQ==", "signatures": [{"sig": "MEYCIQDpWybV7+4hdmOucTXy8jW3Stf1/nemND6kCdgnHf8htAIhAL2rzQw2zmG59E9KTFleA/2kJtZ1d7y/h53L77mOU3On", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "dependencies": {"ftp": "~0.3.5", "debug": "~0.7.4", "extend": "~1.2.1", "readable-stream": "~1.1.0", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"st": "~0.2.3", "ftpd": "~0.2.4", "mocha": "~1.16.2", "stream-to-array": "~1.0.0"}, "optionalDependencies": {"readable-stream": "~1.1.0"}}, "0.1.1": {"name": "get-uri", "version": "0.1.1", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@0.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "9c39146e62acb9f5c0b36895494a19c036fe54fc", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-0.1.1.tgz", "integrity": "sha512-3DZI0OewQRo0Gtu1G538TdWkQNoI+tH9+Y/Swc6ljxgpbBq9uuMrHVoOht9FAsv8OSlGm8nEhctLFhY5rAc5wg==", "signatures": [{"sig": "MEQCIGdokM3cgz5ylWY7IgDDWqEto3s2ZMLsVhkIIo2VrsHAAiBk60VVD7ZQtSw/wY0Wg0aGoJhOOrDw3ibXrEmdcVp2Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "dependencies": {"ftp": "~0.3.5", "debug": "~0.7.4", "extend": "~1.2.1", "readable-stream": "~1.1.0", "file-uri-to-path": "0", "data-uri-to-buffer": "0.0.3"}, "devDependencies": {"st": "~0.2.3", "ftpd": "0.2.4", "mocha": "~1.16.2", "stream-to-array": "~1.0.0"}, "optionalDependencies": {"readable-stream": "~1.1.0"}}, "0.1.2": {"name": "get-uri", "version": "0.1.2", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@0.1.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "59758b3e2bf1059ed4f7921c46349df17a7716d1", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-0.1.2.tgz", "integrity": "sha512-u5WIi+4a5bGfFmWDhg4JH8fHbS8jd5De8dtokVMYodp+hxbDzTnz0WpjVkdM/4GSg7RZT5YsQrRY4PHm8awG3A==", "signatures": [{"sig": "MEQCICIntDf9UzOTMSeZ5CGNoo5PvMf//lh7BOBt967odMUGAiBmtltYtwn8C2wmAVo8zWQPtGLJC+DddxasekAHDdZeRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "dependencies": {"ftp": "^0.3.5", "debug": "^0.8.0", "extend": "^1.2.1", "readable-stream": "^1.0.26-4", "file-uri-to-path": "0", "data-uri-to-buffer": "^0.0.3"}, "devDependencies": {"st": "^0.2.3", "ftpd": "0.2.4", "mocha": "^1.16.2", "stream-to-array": "^1.0.0"}}, "0.1.3": {"name": "get-uri", "version": "0.1.3", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@0.1.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "b1b3f9dfe0da1c0909097b1ca1ce4d4b72a8d1ab", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-0.1.3.tgz", "integrity": "sha512-5Fl4yM4X8M+e1lnfuBMJnDjpzC4uhDq5CnSs25yBpF4UsGm5UYTG4P+RPrnYFv6dWC+7z1UWdXS8+RWlYiQuvg==", "signatures": [{"sig": "MEUCIQDbcUfLl7vyArVfKAkIinlfD11COAjutPfddeB2EpkM4wIgGzmMGmC/lAI0XFPXz0o/LykGehw4laJyKzTtAkRWLQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "dependencies": {"ftp": "~0.3.5", "debug": "0", "extend": "~1.2.1", "readable-stream": "~1.0.26-4", "file-uri-to-path": "0", "data-uri-to-buffer": "0"}, "devDependencies": {"st": ">= 0.2.3 && < 1", "ftpd": ">= 0.2.4 && < 1", "mocha": ">= 1.16.2 && < 2", "stream-to-array": "1"}}, "0.1.4": {"name": "get-uri", "version": "0.1.4", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@0.1.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "35f8a7954c129fb132ff2ddf5ed81a57cb8a9e54", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-0.1.4.tgz", "integrity": "sha512-xV5cksh/mERjnnDgZe0wKO2VokqUeTe/QaKrhUHhm77eiLXhGrWi5qiz20tF6NKRTcY47YP1OzSRgTHadNY3mQ==", "signatures": [{"sig": "MEYCIQDASSY/99yQ2QUTJh9WnvXj6ThRp5e74lnZIPQRigBwYgIhALRkW+iT5e6+u3s/Rv+iOs7uYwwdi0N9l8ze9IcmPWYh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "35f8a7954c129fb132ff2ddf5ed81a57cb8a9e54", "gitHead": "149bd9dd21a4871df459b5960895ba225967660b", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"ftp": "~0.3.5", "debug": "2", "extend": "3", "readable-stream": "2", "file-uri-to-path": "0", "data-uri-to-buffer": "0"}, "devDependencies": {"st": ">= 0.2.3 && < 1", "ftpd": ">= 0.2.4 && < 1", "mocha": "2", "stream-to-array": "1"}}, "1.0.0": {"name": "get-uri", "version": "1.0.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@1.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "ef5b6a6de8e8b3916aa2422d84978d977352d8c1", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-1.0.0.tgz", "integrity": "sha512-3KLtftS8RD0scOgDppg4aq/sN+HC5f1YRk0JjON2kKTzHqMS4Zfl8PA+eeSuQf/ApsuMpC3zRNYL/G6a67L6hA==", "signatures": [{"sig": "MEUCIQC4zhO6N/3z8YNc2nRtaIW36qwm8X0bBngC3KbBUqKhzAIgbkrcnivlL6ZMrxtTFMFZfGn5q/CW8VlYU9Ev/iNxsA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ef5b6a6de8e8b3916aa2422d84978d977352d8c1", "gitHead": "c0a06d3f0b8212cc8a1d5cd6772b182aa0f6fc14", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"ftp": "~0.3.5", "debug": "2", "extend": "3", "readable-stream": "2", "file-uri-to-path": "0", "data-uri-to-buffer": "0"}, "devDependencies": {"st": ">= 0.2.3 && < 1", "ftpd": ">= 0.2.4 && < 1", "mocha": "2", "stream-to-array": "1"}}, "1.1.0": {"name": "get-uri", "version": "1.1.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@1.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "7375d04daf7fcb584b3632679cbdf339b51bb149", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-1.1.0.tgz", "integrity": "sha512-wI/6HgPCAUPOMAQ7Nmgwe4cTcchoFUdlIZzH4AzGbTVA9D4PVN9Auuo4Opqc7i1iD/HxCYVF6+4VwfGMg04wVQ==", "signatures": [{"sig": "MEUCIHDN6W1U6eYlEUufj5mqQUaeTPLIuQ/Y5SMBzQje41/lAiEAve27gvKRtgrBRBWNwKmVxW95EKARELnPmYAkMDZ+SKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7375d04daf7fcb584b3632679cbdf339b51bb149", "gitHead": "0358bd1de11469aebc6e7535b3bc9dfd8fd6c652", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"ftp": "~0.3.5", "debug": "2", "extend": "3", "readable-stream": "2", "file-uri-to-path": "0", "data-uri-to-buffer": "0"}, "devDependencies": {"st": ">= 0.2.3 && < 1", "ftpd": ">= 0.2.4 && < 1", "mocha": "2", "stream-to-array": "1"}}, "2.0.0": {"name": "get-uri", "version": "2.0.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@2.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "713e47cbcbaeab38f88af1cdfc85fa7f09b00738", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-2.0.0.tgz", "integrity": "sha512-ujxD7UHD0chZmks3meivB32vo95TiQWTZuQZxMpHmoYrLgvaWNz0o8FneeokK+xNJ5tun2jjFDnaPrFik5URlg==", "signatures": [{"sig": "MEUCIQDehQWel4MNdaJ6regD7pAzpoF8HHgfZoo+5uRSvzhGaQIgRB4oxdDeEpfxrIhuQbhut8fGaqi/git4JREkdL9gbac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "713e47cbcbaeab38f88af1cdfc85fa7f09b00738", "gitHead": "996a7f9218b28df5813d1f46f2348c62de76c55e", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"ftp": "~0.3.5", "debug": "2", "extend": "3", "readable-stream": "2", "file-uri-to-path": "0", "data-uri-to-buffer": "0"}, "devDependencies": {"st": ">= 0.2.3 && < 1", "ftpd": ">= 0.2.4 && < 1", "mocha": "2", "stream-to-array": "1"}}, "2.0.1": {"name": "get-uri", "version": "2.0.1", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@2.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "dbdcacacd8c608a38316869368117697a1631c59", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-2.0.1.tgz", "integrity": "sha512-7aelVrYqCLuVjq2kEKRTH8fXPTC0xKTkM+G7UlFkEwCXY3sFbSxvY375JoFowOAYbkaU47SrBvOefUlLZZ+6QA==", "signatures": [{"sig": "MEUCIBjxAqUDmawK9Krb8GwTHh8QNUoekAe0utfihj/tUxfsAiEAtlRlIa1iJH/OMHZlADwayo27t9FmiM1Njk7uxeYMITA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "52dee08ad45b48882029163d19f49d2ead961270", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"ftp": "~0.3.10", "debug": "2", "extend": "3", "readable-stream": "2", "file-uri-to-path": "1", "data-uri-to-buffer": "1"}, "devDependencies": {"st": ">= 0.2.3 && < 1", "ftpd": "github:sstur/nodeftpd", "mocha": "3", "stream-to-array": "2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri-2.0.1.tgz_1499796650884_0.35804933751933277", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "get-uri", "version": "2.0.2", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@2.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "5c795e71326f6ca1286f2fc82575cd2bab2af578", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-2.0.2.tgz", "fileCount": 21, "integrity": "sha512-ZD325dMZOgerGqF/rF6vZXyFGTAay62svjQIT+X/oU2PtxYpFxvSkbsdi+oxIrsNxlZVd4y8wUDqkaExWTI/Cw==", "signatures": [{"sig": "MEYCIQDt5vb9+ijm9eLPNRmWlHqpBJJj9eZ3xbV2uXIRxFf+sgIhAI2aFS3OZWEF/0gwgSIzo4lWfkuNhMvoxNbPI35/XOk3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+w+hCRA9TVsSAnZWagAAlrwP/iNBnCQb/Iskh4yYKhBB\n82JI7/HLn69PKbdACHSC4FmuqZ/kTfdi+qrFBJO77IM5Es/2QffEBR5ggIzw\n4ZejYe4cVVgSfGnukjn+5t13gaRvZKmQRy0AZgg5LUgEQ6DE8I0McClx+ke4\naEEHd1CVwXipAPBZt7vkp6TPMcPKoEHlExWOiZXpILd1egpQXfiDFn/x9ow0\nnfa98R4/4eFX4U70ZDGgMPcwz3vzCFqmPuhyuxNW5/b5Gx/UBnbjoPLFDQol\naPXV+HehBeZkPdQhoftmDnL2u0YYJZ5nZb6Yb/OimdOe13mXE/oXHQwwrrvn\nzKHvPKlaNnnYSk7rfqNFy4gFDwzSiFC+WelrvNotovDAyYua1F4g05Zufj09\noG2rUB1QybhCUPF3ssHugwmBK0UpNY2iZ5WYjIuN5U+COV51FAz+4wUM41ul\nztOVAfqAomUaW1ccU+q0jGj11amE4NFnQ33TuHxsTAUB9Ui0LUB9XgurP7Hz\nhJrphXFYdvtRa1+opr/Bwil+NwHQ9UDBaYudAppKbjNNCTX3bC5mbaxWFjXq\noDrLVLkdhl7cbxfFlcYbYQv2fVAJ7OMf2rGDaI9PxMxpbo2ytY1Vxfa5YdvT\nuiAYxH+FsQnJgnvB0Z6ZTCd8fly+XmQIGJp0TXtBBYt+lpepu69iJt0f3AZk\nXYnD\r\n=Iife\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "eb020f283c9ac57aa2521257c17197ad3ffd5048", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"ftp": "~0.3.10", "debug": "2", "extend": "3", "readable-stream": "2", "file-uri-to-path": "1", "data-uri-to-buffer": "1"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^0.5.5", "ftpd": "github:sstur/nodeftpd", "mocha": "3", "stream-to-array": "2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_2.0.2_1526402976351_0.4254732364786311", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "get-uri", "version": "2.0.3", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@2.0.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "fa13352269781d75162c6fc813c9e905323fbab5", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-2.0.3.tgz", "fileCount": 21, "integrity": "sha512-x5j6Ks7FOgLD/GlvjKwgu7wdmMR55iuRHhn8hj/+gA+eSbxQvZ+AEomq+3MgVEZj1vpi738QahGbCCSIDtXtkw==", "signatures": [{"sig": "MEQCIAk59ye/gDUHGjWhvT7BYKusOLxJGInLbmvc9nYBFJAKAiAgmYFiNhoFdsFuiWkBJfvQ3rjHU5h4OC20I6kxM8JRrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPwQ3CRA9TVsSAnZWagAALBAP/0CA2fQJReHhf11t7Zsf\nei3SfAkQ3NvQkSKVM7W8aiAkHH5rtCGAWDUKPqCSNJ5MFz1ik0M6E6CMQv0N\nczo9/7VIOA23HlR8uxTurdFUxAHh6+XzOXYok2eYbmWN8cMTASvYkpA5bhDe\ntbI36Gem2ixiE6DvecdfhGr+rtUZF+7g6v05SRqFWUTBcSpIhInpxs6B1KXe\nu/8sT4Qius2aXX81r5IeEdEzoCUJRvIuFMoDTsM2WytOR4nGknk8Z6sBjKPv\ny9cCU4pwDJLZWFWH/woXRtqgp+KSVRsEHKvBfvz0Vc+yh2V7VcCd07T7NSHY\nQXEsTM2Qh+bujLEcLU4V8vU7JlZSKJZ9dEfM/NUerS/gIsijYhQX5Qsic98p\nJTLmF3QyQbiFNWFmozMGthq36rioPmbxxfZPOtKWc5Rx4GF12Td6pgFXFZP/\nK+DiDE4+jrRI3FGvgR0qDTtWRyMDwu9uuFE0aLyTKqqGXWcNz6FHrUtcXRbM\nuYHeUwqu++w4YL+FHteqOBORlwp0K1Eokbx8iTrY9HPt+dUVXDYiJsL4+XJV\n/NU+q+kic4tum2101Qg/i3SpyTXnn8TiRwJ7l+aay+HkFDz/YY35g0qXqmGn\nzzIyxecza+n29n+EMSgOfjpYdytezCPW7x19Fcg3QRzm04K6MhMnWjXk4KT3\nnQ2O\r\n=3xTY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9336a588ff9b03de70d59f5c197ede62ffd9aaaf", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"ftp": "~0.3.10", "debug": "4", "extend": "~3.0.2", "readable-stream": "3", "file-uri-to-path": "1", "data-uri-to-buffer": "2"}, "_hasShrinkwrap": false, "devDependencies": {"st": "1", "ftpd": "github:sstur/nodeftpd", "mocha": "5", "stream-to-array": "2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_2.0.3_1547633718761_0.6664359596056204", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "get-uri", "version": "2.0.4", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@2.0.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "d4937ab819e218d4cb5ae18e4f5962bef169cc6a", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-2.0.4.tgz", "fileCount": 21, "integrity": "sha512-v7LT/s8kVjs+Tx0ykk1I+H/rbpzkHvuIq87LmeXptcf5sNWm9uQiwjNAt94SJPA1zOlCntmnOlJvVWKmzsxG8Q==", "signatures": [{"sig": "MEUCIFaf1EjEjM3eKFVVELtK4GDXA17wUT7boRlPeGcsHkkGAiEA/6xfJDZKv0reGleKefm/fh4M92lJttHCGlirUp5dx5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn4Z4CRA9TVsSAnZWagAAJcoP/1rdgBYA75cVh8Bf6W3k\nICadTDCNXizEjJiv0GcKky/d6F0NRYOr5iq0sumkwtXKmtseCdYs6nO+nB+l\nhTqwjQgxNQ9DD8e2U1eCidfpTOgT6SoYQ+rUHV3CYVGjKGOTQeYbTgZF+2li\ndp2/Sl70+Gm+XLgWOPS0adtpuovtsUUK+YCyvxk+gH26P0IM5neywMeLDMLg\n10a7180fv9PItmgbsET1khBGlwhANN4GRaePvfjkhml+hF8drw2xYYtiKcCy\nbF24lF4otkSjoe1TpEKRocB0ckpK4K3kWMkAN9/pMB2vEtUhawDTKhHNqTj9\n0XsAVGT7a+C68Kf+UUPH+DU1szo5HEbllWbP++KORH40QvQqX3UHrDFJJ4va\nItKH98HDTkn2KdSMZBLxwna+QqBd/0qFmufQZq0Xx3fwudfeOJIBj2DSKJFY\nbwH93mgjGl90ky2HKtT9X2gzC8DW+DHJCWop6BYPPoeGfisDZMLoIYZrZHlc\nAWxZdMXQ3eab4PB44wv6nC6MfnjNSsevEU+bpg9rBUjRTzvPjd9rtMOTiBRn\np0LOZ/B7adX47n0YdClFwk6q2SND1VnkVR4PUWur7czzjiEVhXip3I0L3WBx\nmDL+knS6Gg3jdqtfDMDfQ0YZxNKifvijjOme0LFdupeXtT2PCA1bXWKc23Z5\nm8Qq\r\n=d0mw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "b1ea813043728bd0ff98266494d83eb663ec8556", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"ftp": "~0.3.10", "debug": "2", "extend": "~3.0.2", "readable-stream": "2", "file-uri-to-path": "1", "data-uri-to-buffer": "1"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^0.5.5", "ftpd": "github:sstur/nodeftpd", "mocha": "3", "stream-to-array": "2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_2.0.4_1570735735947_0.575295699652207", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "get-uri", "version": "3.0.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@3.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "8d18332396369002ec1b613bb64c1061a5f19a82", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-3.0.0.tgz", "fileCount": 33, "integrity": "sha512-0KeVihKo6rC3koA1xac0dG2BglKR/h2ttwuYJGEuO3CGqVq/5jArLIK+DpHLBalKVy2yFAylBYX8l4188vGO2g==", "signatures": [{"sig": "MEUCIQCEs1QXsJmLQQ8ak4tTB11L2y9Zr1Zyfg5pJPaAjh3/gAIgBvSq7W53iTGgrqeE2dhmJv7ZUN8YPO0qpUy51N+n5ns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePcYlCRA9TVsSAnZWagAAigQQAJrfUjMrYqZcHSoB6NmE\ngmIQUmRA4/lyXRASwW92PBNhq8419jLHgIaxmNZREa932f7xUXUBZp1wXZ8U\nlj4xB7idMnbKfYqdqrOQQb3klURGsYLxD96Ch7pFKxs3qpArmCOxJQAkHgSX\nvWa3XzCuWn4DljyqJEA93T7N95QfeOgmz+oi3YRcHWiFLW9WgBGezHvGwS1X\nUVWadKM9fdjTnEzqs12wL6UvJ+L/0c2XKyxJBSk+7XXZLf3PLzBWTMyldWxC\nNuVPPU1Q98DzVcscy7BlvzCmIyk945S/FfKeRylEdHADKl5COf4/eMp8h76g\ng03zr3BrO1rIky1lUFtuj0gAOkER27Y8gz5NQO5YDUSPPsjtbu/hi5lnZLTK\nKMdXFbnEZOT26Olo3nYQEaUNOu7Ue87uWR5twu7iDXuGmTAQoQ4gDCjQeQdQ\nJCv4uiDNTlL57VRFSErS2NVBueGJyPssZP92slqoAnr4wJ8aUHLwMJD0SpAn\n38PX2OR+FSA/8G0NpPJJFGKse8Ib+c9Wg1o0I5mEFRV6h8FP2n4LqvoM1yV4\ntB+eIHyPhgA2rm6F7FuO1X5kRjzl9nq8Yx63GHgiFatK54qcYqL3oCLpcTK3\nE6NoVuccr+dG0q4Peb3TPGyNmYdT63Lz9lw7+p4CEKFU5LRXIR4+yBVtUmTK\nWTTC\r\n=rL2g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "cf28e588b2d5bcdecc1126e27e29aa564bdc6fab", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ftp": "^0.3.10", "debug": "4", "fs-extra": "^8.1.0", "@types/fs-extra": "^8.0.1", "file-uri-to-path": "2", "data-uri-to-buffer": "3"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://ftpd.n8.io", "mocha": "^6.2.2", "eslint": "5.16.0", "rimraf": "^3.0.0", "@types/ftp": "^0.3.30", "typescript": "^3.7.3", "@types/node": "^12.12.11", "@types/debug": "4", "stream-to-array": "2", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_3.0.0_1581106724768_0.41689305776854546", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "get-uri", "version": "3.0.1", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@3.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "7c371476827487d6583a3a8a369d8ab8135d0c32", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-3.0.1.tgz", "fileCount": 30, "integrity": "sha512-daU+rXdaAsTixSFwf6Qn0dG8a2MveeYITeKT2mPwxCTqW6lbggh2vTowKgPJJi6hHATfpzBKw3GTDjNmf0dSiQ==", "signatures": [{"sig": "MEQCIElL2dIqCGvB0MBUKL1gvPT/qFJGaeSjL7/acuEXy6tNAiAx3s1Za/L5ToGibyILO4Y9ZoAB8eGX+AVIADmoTHOKFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQY/NCRA9TVsSAnZWagAAjRcP/jFhCEGF3iAkhNIcbXHM\nYl9sWp8+Da6B+suhgkhLPO/wbRiXayDtstVd0o8TlwW/xxlfEQEMVS65sj+a\ndogfxYYLjX452meN+Hkg4I1mB0S13pdARl51khnChOTKoFAG21jwVMF/1GVc\nCfLN8yTQMWTTadp2vCVyUlPVSHhuM22XUsoDVCBX1gTxTfLbdRZ1zo+9RMN6\ngxthDRPcz9V+mg+wsiOJW0pwtOav2cLRbSpBBJaaVICShh1jXsSruwhthp4k\n0xIrzQ2t90Yb82wtpTcBiklvIIilTTjntGQdEDYZqWrwH05xNjJpFRbeCEEL\n3DqHynkaOuNu+oiXKA1gzUDTa8U7+lky8m29tXFOcz15jaHx0b8sL0UrPEgg\n1rV6ts4J+7b5AaycBrSZpfEiMVbqhZP77XQPHnohKiCvXIudmnvs7szKD+ck\niCNRoWoOJaksDutqoPGHgNqQQhb6zylSPSwvSE6yHARz7jKQR3tZUajPX+6M\n1gzNIPEIEP4G80Y1nciSt/rvWoFAhqyeYNni+xaJuf3DBWvzRUd+eTIBRqgW\nxH2XVxfJ8HqrH0erfuwvn2KxszTwmml5C3V2XagNObswnBLyAG5oOjHNabEl\n48OGoZiaJnAezYr+5YWFu6pbUvAPeKZdSas1CExGsyW5kF6yPzewr1Z2emm6\np19x\r\n=4cvw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "5eb62d6c57f9a707cc09e70648d8ce3982ce9d7e", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ftp": "^0.3.10", "debug": "4", "fs-extra": "^8.1.0", "file-uri-to-path": "2", "@tootallnate/once": "1", "data-uri-to-buffer": "3"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://ftpd.n8.io", "mocha": "^6.2.2", "eslint": "5.16.0", "rimraf": "^3.0.0", "@types/ftp": "^0.3.30", "typescript": "^3.7.3", "@types/node": "^12.12.11", "@types/debug": "4", "@types/fs-extra": "^8.0.1", "stream-to-array": "2", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_3.0.1_1581354956987_0.22512058428253812", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "get-uri", "version": "3.0.2", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@3.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "f0ef1356faabc70e1f9404fa3b66b2ba9bfc725c", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-3.0.2.tgz", "fileCount": 30, "integrity": "sha512-+5s0SJbGoyiJTZZ2JTpFPLMPSch72KEqGOTvQsBqg0RBWvwhWUSYZFAtz3TPW0GXJuLBJPts1E241iHg+VRfhg==", "signatures": [{"sig": "MEUCIQDbB7FZvIv2FUdNm+zIYM1klDKqyaut2Efwlpbcj56GDAIgMWMYWvnppOYvhpGYZbENGeI3BbX+MO/ss1o6iRlBOL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJej52gCRA9TVsSAnZWagAAPJsQAIZMjNEZv0MMDplUt3cv\neMKsygMow/MZIvG07ZCs1VvN2hXBciib8IWSezPiHHL6mIUiywVqgjni5iA+\n/sqRr1ZKzL+SHq1xf7LUSFuuylKJ+xGOsojlfdbfjbmVdaRIkFcd8lnSaLgb\nGgMonBw8AjsCOI/PlIHJwS4mKqOfQy3mHnHLdB0tkmkOnnQNsVM/JGvOnKN1\n+q0v0m1md+1n5EuOq2U+4N6qABf4YvCsLyiTf0ytAT6UD9kvMkYSxuwebaPU\nmY+7/KwGf/bgzsgXSc3kmGU0pp77boI5niOzG6IuAhpXw/jYFIeR7sgo3RYX\nuevctqqXEUv9E3oE2UARot//LvAn1MewCxGQi+5wiLBtJ6ynOAdbyoub3KXG\nv1sZmRVSstBvJYKrZJm8mRXrmGtnqX+XemQFLXnSpYLcAWIH2791FDhOgKwp\nUzd5Wlt0w2qvIsfF8c1e29Mv/IVoThOjlV9CzhnZSs/uV+JhTnVMhq8lnnCb\nacZMQRq74Mfq2zNZZb7G2Me8L6hUQpL4NB7Z2eempDj4kuASbO+jE/p+Boht\nfJHyBlNtD5DTB18b5ML3ZHd6l03b27Ps1/4xKVNhfTQez/jM39Rcg5EnGIhj\nHWXmyZqEPfryswZTRQuofYcYEZDcUeYk+GmQUaopBRyINc1KyrmoBrFtgLe1\nS0zh\r\n=DBIQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9d4a98f7a093aaf96ca4ae99565ce39a0a18cd2e", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "13.12.0", "dependencies": {"ftp": "^0.3.10", "debug": "4", "fs-extra": "^8.1.0", "file-uri-to-path": "2", "@tootallnate/once": "1", "data-uri-to-buffer": "3"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "mocha": "^6.2.2", "eslint": "5.16.0", "rimraf": "^3.0.0", "@types/ftp": "^0.3.30", "typescript": "^3.7.3", "@types/node": "^12.12.11", "@types/debug": "4", "@types/fs-extra": "^8.0.1", "stream-to-array": "2", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_3.0.2_1586470304229_0.5636272913557767", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "get-uri", "version": "4.0.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@4.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "b1777a6617f1b847fd1c3aaaff44550e2c946d15", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-4.0.0.tgz", "fileCount": 29, "integrity": "sha512-r7ICkxG9Tmq09wSQGqBulOWJf4Kj/tqyeBQJ2fN/By4y/eaMHNfuscRBQl1my+WOC5D3PEszKzg7hfaZ2fDSkw==", "signatures": [{"sig": "MEUCIBjFZQg7SDgUA7OCesLkm6BWyqMf+Xn40+rZo+U9MTMfAiEAwM9Lo86y93vcYV2ytaK4Ff1S5VyvgEJn31VGg6Czub4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2sMaCRA9TVsSAnZWagAA7qUP/0sfLTLFCnpGEpi+U/Zk\npS2yFV2dLWUnFXXdcs0/pxGsXhLML/bv7uZ6/f8wjVj+pXhwLNAwf5DJ9HgI\nx0QP8TOuGlQ04xfkqMk0Vn6hJmBusVS4jFMBKDdnqUXc0IR1orbe2K0lD8KO\n2Wgcs+6XpzdtJ71kvt5BpO2fAqKSxXPG71dL9Pd9qoFoJluSINwxUP8byUZZ\ne+GH21VZT+7DO++AmdSaYBfXkoa2iyxgEar0rPSXTiavlf+Qzq9QKGJpF31I\nSysxyAHdWPPjLqiQOij6VHr3Y3IOPfRAvUVBn9KFMylD1+YDUq4X2drUncDu\nJNgKR+ueRzTFbc7tq/sXqDcriWnAZ5gXjk4izn74ZNSDuo23TVoMrFy5YDKz\n+2qwAP7GmyBzvSdV/As3TdWa8yHzlBe0xFBauWK3b9O3Cbp5OsSqfVX2nqEc\nw6riuQiajygWhqoifladG/NR8NmbbJItmbFsCL2Afc0gOsKS4b3R5nV9F3X2\nlP55K4RnUHf/BpmNEPQNi20vw3he5sveeBiYkVnbCJUsa2AP3iU/jUnypEe2\nk97xW9B/hU4vT3D6IC/yJOX85SEZmcEX/4jQL0PJHMGNtAmeh//tTVka61HT\ngjuYbPAkFlHD/+YFxGH85yOPYnc1jFcjhqJb/prZ6QHvn5Ry8d7B1t8ewgsu\nMlNq\r\n=pn3f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "fc0f1aa92c3971a10bcdc2f0a753c6d0ab68b67e", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "12.22.6", "dependencies": {"ftp": "^0.3.10", "debug": "4", "fs-extra": "^8.1.0", "file-uri-to-path": "2", "@tootallnate/once": "2", "data-uri-to-buffer": "3"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "mocha": "^6.2.2", "rimraf": "^3.0.0", "@types/ftp": "^0.3.30", "typescript": "^4.4.3", "@types/node": "^12.12.11", "@types/debug": "4", "@types/fs-extra": "^8.0.1", "stream-to-array": "2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_4.0.0_1632377963056_0.42972079741966906", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "get-uri", "version": "5.0.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@5.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-get-uri#readme", "bugs": {"url": "https://github.com/TooTallNate/node-get-uri/issues"}, "dist": {"shasum": "78a3c9f896f3d8c27a243e6cd4d44b5fac5d67cd", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-5.0.0.tgz", "fileCount": 29, "integrity": "sha512-ZpgjCNTMz0s+kcQghWHJrCZ/4LL5sQp6ASw3LquQLiLaI1zd+WU1ZuNiFxPzZNZVDuSM/ZhCZIbEnK731C9Ugw==", "signatures": [{"sig": "MEUCIHDMY0SwljbtfFXCG3/3D3TR7dWaN492rFRe+bOpGtQjAiEA0NO1/Ms/ZVBEfNgg5j5yegtDHGT8LTebmb1pY4Qz6U4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSKpiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9yA//dD1PhI8b8qF95BKrlpTth+tmDHjo1uR+vGYlujbOS7KpyXrw\r\n0Uqe4igJk0FRxQeOVZWLzR2iy/TOHZJ3VrWsV8WIwkC02xBIcuJDiHWt8ySe\r\n5Up7icXUPXX9mNLsJxlVMevn57ninULUNdfMutdEXVCruFp+WDx64w0dSs67\r\nOeloIbeTwqveeihW7+ukzPXNZWT/qhR/kvKEb83MvPLr6ILqwwpn9z/Osw03\r\n+Hq5CqsWga1WGX90e1vKqz63Gndl5Y8DCJExGCZyFV77NNyT6R4YQEsPG6xE\r\nF4PaJoxsHhSd0nlyMojGROsffutP+2oX0WN5pwgtjl1GQFZyKmh+qH8lTKNb\r\nQ50yWumxPiNZbz7CXFe4nUww7dAS+L4egPw9u8HNZm5HqfBw8aXwn5KNSq0u\r\nFzejGNi+gZvcb5FD9Fzd6nvnp0D6HjkKsqDUpZfFnihFiicKDI7sCaBlifnu\r\nqtfyPi2pW9Lkect5G4Z7B5m4INAn9yeTgKm5rnpmmw8HV9Eg4Zcfpqq05I67\r\nYs7M33WaNpM4xZ/hy+YhpNMmQVsYMD5HKLnnLhygLpSJnpu8SC69RKNya+kx\r\nFFZvunhi1bYu3Hg5PjDucarNO+hvCWRGUyH106XUa/3+aSqagyPeYUVOR9H0\r\ntfECdDZOBdNpgtQBDzIighNClcVrUIF+mrs=\r\n=4LvQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "gitHead": "3a0488e380b452b4ddd715e9ea219a1b58d34374", "scripts": {"test": "jest", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-get-uri.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "19.8.1", "dependencies": {"debug": "^4.3.4", "fs-extra": "^8.1.0", "basic-ftp": "^5.0.2", "@tootallnate/once": "^2.0.0", "data-uri-to-buffer": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "rimraf": "^3.0.2", "ts-jest": "^29.1.0", "typescript": "^4.9.5", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/fs-extra": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_5.0.0_1682483810119_0.5774690442903083", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "get-uri", "version": "6.0.0", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@6.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "a4ceda170643fc2a9a3fbfbb5e4e3e4b44af95ca", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.0.tgz", "fileCount": 30, "integrity": "sha512-XxbO7y+8c+srFbHbeHzXMQzARaGjkS0oOwYHCfmtPhQ7+7qgv/cBLOUJt2wJGjp1kbeWVGK7F6T62qDiVhMxng==", "signatures": [{"sig": "MEYCIQD4/T0eFd1r1cFpTHmHmki9AuXlTYeOPCkeMDgwSnDyAAIhANnjknDHHaTt7h8PbRDnw7td5r6eLXlII7BXGtDOPOig", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowsRAAj/XeTr7VNQ23v/QZDOqP76Gak2qW/P9OZp7eUnR2523sk7sU\r\nmmwN8o/yJ0PHGoiLNQNxMyd/QSjHCMvapLDlfWLv0FquVAyNLtoXGRlrlIEK\r\nhZmwWlc18LPQ9vVmbDcyoUTW/M7u3Ax9GyWT7prp6zcfCv5hip0fAQN6ve9i\r\nZKyNgGsdEMJCy8KQsvbwCte+gs4sWNhUKy4r7+g/TAWrOHbINW6ngOscokoI\r\nsquwK8iwrhbnFycTu9Rnf7Xcj4NWkSZLjJPGcn6v7DARDdvRUWGIReEu6uyq\r\nBMBJwlmvq0JDyExGP4YT2oZ5HigLdjJAQIPwHdPstrw8eGjJnBDq43cU8RX7\r\nL1f/fry5oD1IPanmDREqN/f1cJPf9L7g+UU5bSCZ5fdLYZwkSI99pgkK1Bei\r\n+izZKwdO/44M06P6BeCiuEg8r+JgmIDCK+ze0Oh6c+fgS7SDF5hntrSYMUF7\r\nuy8H+2vUy8hobVKu3xxzxpbUQYVicipTXn9aKwS3kt0xW84fgBpPUQp8eKpF\r\nPiSci1BBKF/1ToSL3GAuOfLlmrN9jGgkc/bSR/fdjJPmRn1VxjvF2dhDrhKK\r\nSb2dwM7cPE6PTrQe5PB3+v5pQYoK7wk6EO1q0Q+Zp27n7o/ltKUT3kA9WALZ\r\nRVd4PClz1umgWEDRhojseqdolNtmdPIEGcM=\r\n=9PsH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:get-uri-6.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/229e69a0d6c35df99c362034c992e83f/get-uri-6.0.0.tgz", "_integrity": "sha512-XxbO7y+8c+srFbHbeHzXMQzARaGjkS0oOwYHCfmtPhQ7+7qgv/cBLOUJt2wJGjp1kbeWVGK7F6T62qDiVhMxng==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/get-uri"}, "_npmVersion": "9.6.4", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "fs-extra": "^8.1.0", "basic-ftp": "^5.0.2", "data-uri-to-buffer": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/fs-extra": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_6.0.0_1683232399556_0.9516038327725609", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "get-uri", "version": "6.0.1", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@6.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "cff2ba8d456c3513a04b70c45de4dbcca5b1527c", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.1.tgz", "fileCount": 30, "integrity": "sha512-7ZqONUVqaabogsYNWlYj0t3YZaL6dhuEueZXGF+/YVmf6dHmaFg8/6psJKqhx9QykIDKzpGcy2cn4oV4YC7V/Q==", "signatures": [{"sig": "MEQCIBzc/oDLcSxP+RuclRC/59RUbVrXT6Q/LvKKV5LwaBRzAiAcIElHa0VJrXxQsm+Mvaez2A+XBUQCGxKl799E7lsnfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44258}, "main": "./dist/index.js", "_from": "file:get-uri-6.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/9ee4a2df1479dd6d88ffa201bdc93c2a/get-uri-6.0.1.tgz", "_integrity": "sha512-7ZqONUVqaabogsYNWlYj0t3YZaL6dhuEueZXGF+/YVmf6dHmaFg8/6psJKqhx9QykIDKzpGcy2cn4oV4YC7V/Q==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/get-uri"}, "_npmVersion": "9.6.4", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "fs-extra": "^8.1.0", "basic-ftp": "^5.0.2", "data-uri-to-buffer": "^5.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/fs-extra": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_6.0.1_1683324250263_0.49715270454271243", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "get-uri", "version": "6.0.2", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@6.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "e019521646f4a8ff6d291fbaea2c46da204bb75b", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.2.tgz", "fileCount": 31, "integrity": "sha512-5KLucCJobh8vBY1K07EFV4+cPZH3mrV9YeAruUseCQKHB58SGjjT2l9/eA9LD082IiuMjSlFJEcdJ27TXvbZNw==", "signatures": [{"sig": "MEQCIF70WDiaQYXruACicCdZn47pBwoU0CNHmhbs2KlL+GVXAiAPijbnaeNJqaXPsdk8fw3rUIuSPiHI8jyxb5uXonoeGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44282}, "main": "./dist/index.js", "_from": "file:get-uri-6.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/fab7384c7142843be4f7f2b8a1274b72/get-uri-6.0.2.tgz", "_integrity": "sha512-5KLucCJobh8vBY1K07EFV4+cPZH3mrV9YeAruUseCQKHB58SGjjT2l9/eA9LD082IiuMjSlFJEcdJ27TXvbZNw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/get-uri"}, "_npmVersion": "10.1.0", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"debug": "^4.3.4", "fs-extra": "^8.1.0", "basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/fs-extra": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_6.0.2_1696084192777_0.7952859508470513", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "get-uri", "version": "6.0.3", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@6.0.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "0d26697bc13cf91092e519aa63aa60ee5b6f385a", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.3.tgz", "fileCount": 31, "integrity": "sha512-BzUrJBS9EcUb4cFol8r4W3v1cPsSyajLSthNkz5BxbpDcHN5tIrM10E2eNvfnvBn3DaT3DUgx0OpsBKkaOpanw==", "signatures": [{"sig": "MEUCIQC6KWCSL9uKI6nXfH9hVsWSY4Jp1bg2blv/43pBv53S8wIgelGzg83b9KxEfLCrtjlUlzeqmJN8Hl0H3EPkFE1PFm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44283}, "main": "./dist/index.js", "_from": "file:get-uri-6.0.3.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/68e5a1c467c53693bf608b06d794e924/get-uri-6.0.3.tgz", "_integrity": "sha512-BzUrJBS9EcUb4cFol8r4W3v1cPsSyajLSthNkz5BxbpDcHN5tIrM10E2eNvfnvBn3DaT3DUgx0OpsBKkaOpanw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/get-uri"}, "_npmVersion": "10.2.4", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"debug": "^4.3.4", "fs-extra": "^11.2.0", "basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^1.2.2", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/fs-extra": "^8.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_6.0.3_1707762261692_0.13626598687265212", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "get-uri", "version": "6.0.4", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-uri@6.0.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "6daaee9e12f9759e19e55ba313956883ef50e0a7", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.4.tgz", "fileCount": 31, "integrity": "sha512-E1b1lFFLvLgak2whF2xDBcOy6NLVGZBqqjJjsIhvopKfWWEi64pLVTWWehV8KlLerZkfNTA95sTe2OdJKm1OzQ==", "signatures": [{"sig": "MEUCIQC/OVH4OmqSkV297IUAPDyZ6L2InOeZEq6gCh8K2t0FowIgQs/sA8lCINc9KMuT3GpJok4YqQWK5bxeOKAhykTTG68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44148}, "main": "./dist/index.js", "_from": "file:get-uri-6.0.4.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/71a9234f5a108c19761364602a4f0f7f/get-uri-6.0.4.tgz", "_integrity": "sha512-E1b1lFFLvLgak2whF2xDBcOy6NLVGZBqqjJjsIhvopKfWWEi64pLVTWWehV8KlLerZkfNTA95sTe2OdJKm1OzQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/get-uri"}, "_npmVersion": "10.8.2", "description": "Returns a `stream.Readable` from a URI string", "directories": {}, "_nodeVersion": "20.18.1", "dependencies": {"debug": "^4.3.4", "basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"st": "^3.0.0", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-uri_6.0.4_1733268692288_0.5559644320227335", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "get-uri", "version": "6.0.5", "description": "Returns a `stream.Readable` from a URI string", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/get-uri"}, "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/debug": "^4.1.7", "@types/ftpd": "^0.2.35", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "ftpd": "https://files-jg1s1zt9l.n8.io/ftpd-v0.2.14.tgz", "jest": "^29.5.0", "st": "^3.0.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "dependencies": {"basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "get-uri@6.0.5", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-b1O07XYq8eRuVzBNgJLstU6FYc1tS6wnMtF1I1D9lE8LxZSOGZ7LhxN54yPP6mGw5f2CkXY2BQUL9Fx41qvcIg==", "_resolved": "/tmp/6216b68e2f458b2bcd5c60e95501762f/get-uri-6.0.5.tgz", "_from": "file:get-uri-6.0.5.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-b1O07XYq8eRuVzBNgJLstU6FYc1tS6wnMtF1I1D9lE8LxZSOGZ7LhxN54yPP6mGw5f2CkXY2BQUL9Fx41qvcIg==", "shasum": "714892aa4a871db671abc5395e5e9447bc306a16", "tarball": "https://registry.npmjs.org/get-uri/-/get-uri-6.0.5.tgz", "fileCount": 31, "unpackedSize": 44220, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCKwqGTJ8ytENUgiOeMtqaXBidMNze+JoY7gBbGS2cg/wIhAI4TFeaB1d8NZXXl2UJCgPoWnP6Ht0iMtYSWsGxDQ7xp"}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>", "actor": {"name": "tootallnate", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/get-uri_6.0.5_1751910243487_0.7580027355292769"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-01-12T22:48:32.824Z", "modified": "2025-07-07T17:44:04.176Z", "0.1.0": "2014-01-12T22:48:32.824Z", "0.1.1": "2014-02-05T19:12:52.411Z", "0.1.2": "2014-04-04T06:49:25.689Z", "0.1.3": "2014-04-04T06:54:04.334Z", "0.1.4": "2015-07-06T18:18:38.279Z", "1.0.0": "2015-07-06T18:31:24.354Z", "1.1.0": "2015-07-21T19:52:49.821Z", "2.0.0": "2016-01-20T19:45:27.226Z", "2.0.1": "2017-07-11T18:10:51.112Z", "2.0.2": "2018-05-15T16:49:36.502Z", "2.0.3": "2019-01-16T10:15:18.888Z", "2.0.4": "2019-10-10T19:28:56.128Z", "3.0.0": "2020-02-07T20:18:45.450Z", "3.0.1": "2020-02-10T17:15:57.131Z", "3.0.2": "2020-04-09T22:11:44.357Z", "4.0.0": "2021-09-23T06:19:23.206Z", "5.0.0": "2023-04-26T04:36:50.288Z", "6.0.0": "2023-05-04T20:33:19.757Z", "6.0.1": "2023-05-05T22:04:10.471Z", "6.0.2": "2023-09-30T14:29:52.964Z", "6.0.3": "2024-02-12T18:24:21.868Z", "6.0.4": "2024-12-03T23:31:32.556Z", "6.0.5": "2025-07-07T17:44:03.663Z"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["uri", "read", "readstream", "stream", "get", "http", "https", "ftp", "file", "data", "protocol", "url"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/get-uri"}, "description": "Returns a `stream.Readable` from a URI string", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "readme": "get-uri\n=======\n### Returns a `stream.Readable` from a URI string\n\nThis high-level module accepts a URI string and returns a `Readable` stream\ninstance. There is built-in support for a variety of \"protocols\", and it's\neasily extensible with more:\n\n| Protocol  | Description                     | Example\n|:---------:|:-------------------------------:|:---------------------------------:\n| `data`    | [Data URIs][data]               | `data:text/plain;base64,SGVsbG8sIFdvcmxkIQ%3D%3D`\n| `file`    | [File URIs][file]               | `file:///c:/windows/example.ini`\n| `ftp`     | [FTP URIs][ftp]                 | `ftp://ftp.kernel.org/pub/site/README`\n| `http`    | [HTTP URIs][http]               | `http://www.example.com/path/to/name`\n| `https`   | [HTTPS URIs][https]             | `https://www.example.com/path/to/name`\n\nExample\n-------\n\nTo simply get a `stream.Readable` instance from a `file:` URI, try something like:\n\n```ts\nimport { getUri } from 'get-uri';\n\n// `file:` maps to a `fs.ReadStream` instance…\nconst stream = await getUri('file:///Users/<USER>/wat.json');\nstream.pipe(process.stdout);\n```\n\n\nMissing Endpoints\n-----------------\n\nWhen you pass in a URI in which the resource referenced does not exist on the\ndestination server, then a `NotFoundError` will be thrown. The `code` of the\nerror instance is set to `\"ENOTFOUND\"`, so you can check for that value\nto detect when a bad filename is requested:\n\n```ts\ntry {\n  await getUri('http://example.com/resource.json');\n} catch (err) {\n  if (err.code === 'ENOTFOUND') {\n    // bad file path requested\n  } else {\n    // something else bad happened...\n    throw err;\n  }\n}\n```\n\n\nCacheability\n------------\n\nWhen calling `getUri()` with the same URI multiple times, the `get-uri` module\nsupports sending an indicator that the remote resource has not been modified\nsince the last time it has been retrieved from that node process.\n\nTo do this, define a `cache` property on the \"options object\" argument\nwith the value set to the `stream.Readable` instance that was previously\nreturned. If the remote resource has not been changed since the last call for\nthat same URI, then a `NotModifiedError` instance will be thrown with its\n`code` property set to `\"ENOTMODIFIED\"`.\n\nWhen the `\"ENOTMODIFIED\"` error occurs, then you can safely re-use the\nresults from the previous `getUri()` call for that same URI:\n\n``` js\n// First time fetches for real\nconst stream = await getUri('http://example.com/resource.json');\n\ntry {\n  // … some time later, if you need to get this same URI again, pass in the\n  // previous `stream.Readable` instance as `cache` option to potentially\n  // have an \"ENOTMODIFIED\" error thrown:\n  await getUri('http://example.com/resource.json', { cache: stream });\n} catch (err) {\n  if (err.code === 'ENOTMODIFIED') {\n    // source file has not been modified since last time it was requested,\n    // so you are expected to re-use results from a previous call to `getUri()`\n  } else {\n    // something else bad happened...\n    throw err;\n  }\n}\n```\n\n\nAPI\n---\n\n### getUri(uri: string | URL, options?: Object]): Promise<Readable>\n\nA `uri` is required. An optional `options` object may be passed in:\n\n - `cache` - A `stream.Readable` instance from a previous call to `getUri()` with the same URI. If this option is passed in, and the destination endpoint has not been modified, then an `ENOTMODIFIED` error is thrown\n\nAny other options passed in to the `options` object will be passed through\nto the low-level connection creation functions (`http.get()`, `ftp.connect()`,\netc).\n\nReturns a `stream.Readable` instance to read the resource at the given `uri`.\n\n[data]: http://tools.ietf.org/html/rfc2397\n[file]: http://tools.ietf.org/html/draft-hoffman-file-uri-03\n[ftp]: http://www.w3.org/Protocols/rfc959/\n[http]: http://www.w3.org/Protocols/rfc2616/rfc2616.html\n[https]: http://wikipedia.org/wiki/HTTP_Secure\n", "readmeFilename": "README.md", "users": {"vonthar": true, "yusef.ho.tw": true}}