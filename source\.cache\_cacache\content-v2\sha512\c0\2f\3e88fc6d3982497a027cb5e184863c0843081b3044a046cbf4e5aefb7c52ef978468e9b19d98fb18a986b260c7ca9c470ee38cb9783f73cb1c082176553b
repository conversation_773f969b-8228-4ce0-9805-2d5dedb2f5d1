{"_id": "typed-query-selector", "_rev": "33-2d4d94a5f1188ca8a49573d6b31d74e3", "name": "typed-query-selector", "dist-tags": {"latest": "2.12.0"}, "versions": {"1.0.0": {"name": "typed-query-selector", "version": "1.0.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@1.0.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "1cbbd28e483e959828969b041ff337cacf5d0b3f", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-5WiEHY8Ql2zSAR99VWUth+AgsnIXL4CGsmZkGdnrmcJgGMWVu7iOOfKkTekrV60HRvw8WgPkul3GNLsXGnfPig==", "signatures": [{"sig": "MEUCIEo6ecvLFHyBcy78mpkN94xIJwmzRJmu4yS9zZIuRHHgAiEAkPeNBVLJEI1qUmjYfofVRf6+5pbjj7wzOndbGc8cNBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrl9kCRA9TVsSAnZWagAANsoP/iGZC2pWTmLs0IVzyNPt\nmAStQ4ozuXd5vVcO5MYwoONITpoBWdzzVIqtMsPdvouRvaLOzmQ+JJo3lssW\nKdz76ouwQNtZpMllmOyImdunvOwNrhBa4gztAKfjWMvZh5iSdxhCosLzm/4H\nHJHgrtmevVMqgzCO5XS3dmCdZOLiqCoJ2CS45Zg09L0XzUW12CHfkgVvg/Se\n+a9we1iqvjTF3q5bXiQuPUhcaMmcgR+E7DFFOPTmW2Bg67YG75id57G56NmS\nIn2eLniNMk+wau4QKJ1Zm9j+MLrN2ngRg/TT09bBA2sVzn5K86gaKDE+RQpV\ncj8moVJ82ellFL72rRek2PGAP7RMnRDv+xm5Fc5+SRrO8AvMdI6KP0Z1y4fA\nm9EM7tNvhHCNBdm5LkAWjgFQjXTYPYaDi2Cm5OzrGVGm+T6QSzSHdz5X0EGG\nJb5Qp++9I6AkvmnHUhQdhK4/6Ti5MZmC81FUe0mcsl1cYREj1t7aEaUyS2Zy\n5qUX4l/wZXh4CnT1QxotU4depuusikZz+5mkHLEFuL+fNJYLxhUw94sS3IJC\nVKfYLjF4pFiWS3Zxko6Q4ryKbHiUrLswZq88uLyWOIK3tKDGN3ZzXwhTqjxC\n+r9PacRkZDOueSd7OYHs0ywhW0u80et2w8Z24QZNCI7Tc5BG/4jd4q9wEsfw\nIwKm\r\n=ZWbt\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "module": "./mod.js", "exports": "./mod.js", "gitHead": "93a88a8c11355ac4b17939baf06b8fa3eacc5501", "scripts": {"test": "tsc -p . --noEmit", "build": "tsc -p ./tsconfig.build.json", "prepublishOnly": "tsc -p ./tsconfig.build.json"}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "_nodeVersion": "15.2.0", "_hasShrinkwrap": false, "devDependencies": {"typescript": "^4.1.1-rc", "@gplane/tsconfig": "^3.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_1.0.0_1605263203645_0.14512356759434697", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "typed-query-selector", "version": "1.1.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@1.1.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "56ce9cfa1f0b787b524015b206419ea038cc4e7a", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-MV6peU6OkTX/rUmo3ebRY8qGgdjUduzjl76YpQDXK4CcLliRYroiAs9JVMlWzEctYpVuUvE6v5b539Ev0Ult7g==", "signatures": [{"sig": "MEYCIQD7hZu08wQcnUEIX9u5+xxwO349eMXcg8t9Lju1kNJ3TAIhAOaaLYOjMH2sKzFJcJiiO9xkkCzN6PDRNEaEOcvC+34T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfr5rRCRA9TVsSAnZWagAACvkQAISdD5J3Wj2qhjsdZDkt\nlNMGvcedvKMQPLNaSp2RvzdaeY58MhoWvWDcAjiDZeemIUgp6LqImYKzAaaj\ntG+34kgeaf3wGMcNDWp+KG33dxyYYgjZB0SsxDA/F3OmIjdH07zoU6+H6YJX\nzFM+e9L/5x/6GoZKoyZ3q9PKiGrJVtVA5z+gCbn1ozd5hxgw9BuE0tSve6Ah\nnY5GskRLXNLfpCTkOPnkT3j75vCYNKGFNX9BsIhU7KzH/cZl74xKlZ8ETpNF\nmTyoc8gN8sZ+mHoKPmW4b1E/WiDnOuKhPtn8vyZSFBa1qtQGhEYLG3QpW2Ym\n4iF22MYF6dggFa07CnOagW8ZNC0x/q7FaCfRW3WWmso9Ls6SNeyUEYs7gYzv\nnES2BmhNMjC/p1ehzggTd10tppVt814i/WN7rW5P7VSLYrb5BHWp9y9521/l\nxVlTCu9PTUjxTIcYts2ZwSdiJFrQmBbqDqjUEtiNrSAFoCCRfty++mNs8BAH\nTiUf1JoiGX/moefkZOI7uglhroh7b0ZVHfACpQ9yCPEnyrx6EB0JVww18iXn\n+67ctNISz06ynp3Qj8D7P0w/EF+qnUkKNDugDKfUQpYsMWQ3ZeRK2nU69ld6\nY8SHs4DFWht/VHcYu7kUkY+4tmZi+7MM9wxhmja0FADpMhMjcUNQzxGO2EBk\nB6Jm\r\n=esqU\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "module": "./mod.js", "exports": "./mod.js", "gitHead": "82b48cb0ec8e43bb28803975027c980f2e7e7efb", "scripts": {"test": "tsc -p . --noEmit", "build": "tsc -p ./tsconfig.build.json", "prepublishOnly": "tsc -p ./tsconfig.build.json"}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "_nodeVersion": "15.2.0", "_hasShrinkwrap": false, "devDependencies": {"typescript": "^4.1.1-rc", "@gplane/tsconfig": "^3.1.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_1.1.0_1605343952564_0.1106591990584791", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "typed-query-selector", "version": "2.0.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.0.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "c21748bdb21b34ff0d2b95faa4c2343cf4a7c180", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-7O3KMyt8lIzT256lh6l49LWnEZPDDfTeXMmV5YPeL4dZll8Pw7dJAmpqew/tgsu731xzZ+Kkvvduu+NROjSaPw==", "signatures": [{"sig": "MEYCIQDJ42ggEaJRIMpBRSgTOnKiVI44++DNvsCDP8j2tICmKgIhAO6peq7T/eMcxbLqUEVO09JBOd8GkUYCttNktSNCbiTB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfsJKrCRA9TVsSAnZWagAAFUgP+gN7ju/zT9/rT1iJJ7j5\n0ImRlWc93gDSOSfLgw/ekd0jqkee2Ww62yLadg8MNIJ4e5IUY1LH6LkO1o4J\nFK4qxkxkpkJafKEZ76ewQ6BGU+2mha4gZHG50DwSZEApalvswpq0L+PzONth\nrCxpqB2Od8kIPiNT7ftJEU/iZ8L94qxuSsLe9d9EqpwWNBOO8gZA1QoZxRbK\nVOAvFv7XY932fzJ2WRpnDigsVGMe+TebubqqQhHfwxQD9lo1QVPoEmCcHoWW\n6z41ilK1hAkcb6+9Ity9ut8GvCIo1/G/3+afQVV8bbSjzDjC91MMcdAETLi9\nM4zBMqVRAJiooZZ7Tm41iE3bIbyVel1W+zUdOTSL1zb9jim7zne/14nCfYH8\nfoifkzhSnS2oE4I7B5o+b48g1xS271fL5Da1moU3I94IeLsSPGpWjNDCzegh\nI8EDDKnExV2l4s7Vqs9S0eIA/TRrH5HYYLoELFa8iiDOIS61D7jFrTZbDNT/\nEOaS6Ik4WkvybJP6OP7Qursss2zibRyYmbN3LzosRrJNuZ0IGZ7uHB4qEHrX\nig49rADGH3jKLavWavncupTXBrvJS6s3o19v0MXHmbH2uohehtn9VQWL5LkR\n9l0xxL+ym+LJQz/c/z0r0cD5NAx+thF9LvDdpdD2PGhHLybrEMQg4+N/AIkY\nGiEx\r\n=sVZy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "gitHead": "9ac5b4ddb0e27400c1429a2e729e262215a5702c", "scripts": {"test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "15.2.0", "_hasShrinkwrap": false, "devDependencies": {"typescript": "^4.1.1-rc", "@gplane/tsconfig": "^3.1.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.0.0_1605407402985_0.9478957606567311", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "typed-query-selector", "version": "2.1.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.1.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "d78b7e5ba9965525a4871adcc6a329bd196593f8", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.1.0.tgz", "fileCount": 6, "integrity": "sha512-bmjSqmgu1cbDQCu7aL9JKfChDBdz67tpfd9Qbu2Mk+82pRAns0RW61fsaALqXYfHvj90SwioJMAS1bcQUO7LWg==", "signatures": [{"sig": "MEUCIF6/3Ud3j6vz5uOn/Hu/OISldjSZmOH1L2LdQJnh51A7AiEA3eg4ELyqnVRKO7dzlhg9Kss2vIEB/Id4sysoyh4UdPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0Ce7CRA9TVsSAnZWagAAMG0P/igKCUiO2nw+PJ39ydWA\nU4074+009LuiuKoXTGhFP8AM7eGbbzoqLnKolihuZnDnpCnhRABUYyrDIXuS\nvbJSaTb3IeYYzCoQzGqLmXIhwrG2/FA7b8oFRVe2ZlQjx7WHJq782LWzE0wj\nu0r7aJmVb69Nqwbl/Tg6cZge0hXuxHvBPcxQjwenSZRlb8VTqoWv0+fDkNbD\nkeWeZPzyeDfUhNTYsxEKRbbcsdTX++0SOD47kbQOzmsUyWwTaPxBYgRBL7KF\nLW/zOAlfJ4aFxd+YBXnR1nJdGPMWzsi2syrvST0p1BVTycCFwYcwu3bMQ8jv\ntDBobmhyRnOH9eWhMRQP+1RNgf8mXO5an32/0Zfn+21wwX5m1jA/wOAZi/Yo\nf+TUrReYJgb5yWXDx57gseEO+FRgLgHWtY67OFYze4ce8xKDQdgqEvozkzoM\nnttyuW1b6inJUXMVM4OW5uZJXzWWbs19BLRvlBK5dR70ryQeg+SyQqNu5992\n4CGFrjQJTdRnjzbHunR6FDHuBOyNk9Ht7eKbjgKLW8vla67b38zDPhB03Mxh\nANqNz+WWYSjU0vbZ66KjdhWF6KxBW3l0mp9PhOh+ApKfOKYaORh9CiVrv59n\nI2EI0+7ZrFUptDDvO39Zcz967Sh3KR81G1A2JFbNZwWzkvuNH1pT+64RiwFv\nZOJu\r\n=J1Yx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "a824c8676a5c717ca8b9764175b7f24c28708149", "scripts": {"test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.1.0_1607477178573_0.1790274430541552", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "typed-query-selector", "version": "2.1.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.1.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "10b3493bae63f74ba9150750aaba93408c8e4ca9", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.1.1.tgz", "fileCount": 6, "integrity": "sha512-SeG4G2JyLNghvPd/ESMZrbMwqGBVLVvC0puFWPl974QAsp8BgeeSJmrDVKuclVTe7h3jJnj4L62wYycZojIZHQ==", "signatures": [{"sig": "MEYCIQDfOG++QHbN0OHps/fTsirsYIs+uT2A7DA4A3KuZuqrwgIhAJ4YEnAbmZ6IhmgHAo9z5qPbjxx7eDFYwbDACl17sFdd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3/l/CRA9TVsSAnZWagAAu/4P/2hb69D3mhHLwwVl7xsA\nYOJS1foTMw9ZbjvHZQHlgZ6XrooQLUvLzAFJFVfAxjV4mli0qgoKKEJ5+fp8\nheIAQTd2FMBAByUKKCw+eyyvc6Xh7dKiqry1G8Neqwy8ORrgJqqyUpkRMIuV\ne91Ai8PfEeYc/uHml8lV/O2N+wxCDv+2AO4E5cd+DulELRxN+E4LIDpOpUES\nuQfeMmQ1tMTAxb4P84KmN1xrRqVDIdHM2cE91+BZbxt3keDsNSF7q+RSfshA\neu65F4yzZgCxfQ3Bcq/XBfrFXTYwXo52pgOgQglZ8QapnCQ85rUBxmVB2Pdp\nAelerHMymhEfNlZjPVzck8MoxnW5wWtWkiZIH2i3WBZT7doTKfEtzggBrWuk\n/o6Hjl9eytlQqHYCW3OY8n60k7TsdDUXD+CFhe6f1Vr2nNBRRZPAfKni0ZEz\nJl0ywSNJ40BAeufMZ+GDL0y9hZdvFNFZm1ozvJlOenuTL7NrZsMaraycPk/L\nGIaQbKscjmm7yco2oRfx58Y0a0HIPlN53YIP0s5JAlPWh4Y8byvs0FC5yQtD\n5t2y0ptyt0ncxbkBCK+0t3hGx4ulVP5JoEYL9UdmPd1QLVTJUU0TEiYOLjsM\n94VIAbPRtxxuno3G21X51cXzQk6ALpVrMMU9lVVQGNjVRa8XRoZz/DsS5lmn\nYpfs\r\n=X6Fo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "aa7dfc79c7e1c5537d23de5b4d8edca699493771", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.1.1_1608513919096_0.5060465623202735", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "typed-query-selector", "version": "2.2.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.2.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "b19eee04f17561e491b1bc4702cb959d53c5b09e", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.2.0.tgz", "fileCount": 6, "integrity": "sha512-SGhRXxUU4ikiD0eaDrpKvnrF9spixX78oufaQX/meh0ziekRo8ge/oi29LBba8KgAYp4HciGWdQ1GMBeqOuHdQ==", "signatures": [{"sig": "MEUCIQCU1IigR+tX81BHHtYUC7McFLcd6IMZO65bVrSzY/OW5wIgBqFyd8ILpuCZ+cM2VOkiB3JpabhdIFjK3wdugqYxDtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6aGWCRA9TVsSAnZWagAAhYEQAI0KssKZTSb4zR0Bmliw\n19P9yBDh5SjGrWPed39dn6jqT8AVTdgj4anai1S5Au1y+g/iYBwAlt+org7q\nEyTSWTe2XybNznnl56C/QLyw5oEzg7YPw5J+XOmhg7EbFI2jPDEvzd9ojBjY\nIp8D2JzpE4sxsnUriMhKGxWZ+0BqiIw3TQl0Qd2V4r2pl5ns6xeJn+1AwVKJ\nm6I6H4dH8JXYhiZAHGvpDGTj5G4hfAz9mvFmTkAiSw30gEcweexFSipDi4BL\nTuCgiL5C+JrKNSCgbsUgEbbb3kehUQoB0LMCRmu8YoVqGfrD+2YIyuDIYPYx\nx9SMqRWJJtxdEkUbUO07bbnC7e8fEQ/OboVA6mLJ4F2VEH0kgEctvDPh3xuu\nDBEgsvWomX+eipt6Tj+qFoqOT4gKj4o3XOJ6EYEJrTPP6S55jUxYp/wJlWC0\nUs0X6+/4K9T8IJ/3bQqiENik8r0SkA8AZjfb5IiO5qF2L+0A2BOdxzR6Zakt\nhlRUpch0AyFzWIFw38Yt9OWWDQseJN+k4/gNMf/je5aJjMPak/0rIw7W+eBb\nh6xb0BUVfJwWKL0kNS3SWxpoCkLImXoW2ph/n1GMWuDYcYyMN8zndXBuiU7v\n3ilvCgbaiutRxkTjPSyPExvOmjDY7Rf2CvQFQ/MZizTQm97Or2OTUau0CQyh\nC8MH\r\n=7wTI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "c74a64605842eba980bf1f7b7b8f16e399ab5c28", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.2.0_1609146773886_0.2404416455697278", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "typed-query-selector", "version": "2.2.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.2.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "0db407e6f438cdf39103be92ccc2b4bafca549a6", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.2.1.tgz", "fileCount": 6, "integrity": "sha512-88BhdY7TPUOmV9kg/xBRcYi1L1alb9TBOI4TzrfO1kGV2I/KpFB/z/SuwJVSmBck5MWB2rrcjAm/TmxJgZa+jg==", "signatures": [{"sig": "MEYCIQD4z7FH8LyxIeilrcpYel8AUDfG753H5YbiqdgZtNdqeAIhAKynFa+QQ8X9xj3eXCYXvY11I8MMDCQLw6HvKHHFsy9a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6aUDCRA9TVsSAnZWagAAnEoP/1cfEyTWz2PlVbQQFBbK\ncnkhEhSgmv5QjiRnHkaodY1b+FcYFDLpJ6RPsrY/+sViKe7s/Prluff8QWiQ\nyWVxtkDjWVMSvlXs0gdqtfJhJhCvFj9ZiGDQ7g1cvVCDICAky57+ITNGomK9\n7gX9ZrKyau963u/Z1XfX7b6s2zU2IV665IxCD/eRT3Z4+dOPSyA+Q1Se3A7c\nO6habOPJX+7djze8PfdfvZ/9rEASNChoP8YWG/KPAiUwEM0ePjr312ISHNMG\n4ujPJ6QcgZ+xTRc4nnaX+bNG5G/nv5ey0HK9qn8X3MvJVGlbz75Fld+7Wbxz\n4Myjz1dnifihpYPnJl0gzROxNx5BbTcngRqnK/GsXaNkwcv/8adQltXnGcoh\nZgP7Asv76GoDBrEhP1tKWws+7Ft7/VrfZkN1eYtPXdxH+WkryMOT2sSEk4Qj\nHl/SWvGdjqELtDpUypgnKXK/2IZ7KiFD+Ic/Bbb9z6puA0Zmore2fzux7ja3\nSfZNO0VcidXcLujyM6/c2qfKwTC9icrHDMQmsXbQRKRYdK1rytXH+73c8JCM\ntD3qMO7YNAGD1pg/+dCGbHRG3vnh1GsscUEm1NPKDPEbfFfYRghzc0z98zXJ\nrr1oVIP4I3SWyetiXMmmtB+31V9DYAkWE4IpCU/RyJPIPnY28akCD69Aj3Wo\n2Fnh\r\n=XyLu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "7835791490874ca24f08a5dbfb34591b692b068f", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.2.1_1609147651246_0.621925355004149", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "typed-query-selector", "version": "2.2.2", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.2.2", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "cc09b851048ca8a20339efc7fd884d4523a36a5c", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.2.2.tgz", "fileCount": 6, "integrity": "sha512-rmfRSSZQeHQUKjsY4/bG+EeGQktvqHPnUUkMN8RYRJavNdgjDrp2O+URHbVTLCLgj340alBK5jl5VKpxNNMp0w==", "signatures": [{"sig": "MEUCIQDpUJi4Bngr0B8iChXirkl5SXLvGK3M/fHdLAUfK6aHFQIgUUAwEEqA0brGqz/e8ylmQirEoJhealkR5Prhio867ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6vjcCRA9TVsSAnZWagAA4VwQAJdyTWwn4BosQGY1BM2+\n+ovvtptqaShw1By+Y5AqEDbD9/unfrvWQcH/mNUZBLElHpan5vrnGSTSKIQi\negTcPPwzY26euhuFWasqxUEvPdAnmiMiwaQ9TMJCoqnedwuVoN0evWQ7BZid\nebIRNzkFr8u913iTMuieF3mNsUhQ90EJLkhL6UuMHyzVYp4CPiONQOzpYpFK\nrEiW8wAqA924c5L3YRI1UsoOQJzjkkqx8kQ7YyW7KTYOaUxDT9+lnipCc6w2\nr3DeohtgtlV+eT9LI8p+0xUW2RcnaCWWl4ECNdRe+pMmRjxKEE4CzyWlaRxk\nB4+NCao3j3BSyXr9DAGgZvZUSyJfqv2Ax7ZMzMi7Oa2UYbuZqQM+u2rv9PMq\nwl5+xhYB2guf20Jwac3pzlRVoGZymlrBx1PyToUl7QXGBkV8wgnolIg4LQSY\nzSz/rKnW/S3fbX7G8tg1BbubsVeQbGrITTfuTJGIjDnJWd9BGoldQnrpwmBj\nEhht3vJoCJWH878uMfZ5AIns/scdqci0OM8GSWSKK2ULsYvkA5npRixr4DUD\nvZZSCBleO+ebC+44ED+iKNltQWcwbb+s59djDkVJEy+wo9U6DjLg105JTpZR\nwb9D6jqXQGcnmJsOjqDTg6+4eTPN+o7JUw6YF2Kpg8bO25ty7EtUm8y+meaW\ngvzQ\r\n=Jkea\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "b98f96aa1ae765e5fca403a224950a66b33b07ab", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.2.2_1609234652014_0.4176331248672347", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "typed-query-selector", "version": "2.2.3", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.2.3", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "c951e11bcb700f7d71aeccd515ae3ef6bb355002", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.2.3.tgz", "fileCount": 6, "integrity": "sha512-YUHIU0bg1tsnQOLpaXah3J9jXZvOKNff7JO1ygOaYwB0MomDYLzFf5p3QIFmwhiEq61P7JzkPIOhTkUUf2tVAw==", "signatures": [{"sig": "MEQCIDSDowPGSNSvpwdAtB17Mc3YWYayTGOP2+xFiLXI7pZ5AiBav9ENusyNTIGOl632YIJ3EqNDCA0NlGF0yUOe+VwcXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6wAnCRA9TVsSAnZWagAAK6EP/R/uX204br5bZ1M1yS28\nHdwLBgYnvXEMGXdWgjLFcgHyrKwdYg48oxcGWqj14uhS1uQzrAtzOHIeMM5D\nkSIvi4mLondbnEcxWjh197LYlptuiJdVMfv/HxadnVrvO1EqHb7AmdneQtjr\nYCjKJKLQxLO2vfCyTbEQWrP+b8PAsVuDY3JlybGKSpU68M9c5X7gKGBOIzzO\nm8AokcCPKxpvHz7/UyH+A8i37FMZYbYmL7TjVMd5D7ET3aO/lCMrPgFJWAVC\ntAi+wvCVScREqzUdH2oIvNMoeRc/+CyFDUIjBqgXgrdVvL1vdZIqOntJAcqS\n+S3Z1+hmpwvxlaxMerMx/6EqN/tHMaoLnFnae4ijiV+EsxClgRDY3EqRnA1K\nON3hEIcYZBe3VgOUaJ+SJqN2776xf20y8P5CN4tAOrfCl4yTi1oeT4YPv7sy\nXk5pGczdgE1kpI6/GIKFczWIfKMe9Il70Uu7z2g9yvXpC7vx1xYYTAKMvPLS\njYBvOPJrGm5cT2Bnss/RzLmkFVRp6qD1Z3PyGO7NULYbgc+rlpFV5Zl97D3p\njWQ7WjsyM5XcUhPRM3LSdR29pIxcuYKW6zTzIZOXzQoQX1/XkE3IDOkX7YYx\nC00wFiZfalg2PmO6CfHb805tz7oER4193FTsO6HL8r/XNYQBNe7EJ5TzPsx3\nJ02U\r\n=FLgF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "a71aef0ea8c0cdcff7cba5d65971ed88a2257974", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "14.15.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.2.3_1609236519341_0.040501853848781355", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "typed-query-selector", "version": "2.2.4", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.2.4", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "af74b7ebefeff71e05099c09ac8a9dc7be1b7211", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.2.4.tgz", "fileCount": 6, "integrity": "sha512-aLkjp8fSgjz2+xjrjfxhQ06vPd5qW1Lg/7/DArTiJqivbPh7SPREoQ6EkGskEfCmq0jhox3/vSy0WIIqgX9VnQ==", "signatures": [{"sig": "MEUCIFy3jzb0cjtRwyyK0+E+iYmTT6g0vRcLEC9ln7epRc8xAiEA/cbcLFapA5sRs86EDNB09aopZiNPhQB4dFA2BR2rW4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7uSrCRA9TVsSAnZWagAACRAP/1ZCkw5532K/aXRVWtEt\nTzQZ1aL8WbQ8GNxKR2dK0yVZnsqt4h/amSDBAzTWP9fQpO3FS97odnMr8zxD\nNWPrKrj+fa/wyD4xrAH1Rq/SuNeKFCBm+Ijc9bsesiYiHqIX8NYxA+C8pzy0\nu/tCzwyifp36NHP2jbX0CuhI05TkpZ1q/O5Lk8MndljLyD/cpN/WbMSxnIFQ\nqOPT9qWTwqCcFDObu3/rMYYj2HI/MvTGmdtGV0BfTgdUuFZRqw2lXgkhaozP\nw1NnhxsCAff382j7lLsIwgJZ8trqitLPagZlDe6o1Zz0JZRB74lkaPGsi5rv\nftOvW4BewnFShACVr94H3qoRqfEeLuzqOT+c4zCXgzHi+gqPhRz/V5kHhwD7\n31BmFxLBJdaY7Y7sBUunv32nhgjGE7/RMtouJuAY4BdqnYKZEVOEtIMqCf4R\nBXK614TZOeCW8HcXZa++WEBuotKeLTSMncfRjUWDU8qSSkguM6UiPtK8uBTn\ndFvFAznMSt+maGowZeL8QiRtUw+ZogoeipqNcoyDQbzqui/NkO+V4XZA44EI\niSDySMTYfwiwGVGLu+902TjkAvOcqDZbb91Wj4WEpBO1NnL23f3lBUhjzpx+\nbHDuU61HbL7kYLf1FKIteBVeGcTiyQC0O6D3P/nJu3+N0Mj+n8pNuUBhQZXH\nVUg0\r\n=ZvVM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "2abff5d893b17a9fb7a6868ce44da756098c9035", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "15.2.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.2.4_1609491627012_0.19433379021739916", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "typed-query-selector", "version": "2.3.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.3.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "a56db9f4d02c93d01365c07869338372947fcce7", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.3.0.tgz", "fileCount": 7, "integrity": "sha512-R0PkWV4BR4FC1hblV7p6DWIp/PwUQVACZ7LVBGi04b9bsIN/9C4Sx7FyRd17MoQS4BrQOImNBY1ItR9gz+knhw==", "signatures": [{"sig": "MEYCIQDgC1DqgO/367lpX8b7GbWPtF4vTP37J6SrugMrd5EC0wIhANE0SYj/6IzjX1IPih3uaVdJ9c5iC5JE7PzDUyhoS7g6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAkrRCRA9TVsSAnZWagAAumcP/2x6i7avv4sQ8NVWXvnd\nF7DhrNYsaTcOtwDCPq/DLyTJizN4V6okJy7jwQptTpG00IjvrpCBNJp2MlDj\nR+0cumZp/bccnAjW/UonfwQKFbBOC5lymAtA+ZoS2Vpu3kHB6/wvMiwNBQfs\nU30x3ncEnD25WcRno+y1+iZYlKiheBdpCSDqhV/kxNmqFopouVhnB+srAiOG\n8I0/7gSlAdcqqWvDjti8tGo9Q6uL6MomM9W3PjAfRJr+3NVg5xVW3dJJB6Rs\nBzMms3bY412W0iMpc6foi8s9aAAXN+6vI62u81giyIV/3ggE+pHPLJr7V7+V\numDUhW6ZsmxjDT1zUSamygshGxYYovL+W4/ZBiRMF9TYx2CwWTYdGHHkIz0B\nv1a8TMP+XXJFMgNgsuY+LHxFqTjXMWjDavmZoZRavupxL1ewMaogkSyH3j76\nAwxslTGPycYoHiXYmFbx7mbSbLrhgqNdzcpdiVS3gsqf+yXdc9Qe8SmQeE1R\n5U1ji2+VOkQh5U5LspuW4yuUj2uQJ+JlHnQnM2Zjz8gskpUMdUoEYlbGPyPe\nsnYmqygCLmjBGFCWD0kvVzYXtHzkm8at0ewCZVAemzIXsKOfRdRwfCcIZh+O\n83Mr+OiarHGmPATPz3RqNDk3GeuSIW0l1z75/wCO4i9RpirkWJwqXisvbc/7\nP7Tn\r\n=XByu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "2efb75b8cf6b671332bc4c9e62a101f276902814", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "15.6.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.3.0_1610762960548_0.25018850954739236", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "typed-query-selector", "version": "2.4.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.4.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "5b07b099e92c910602f55c26cdb7115618d96b6c", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-LVsEnvOgSjMNCPX/6zi9rhpjnTlBfnLsTta+FFJywBdQom0k5yhNraru79HFtU1Q4ncUDtRhfMMHLnbZ10NCLQ==", "signatures": [{"sig": "MEUCIHlQcXjHsRc9/ol2kW9JRhJbWZeeV3deCJ+4/Fvsh8kOAiEAr/VakBs/cfziwQnBgwki+9raxc/j70uBBle9mrmpxzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCSFGCRA9TVsSAnZWagAA/L0P/RHmodELpAdxOgubLEzH\nhdVtqSZQx7s6fWZwDWipqXufGzS+JYJWmnGASKX+ftRSsWL4qaEX5TpyzJb6\nRtAZpN62Zqr8+bhxxhn3tFtJKhGz6ST2B2cJoHaOLAdtpsHC9ayZNcucApqo\n48qNhAzjX7qARCH6CACCn97e9XSXFThxQEpbZ6oepukmwZZnaPhLqf03/KEX\nJBHt+/X0zsc8mHq8mWhKJVXP70fnO2gXzOew7TlkuXr0FjCds0YT5rmoQ9JH\n/ZC7a6PnayktMZ5yES5s6AmcpuYnsCIZgg+DCpeTVFvMInEDVBJoW8/QGROw\nKj93E+jbhDMYiFZFs5PdQ1r+X15NFLjYrPz+Lqf14qb6wlmqHclrq8rXdxUU\nlPKhKFTpMh3CF5OpNG+/JLHH7JeCbQ5A6lseBV94zHR27qaJT2SF/NBWS885\nfkJhvdx/PcXXQ1vs+rO83TQb/Xi9OHNxLyH+pbDkIDIxggcm+QdCuqzahbCO\n2uNzHNN+qyxv4K+nHBB/hoZduXEhZGAPz23bKhD639lj00blHlq2qPEVVltt\nKqeWXLVKjejijzJxBcoaO0pg0z2kPhGMs1GExGG9EnDXzIVi6JAaDec4lg1x\n1vcl3MdxbOi+S1bQ7fLD/NDfqXI6kIRLRQTRPIRcB3yE49mo4Xnf7QOzu2kf\n9YP2\r\n=fRua\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "44d64d02470cb9fa30c1243e31b76ddf6d834c79", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "15.6.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.4.0_1611211078066_0.7950236324977356", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "typed-query-selector", "version": "2.4.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.4.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "81f74bad05091dcbfbbe96c3fa2e2465771894b4", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.4.1.tgz", "fileCount": 7, "integrity": "sha512-X6fx0EywD73wsH/w0X2YMqx9wiW99vIhN9nptTP+uvNB5wk0U3t1zzq50IFgdtxbwFP8Ap3N7k7BL2vmKJf4+g==", "signatures": [{"sig": "MEUCICYrNwk57C6egK1shrcIV7jrvLyL8UMTBBKZANQ86/VwAiEAudQxjJujYlLrAbx9DkCsV4M0/U9ACQfkAF9ecz7Gkvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEhhwCRA9TVsSAnZWagAA6QwP/jTKlM55SHDCvLOc/ChZ\nZIWUbz3Bmscxw6YT3sqq+dPXTNnz+6sHFYCwn+yoYppnvQfRkOLC7LkJEGzB\nnoerxznpcsJPtqLrDj6Cu946q9r1gwF7k3ZnwQnIDrokluugZa5EAErU/cbG\noj2ck8kO4Wv2gihuBGoBnlPwTwhzuHzrLDrpa8PveiUbUgBJH/MqBlpu7L1z\nS+pLpTI2itIzU5ZuI0pVjXBGZRhB1lpUleY7h+3FPXNZZ2w0OxCuVgwUeqEz\nShL7X2HhHVq1WNq9kJCwmEsATFzXJdWWf5I0wkkz2oszzMnR0fm+Nth/YVEC\nwgzvqDFppzchrKpOA1ZPU/z1zZ1UJVrkp2z+zgthJw3qstCx2o66Ve8gIiIB\nKZH0Vr2KkwIv54KZLrnyALwjyWg3cqySQFLgQyxsvRT/1XSyjcQQtX3Tu5G5\nzDWNnnO77aj2sCA+Q+2FLWB+rMVePItCsspnUjC3x9IQuvO5pffofvf/Z0Tk\nusxKP4dD0IW0vCi0AIh17nBiSRmHLimakEupolMC8/L+IxUw8d3lBA2WOJQP\neC824FFo0urXvkp1kU2sUErfo1rejxGTiydVJxY/0G/atz1JT8RVvxxInA/I\nheIs9oUCdTNaoTihHnvOS3yddwkph7LsXUmq6trjOeTgXcVF9f6di0pY8wHd\nYxvg\r\n=ENBm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "2ac2130251242e444c3e7e55e996ede6930f1de5", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "15.7.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.1.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.4.1_1611798639555_0.23918314784849715", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "typed-query-selector", "version": "2.5.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.5.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "dist": {"shasum": "37f0ce6b5fe148c238f1bb63cfba0e61f9aad85a", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.5.0.tgz", "fileCount": 8, "integrity": "sha512-2nTkMic7CJlU1+gVOH6d++IH5DMYtRkIyADjRJCGmdc46DwotXZvjzq/CVXZf8dcntwqj5jBOSxjQM4O2YPlUA==", "signatures": [{"sig": "MEYCIQCictMnp/T+DqEHun0vO3jkpUym+rJ+I9dbTiF4LtZhUQIhAOB0okP6nTPPVm0VWOOYYMOEI6kBs/nldC/i1cSHl0vh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgetTPCRA9TVsSAnZWagAAhJ4P/1kJjXW9E7KyD6mhdR29\nPVM1awaOGzkk+mN7Yqxz40KPhH2pL5POkEKSLj2sNdtx5MAkRC3+J7+YK3Mm\nv/IGvYakMrJUT7DJtAhPTAKnMAWsFFOPTIxBhXA6/ZvU9xb4u09Z33oUznng\nigs/P1EJ/tCmSMGMp0XbYmOOgWJ/oeTkaV6nUBOBjY/NJc51Yxjg01IE6kHj\nxMW+8jEMrIGOGFPjVSb9tj+H6Bne1MxpIVvTq/Uh25f0cMNcHaQktupEXqH1\nTphdjLaqya6QB+M2R3TiTr+7ndvdCbI7PEUT1G0jfilGAK5nKR2r9T0w5lgr\nv1qmWjBannuCD/TQus7YtG5jQfc+Uu/BywIIGecw0v4sn3VC5xI5PTKcAQm5\nlMUHrAjD4PqsiEDzblRKunbIsCYUZxFGbAO47uyMCpUS5BAuL1KXOT4jwQNL\nBV8ahlx4Je/bfhfpMZuwPlJVT2CCdyx9F52FNrnWlKnYiJ1aKgAO/Yn23e9b\nmeySL8vPSYK6SQMNReUnbYiXH4Iihzq62N0hPFHTtBk7+rDY0/MePvpfHxiH\nom/0LY8ZEsiyGFoS5C2Ani023uuo+MAvGaTer8iU6Y7fsVALLAjzKSXzDq55\nXuS7AHa2H6FVNNxhu71M5HNcf5xFBSbowZE0AYIjcps1ecT0TV3V4/6yfptY\ntj3s\r\n=2WNn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020-present Pig Fang\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "sideEffects": false, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.2.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.5.0_1618662606882_0.4723043838558134", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "typed-query-selector", "version": "2.5.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.5.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "dist": {"shasum": "4e9bec6c6f2e246a74408d8dca36619653d0dc72", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.5.1.tgz", "fileCount": 8, "integrity": "sha512-Yq3y5HUp4ZZpadk/HXthq+P0qtQ7buRBFyhI5Jnv+e31TAQ0gpg7F2i2t4Bbj5/wULbzn4DEDBYvhjQjyPKLQA==", "signatures": [{"sig": "MEYCIQC6xYbmSGJc0n8RuiN3AhyppBjoY2wes7xsXvbvsXhVCQIhAMuTLwWWWLpU1jT2jO1LVvWWrrWzR/zvcDVRw1X1Tw0z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge6vcCRA9TVsSAnZWagAAy0IP/1arzLq4nKoA/QDJ3+gY\ntQw9hZCweDFxLpTG+4qUBifZ6XKFvzcUm30J/jsEnPJmbqLu+TUmahlbHu9R\nU/18+iBu6lAx4AaLrxNKNl2o1WcBqBi4SstJf63MhTpzx/zb5sh335GfzSuv\nB/Uf8Dzcu1mO912i2YPtzApZZnv7loyRCFto4bgjvwAg5F0m+WyusBEC86wn\nn9ODcHQpOXCSxbkeaGw08Vab/5KHnvYStgGQeV1Q6yRRn+MVqtRs2usggcI5\nhRdLwYWodoRqGFM2FrdjVXWwD9UGR753aJDZU5e0BvK22RcHYhcj8KdezKNS\nMh9m7KZIUIhHLXlAaElv5FOFVFKVbdaT2TJGr6+aLa7zFKO+Wclo6biQKzuZ\nkkw4k4b4DNMt9RpSVQS0aTfJ0XMYmPdksjQj4ufyVubwbC636kHLHnQO/lh+\nKaVdocUWYhyFfbojdI9ZleJgrdqxM5oye+ZwS9GN63FTDhIdInm5IYVp6JzN\n+a54wfLn+7uhHRycwxridk+efxj7q5pH5ARyM3MHKM7z3ANbiwbSOLziETDB\nTqTX751OE0hThxEzOJYOz04epvNLcBkfmkK79rzNNfQNIKIRUX65iAMRqvQj\n9ke2fy+sntRrN/pXseysJE8FFfKXy5diuEkwM9MY4qPpCNh78yI78/rjkcyF\nQPz3\r\n=XLar\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020-present Pig Fang\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "sideEffects": false, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.2.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.5.1_1618717659616_0.6479921063447371", "host": "s3://npm-registry-packages"}}, "2.5.2": {"name": "typed-query-selector", "version": "2.5.2", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.5.2", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "dist": {"shasum": "06d99b4c8a2510363d5b72bb60e16056d66336b2", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.5.2.tgz", "fileCount": 8, "integrity": "sha512-34rWMn6KIqpGGxluHUfH394tTVS/ZKhS/dJL2lTLd4i7I4Rz2D2wDGuj59I0Pk0La6s34pynDi1Zd0qJ4l7foA==", "signatures": [{"sig": "MEUCIQDQSo963k15piWRDt9tLP1LmaJjQRFSLGeGwkh2Zhf3LQIgWM6929dFx7XjfAlWLvgxCQunCM/a+p4nqynFoFLC2YY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfQZLCRA9TVsSAnZWagAAMLoP/0KpAxwcXhMLjiYY5fUF\nhNIP9bjWAUjxuY/xkLuL2gjGK5gufDOi5uY4wP7jHzUIK7ISpMi0KdN607sU\nQFXlDL4oZh7C10kT1svLQH4mQOarQWfQUHszeuMaEHd7u3VVKeM8yS1Agj7t\nh1U92bXpd5sxBOLyfDkbCPJdro90Ax/3BuwnwniKlBl3lRSFA2l1c5A5NSlL\nKUY6zbo65cAFssi8alF4miB8BR8cLcCXBxeQm+8eS1gpsnadKudPGzXNTzm0\nsSKvtlOrgu1i76CEnt0HcYsUKu5gOmO5DUm9ZGK0ytwH7EU0OdtnzEF8QaqY\naLxuj2y1I7ySjBhabzKbeWD1C0+pNQ4Qv0NgJMH1E4Q43bXdvDBly/Q6uavE\nPO9jeP8VI6vrSrplEMEgVdU0jnagVmJ0oQX2J2o5iLWtatI3O5LJY7xPbfPi\nlTPPOk8vS0JY4rI5+cLUGpcIifezczGIQIgbB1nPmMWQJjvTyO1xXkom1hPr\nxyIiFEMuKnzdlo4Kr3V7llXPM09y7gP7hErt29GoN/wY1ttFO8eFLCaa3/3K\nUNY4V1LF1H9mMN45b+bqP/oMsQzNLhFFIJ5WqyE8NlnfWefbZYH1qJSG1VMb\nPE2ggbMgft6uZoXh1SvAQFN+232ioWafqSjQQAZF53N1lI1s4CoT7toAa5xK\nbzCg\r\n=2ZaD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020-present Pig Fang\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "sideEffects": false, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.2.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.5.2_1618806347471_0.6322434298853716", "host": "s3://npm-registry-packages"}}, "2.5.3": {"name": "typed-query-selector", "version": "2.5.3", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.5.3", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "dist": {"shasum": "85af45be833ce5f70357bc714aec7afeeaba0c6f", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.5.3.tgz", "fileCount": 8, "integrity": "sha512-Nv84/0VsOB4U1iTqGlCAgO8fOLtzScEpIppmhdQhGk0KPtA+aPZarJHIw66t+VBfobUpOQlY0WUX/hvvwOVOxw==", "signatures": [{"sig": "MEUCICEMR0KE/PyEniBijjIplPwJpnLruYVgGqSGlgHZkMj5AiEA/0/9uN6YsdHvnFZumctJTm3hRhH+d51RSz8U1ULlz1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfSDYCRA9TVsSAnZWagAAnQsP/iSW89ZrDn/qd6MBc2Ch\n/HAX3eBVMZA4L5u7vaU8HPl9ukyLrBQ0weSWyjF01jFJ5ticjxYfuCk6X8+p\npDTJtwOCUZMn7qujRUXZ3Ji/HWcGe1895j6pLeci7tKMaEB11l7jFB7dFjGO\nSj7/74qGikL5ePv4r/XgAj9nGw4cY8NW+OZAE+ipkhUcGWt27/aUCjYresPz\nd8n2Pn+8U702ov+6sIMKBRHsY4GxJXPhqkOvmJjZjPc4AjiInlzg06/xL7gG\nHp94b74tLOAhx45suVcgWg1KERTTlKK97Vkttqd9udDy7qpQRs2XGJ7xVocH\n/dr2Oajh1ENhtfZlok0/HndIkmFBywfOtm4NQkEW4n/N1eIVHUyxp5m6SqSU\nBmy4i9AzTke3zJSYxG2xOdMFHzlpxAsmP1KG4giAixT4PQQ86ufg0UrPHqvx\nsmIgWbN1vNy0bKomEE37DUAjKgKoO8K2KLeU8/SUu11fGEmUWnKWoDmzwncO\na0Os8BAODND1zt3e2I/erTyWzEdBxVxYZNripL98C2xLiTssnCpWHb9U8b11\nXxC/8VdGYr7yDG0XV1U40EhvpF7x3L45hRxtRJefbXYR6xZFY7Ch/SnK1Z8A\nxyne4QjhabKcU02+j0RLKMECZ4eg/ZZvVkKRGGteCquLpkOZIPvsgvQNR/wm\n+mRk\r\n=gcgK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020-present Pig Fang\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "sideEffects": false, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.2.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.5.3_1618813143473_0.20490450929366477", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "typed-query-selector", "version": "2.6.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.6.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "dist": {"shasum": "3f3d7ef65004dfb7d9ea9e554706d5e0c9da6983", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.6.0.tgz", "fileCount": 8, "integrity": "sha512-llEbyoMma7brStvVKDGZaCH1/8kcJis3HZibB4LrLRlZz3C7wGkPH/B1jE9+eiArBh0x4tIFIjNt30HsT0VF3Q==", "signatures": [{"sig": "MEUCIDVkpzhMBWQRpH2EnT5AIYaPStxkZmkysEZ7Zanu+beDAiEA5srvgDq1/wryuhWLBU2+bq6wJmuCU90/cN3DwCwEnVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiLHzCRA9TVsSAnZWagAAYYIP/3CfTxwYFLrZJeNnA0yE\n77Jk5oUJChFHxH+FpkpnHjSd4aq8+nDK3Wa6aW8GIq2Cab+C4AEH9eEPj41l\nXah09sxPeyNNYiEYDwh3qxdUYAvn46rkwtAngtMHVdWs2fDLRhShoRqDr5N8\n5RjdKiY5F/nZlf4ESqDH8ufuWJpgaAMNbahXhr473f3reLPzm/OkwE5rXKV/\nNbrujI7GIeuPN/SfHt6VxzVtpMPnmBOWprJn3uzepSNhfAJQTknRVh8Hef7/\nIy1A89dmrtN1+uOa9G37VN1KdUERCEMKnsM+ej/cKjN+1o8MmcA4OEjvaQkJ\nlKb0R0qSj2agjosvyBqboyARte60jRQCsI/6OlJ3TzrI4GCLE64iFS0wUCUN\nPVyo3M2Rm3BJKitYRr94NNxqvlgY/1COCbTkKUwK3dnZKrOx2RgKdS2lJjkI\nt7lZY67eat4L41ZQRwXHaqnsVQ1Ihd4YfiUMm0Nf/dS1LEbnwH+u/XWYHL9V\nLd9PfJOUo0M3IUV0wqpopNORFxKUkNjgeFhEims8vWMKXF5iov9V9TL0rele\nQFRKpsYix1NUYOWqZXQe++jEL7sno3pJDo82soWD3u/Jbnr9k77mP3jhIpJM\nKUjpGOsvC5XitFNwgwT0ZW+GcKiFePVJpG4QjQWGG+1tH/d0qa8cvl21Wx6k\nbf84\r\n=OAS7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2020-present Pig Fang\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "sideEffects": false, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.2.1", "typescript": "^4.2.2", "@gplane/tsconfig": "^4.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.6.0_1619571186932_0.8181567726739083", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "typed-query-selector", "version": "2.6.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.6.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "73b6f591974129669df59d90f0bec4216e68a434", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.6.1.tgz", "fileCount": 6, "integrity": "sha512-nzzcDrI0nncM5XTNyqeG7MrcXTx8lelUtNlTP+NvpnOfRzApyr+ZW4H/FoOaPfzmjn++Tf0ZxXpBN7Q3FN3ERw==", "signatures": [{"sig": "MEYCIQDavq5pmTiUc6LMZcoHXqOs/m2OMfeSggFRSf1qfQKtQgIhANG6yoF8VO30kAiF8Oq2ocsJ9wKeuFDDAUP5QuXEn5zy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlwiBCRA9TVsSAnZWagAAYJIP/RNp7OqScoIJmjY48U3l\nhdF3V5pN4TYOsOZGBl9twMkjXLSYXNSpujGOwjIrlFB1pU9lTYVid5Y/AdoN\nOhyr1gi8fEgj2+Il72qL4TPtYqU47jlRMFV4NFaDrXeBJ36hNEdNB6222Lpl\nJ90/MiVKU4UVcpc51HJr+FUiS8cCfEX+pUSEfiReAnb0GJrFEp+aLYzhFtco\nkryb3+OFWQESUKF4U9Q31taR+IRPjEfgA2tv1j5VpV9k5Y78AyV7pv7wBhb+\nVyAwMoC+YNsuF4kryPmXvLucF879uHWQ7+UXzdDGIA4vrB67yNS0LsWdsp4X\nTvlYXL+dfDcgZLO+mOQUOrbUTKM8QsWTy1IhEzt7FVH0B5JeWiUwflvO29bE\ns+J1WCRwp0/0t8vZxtfzMJdLe2w+rewXa4Wu3TJ4VRDlWijarnFDXs66qsX0\n0CjkU2ISVvMvCv9p4TOnvrE1LL3/OHvfZXXuGbH9sIE6AK3yYN9NdfRsgcNe\nHyafN0l5aaerSEfzov7jfgj04s/pPZg1OAPw5xp9ZkbML9zTPrZ25yGt9B50\nzrYQGBvFXP26wuuMg4wgY57HNZhpxikgRKqEkDmxhsv5cQileA7G6AMw0GF/\nT2i6bgXGTNFHO6DUrtdmRlHQXcnK6Zbca+E3nBtiFuKS4chxwJPJzt15cFTV\nfW2L\r\n=DGfp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "a3a5b7b18e213f9dcbe347183c203e083ac09fd8", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.1.3", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "16.11.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.4.1", "typescript": "^4.5.2", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.6.1_1637288065101_0.7220128863469981", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "typed-query-selector", "version": "2.7.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.7.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "1fa86db6a80b5a245ab075a36b0c3230292f64cc", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.7.0.tgz", "fileCount": 6, "integrity": "sha512-as41ht96B9wCJflTJtuNL8LueDlkhctp+p8Rn+WBkEZZ+Rakpycr7cQMbBsOND9iHY5/7pv6DVv/aojfQedDnQ==", "signatures": [{"sig": "MEUCIGStKF2FDmra+K7NXEzPiBwLKPCDy0TJih7dRVu6k1VoAiEA7rN2OCquugcJCOqnV0Nle/VWGWaEQ76FoyMjfv9qoME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEGM4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJ1g//RzY0pQsMlQ7XyzIj6hc1MnkHRHgkHBwEAmQXejoqHQQZZ6VS\r\nX6pe/VL1YNbN5Y3g9gIBLEsyyzlgomIChmxVXn4XSlnyzzXRW52Ebn+HP/2r\r\nM5Skdm3pJCeYKJQSRnT+YJdjKS6fqGdLbu5pyy7+d/xykObWvsDBMv+Kd/Cd\r\n7jx5yIxCDojL7A2kusX6vBn1RfdWgVljr9BQN/9mhn15R6+LA8gKIrVynw1y\r\nYf4xF2QlGHHjWg05ftVXABVCi7b4yUZkrHX/fkTY/j5/jMqyu/JAPkUkKVcf\r\nA2ozXWHYaFt34Sg5wYG8v3RciyoYvRR9Czo3vTuxJWQia0tkm76fF78F9zfR\r\nKrkppYJ/U9emBOZ9K2F67m1XbDkitYKPoMxryo1GmaRnT9TYifYx+yCyT97t\r\nOScIYGYSrDYd7VGhVyhpd676a/A5AwMrY9QEKQb57JDL1U0gnsoyhUwIy9AB\r\nZ7+ySWfiY3B8QWl+/6R45GvAp8/+QHMQjhGjrGkwl2TfFfRQ/ftdcry5WEAN\r\nuyQ//C35NqG8grcQVX1j+TtZuEUo68bzN7qyWV3CoQb2Hdo3ez8TA9K3Tk03\r\ng7gmqGsvcD+g/UzXZ6OGDv4XsHP8aZ+TThUfCD4FQWPUJzo2YMbmDDRqt/nf\r\nnvVSm7lbUJFCz9f7iwgrYv9XYgZfnCY5j38=\r\n=jG3t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.7.0.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/46addec92eb3bc75476553a691151552/typed-query-selector-2.7.0.tgz", "_integrity": "sha512-as41ht96B9wCJflTJtuNL8LueDlkhctp+p8Rn+WBkEZZ+Rakpycr7cQMbBsOND9iHY5/7pv6DVv/aojfQedDnQ==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.8.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.7.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.7.0_1662018360150_0.6020647325710686", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "typed-query-selector", "version": "2.8.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.8.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "4983ec340a71098a4ebaa6dfc56778331755871d", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.8.0.tgz", "fileCount": 6, "integrity": "sha512-jo9NLGQ0b7sT9TK3mxzemATQqTb/uh8TRvcOK6WSwKNewALt3AA8q2m7cdpF/uUbd/cdixaBxCGe6fpzpEWbGw==", "signatures": [{"sig": "MEYCIQDxLqiK4MhGjQPJRexylyW1hNdkXlVpqJORNflV8SlQFQIhANLZ/ErpNyzCGhC4dG3CGLzBMZAQIyFSAhxbSMQPdsBU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMl14ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSTg//dHbpQXNlTE5c+U4M0LNL/k8bgOF8M6nhQ6+lMYNMGGd4HsRW\r\nwab8c83UCU7R/iSRp1ZkU9gKHcipimWH5cgl9alBCsGXNafZ0WNxaR2tnBHy\r\naELa79eDFP12jD4JwQcsAE3yBNcMFY9Z5xNUPenqC8I+ebOmJ338ZuDobUTl\r\n2EYHB21tgMHUwdwdW/r0On8stIjAHqRtqag9gUuS85ZtNwQbDfpDrNdIVkd5\r\nsjAakIfCwVWE2JhiDQqIa6cDz9NN2qBY1s/PkVfA9wZaraqEzYNlndCRqP5y\r\n7Xv+bzn+0P+gKt5WrSANFQy0crrEqGkFlJxfPLHK7IdZkqIQe//kMkiWNYd3\r\nd2/6DHaWDoPDoMBo+IS9aUbQGRUt3wSSpvXFA1z67cv3yShzA8XNa0Bbi46r\r\nb6iWzPaV5Y1d83AtalE6FjmvBzNzeVpnQ5+Ivbr6nXoGb3ykKBCCDocyg4Kw\r\nr3cdftacV2MuK42VVTI2anGEAbhCyb+uX+hRrXxXqLt1YRx9bbxrcbzS+byW\r\n05P/VP2DJw/czLO45qsb5iJIvomVWj+qf4yKvEI/zgaQe4tgAE9mI6Qld+sm\r\ncnnYXUPkpapqCNGpVdniWjRc2JkOjRpqky+gfNjagSTvBeUdrNQtLxB7LGUL\r\nby8oUzGBAPBmn7r2uj/0lji8AhOjHxB38Jo=\r\n=RcEi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.8.0.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/388cf04f9396cd823257734959660ffd/typed-query-selector-2.8.0.tgz", "_integrity": "sha512-jo9NLGQ0b7sT9TK3mxzemATQqTb/uh8TRvcOK6WSwKNewALt3AA8q2m7cdpF/uUbd/cdixaBxCGe6fpzpEWbGw==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.9.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.7.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.8.0_1664245112712_0.9522967158857361", "host": "s3://npm-registry-packages"}}, "2.8.1": {"name": "typed-query-selector", "version": "2.8.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.8.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "da8215000dc42899670c69dc873266a9ac20b51a", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.8.1.tgz", "fileCount": 6, "integrity": "sha512-nk7XU2Wx8vUuFIprjc6kix4vtZqZGH5w8lko6fvfml+miNBvh189ZZj4JDp5HhP0dWUx+0vL4X/ElBXiaWUBFw==", "signatures": [{"sig": "MEQCICW7GC6f8grmu52IL0PVqjBii2QtmS7BiFcLMZNz9a33AiBN1otZEsnxUAqBs5Uwp3H8fNCRCQO4TtX9dlQ1jRiB8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9CaCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN5w/8C1rZ6O06q+B6S1xxY+qjPMuIZGKO2GU/2Ht7vEFw0aiuXtV+\r\nCLRhviKteGh+cxrWC6Vkmu/b5YPjHXD1ZKyX+CoeC4l/1hsE94CTIlWGsTV4\r\nAuXix/iUXT9kRDvLDc4whuCWVmHuSVTL+syjV0M4vqcfs1vtfjHqp+w8TMiS\r\nYSNkTtchD3Ex/1W/xSdX+ihSo3tUYlERvISpTJIs6EA5lcLKfT7lLQUOIf8Q\r\n15/fUl0kHu03TkoTt0G8m8m9xRfihwe8GvjfmCA8l3WNXvrNRnS6s0I81IW7\r\nogwm1dp29KtYv7GCz/pb0Vh3QKQ3avUn1lGFN47868x5vyzF8EH8I8rUf5CN\r\nVr0oe/faeFsU4kadaD8GGnbGE/xhsqJ3/wKLTZWkI412C84fhrfTIFmZStSs\r\n4nkZ9p0RmGAIchdgwgB1KqMakdRDM3NeR5bDh8T1mbWNwSREXegi3ZZzrhkt\r\npzHarb4im5nG0+aJoOAun7f+ThmGRWkAxpfbmoD190zgRXqRQEFt62Mpaksc\r\noW1pEmTMp7whRfQxAXkhfTTv4JQNrd5ISQ3PbAz0ReI94hll8U4/w1A0Kr7y\r\nJ8YTuGdaB+ykVgq6K1+ofyUvMvE/ttwsy6GsVgMMqzSQWnbjGqZa8tlKcwvs\r\nkIo9ea1sa08gYMBxZ/SI1zp3ESkyK9Ou8Vs=\r\n=WVxh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.8.1.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/820d65ff8da5745a575fdcd5457ba106/typed-query-selector-2.8.1.tgz", "_integrity": "sha512-nk7XU2Wx8vUuFIprjc6kix4vtZqZGH5w8lko6fvfml+miNBvh189ZZj4JDp5HhP0dWUx+0vL4X/ElBXiaWUBFw==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.6.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.8.1_1676945026195_0.12109328652771323", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "typed-query-selector", "version": "2.9.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.9.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "ca15c1163b6858a830d8c41432fcbe16e6225e7d", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.9.0.tgz", "fileCount": 6, "integrity": "sha512-UVibdpOGRs/oEZeBqpLNGxTxRdvUPJAcwOnbKiutxYM4k50aoLPbvkz6vRsvr6xLx8BDibHWWGypOZCcn6+YpA==", "signatures": [{"sig": "MEUCIQDMCvlq/J/GnlmyIZigNcEWYV2ZGmzodf7u+/D8GitengIgLPrWuRZAk9F5zITh6bYDT/0dIVFuXWbJmd2IxMgRlQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9GXxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGVxAAn8mwX+Y7qYo93apLK9ohpxDr4RioO/1qA2lWqz/5/Gwtwkh3\r\nXTZ8Gpmz5/7jmAsEj1ggpTWoBFGSaySCFO1saclOBS4oEYICxmTjAH0G6dm0\r\nInD6d/WiVolT1Yfaq0tI1s7Sfcc2UderopiNQNh7z8c9wCVLLtUdDfnhQ1Jr\r\nsQrYmeKO8GLrTh6o9IpAd9F3UwBfL69QnP41fCtu2JFCyLpVe5Zv2oVhOJYk\r\nzR8jcTg2oKlh69G2xjZL3JusAnGxCAqTtXKC3ZPj5ZUEFc9+Ycz6yUAAdoIv\r\nAh5+tDRJ9AShDZW21e2ebYeDo1ZaJevHusICth3ECYnAXCh4LUxXVdnB0RRy\r\nGg98dEmm/a4JiT7Ne+aIkbN4l0c1PgbrhsDj0v0++AfnQB73OWHXNDpmfiNG\r\nrEAACjO+VshdHy81bpHMj5Asdqo5pNn9TRUbwJD+2uH8MDLmgTK0vNZIU+KJ\r\n4EfhrhL6bAbob5e55G8kJheDafD06lkkJF+ZR2cKY2gBRYEKtfPAyBtekOpD\r\nD5p/tHgw1tEMStoKkJrTImi+A2ouD5wHFx7yJcDPMFpo0MUOK9SV6nKbuXLc\r\nFm+35QXX30NE4BX8f/u6z99tZwVQFG5iYmCM5snRZ1ezlsNmCM+sUeTFHJ43\r\n+2L09uHHu6I1EGP1XFsA/78i7BM2HF4C6gw=\r\n=7/bC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.9.0.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/7c60f3d92dcf9225b914136cb99c2861/typed-query-selector-2.9.0.tgz", "_integrity": "sha512-UVibdpOGRs/oEZeBqpLNGxTxRdvUPJAcwOnbKiutxYM4k50aoLPbvkz6vRsvr6xLx8BDibHWWGypOZCcn6+YpA==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.6.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.9.0_1676961265121_0.6007612091367374", "host": "s3://npm-registry-packages"}}, "2.9.1": {"name": "typed-query-selector", "version": "2.9.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.9.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "6c8c09a6448d29ebdaa6f21de36968bfdc47acdb", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.9.1.tgz", "fileCount": 6, "integrity": "sha512-947suHY5s2HRSllSwdMUS5kJBsM9FKLdd9OCxC6FWFVVIHodEy1IErYy+PuFGwxj/Vvc8gW7DgrBKdrsMEutjw==", "signatures": [{"sig": "MEQCIAhApXeDG+AuucW3rxzcJkknUSnwfbV1+TIYLHzGDTT8AiAH0q3WoXeXWj+lceWDTdkzB+k0rbLx4yfOEfgfvzrooQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+B3ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmaQ/8C2AU77GrkeqI9Ndw73AQboeVwFOqcf2+1st2u/TOwY/QPkiY\r\n3cjGMlkYzeZAhnbF8vHvOld37N6aC6JankEFMf0q7cwjuoHqkv59d+SC1ARc\r\noG3f01hdHrxVsr5nBk3Gc2O+cP0hnUTdT95uBzeckICmFmdT8ao0svjzQC6m\r\nEciq3j5l0pjKmEruDD1w2qCG8p65lKb8qO7+wysYsda/Lao0JkSSYcA4S2Dw\r\nFYLHLGQDLy94vpRKlO21AI0BiDs1QU8T92Qw6ZeG1m0aW8XQI+rVAtBTZrc+\r\nYuzokSxcsVn22gCsGnk3eGzUuMOl9phVpktp3QitY55Ut2cpa5J7jlJoqugp\r\n1fKn0zKYT48SQHZhky+juWrE72np7uXlBWkCe5V6Wv9/kqV4e2+gUthdwnOk\r\n+w9FFzfkRoB7Vk/5wRoekPVLiM38M8kSlgWqWRNy7QBjXrYOKmG8H06SCNEh\r\nXkjn3J45fx7wFbzHlaqgMZeJqfOsOmEysuvhWjon5+ZLQFSeIGoq1RZkblw+\r\niKLAIg65qOUAyrqParFYmQbMBdvQ76jn77AX8hCNNaKFJnB5LKEOKHKQIiax\r\nrWFH+E8TlbubWCOW+KHbGPoY/Ucj1Mm9gZzSGH4/iUEYfhEpeijCNohJyhxV\r\nms6tqmBmkbbJqltXrtWoRLXL+AvbauK5uKA=\r\n=NBjf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.9.1.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/9f6675f708623bbe0b2275b396030d51/typed-query-selector-2.9.1.tgz", "_integrity": "sha512-947suHY5s2HRSllSwdMUS5kJBsM9FKLdd9OCxC6FWFVVIHodEy1IErYy+PuFGwxj/Vvc8gW7DgrBKdrsMEutjw==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.6.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.9.1_1677204952915_0.2238072765819925", "host": "s3://npm-registry-packages"}}, "2.9.2": {"name": "typed-query-selector", "version": "2.9.2", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.9.2", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "0ff103e02fba964f72764d7c14ea627cb4b01ebb", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.9.2.tgz", "fileCount": 6, "integrity": "sha512-mOnelGQ0JBbYvboX6v/3vijUkIW9L557DHVRUFQTKG352EWvW/uDN6ORlPvTPRivefqb3QbbyZgLtPmxQaxmug==", "signatures": [{"sig": "MEYCIQCXrKFX2eH1Rv8wQa1F0k0LOGV4nUzWpVJwRKLc/gdlZgIhANnb+t7hhwLK7CQFJiap/m3TB86juvF9wq8U2amf3rLV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAB2OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/Hw//d746474oG7/i0S/pwhLWbVWvY2xSjgdqrfE1PZZjvLG5LCK6\r\nn6ISJPC2STvKNZGFiEuCtwiEtfwXFNlfu2e+xCwC9lWv38HA7Od53VzVdGKn\r\nvdbHreo9bLR9rB07ayK4I7xh6uSF7CKiL7YT7dy1fRepjGTgCXzSfp4IOt7w\r\nHbglkiciCbhRkiIGbe5UIa7qnGjP7vJfVsetM0/KQpLjcFSYWGoztC/s8IzX\r\nDo4V4K9AiVoJjBpHXhlwLqSY8RoPtj3+lsR8Wz4dbE8bU1e+uSLPllnmUwAI\r\nGmML32Bq1NQx2SQ9aFtjnzRJgV+P1NSJ6rxKrGpQFNLqf17fsjLo6y+/bn7b\r\nNUolwEFdCn2LWlCmeJScS96tgZ5nvdazSybw5CK4XItGUupcnSbiYtACR/eB\r\n1R26TWIluy5trjL7h2xEXPYoagYosUS+DNMP+Alv+XJmbFprMX5q1ruvqWDJ\r\nDWeDxcD+RcHmYhmYOgUvdf/GnELKXq9C8qnj689Mg+vr5QH9l9pvU9Ozy4OE\r\nPuG7wWXknI6QF6QLLuu8XpKOfq63seDRCYCDbsECZWLGz16Wfee3U4SN7aFu\r\nmBmr8rzEs9FY7sDd5krYTygw8OeNoTBn/XOnRBSLu4cNn4tPTuLzKGyf+c3S\r\nps7ifl1jWhyy5RQeNHO+rLp3IR46sLxlhqs=\r\n=2reR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.9.2.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/89d441ffa7f17d9e3775f584182115de/typed-query-selector-2.9.2.tgz", "_integrity": "sha512-mOnelGQ0JBbYvboX6v/3vijUkIW9L557DHVRUFQTKG352EWvW/uDN6ORlPvTPRivefqb3QbbyZgLtPmxQaxmug==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.9.2_1677729165952_0.7155277633933557", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "typed-query-selector", "version": "2.10.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.10.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "ddc48b194d4a2df0d2bfed88815d2967c01c4b8c", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.10.0.tgz", "fileCount": 6, "integrity": "sha512-etmpQ3OqQeUk197PyNMDcWzhftp5xwFoB2GKfZPOsCgbNfkJKfyT+8SkxmUs6HsD458E1cypE9oHhYhPWvWOWA==", "signatures": [{"sig": "MEQCIGzWLz3th4JotKMPJmdHcBUzpn5mYa+IGhxbVYUq9u9YAiBW2O7kRfzeOLD4ugymQnhQIj7sSQDVYXp9fgl5Iefr3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPOhGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFDQ//WHyT/Mod8lPwNemSTt2UNS7HsCyPzv++jPNqrKGlZ1CComLL\r\nTbbJJr4N4stY9QB6AesR3ceGE+DQZq1Q+empLev9ro6ki/zEQUlK6Rt8gbEp\r\nEW87AnEEEtadRvS1W3yjRz184LTpnH3nCi1X7eR0Bx7F8rTRiCrygfX4R78W\r\nSLVG746nxbYLUK/xK1lDy7PfLvrSXTCPEsHkgSd+4Pv5MPlXVqztVvaG3kAx\r\nt0v704WsLnumq4k+LGC6YqqrCAJ50/+7+Oj+5zLQ98jAiXvlAM30o4PRgr5+\r\nCxvuaZu4u+itIZQfUB9smz2f+ij7ybr0Xxtzs+3bS0G7eLsk4zw/kMR8tQav\r\n0pwDyC6UYRTK7SYd+dviaq8XSHO7kMa2BJek+pVq8bBR/g75SbBQYYuMOjv6\r\nS3ydhHoTTc9xz8ctVwkbqffUjLg3xH9WICYCgUpr2yFEfO212TV2L8rJFrA/\r\nydbqtILf5o/DB8zUhCHx9pL3WDBpFg/Z20B9PrV7n4rBER5TgtRLeilxspiu\r\nSeelWh5siQ3yJMpPjjWGWhV9C6ZYkJjQy/f1zjgLTXBynLrlzZuw3Z49OptB\r\nWNnqfpR42AJCyUwUhym+MUUWLb4Ypsv2vfzYN8iXj2Ff67DNCxGN+NLQ1gQH\r\nGu0rlQMH5SnBxnitC9fjaWlP725kZBNoptw=\r\n=YVMz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "_from": "file:typed-query-selector-2.10.0.tgz", "types": "shim.d.ts", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "_resolved": "/tmp/383f930f29193df2693edf32eddcc3d7/typed-query-selector-2.10.0.tgz", "_integrity": "sha512-etmpQ3OqQeUk197PyNMDcWzhftp5xwFoB2GKfZPOsCgbNfkJKfyT+8SkxmUs6HsD458E1cypE9oHhYhPWvWOWA==", "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.10.0_1681713222094_0.7840772663642102", "host": "s3://npm-registry-packages"}}, "2.10.1": {"name": "typed-query-selector", "version": "2.10.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.10.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "19b9c78523871792857e0694cebcbc423b0a2f39", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.10.1.tgz", "fileCount": 6, "integrity": "sha512-VyNeetDghQngbLWOVyxUFB95nZF3z3QIrvdsWmuDwwYbePEup9hjZbNT1IK5YP3t8LqicnYNnDpQPW3vq7iCTQ==", "signatures": [{"sig": "MEUCIDXl+yLoYjF371mINaM0Xmj2AfHkOGyRzuco5QV7/ZKMAiEAzkUlzsbRiG+zi9RpEMRHZoHYh+mhSAB2/RCPKvaR1L8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.10.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 18112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRKTSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0jg//RbwCDDrMTOoMRngqnlVeN3X9yKxv64H6EELng8j1t0hSUwcE\r\n9uKbnYMeGwoRPdhh3kAjMiLAeWdfSt7svxN6wP6qd6RnXskdGRMOOAiZ4isn\r\nc9/k9HN6pzO8KvoVR3qiT7M74C9FoU32NMP/LhlialfWRRvF9iw17LL9Ikqh\r\nhFRORexFKEgNBLU8PFVBMC6nAaypR5gzyUmbwyV7Y55Hha/OqE9jfSudZhH0\r\npJl3zuKN7U/YDLF2CedpW3U/mm3+R6JThB7RQETzJND+7n19GD11e0uevC0M\r\nGHxNpZa/XIaoHvvWry41Ku1bAH/WrwZLJnJQ/BSxZfsEjB/La2ebMJcwazk9\r\nvM/BwzS583C4PJnk5/hUIuMFByKdnmYlIsJlAyrvQDw165Z61qK0YM9k0mRg\r\nsaaOcHljyFPC9vQkXh40Q7Hs53yfjINVgPsISnSb2b/0TUAu/oJW6kcxIJNF\r\n8DbUYJxiWvhREYHgQsmrviUDJ7ejiHkR6KLIpxZlBYMghTyvUcKZs1/dpeMD\r\nP/zRrGkK66/vWGKBpo45FejYl3iIcHQTeKEYI8wKIcKaD1HtUPSP77gn3hWu\r\n+JMIcTWN2vxF3p/yPaftx4xgnDmrdggTEe/Gsawu/GssBEwQk1O68FTKXJlt\r\nKnSF3ZTmr/sCLWUoVO24pBiUaE4Loyz7fns=\r\n=xbF3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "4889cf7fdd696aec1fa51d8de93c8382e45c3633", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^4.7.3", "@gplane/tsconfig": "^5.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.10.1_1682220242554_0.9732527288080086", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "typed-query-selector", "version": "2.11.0", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.11.0", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "f9a4f4c9bd3a3564a20d13c9d64eab77c3780d0c", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.11.0.tgz", "fileCount": 6, "integrity": "sha512-qBs4sfmnLlPOyo2oSdvHbIFHe2CPgU54/1UGfSNceb7LARpIEVxUaeRX0Doje6oKpuySS2stqy90R3YrynR8Kg==", "signatures": [{"sig": "MEQCICm8R4EAHF+qj50cuj9Q5+9n0Q1snO9d40iwYyqNDkJgAiBY/yPn9hwmxS4PlJG2OwFW/4Soyn39KneaZdbgzWxaow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.11.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 18118}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "7f85bf5fa402f29e3177d854a0622e5f26596fe4", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p ."}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.8.0", "typescript": "^5.1.3", "@gplane/tsconfig": "^6.0.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.11.0_1686722823481_0.36626719788122686", "host": "s3://npm-registry-packages"}}, "2.11.1": {"name": "typed-query-selector", "version": "2.11.1", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.11.1", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "56e7ae447e5ea6edf448d51d64b842cbc2275418", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.11.1.tgz", "fileCount": 6, "integrity": "sha512-/ETnbg+mXwdtSc8FmotqzGJsFn1frnREGNi9ki/Gd9MFDyUbSu2i0WvU+29Wfspd+9awvk6DQbcWwZAASghl8g==", "signatures": [{"sig": "MEYCIQCXnuMS5MeS06F+oQccgm6aUiLMXfif/phPfNKSqAm8SgIhAKCQ4YI0eUiPEm4MeTMh1x67X87hiRT2xbuGTHKFveqC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.11.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18305}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "c005bb23e6f480c6885616823039efaa439bdc0b", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p .", "fmt:check": "prettier --check *.ts"}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "typescript": "^5.4.2", "@gplane/tsconfig": "^6.1.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.11.1_1710210293666_0.07148739951910232", "host": "s3://npm-registry-packages"}}, "2.11.2": {"name": "typed-query-selector", "version": "2.11.2", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.11.2", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "30f9a2d7d51fc08781b277dff91c61aa8dd756c3", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.11.2.tgz", "fileCount": 6, "integrity": "sha512-6rZP+cG3wPg2w1Zqv2VCOsSqlkGElrLSGeEkyrIU9mHG+JfQZE/6lE3oyQouz42sTS9n8fQXvwQBaVWz6dzpfQ==", "signatures": [{"sig": "MEYCIQCE9gIerwQv7LEbCve6G67oxsZcHflIJUO4PYEm+GhJpAIhAO8LTwgM2M67vrH6V+aTzYlnJ9HvZWffwHa4PjIP9rs2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.11.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 18351}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "03bcd6287598e2bd2b57e080dcfc656e4fd8fcf7", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p .", "fmt:check": "prettier --check *.ts"}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "typescript": "^5.4.2", "@gplane/tsconfig": "^6.1.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.11.2_1710730721507_0.8671719517026306", "host": "s3://npm-registry-packages"}}, "2.11.3": {"name": "typed-query-selector", "version": "2.11.3", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.11.3", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "b8791f504dfb2a675952c23cc468055ae49d5792", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.11.3.tgz", "fileCount": 6, "integrity": "sha512-lMG8vpGrthemzydrNhGbpFqLEDEe4ivjNcofh2L2JYC8OBnkIAZLAsNVEkxS8rix2YZhTMqbwwJh91uk31kKTA==", "signatures": [{"sig": "MEYCIQChfpKiyRAuyt+v3ky6aPH+S/smQ+rCCTlyOcp+fgtmOAIhAP7PpWOkyqjfD4rwnhrvAnRreQoYpQz4gR43n3lYo8ro", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.11.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19220}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "02e82b79b406ce69c514ffb8694bfda63f9b3dc4", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p .", "fmt:check": "prettier --check *.ts"}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "typescript": "^5.5.3", "@gplane/tsconfig": "^6.1.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.11.3_1721261126273_0.8747622413592337", "host": "s3://npm-registry-packages"}}, "2.11.4": {"name": "typed-query-selector", "version": "2.11.4", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "_id": "typed-query-selector@2.11.4", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "homepage": "https://github.com/g-plane/typed-query-selector#readme", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "dist": {"shasum": "fbf130efd41aa9c8f2213f4491c66e2e73877955", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.11.4.tgz", "fileCount": 6, "integrity": "sha512-O/56BMFJc62KLGaw1HTz66N2RRCPU+JnoJUL5q07LVmTXTlhoUdm9azor7keLwSncKTeVtWTJP3TZlMopynJBw==", "signatures": [{"sig": "MEUCIQCujKU/hB3X672WfaA3e5kOdI1QhzVqS2hhhMTeEaEaiwIgUJQ9gY/O4+3qmeeRPhTEx2ny0IKWX/IxD2nRNgdao5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.11.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 19397}, "main": "shim.d.ts", "types": "shim.d.ts", "gitHead": "82e199d3a57dde9af8f8aa3075a1ac4400fd81ed", "scripts": {"fmt": "prettier --write *.ts", "test": "tsc -p .", "fmt:check": "prettier --check *.ts"}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/g-plane/typed-query-selector.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Better typed `querySelector` and `querySelectorAll`.", "directories": {}, "sideEffects": false, "_nodeVersion": "18.20.4", "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "typescript": "^5.5.3", "@gplane/tsconfig": "^6.1.0", "@type-challenges/utils": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/typed-query-selector_2.11.4_1723476532493_0.046311550703743265", "host": "s3://npm-registry-packages"}}, "2.12.0": {"name": "typed-query-selector", "description": "Better typed `querySelector` and `querySelectorAll`.", "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/g-plane/typed-query-selector.git"}, "version": "2.12.0", "license": "MIT", "main": "shim.d.ts", "types": "shim.d.ts", "sideEffects": false, "scripts": {"test": "tsc -p .", "fmt": "prettier --write *.ts", "fmt:check": "prettier --check *.ts"}, "devDependencies": {"@gplane/tsconfig": "^6.1.0", "@type-challenges/utils": "^0.1.1", "prettier": "^3.0.3", "typescript": "^5.5.3"}, "_id": "typed-query-selector@2.12.0", "gitHead": "67605b19ac69313081d2f9e14cb159430f9ab55d", "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "homepage": "https://github.com/g-plane/typed-query-selector#readme", "_nodeVersion": "18.20.4", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-SbklCd1F0EiZOyPiW192rrHZzZ5sBijB6xM+cpmrwDqObvdtunOHHIk9fCGsoK5JVIYXoyEp4iEdE3upFH3PAg==", "shasum": "92b65dbc0a42655fccf4aeb1a08b1dddce8af5f2", "tarball": "https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.12.0.tgz", "fileCount": 6, "unpackedSize": 19376, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/typed-query-selector@2.12.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBflJ5FzuFoRzQCm1xnu3TERhiaq6IBBx4IdI4ADI5XkAiAnoAXQGkxcMPUAaojS3tsfRW5H24+0iXcKAIrxpBkKdQ=="}]}, "_npmUser": {"name": "gplane", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/typed-query-selector_2.12.0_1723629655894_0.6377077935091118"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-11-13T10:26:43.644Z", "modified": "2024-08-14T10:00:56.427Z", "1.0.0": "2020-11-13T10:26:43.785Z", "1.1.0": "2020-11-14T08:52:32.738Z", "2.0.0": "2020-11-15T02:30:03.136Z", "2.1.0": "2020-12-09T01:26:18.745Z", "2.1.1": "2020-12-21T01:25:19.230Z", "2.2.0": "2020-12-28T09:12:53.985Z", "2.2.1": "2020-12-28T09:27:31.408Z", "2.2.2": "2020-12-29T09:37:32.156Z", "2.2.3": "2020-12-29T10:08:39.450Z", "2.2.4": "2021-01-01T09:00:27.198Z", "2.3.0": "2021-01-16T02:09:20.715Z", "2.4.0": "2021-01-21T06:37:58.193Z", "2.4.1": "2021-01-28T01:50:39.682Z", "2.5.0": "2021-04-17T12:30:06.992Z", "2.5.1": "2021-04-18T03:47:39.765Z", "2.5.2": "2021-04-19T04:25:47.579Z", "2.5.3": "2021-04-19T06:19:03.730Z", "2.6.0": "2021-04-28T00:53:07.104Z", "2.6.1": "2021-11-19T02:14:25.265Z", "2.7.0": "2022-09-01T07:46:00.334Z", "2.8.0": "2022-09-27T02:18:32.873Z", "2.8.1": "2023-02-21T02:03:46.393Z", "2.9.0": "2023-02-21T06:34:25.272Z", "2.9.1": "2023-02-24T02:15:53.065Z", "2.9.2": "2023-03-02T03:52:46.190Z", "2.10.0": "2023-04-17T06:33:42.318Z", "2.10.1": "2023-04-23T03:24:02.699Z", "2.11.0": "2023-06-14T06:07:03.642Z", "2.11.1": "2024-03-12T02:24:53.829Z", "2.11.2": "2024-03-18T02:58:41.641Z", "2.11.3": "2024-07-18T00:05:26.450Z", "2.11.4": "2024-08-12T15:28:52.636Z", "2.12.0": "2024-08-14T10:00:56.075Z"}, "bugs": {"url": "https://github.com/g-plane/typed-query-selector/issues"}, "author": {"name": "Pig Fang", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/g-plane/typed-query-selector#readme", "repository": {"type": "git", "url": "git+https://github.com/g-plane/typed-query-selector.git"}, "description": "Better typed `querySelector` and `querySelectorAll`.", "maintainers": [{"name": "gplane", "email": "<EMAIL>"}], "readme": "# 🏷 Typed `querySelector`\n\n`querySelector` and `querySelectorAll` functions with better typing\nby leveraging TypeScript 4.1 template literal type.\n\n## 💿 Install\n\n```\nnpm i -D typed-query-selector\n```\n\n## 🍉 Usage\n\n### Automatic shim\n\nAll you need to do is to import this module,\nthen the `querySelector` and `querySelectorAll` function will be enhanced.\n\nThis module only works at type level and doesn't have any runtime code.\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('div#app') // ==> HTMLDivElement\n\ndocument.querySelector('div#app > form#login') // ==> HTMLFormElement\n\ndocument.querySelectorAll('span.badge') // ==> NodeListOf<HTMLSpanElement>\n\nanElement.querySelector('button#submit') // ==> HTMLButtonElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHowirxDQDsYA6fQ5AMoVqtKAAoEGYADcAxAEMwYBAEo4AenVwAvNoB8cABIAVALIAZACIyAopXZdGzVg558iQyjWgSpcxWBwBgBm0CCy5BAA5sAcqhpaugYmFgBiYXZork4sbJxuBB7C3lAAguTkEiRg8hzcAEbyGFFo8Zo6+nAAchAYaObAJDAA8sEAPCnmAjUcma56TtTk8lBocJTwtXP5AFxGZubbjvRb9vm8hYLFohL1eDC0HLIkePUgwDBtiZ2TAEL3jyOMHoQA)_\n\nThe example above assumes you're using bundlers or build tools with transpilers,\nhowever, sometimes this may not match your situation.\nFor example, running `tsc` or Babel out of bundlers.\nIn this case, you can import this library like this:\n\n```typescript\nimport type {} from 'typed-query-selector'\n\ndocument.querySelector('div#app') // ==> HTMLDivElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBDAnmApnA3gXzgMyhEOAciVQBMBaARwFcUpEKBnFAGxQGMZoiAoXshA40QKAHYwAdLXqIAym07coACiJlgANwDEAQzBgiASjgB6U3AC8lgHxwAEgBUAsgBkAIloCi7URKA)_\n\nThis looks ugly but it works.\n\nIf you aren't going to use ES Modules you can modify your `tsconfig.json`,\nhowever this is NOT recommended, unless you know what you're doing.\n\n```json\n{\n  \"compilerOptions\": {\n    \"types\": [\"typed-query-selector\"]\n  }\n}\n```\n\n### Strict mode\n\n> Available in v2.3+\n\nIn strict mode, the selector parser will perform additional syntax checks on input string.\nIf there're syntax errors, return type will be `never` instead of `Element`.\n\nExample usage:\n\n```ts\nimport 'typed-query-selector/strict'\n\nconst element = document.querySelector('div[test') // return `never`\n```\n\nThis feature won't be enabled by default and you can opt-in.\nIf you want to enable this, change import entry:\n\n```diff\n- import 'typed-query-selector'\n+ import 'typed-query-selector/strict'\n```\n\nThat's all. If you pass an invalid selector,\nbecause it returns `never`, TypeScript will prevent you from\naccessing properties/methods on element or using element at somewhere.\n\nNote that it doesn't guarantee that it can detect every kind of syntax errors,\nsince such parser will become very complex and compilation performance may go bad.\n\n### Use the parser\n\nIf you just want to use the selector parser itself, we've exported for you:\n\n```typescript\nimport type {\n  ParseSelector,\n  StrictlyParseSelector, // or use the strict parser\n} from 'typed-query-selector/parser'\n\ntype MyElement = ParseSelector<'form#login'>\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBDAnmApnA3nACgQygZxQGUUAbFAYxmjgF84AzKCEOAciVQBMBaARwCuKKIh6FyVaAHoweQlDYAoRZzQBZRAFFyIFADt4AXmxziZStSgAeNg2ggAxKQgBzYHrYA+IA)_\n\nPlease note that you should import `typed-query-selector/parser`, not `typed-query-selector`.\nThis is safe because this import doesn't patch to the `querySelector` and `querySelectorAll` function.\n\nSometimes, you may want to specify another fallback type (such as `HTMLElement`, not default `Element` type)\nwhen failed to parse selector or failed to look up, you can pass a fallback type as the second type parameter:\n\n> Available in v2.4+\n\n```ts\nimport type { ParseSelector } from 'typed-query-selector/parser'\n\ntype MyElement = ParseSelector<'unknown-tag', HTMLElement> // ==> HTMLElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBDAnmApnA3nACgQygZxQGUUAbFAYxmjgF84AzKCEOAciVQBMBaARwCuKKIh6FyVaAHoweQlDYAoRZzQBZRAFFyIFADt4AXmxziZStSgAeNgL0BrPRADuenjBwBzNgBo4ACQAVNQAZbRRdAwA+OCkpOENDGKDQ8MiYIA)_\n\n## 💡 Supported Use Cases\n\n### With class, ID, pseudo class or attribute\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('div.container') // ==> HTMLDivElement\n\ndocument.querySelector('div#app') // ==> HTMLDivElement\n\ndocument.querySelector('input[name=username]') // ==> HTMLInputElement\n\ndocument.querySelector('input:first-child') // ==> HTMLInputElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHowirxDQDsYA6fQ5AMoVqtKAAoEGYADduVCFwCGwDoQQBKOAHotcALx6AfHAASAFQCyAGQAiMgKKV2XRs1bOefIkMo1oEqWkAYkUwMA1tXQNjc2s7aUc0D1cWNk5PAm9hP3EEFTA8GABtDkV2PTwyKFL2AF0InX0jU0srAEkOAphE5KZUj15MwWzRCXzCgC4AM2AoEhgsKgALYHIMBqjm2PbOwp70oA)_\n\nEven mix them:\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('input.form-control[name=username]') // ==> HTMLInputElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHowirxDQDsYA6fQ5AMoVqtKAAoEwDmDw8AZtBBYqELlAjkA2hwCG7ALx4yUXewC6CAJRwA9Dbj79APjgAJACoBZADIBJabIAopTsXEA)_\n\nAnd with `:is()` or `:where()`:\n\n> Available in v2.5+\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector(':is(div#id, span.class[k=v])') // ==> HTMLDivElement | HTMLSpanElement\n\ndocument.querySelector(':where(div#id, span.class[k=v])') // ==> HTMLDivElement | HTMLSpanElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHowirxDQDsYA6fQ5AMoVqtKAAoEALmAkxGYADcAxMAwAaOCTABDDtyrltJEgG0A1gF4FAXQCUCW3AD0TuBYsA+OAAkAKgFkAGQARRQBRSnYuOAAfHwDAgR0OCLQomEZmVnTeAiIhShpoCUkAdwALQjQ5RRV1TWT9Q2NzKzsHZ1d3Lz8g0IVU9Nj4oKTdQc4YIA)_\n\n### Combinators\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('body div') // ==> HTMLDivElement\n\ndocument.querySelector('body > form') // ==> HTMLFormElement\n\ndocument.querySelector('h1 + p') // ==> HTMLParagraphElement\n\ndocument.querySelector('h2 ~ p') // ==> HTMLParagraphElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHowirxDQDsYA6fQ5AMoVqtKAAoEAIwgZkcDMABuCAJRwA9OrgBebQD44ACQAqAWQAyAESUBRSuy6NmrBzz5EhlGtAnTZcAwAzaBBVDS1dAxMLADEQuzRXJxY2TjcCD2FvcQQACwBGOABqODAwzR19IzNzAAUAQyh6gHMmsFyEpKYU114MwSzRCVyAJjgAP1LyiKrousaWto77NKA)_\n\n### Grouping selectors\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('div, span') // ==> HTMLDivElement | HTMLSpanElement\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHowirxDQDsYA6fQ5AMoVqtKAAoEGYADcANHBJgAhhwQBKOAHpNcALy6AfHAASAFQCyAGQAiMgKKV2XOAB8TFywOUcHaJzCA)_\n\n### Fallback\n\n#### Custom Elements\n\nIf you passed an unknown tag, it will fall back to `Element`.\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('my-web-component') // ==> Element\n```\n\nHowever, you can override it by specifying a concrete type as a type argument.\n\n```typescript\ndocument.querySelector<MyComponent>('my-web-component') // ==> MyComponent\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHo2vIEMo04q2SS4BZZAGEI4CADs04+GgAeMKRj4AJACr8AMgFFKIKfADeAX0YYIVPHukA6fIWQBlCtVpQAFAhDEA7mgBGWFSikJLSCACUcAD0UXAAvHEAfHA6aFYwpuaW+rYERE6UNNAAPIIiYqEwiR5eWL4BQRX6EdGxCcllwRL6QA)_\n\nAlternatively, you can use [global augmentation](https://www.typescriptlang.org/docs/handbook/declaration-merging.html#global-augmentation) and [interface merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html#merging-interfaces) to extend `HTMLElementTagNameMap` with your custom elements.\n\n```typescript\ndeclare global {\n  interface HTMLElementTagNameMap {\n    'my-web-component': MyComponent\n  }\n}\n\ndocument.querySelector('my-web-component') // ==> MyComponent\n```\n\n_[Playground](https://www.typescriptlang.org/play?#code/JYWwDg9gTgLgBAchgTzAUwCYFoCOBXNKZLAZzQBs0BjGaBAKHo2vIEMo04q2SS4BZZAGEI4CADs04+GgAeMKRj4AJACr8AMgFFKIKfADeAX0bNu7TgHNyEAEatycA-Tiu4waYQBmrKpzWaOmh60qqslgByrHr8rGBOLm5JCCDEAO5otlhUopCS0ggAXALCuRL6ANyJriYmTBBUeCEwAHT4hMgAyhTUtFAAFCnpmdll+TAIAJRwAPQzcAC8CwB8JSJi40A)_\n\n#### Invalid selector\n\nWhen passing an invalid selector which causes parsing error,\nit will fall back to `Element`.\n\n```typescript\nimport 'typed-query-selector'\n\ndocument.querySelector('div#app >') // ==> Element\n\ndocument.querySelector('div#app ?') // ==> Element\n```\n\nHowever, if you're using strict mode,\nall `querySelector` calls above will return `never` type.\nThis can stop you from misusing it.\n\n```ts\nimport 'typed-query-selector/strict'\n\nconst el = document.querySelector('div#app >')\nel.className // TypeScript will report error when compiling\n```\n\n## 🔩 Technical Details\n\n### Why returns `never` in strict mode?\n\nIn runtime, if you pass an invalid selector string to `querySelector` or\n`querySelectorAll` function, it will throw an error instead of returning\n`null` or `undefined` or anything else.\nFor details, please read [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/2/narrowing.html#the-never-type).\n\n## 🔗 Related\n\n- [Type Gymnastics](https://github.com/g-plane/type-gymnastics) - Collection of wonderful TypeScript type gymnastics code snippets.\n\n## 📃 License\n\nMIT License\n\nCopyright (c) 2020-present Pig Fang\n", "readmeFilename": "README.md"}