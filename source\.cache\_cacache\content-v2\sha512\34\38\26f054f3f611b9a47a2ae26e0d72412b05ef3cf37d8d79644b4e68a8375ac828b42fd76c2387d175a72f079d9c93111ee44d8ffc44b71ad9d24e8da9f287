{"_id": "extract-zip", "_rev": "49-99f232897490df8ccec562bf28c70079", "name": "extract-zip", "description": "unzip a zip file into a directory using 100% javascript", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "extract-zip", "version": "1.0.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD", "repository": {"type": "git", "url": "**************:maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"minimist": "0.1.0", "debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "^1.4.6", "through2": "0.6.3", "yauzl": "^2.0.3"}, "devDependencies": {"rimraf": "^2.2.8"}, "directories": {"test": "test"}, "_id": "extract-zip@1.0.0", "_shasum": "53397d86d4ae3a8df17492133cc0be01336e2400", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "dist": {"shasum": "53397d86d4ae3a8df17492133cc0be01336e2400", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.0.0.tgz", "integrity": "sha512-ZCSZ0lyteMCYkSMweJjVlRVpfLVZisMBQ4/sRVo49KIBHeXag9oW/EG4JstdJTej/EwY9dJ5SXmlXMShh5IxKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGPOBsFIfJlOPbpQO7ffFLTUjX4RQrjQ7F4/YFjqEKqwIhAOdsN2xPKnLweqwgNyIPyT2jF9RjqwMCKKSopZHLWSO3"}]}}, "1.0.1": {"name": "extract-zip", "version": "1.0.1", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD", "repository": {"type": "git", "url": "**************:maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"minimist": "0.1.0", "debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "^1.4.6", "through2": "0.6.3", "yauzl": "^2.1.0"}, "devDependencies": {"rimraf": "^2.2.8"}, "directories": {"test": "test"}, "_id": "extract-zip@1.0.1", "_shasum": "32563a636d6a40a062e1f4e2e42e47176ab46692", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "dist": {"shasum": "32563a636d6a40a062e1f4e2e42e47176ab46692", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.0.1.tgz", "integrity": "sha512-sl6e+MhY4RllY7R+Tw1qUDHFhf7gcThGT1dHOy2Qa0NXC7LR4CYsGT6YY/yfw8rb8tYSyailapnW1IVcJxhrRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDraifY3ULJcVq+BfFkaPOQiGPupCTpdc3MapVlmZ2MZwIgKt4cDIQVDpSQfsALGccOd7QLFr/61rZ/uOD3aSj0fqI="}]}}, "1.0.2": {"name": "extract-zip", "version": "1.0.2", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD", "repository": {"type": "git", "url": "**************:maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"minimist": "0.1.0", "debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "^1.4.6", "through2": "0.6.3", "yauzl": "^2.1.0"}, "devDependencies": {"rimraf": "^2.2.8"}, "directories": {"test": "test"}, "_id": "extract-zip@1.0.2", "_shasum": "cff2d67dd2c987d1ab0d7909ad408d8fdfdf0093", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "dist": {"shasum": "cff2d67dd2c987d1ab0d7909ad408d8fdfdf0093", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.0.2.tgz", "integrity": "sha512-X3DX4MNVShzJ/XLsi742jZsdv6nsqcL5PpvcvNvd8nfatZ1bP42wJi0V46dANPRghlyriqNJI2jtst1vr+ZFnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrf6Zub06VP6U/jZZskiS00Pz3Smoq4ZMEspoD96AhZwIgaF+Pol2m07c45R89nKnbf8ZTo1glfaghvh4wQnSD9eE="}]}}, "1.0.3": {"name": "extract-zip", "version": "1.0.3", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD", "repository": {"type": "git", "url": "**************:maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"minimist": "0.1.0", "debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "^1.4.6", "through2": "0.6.3", "yauzl": "^2.1.0"}, "devDependencies": {"rimraf": "^2.2.8"}, "directories": {"test": "test"}, "_id": "extract-zip@1.0.3", "_shasum": "cb5b3d11211996413f2c26fe50123e5c0d82008f", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "dist": {"shasum": "cb5b3d11211996413f2c26fe50123e5c0d82008f", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.0.3.tgz", "integrity": "sha512-1jXvjBRt1J9Yntjjg8w/xsZdouwSRtFMRUOrQszsjm3qBGCLPYds1oTrxjXPx6x09Z6/8T8a2KoF5nwk/tHlIA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoYPztWEkZPrOmwZw4NMr/xKBou/m7TvwEWpuj5Mzb3gIgOE4p02s0jXpuukzFkp3dnQZBwLmNXqVAYqw0pPwMJ80="}]}}, "1.1.0": {"name": "extract-zip", "version": "1.1.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "1.5.0", "yauzl": "^2.3.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "8f8c83c4764ffa1711c100c1e76de147be49dcdc", "_id": "extract-zip@1.1.0", "_shasum": "214c3cb73a623d9fcb8ccaf20c316b0ffce6cbe7", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "214c3cb73a623d9fcb8ccaf20c316b0ffce6cbe7", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.1.0.tgz", "integrity": "sha512-56eeBdEKJM6hMqiSwcaMzkzEBOTq/a1Xu1BYelNIlKAPU+Ul02wsTZ2v+TOPCMT0yj8hr3BXZZ7ZmFqf/jwI5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGFAYLr/wXDUEm09//ucnyXLs66ilgFL+Wafgg1X9I2vAiAu/YrQ1/xLaCXy9qCavgCqsvvDHp1SLvncr3hoQxWhvA=="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.1.1": {"name": "extract-zip", "version": "1.1.1", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "1.5.0", "yauzl": "^2.3.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "ac5f6d51507404e76950a5de1c6100a8ecca175a", "_id": "extract-zip@1.1.1", "_shasum": "6936372b13a6d2e4db01c116e5289c438f519b94", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "6936372b13a6d2e4db01c116e5289c438f519b94", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.1.1.tgz", "integrity": "sha512-PP3q6RRdcMluiHSimIhfD6JoepRpB3Uz3v8hKjxgemkQjyPCLDzTn0P+yl6G5OFRdi4ZA3AKFKzvVCdYEY+4TA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCScixh8Fm+gr4F65zhTPsbQLNR6gHPGV3ypk98u5QK2gIgO9CL76WuSlZh4Xp6Yy7HdtkfrOl2CPF00Yh5KJ5gZLU="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.1.2": {"name": "extract-zip", "version": "1.1.2", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "1.5.0", "yauzl": "^2.3.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "835ec04416298a5587ffc2f54bac9bb7e5e98374", "_id": "extract-zip@1.1.2", "_shasum": "7b6d6e0dd2149b6b89f2cc44a21d8500e327cd97", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "7b6d6e0dd2149b6b89f2cc44a21d8500e327cd97", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.1.2.tgz", "integrity": "sha512-2NLDIL+MUSfXrlruFiZxtK+cm4V+bCkpAv+0d9knJH2XRh2D5igWV/UD8jKsV6CLb2Axbvan78k3Uf5KrS+KwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKkC37iZxhrjscDATLDSgt7Qz1t9RNY3l5VQW0kTWj6AiEAtJBcsmC/JcVOQMybGBt2pEQtNQmZIDcvSSJXyVHvehQ="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.2.0": {"name": "extract-zip", "version": "1.2.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"debug": "0.7.4", "async": "0.9.0", "mkdirp": "0.5.0", "concat-stream": "1.5.0", "yauzl": "^2.3.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "30462debe058a8e3625a091093192bfa74f6d412", "_id": "extract-zip@1.2.0", "_shasum": "ca072a1e4283af084747c5132441d6ba2fd211ed", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "ca072a1e4283af084747c5132441d6ba2fd211ed", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.2.0.tgz", "integrity": "sha512-RsZYDxcVXD/+kVj5c2Xk39p44K/6njqnLcTCp0QhJhCnwk9RFjiLQqktAjEYSpyHyCJ8s9aaBsz3z3ulmCLiTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCIDVOVBlYlg59lBBjFJUkw9H/+whha0iw2WX2eH3o8zCWAh9wH6NfZ118mMWrT09z9cbzR6t+nKAKBh4iZ9ocUzFL"}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.3.0": {"name": "extract-zip", "version": "1.3.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"async": "1.5.0", "concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.3.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "4b25de2752aa9e4dd9cb81a874d371bacc207259", "_id": "extract-zip@1.3.0", "_shasum": "ac72b5e86f842b153d1b2fb8573defd7e8e49f0c", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "ac72b5e86f842b153d1b2fb8573defd7e8e49f0c", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.3.0.tgz", "integrity": "sha512-SpdAKtXhDr82GZ4VjpReOM6lb3fKXMlLzD6Ja1XZTMBlKPDq4+7c+qt0WguqTERoPiftAn212SbEKeiOIQFOYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZUQaH0xC+NDf2ZfaXQq09wfqD6kH3yzH5KgYFGm6UlAiANsymW3N/cR3hdFqEUSA7l9qLCq8syfYzmn6nqr0S7wg=="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.4.0": {"name": "extract-zip", "version": "1.4.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "d79cfd14859034dd753a54cdb962a10e4984b867", "_id": "extract-zip@1.4.0", "_shasum": "7d3aa7e042aee1e9f248b000da4378ac21233562", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "7d3aa7e042aee1e9f248b000da4378ac21233562", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.4.0.tgz", "integrity": "sha512-W15iZf5gb91UMydoTXqg6FpmblZgJGfOijypVZ1h2ht/XhKC40zBMBT5wrgNEYLD7aQd+nIHuLjMpd9NRp+AvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFpD5T1JBdJn16u5Uu0SsjRNs+7TPoEMPHYdZRcUnfOwIgJQjOlL3R9ITjcYFis/A2hEVH/PSA4kKKdhcWOzXAZbw="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.4.1": {"name": "extract-zip", "version": "1.4.1", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "e26a779b5113a153fabfc78b6189c2d9c53599ec", "_id": "extract-zip@1.4.1", "_shasum": "6c1afdca09083eb67fad3a7ccd1fdd4c02b70a60", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "6c1afdca09083eb67fad3a7ccd1fdd4c02b70a60", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.4.1.tgz", "integrity": "sha512-8L+Tr+tVK0XrLt8kf92AZbwg9zc0LSpKzaukvMSZ8f7axlqZPVQVYgINhVEaMxNCfcL9Bt/TOVrjDP1K7R+R4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhLdg29KAESiXRUS4UqS69Om+mwgZrGOMHzVBv7Qbz9AIhAMEnFvEjdyMXUaLpqswkKqEJEf8HNhXihxXplqj1/Vm/"}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}]}, "1.5.0": {"name": "extract-zip", "version": "1.5.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "bb5798317dad5b23af6ef098cc43379b2d19d0b8", "_id": "extract-zip@1.5.0", "_shasum": "92ccf6d81ef70a9fa4c1747114ccef6d8688a6c4", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "4.2.3", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "92ccf6d81ef70a9fa4c1747114ccef6d8688a6c4", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.5.0.tgz", "integrity": "sha512-Ht7oUiEXWnX5BvLzMX/UBNIjrAs53lhXtNxMNeUe8Nv0S8rfy5UGqsKOXpP8ZQMWLvheOvRqYYShBoj6fTO9bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAtiEaR/qSJRPFzkE12PJy+EMuvLAr2YadeP5ki12cYcAiBu2XGeMqMWFUzsuvryZYWC+8YfKttz8LhnCtVnljMWHQ=="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/extract-zip-1.5.0.tgz_1457378610579_0.8177433146629483"}}, "1.6.0": {"name": "extract-zip", "version": "1.6.0", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "c5d2dec11b9fb3f5e67a91325309244da4efcafd", "_id": "extract-zip@1.6.0", "_shasum": "7f400c9607ea866ecab7aa6d54fb978eeb11621a", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "7f400c9607ea866ecab7aa6d54fb978eeb11621a", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.0.tgz", "integrity": "sha512-hF7/5BdEvVgrJuNxHPxWUqjks/f77xd7WvHywinBaPgpnwAhCtW0crNLpKiJY1RjRskzzGduahyrZdFhiiL5MA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvTGXgjFdo1kTD9rCkXf5QFKVTtxeYIOnmaUpXVkZf3gIgfOaynJV/VNzRciSDWSnfnWXecZbijcnZOFme8degpcM="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/extract-zip-1.6.0.tgz_1482296777203_0.3630462531000376"}}, "1.6.1": {"name": "extract-zip", "version": "1.6.1", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/erisds/extract-zip-fork.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.5.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "40a2389ee7f20ad98649a7f7ec087f6e6ddaf355", "_id": "extract-zip@1.6.1", "_shasum": "e35c446cba4638ca703737337729673224bdb769", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "e35c446cba4638ca703737337729673224bdb769", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.1.tgz", "integrity": "sha512-lrrHFU6PCrKvLTaOOaNaKuHxZT/dUXMwr4VVdnip0CNJxNXIF+ZK3Y6uRrWUE4Y3+rMA9NEpTxV+Zf6n0kICUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2/c46U8A+1F5z2sWwCxIUrzJzO/l7aU/JqU5O24pG1QIgJdK+IZdY2iJoKyIUnABrtieJXANQBPuibEqgP1JJ8fU="}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/extract-zip-1.6.1.tgz_1493500301655_0.0698060451541096"}}, "1.6.2": {"name": "extract-zip", "version": "1.6.2", "description": "unzip a zip file into a directory using 100% pure gluten-free organic javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/erisds/extract-zip-fork.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.6.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "7bf277460a725143714d2c295e9e10715543d9e7", "_id": "extract-zip@1.6.2", "_shasum": "8d9b5a8e572734dcb5fa68744f17e580b40bb49a", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "8d9b5a8e572734dcb5fa68744f17e580b40bb49a", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.2.tgz", "integrity": "sha512-DYHhjHhUes+dlYggMPl/MaEIz7Hi8goWWOzjMVdHTuUVUxkDwRSddQl5PNE++/MR/GgLyQaKQERu4mieZpv/8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNnV9+aKOsIcNURyJ0CIXRVwYQX71xblidhr/9eH7uFwIhAMOpq43vh8adwjA7s0edt6ma86vzvrFFJe5ZD6wuxA2X"}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/extract-zip-1.6.2.tgz_1493500564528_0.6069421058055013"}}, "1.6.3": {"name": "extract-zip", "version": "1.6.3", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/erisds/extract-zip-fork.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.6.0", "debug": "0.7.4", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "7488adab2b17b8d89fe9f99e8616e95973d26307", "_id": "extract-zip@1.6.3", "_shasum": "d5eae1b66e5a132314e0b3cf922a85e75ed38012", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "d5eae1b66e5a132314e0b3cf922a85e75ed38012", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.3.tgz", "integrity": "sha512-aFsalkqVoYZdqz2+ayT2MnMU93ieu4TFuvAaMu2/v+Xg8EI3+SoO8vEguPcthJyiF56bWeCC6qHUEdY+G04m+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfGeLq1IyilX9nyWyR2Cpy7yjxp8mIGheSlMYwkTcm1QIhAOVwKmMOQlt4wNbPA9hYXuqAOFvEwMN12SRXEwyumJHe"}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/extract-zip-1.6.3.tgz_1493500690786_0.06246409798040986"}}, "1.6.4": {"name": "extract-zip", "version": "1.6.4", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/erisds/extract-zip-fork.git"}, "keywords": ["unzip", "zip", "extract"], "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip", "dependencies": {"concat-stream": "1.6.0", "debug": "2.2.0", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0"}, "directories": {"test": "test"}, "gitHead": "ca3688469d7add5ce46eab94c023bff89db41ee7", "_id": "extract-zip@1.6.4", "_shasum": "0ee810e214c83ce4ce416d2898c903e3e7f6c3cf", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "dist": {"shasum": "0ee810e214c83ce4ce416d2898c903e3e7f6c3cf", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.4.tgz", "integrity": "sha512-5vNhKMYOn3QTgX/ErfHQtsIjV42hkN/qeKj8ORW5V8IMhgCclMLBzDIjJ3WaKfQogad6Vt6cZvlNKLuggj6gLQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHqG3NnosTMQBFHKd/RHZHE2rag9TV/m7UP6HgcUbPVAIhAIiN2PTuzYWQyxR3X5gXXnHWoKbsLk96DDKfS4A3VK3Z"}]}, "maintainers": [{"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/extract-zip-1.6.4.tgz_1493500791338_0.5411609627772123"}}, "1.6.5": {"name": "extract-zip", "version": "1.6.5", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "dependencies": {"concat-stream": "1.6.0", "debug": "2.2.0", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0", "temp": "^0.8.3"}, "directories": {"test": "test"}, "gitHead": "f200e35cca79a0cafb89c001c5e9d93486c92870", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@1.6.5", "_shasum": "99a06735b6ea20ea9b705d779acffcc87cff0440", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.10.2", "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "dist": {"shasum": "99a06735b6ea20ea9b705d779acffcc87cff0440", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.5.tgz", "integrity": "sha512-ik29Ktq4+LKlROwTSVJeGnbr3ltILxDObjxqWcQPMM0wZ734UUa+ZaTBjPZ39lAE5742KsMgAGbnQ+SX900ZFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpCRYPaXoGWk6C4KY0rYOv1Saf+WJ8xfiDYjTpWoTJSgIhAL95Tw2D/NeVftUufERSaP3RxiUzr6v15IkLUo8MMz1m"}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/extract-zip-1.6.5.tgz_1493615442815_0.5959967295639217"}}, "1.6.6": {"name": "extract-zip", "version": "1.6.6", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "dependencies": {"concat-stream": "1.6.0", "debug": "2.6.9", "mkdirp": "0.5.0", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0", "temp": "^0.8.3"}, "directories": {"test": "test"}, "gitHead": "4fe49d66b07abf95eaaf52400acb84c9aaa3cc55", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@1.6.6", "_shasum": "1290ede8d20d0872b429fd3f351ca128ec5ef85c", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.5", "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "dist": {"shasum": "1290ede8d20d0872b429fd3f351ca128ec5ef85c", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.6.tgz", "integrity": "sha512-zzFIURH1cHodCESbwDsIijbki47jXAvffc044IpX4uBiTWldwQ2viYkYrb94x49ZvAcanD0gobvQwgAaaQOUvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQWFv+CZKK/KZzqDjaXoaEIZEe2Aczl6b0sECs6lomxwIgU48RsmAOX3tqmFNmZwXK4BW0GC8HxmQq81oL0XUH6tc="}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extract-zip-1.6.6.tgz_1509424671032_0.9591677887365222"}}, "1.6.7": {"name": "extract-zip", "version": "1.6.7", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "dependencies": {"concat-stream": "1.6.2", "debug": "2.6.9", "mkdirp": "0.5.1", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0", "temp": "^0.8.3"}, "directories": {"test": "test"}, "gitHead": "422a39ff47996b802946169a3a47f1496253c81e", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@1.6.7", "_shasum": "a840b4b8af6403264c8db57f4f1a74333ef81fe9", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.14.2", "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "dist": {"shasum": "a840b4b8af6403264c8db57f4f1a74333ef81fe9", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.7.tgz", "fileCount": 8, "unpackedSize": 10427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBh4bCRA9TVsSAnZWagAAN70P/igN43hKAyENs2t6/C+g\ndQuPkUE4jA5WzhqK0jvGyzX49Xh5535pj0ig+VkEavl3J/bX0Bu0kfY0/YDD\nfm9s7RAh4Q3zTizXt2BkI9nosdsOOFvRbaCphGntcl5ChOe/2TcnmHtScbmG\nEaxD9Ja6WwD8dNJXcXSYRVhZyqe8YlX1nv35VW/h3M2bAGl5hJz8/uk5U7W1\nSSc9XRRfyzDurIvDWBRjw+lw68t3yWGJ8dGugq+dT67PoBUmH2caDt9sgAfj\nq0cYy7k3Bc0jWa5CxMdNgCGrK56xwLnkejGy0yvtrp2FoIBx+nReJOq4foSk\nkCF29rpA8g69TU1MrXuARWVuTtYqstX1ZkvHCGuy5qJQZDHwaU4UVds3SPrZ\nnAnDaFU18bOaY1f4Q/OowHXhq9DJ8UsULd5KpF2JBibDi1mQcOgFWo3Cx+Hj\niUWB2xP2Zp3paPvLudb28Vl3ijA6WUh9PnEaAblrdPjknQzqXLWks+8pnmdk\nQYUavRJiiRD9YqHRTlhLiI+K5waNi4SIehaqzdyuiQnTEPh+7y1yW751UEpK\naMNsqm46mn27vER/sBcV5gzFmTnzd7pbDjhICqZvih4U7Sq7BT1ItTmgaCXh\noHiYh/RenHrg/gM6at5d/jk8bcLk80wbu7H0/TUaFfBKo2/zqpZbMKFVuX7N\n4z8p\r\n=55W3\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-NWFb/0zxv3qh7f6hEy+F+Y+jPAqt1bfT52GR8Vi7sEFg2fBZlG/aM6ZrSGPUscP0I4JRhtgVG6I17HOuD7GESw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFJr2mLgV036Lv0qqBX9/YEuZ+RPCC2BSBQRJ0XPsMcgAiATR1nuv5+yNKbxIILc4ueN5PRDFhdvINW9U5gL97z77g=="}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extract-zip_1.6.7_1527127578224_0.9162413148655575"}, "_hasShrinkwrap": false}, "1.6.8": {"name": "extract-zip", "version": "1.6.8", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "dependencies": {"concat-stream": "1.6.2", "debug": "2.6.9", "mkdirp": "0.5.4", "yauzl": "2.4.1"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0", "temp": "^0.8.3"}, "directories": {"test": "test"}, "gitHead": "2a8df24e421d5d48a1031b5581592f7386bc0b3d", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@1.6.8", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-iVU8H2PVOWCjhtfwRlWyK500NevDfxygRkVwIgh0+Yp43xTDtOZWZ7Oy7SGwLa35H7mS7GgocoCmarpPqsXZrA==", "shasum": "19a7b2e5d3fac90b1e011c8de560572ff9264362", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.6.8.tgz", "fileCount": 7, "unpackedSize": 10422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeepwVCRA9TVsSAnZWagAAnqUP/1x1ADYGItrMviwAMcu4\n6bjgFYtfC1rADYfMnrgwPhUC94gGE/APdNCrhVWCOgJ6AVXbMTnM5s7JtRwj\nDRhU5SGjfqhVYoqDX+VAuVtPVMvvYYtSc8LKh9rAZ0eP/IDlxJrAWSBfkwWc\nxuauX2T7mpvLype1ElN/nwPVrpSFJF8Qs21P8H0AGf/qCLJkQA9VNP8EL3eo\nhs2evf9H1pHfFI+MUNzpvcD9EamDJi6sCO1roi9hCdp7ZfPT/iHBEWG870Ej\n+HsWt6qYI5QUDwYn+gebJPThZsQVVYKYcvYtMVl83Z/BM380YpXD8GdbCo+9\noPo/vCDTNEHgPZwqAj9vQhjIXU+azDjFUMZNjuRodWFHnJKXT8Np2y5t2KOY\n0q1hHdktxTKXp0HYu4iF27HD/YtLHxCmD1cC8nIngWKC0rJOnjj0i0afhBW6\nKjPSG/SqoejnwMlTbKCJc8EKYN0SH6roiFHkrjdz+YjIjIHhFOULKst4KQOF\nnTVVDo6Yhn5R8kcSCoob3rJSLoa9SEeYmNVffi5lls1aoi41vLGe2GkhvXKl\nwDjhz3MpmH/5nUyVCUcOGaJud+co4ttlu6FX/prwhUfxIAgZb/u/vlISNpjG\nLginZppV9w1s54dvI7FTZ+46Bl/EHjGDqyax7aiEU7qKRknI4NdjpPU369Vw\nl6vv\r\n=EMBU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKvT6klHX4tZ5Z0JsDrJb6kHNIHAucpQAfuF+aIEu6HAiEAwuzY8GKRBdXzFA4RkDPz/n+qUrwe04SmoqmeTEFyA3Q="}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extract-zip_1.6.8_1585093652618_0.9897098157038784"}, "_hasShrinkwrap": false}, "1.7.0": {"name": "extract-zip", "version": "1.7.0", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "bin": {"extract-zip": "cli.js"}, "scripts": {"test": "standard && node test/test.js"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "dependencies": {"concat-stream": "^1.6.2", "debug": "^2.6.9", "mkdirp": "^0.5.4", "yauzl": "^2.10.0"}, "devDependencies": {"rimraf": "^2.2.8", "standard": "^5.2.2", "tape": "^4.2.0", "temp": "^0.8.3"}, "directories": {"test": "test"}, "gitHead": "c2b1c17477b9b72c7dabf1e4762dd7c419e8fa2e", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@1.7.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-xoh5G1W/PB0/27lXgMQyIhP5DSY/LhoCsOyZgb+6iMmRtCwVBo55uKaMoEYrDCKQhWvqEip5ZPKAc6eFNyf/MA==", "shasum": "556cc3ae9df7f452c493a0cfb51cc30277940927", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.7.0.tgz", "fileCount": 7, "unpackedSize": 10560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeqz/CRA9TVsSAnZWagAALFwP+gPQKR2wPM2OpOnSKcoU\nK9iIcvdM6YAZvsSIpRUCYvqCh+e9OukaZT8cUMbmTiVSmU1cvyain/KrpabZ\nDsedjBEEYIv434YEsf4EQOYwNW0Rg75SG+FRMVlu8Iam/MmRJ6Q34/EfH3qb\nVJb4khDlj1cJVCrwQG2aeu9cVAP1WKkn1tKDGvvdM4ZorFWw0GPtzDEMi3Gh\nVnjn4qLcetcPDppBguEQxDhDQ0+UaiEOoBvv6jfTWBcciqkKLo42/tZTv0Me\nh7tVjN09NxIFZZKdkdd7wII9bHUBimeKCFHaYweZoXNix1+/EKc8oKh5WtOn\nn4Ip3czLcNHWvdEJU6qe4xh2LrSU0faVoU2jqY74o3ONFTzwEpF8OUkTUDBm\nySYKWbtztRXLzQAqK9kgeDcxuGRI4UU+4/2QJMMXJjA1+/1QaQzDyQFVJNAx\ntEhw4hEEIbotikBv/yIAPy51P+Ssk2kpTg3xMYgjoP77lBxv3E06eEJ6G7Va\ng8X1q5wkg3huvGwZ1ok5nn3oaN60/NrM+n81ghTMwo+Ju/55+Eh+H72PJxiZ\nJOIH6p3T+DhF0flf7KQMBtEY3K/kSbxeS/vbpihsB+/pBNfLyAS10HXGunZK\nAdkFxOzLQUjXVS7TECdeM4syyBgjsfxk/Q2FI9Q0gi5iT5GF3rDlZVaYc6sh\nhH1k\r\n=y8Pc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG3hPlLaicj5i5gNqRDa5F56iTpxOBzvHymtkKsFUJqoAiB2uqCqA/t12Orm6GpliNOYfGgwdxqS5g8QngJ64F1DCA=="}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extract-zip_1.7.0_1585097983531_0.2719609479903069"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "extract-zip", "version": "2.0.0", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "types": "index.d.ts", "bin": {"extract-zip": "cli.js"}, "scripts": {"ava": "ava", "coverage": "nyc ava", "lint": "yarn lint:js && yarn lint:ts && yarn tsd", "lint:js": "eslint .", "lint:ts": "eslint --config .eslintrc.typescript.js --ext .ts .", "test": "yarn lint && ava", "tsd": "tsd"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "engines": {"node": ">= 10.12.0"}, "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0", "@types/yauzl": "^2.9.1"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.25.0", "@typescript-eslint/parser": "^2.25.0", "ava": "^3.5.1", "eslint": "^6.8.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-ava": "^10.2.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "fs-extra": "^9.0.0", "husky": "^4.2.3", "lint-staged": "^10.0.9", "nyc": "^15.0.0", "tsd": "^0.11.0", "typescript": "^3.8.3"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:ava/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:node/recommended", "plugin:promise/recommended", "standard"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": "yarn lint:js --fix", "*.ts": "yarn lint:ts --fix"}, "gitHead": "eb3c1edb8481bbf68da05cd3a824b1dcc697f908", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@2.0.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-i42GQ498yibjdvIhivUsRslx608whtGoFIhF26Z7O4MYncBxp8CwalOs1lnHy21A9sIohWO2+uiE4SRtC9JXDg==", "shasum": "f53b71d44f4ff5a4527a2259ade000fb8b303492", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.0.tgz", "fileCount": 7, "unpackedSize": 11321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefpPkCRA9TVsSAnZWagAAXHcP/ijZ0+g+lW0AP2Jjy0GA\nc940sVp/e6eAi9Usa7+pDYCeRB98oq60M0vRWBGfjlLeeMs7uhqe9qZ2CRcd\nzRbyvAbomS1k+nWsYYLFQP0HH3ikKk4uNzp0vixrmpXMx/Lqae/H1/Av86BO\nzlTqfyUZh/vAUgrmfujYIu7CnH2H+ips1mjOJrTOSfECDschsddQH/hUpKnt\nCUsgNiqIjFCuvwBva+XFLue/UO6fhtvQjYtMC6LA9sdSVh4DL3CyLAtSBhbA\nOa8rOG18YGz2PYcJI3PqD8hgDZXnUQQYxCRDArCuhQMAYZ2jf6ycFx4GFlu+\nYOl7HlVp8MkDLVX9wk9FJIMTUagRqFDXBAO1NnCtIEKHMUPeuiCsHmGUju83\ni928+66CyEN6GP0IRcioHF29sB7+u+oJdZPzuuOMQwkZpJdVC9mtfQn0FtNC\nnajKD53F2RquOA3X3ImR++XgnKsbSZFRlFPxspXR+rUxJ/Qw/dJSz51Mzd6Q\nKwMR+b3F7fm+nyuZgD+f+Wb8CiekxJ6JN9aUFyUoM1fq0PEgBVAvDEQXQ9Wn\n85XsHaiMqzUx75Gs2fQ4e+FjMhEs+MFqAJwaGsVBkOyVuIrE8iCXqUapddW2\nCP4KlENH3G3WeT/jzE+PQXCnR1t0JR1LX177Sv/LuU3gcSGBCFLeP7gtPtM0\n/o8G\r\n=8X7d\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGx0N8jKsApwWVahkTzoAttULcz61gGg11C61aDY9vYXAiEAoR0V7QkdyeChGJRHqgkVuEXxyhCSm6wxt8sGLYlYRKk="}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extract-zip_2.0.0_1585353700031_0.26811210827991583"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "extract-zip", "version": "2.0.1", "description": "unzip a zip file into a directory using 100% javascript", "main": "index.js", "types": "index.d.ts", "bin": {"extract-zip": "cli.js"}, "scripts": {"ava": "ava", "coverage": "nyc ava", "lint": "yarn lint:js && yarn lint:ts && yarn tsd", "lint:js": "eslint .", "lint:ts": "eslint --config .eslintrc.typescript.js --ext .ts .", "test": "yarn lint && ava", "tsd": "tsd"}, "author": {"name": "max ogden"}, "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "keywords": ["unzip", "zip", "extract"], "engines": {"node": ">= 10.17.0"}, "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0", "@types/yauzl": "^2.9.1"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^3.2.0", "@typescript-eslint/parser": "^3.2.0", "ava": "^3.5.1", "eslint": "^7.2.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-ava": "^10.2.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "fs-extra": "^9.0.0", "husky": "^4.2.3", "lint-staged": "^10.0.9", "nyc": "^15.0.0", "tsd": "^0.11.0", "typescript": "^3.8.3"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:ava/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:node/recommended", "plugin:promise/recommended", "standard"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": "yarn lint:js --fix", "*.ts": "yarn lint:ts --fix"}, "gitHead": "d64157132e8d9841054f72bb5d76b87a8d9f246d", "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "_id": "extract-zip@2.0.1", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==", "shasum": "663dca56fe46df890d5f131ef4a06d22bb8ba13a", "tarball": "https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz", "fileCount": 6, "unpackedSize": 10749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4IdNCRA9TVsSAnZWagAAgxAQAJJy5ouSkFuMWlCDsSeY\naoNA0q2l1Fc1AjGtB9bb0W/IJai4skOAkSPIBeUu1Rlk0ygbAXS8umtrU1Ag\nA1d0DF+hHwWDd3b3AWA5AlrWEqdZOwSejWcHf6EvXLj4hp3FQd6RT/Y9VgAU\nfqzVjCSgMtY4FpxXuYmkk0HfSmb1kqpUZH5wDlIypOFPnvSotL7RfHMU7eYc\noq325npL/3FgTv2S+zse/v/kSmZOK/HNP5XQP6lkHDgitFPrhXuKzwUD/MkQ\nV+utzko7J+w9B9Qb8r/eP4Lw++NjaLqMSKAYfq6O4OrEQqSC84f+cjO+C7qE\neaPgelpDa1vZsKJhssN+sh8WRRqI/nFyoyPFiI+Stao7tJ6hwpI33zp7Jee9\nTLdY9waLm2ttU2KHFyxDBZa9Qq/HzKKmCSjMKQvNo3h7s1YCeRb09Cs3D++i\nSe0797DqY/3asVc55HayZ3xO1iKi4qBepWmDosjkMIsMoj0h42B0Xu4cMfxc\nCSkERs1cHEZ6EHUWWkzxNhJAxrZHY2TY75I7yC+WY77ULu++nBZB1e5G5LzP\njYivHHaeVrBmUVzkc3JUjR1NCCvO4yN9ioRz6/xFoA39OgnsxaC+Db0fpRGI\n1NhGcZMue4/gRa4oIMcw9YwP5W5uAt9V/NQrIizRPnJOhXlR5JwBl/zShtbo\nukYN\r\n=BDoG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJOFiT/rLdl26pDYG+pWaGHDveH6TohQY6aFjJhqbBDQIhAJp0zXA270pd1Xwo2wevxHGaqdar4hSMlO1zoA9uyvQP"}]}, "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmUser": {"name": "malept", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/extract-zip_2.0.1_1591773004722_0.18272913550259506"}, "_hasShrinkwrap": false}}, "readme": "# extract-zip\n\nUnzip written in pure JavaScript. Extracts a zip into a directory. Available as a library or a command line program.\n\nUses the [`yauzl`](http://npmjs.org/yauzl) ZIP parser.\n\n[![NPM](https://nodei.co/npm/extract-zip.png?global=true)](https://npm.im/extract-zip)\n[![Uses JS Standard Style](https://cdn.jsdelivr.net/gh/standard/standard/badge.svg)](https://github.com/standard/standard)\n[![Build Status](https://github.com/maxogden/extract-zip/workflows/CI/badge.svg)](https://github.com/maxogden/extract-zip/actions?query=workflow%3ACI)\n\n## Installation\n\nMake sure you have Node 10 or greater installed.\n\nGet the library:\n\n```\nnpm install extract-zip --save\n```\n\nInstall the command line program:\n\n```\nnpm install extract-zip -g\n```\n\n## JS API\n\n```javascript\nconst extract = require('extract-zip')\n\nasync function main () {\n  try {\n    await extract(source, { dir: target })\n    console.log('Extraction complete')\n  } catch (err) {\n    // handle any errors\n  }\n}\n```\n\n### Options\n\n- `dir` (required) - the path to the directory where the extracted files are written\n- `defaultDirMode` - integer - Directory Mode (permissions), defaults to `0o755`\n- `defaultFileMode` - integer - File Mode (permissions), defaults to `0o644`\n- `onEntry` - function - if present, will be called with `(entry, zipfile)`, entry is every entry from the zip file forwarded from the `entry` event from yauzl. `zipfile` is the `yauzl` instance\n\nDefault modes are only used if no permissions are set in the zip file.\n\n## CLI Usage\n\n```\nextract-zip foo.zip <targetDirectory>\n```\n\nIf not specified, `targetDirectory` will default to `process.cwd()`.\n", "maintainers": [{"name": "malept", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "time": {"modified": "2023-03-04T05:05:11.590Z", "created": "2014-10-17T07:21:00.197Z", "1.0.0": "2014-10-17T07:21:00.197Z", "1.0.1": "2014-10-17T08:48:01.746Z", "1.0.2": "2014-10-17T14:33:50.422Z", "1.0.3": "2014-10-18T15:16:33.995Z", "1.1.0": "2015-09-16T04:47:04.282Z", "1.1.1": "2015-09-16T15:22:35.417Z", "1.1.2": "2015-11-06T19:51:36.166Z", "1.2.0": "2015-11-09T19:54:23.013Z", "1.3.0": "2015-12-08T20:24:52.049Z", "1.4.0": "2015-12-31T21:36:38.033Z", "1.4.1": "2016-01-04T18:47:10.993Z", "1.5.0": "2016-03-07T19:23:32.823Z", "1.6.0": "2016-12-21T05:06:17.439Z", "1.6.1": "2017-04-29T21:11:43.381Z", "1.6.2": "2017-04-29T21:16:04.764Z", "1.6.3": "2017-04-29T21:18:12.710Z", "1.6.4": "2017-04-29T21:19:51.619Z", "1.6.5": "2017-05-01T05:10:43.049Z", "1.6.6": "2017-10-31T04:37:51.142Z", "1.6.7": "2018-05-24T02:06:18.263Z", "1.6.8": "2020-03-24T23:47:32.738Z", "1.7.0": "2020-03-25T00:59:43.674Z", "2.0.0": "2020-03-28T00:01:40.160Z", "2.0.1": "2020-06-10T07:10:04.849Z"}, "homepage": "https://github.com/maxogden/extract-zip#readme", "keywords": ["unzip", "zip", "extract"], "repository": {"type": "git", "url": "git+https://github.com/maxogden/extract-zip.git"}, "author": {"name": "max ogden"}, "bugs": {"url": "https://github.com/maxogden/extract-zip/issues"}, "license": "BSD-2-<PERSON><PERSON>", "readmeFilename": "readme.md", "users": {"karlbateman": true, "chriszs": true, "jyounce": true, "webs": true, "largepuma": true, "ukrbublik": true, "leizongmin": true, "nate-river": true, "robin.xi": true, "skymap": true, "ngpvnk": true, "andrew.medvedev": true, "restmount": true, "johnshao": true, "flumpus-dev": true}}