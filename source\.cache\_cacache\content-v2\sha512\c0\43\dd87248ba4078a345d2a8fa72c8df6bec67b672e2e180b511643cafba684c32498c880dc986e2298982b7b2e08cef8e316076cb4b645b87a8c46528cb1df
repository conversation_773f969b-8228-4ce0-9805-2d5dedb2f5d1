{"_id": "@types/puppeteer", "_rev": "1020-3551f1ab39f929162d23d0819bd16fcc", "name": "@types/puppeteer", "description": "Stub TypeScript definitions entry for puppeteer, which provides its own types definitions", "dist-tags": {"latest": "7.0.4", "ts2.3": "1.3.4", "ts2.4": "1.3.4", "ts2.5": "1.3.4", "ts2.6": "1.3.4", "ts2.7": "1.3.4", "ts2.8": "1.20.2", "ts2.9": "1.20.2", "ts3.0": "3.0.1", "ts3.1": "3.0.2", "ts3.2": "5.4.0", "ts3.3": "5.4.2", "ts3.4": "5.4.3", "ts3.5": "5.4.3", "ts3.6": "5.4.4", "ts3.7": "5.4.4", "ts3.8": "5.4.5", "ts3.9": "5.4.6", "ts4.0": "5.4.6", "ts4.1": "5.4.7", "ts4.2": "5.4.7", "ts4.3": "5.4.7", "ts4.4": "5.4.7", "ts4.5": "5.4.7", "ts4.6": "5.4.7", "ts4.7": "5.4.7", "ts4.8": "5.4.7", "ts4.9": "5.4.7"}, "versions": {"0.10.0": {"name": "@types/puppeteer", "version": "0.10.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "5b4c0e968d8790b9dd54b39eedcf7b42dc314d0e648945ee943e99b717ba247f", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.10.0", "dist": {"integrity": "sha512-iTLc2wMC7bLNvhmh1rHMpUw7DQa0sHoS6SGQxofTflhVgj0ktWBf5UmuMA0jqWXhomMiBIeE+T9eWeRxj+6mOA==", "shasum": "7be2919a58c3a6b44f206ce5ab53d34e69d7af67", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.10.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDPHm3TnqP/LVkeiHefbyUIcA5Afl4MW+a9Z9/nNz1tjAiEA3TksawW1fOwMBCBIyZ7vQsFDvYUby6QUg8ndmPgvkis="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.10.0.tgz_1505945309560_0.044969101436436176"}, "directories": {}}, "0.10.1": {"name": "@types/puppeteer", "version": "0.10.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "0ef6148a500d08c09b057d2fa5f3ef4504fc378cb0dcfd9659cc59f0b596596a", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.10.1", "dist": {"integrity": "sha512-sl4rhjHeXlMMNSXce4lUz5C+KhNqLi5dXm9PPK3y5RaC4pg8sHFjmTO0cGztTia9LZZt+3rrRltujY+2yx5xFg==", "shasum": "8adc19a19a857e8ecc375c1c9f9673b6b54505c8", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.10.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDgFz28YJY5d+2QPHulEXlWtVkVZ3ePeSns7uctym7BEwIgbueHoCqyjTOlP+3PZWMo4JoBpDlY0Ci6h9z8vDHdZ+E="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.10.1.tgz_1506103092486_0.5140326472464949"}, "directories": {}}, "0.10.2": {"name": "@types/puppeteer", "version": "0.10.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "a23ce40ffdde1fa31f96b9da3cd902d95908456a7cc88c3ba205e292a506cbc7", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.10.2", "dist": {"integrity": "sha512-poiHQGu3FYk5YP7NJ7IgG5eWc3wclrwzDqmS3fM7frF/9niVHehn/GYXmoREjLwQdAiRuijbeLGy3/U1IzBEmQ==", "shasum": "b285c17e7ce5a006f7506e4dc75aabb20d06704d", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.10.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAzxoY1b45QO5Rkv2OfVLc6WAspiwFl18vQSzRUR24GGAiB2r/9h8YxTvVIxF7uGctWU0OHBpWCdIRhlpBsIwFVyMA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.10.2.tgz_1507141290048_0.7752966349944472"}, "directories": {}}, "0.10.3": {"name": "@types/puppeteer", "version": "0.10.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "9b239e2a2c3b6135904a66a25391fc4e56e976d914255b5197844889119084d1", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.10.3", "dist": {"integrity": "sha512-uFOhZi+0ePycSZ52gBZWdMqX4QNqoKs0/G2lSpIAgRrCXqxsnk8PSHiva12S4+hQf54f8iafSMW/dS1MziSnCg==", "shasum": "79b6f037d6f0aef2b7a378fe3a495ea7bc03b681", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.10.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFM+da+tS+5Sj87G/e4WiMcX9FE2kUuzIPzq1OlgJH/pAiEA/J7bvvqSDlOz5rOlV+qvD8lq4RaShlG6Bpq8e2Msdo0="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.10.3.tgz_1508184723291_0.3711262308061123"}, "directories": {}}, "0.12.0": {"name": "@types/puppeteer", "version": "0.12.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "458a24fcdf15bcd21fcffe3c24d85bf503058f4f27afcc4ac8d234e65a42070d", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.12.0", "dist": {"integrity": "sha512-apIrQ6wy2Z3uHQIFfx0sbrTOB+kJSaoRhviUhY9fjQv8ON3gRV2ZHjWs5GQ5KNYT7y36sODs8uxG03eNtjX4rw==", "shasum": "ba48be19d08abf7774192304a5cae5ce8e519481", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.12.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmjAZwrIidt3imphsq5Ju8nr9s7PRwo8IsOkmAsouQqAiEA3k98U2Ir14ChqjXbVX5XTFR3wQy3YYxI0NNhaNDVv6k="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.12.0.tgz_1508256651775_0.4189001864288002"}, "directories": {}}, "0.12.1": {"name": "@types/puppeteer", "version": "0.12.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4c251a5d3884c2b62340bdd209334347752708a35e49510b8436ca67bd422993", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.12.1", "dist": {"integrity": "sha512-+P5Wc/eKiTXG/sYuZdNjubE3PFFNZsrHu4UiUQ7JWSyeYWH3d5bfWIu4yL8On+LfIFQDTEoOtnDKGgzf744rEg==", "shasum": "a2b3f0e7b624faad2d5948bda07fccc859633415", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.12.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWCNxjL9xtQ7FWTXOXFrOKxeXlrdlU97HoSH2uWqh81wIgSAinervBBIXpWEMZELOnr+Ur2zyvCpj9kL4W8QopS3k="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.12.1.tgz_1508948533651_0.5192696137819439"}, "directories": {}}, "0.12.2": {"name": "@types/puppeteer", "version": "0.12.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "a4fa21779907cf3bb3ff61c323af4f3aa50b42989fcba791d2ec2a025b8e02c7", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.12.2", "dist": {"integrity": "sha512-B4dTfCzrCc91RZlv7SnXuwKAvWqC2E98bIOgV82JbnmqV3m0IFSLvntWjby5jjbTl35WQONV2MBFSYCrkTvzhg==", "shasum": "61263ffde860bcbe3848721329054eca8daf5e58", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.12.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqbgq+Ae9UbIwe6YXYzoMIwr7ePedREReYXk1ejmjAWAIhAPSnKZobvz8+T5L5ewrYTTtJqoC5SdSMlqsKik4Pu42L"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.12.2.tgz_1509769047089_0.8047796301543713"}, "directories": {}}, "0.12.3": {"name": "@types/puppeteer", "version": "0.12.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "856bcd1fd0dc56373a1ca624853b8455ef435509a1c7ae53a82a89c84041bf0a", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.12.3", "dist": {"integrity": "sha512-aoay3bX/Fy7lmI85bRxGJk0nLrUU9vuhNKi3qNefqY9Xv9tBRNE0GiC5re/9VnPSvWaT3p59/SdeHPDjVzJ0JQ==", "shasum": "78393d8a725ae9f922f32c7c685afd173023bd61", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.12.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH2Kiz7PSKGoe34aa3SpwJvyYow8TI/3GeXhL3ddpPczAiEA5DRNwIIlsQggR19mFlXRLrJUZgjuVXz0b/v8+uwXrLE="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.12.3.tgz_1510349608037_0.5417936777230352"}, "directories": {}}, "0.12.4": {"name": "@types/puppeteer", "version": "0.12.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "18567dacf97ceb045aa71bf44f021d15fc99aa3cb5f62705ae7bcd6aa4b9bfeb", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.12.4", "dist": {"integrity": "sha512-kXnk9ukVlMHFSAfNFIjMfGg9gMvL1VsmSL20YNP79O/FBM0R9xedSsV5r+oYifKpjg2mrH0FQiuLn91JYG9Vmw==", "shasum": "e4572e0983bed3adaeafdab099231c723a30983b", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.12.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPchUVxhi2sYmA2abACSAMlrzk8O+75Bxx9UMkOOxK4wIgBX8yA49xUxLkAps4mTczWAFMDEmS0Ea0nKnB01xdOKA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.12.4.tgz_1510586068644_0.3822280738968402"}, "directories": {}}, "0.13.0": {"name": "@types/puppeteer", "version": "0.13.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "bb1d34def34050cfa6244d9bcafb9b97e14968f9098d7617b1386b54b134e04d", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.0", "dist": {"integrity": "sha512-woG8vwX+meDol0UR8SXeXj5EOoj85C6b5hr/7ixhff2RIwNEG6DB8e2lxcDA8qlov+MvU6JE+W/4c0tL8eBEng==", "shasum": "a12e791f15ab92f6160e1e8a295fd35dbf9cb90d", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCN5nvNQQQJvkv55xRcmc0DXcA/NZzKUwuwC0pPycJ4xgIgGheLfih62Z9ZlPKTasGdnwMiRqziZeNkKCHCVKfm4D4="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.0.tgz_1510677145423_0.27835458307527006"}, "directories": {}}, "0.13.1": {"name": "@types/puppeteer", "version": "0.13.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "29ebed8530ba05b5cba9352075df6fd232485728ca0297612307e75329c02474", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.1", "dist": {"integrity": "sha512-7/ghO4Awsr7yAUpqPfWjZ1mKgYhdysVR+8JC4ta3O/C5BCFlQmogSHnfxprmUT2BF/bpkGCuOtvR+zvxCpZbAQ==", "shasum": "02e4756476dc00f0ac6e3073eb345d1b474a69ee", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRmBWAAm6dAm1W2uJJNvmUw5bHAhNij6Hm7Joak5EUCQIgdIc6Q5dFYV+mGpifTYjnP1OB2bPV5zU1vORp6UbI8do="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.1.tgz_1511288544811_0.18954220577143133"}, "directories": {}}, "0.13.2": {"name": "@types/puppeteer", "version": "0.13.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "e5cf371978439e6bdff19f6c1322298da7340a88a383ada90c9dc7a7c9347b51", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.2", "dist": {"integrity": "sha512-qwe6jzZuzErw5BxSw03yQU4iCKO7a/zRtwN4tF+0WobB5B7blgHvPkLxXyYHsIhKL9A2rTr6O1j3u+d52g+FFw==", "shasum": "32611f6e65d3cd48a388a05d0d610e5b04e4446f", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeVG9cFwO2vKMhLUPsPYWAdctC4J2JLTqnhhsuKP7UuAiBEowzK6nPEutCpbxQ1mbGJ8UXa8MJKcbv3yWfLRkfpsw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.2.tgz_1511289790472_0.8486374004278332"}, "directories": {}}, "0.13.3": {"name": "@types/puppeteer", "version": "0.13.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "80f713e5863e4357065a18ab0b33037cf0d698eef97976f80d6a98eeb39c2f2d", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.3", "dist": {"integrity": "sha512-QPzng6tTcWPnjG51EATK74TRz5dtst8BqXdZt9QgbZAaY+2bq0t6m5Mj3hJ8hJV9UFlXOq3B6PYBwWIh7qZX5Q==", "shasum": "293b901ecbfbd3b08c5ce4d98636977f298f0edf", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCM0Weiz8XRUs75QL8Q+92w3c7yHC7xAh9tu8IFI2MyLwIgWXkn4Lns+xCjGOQLaHTMdcqhrpq3kBgqy/y1GSMcwXA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.3.tgz_1511371078365_0.8197329677641392"}, "directories": {}}, "0.13.4": {"name": "@types/puppeteer", "version": "0.13.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "9bcdf9e387b7b05809758a734caa5143459a07456a2426113e1b734ee0eaad9b", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.4", "dist": {"integrity": "sha512-OqFohUh1XekGAMeGL7gQY8qqDAsJvexnP3PEXWIPKxyChBq5pw2XaJB7WXQv3/xEwTNTbFD7tK3r/QythJYJgQ==", "shasum": "69e6edc59b8651c3f80dad0f5a2536d52e9bba97", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxp/e/bFt+tHVYa/lDy2qab8HbiOga/nDJwl31T1QO9wIgRDjqSMqLqPeWbWjQRFURxQk9zoQezv0OtCu5I5L7A0c="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.4.tgz_1511829099109_0.7969214820768684"}, "directories": {}}, "0.13.5": {"name": "@types/puppeteer", "version": "0.13.5", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "94e777dd0c51013e8633b064dabba3f4351f11e329bc55464cf5d4318cee0e34", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.5", "dist": {"integrity": "sha512-KWV2yOuCY/H8s0LLEc4Q+SSxduvZa2DKeLY2IEZuJONpVHXPHbnjS3JH2YZtoFKX3eGrLzloXCAu18gXJx+CXw==", "shasum": "5a0c559ec9776530b2b87a4bcc259f8223f53a67", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDmkswTj9CKDZfSK65KLvSKsihw0VBY7GPWFeyzIeNOLQIhAOdEIzMwIsV9PR8aeKXQDPtRlH8Awg0F9ym+XLgqfXuY"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.5.tgz_1511888715665_0.7322735756170005"}, "directories": {}}, "0.13.6": {"name": "@types/puppeteer", "version": "0.13.6", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "33f569fbbc35159b100f24e575f9267af2695b0fe7a50db5304be5a622918b01", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.6", "dist": {"integrity": "sha512-Te9mlPmC2NZUG+WRZOkVVViVSnT233oDO1N/d66pVHD17y9Hy15rCJ1omXW1+Am70d1NV01cXkhJHgVW4h9/Iw==", "shasum": "989afc5dd1513f35ffc890d4c18c552dc2616fec", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk8nIyq7doZs9WQRBTNgc/p4jyxWqcIZ7lQSrWx1M+DQIgTzoSH5mKIHpvQ0dNxsRUOS5vNWjzjoOpe6SdDRAkwzc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.6.tgz_1511917010778_0.16478901682421565"}, "directories": {}}, "0.13.7": {"name": "@types/puppeteer", "version": "0.13.7", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "7134c4c570b83dd236e7444d6f9aaa8b4912a24f5ea9b2a46d54314cefa7d9c2", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.7", "dist": {"integrity": "sha512-oPXRyQCH+Ogx8sZztshwPAVlcGjlQDDabaXoSHv4lZet+6thhOSqQwWZuxptGcdPNFFWzEble2EWeX3CFp5qUw==", "shasum": "6a6aff56913bbbdcfe5f1201e43a66de341ef152", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.7.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAi4rSc5sWlvRGuAti84LWTU3l63A1yh3jdfAc61KjO2AiEA4TyX3BwKKSBeTTTT5gCuwOGPpYH7ntXad/V5AkxmJ0k="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.7.tgz_1512591049802_0.35970478714443743"}, "directories": {}}, "0.13.8": {"name": "@types/puppeteer", "version": "0.13.8", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "496c53239b36eef024c52efccafd00f91b9a5ce2a488f58298951f354d0a27ec", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.8", "dist": {"integrity": "sha512-w0H9FSFDhAVlOqT80oMwNL2xOcPtszRHU23oMMvYiznWgl0RSSJ0d1vqmzMmkNX3dg+Wdj9h90ONDN0tQghX+w==", "shasum": "b52ee91a547bea2647b68b7fc2a2ab6e49597743", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.8.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDkyPBjSRRlo1jlOes1hgWuLe8c6MCXyM/yd1ppzK7DQAiAlD80fjWqG4cjLkuof7avQONhLfQmw1DtB3KELXFR+qQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.8.tgz_1513019820200_0.2229745949152857"}, "directories": {}}, "0.13.9": {"name": "@types/puppeteer", "version": "0.13.9", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "d3dd271bf93b0123f55324d6be35ebb5ea459a8731e534b3d1fb25837c7a542d", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.9", "dist": {"integrity": "sha512-mxDCjjpT0/2Oiy9J8iExULZRRyoWqxb8jIyeJxGWmdqJ1lBGkQ3lgo2iTbRx1fSybk5nJ0ptGrbxBcS+mr9G/Q==", "shasum": "2448ebddb91e5336bb2f771d76401c70b43a7fe6", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.9.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwZPhAMMih9kNsbrLk25ILvJwjFKsguuk57vTnbWzVHgIgPJFShjqSjCGrbjWF0zAzXNg1YC4ruKFIeoyXgBolZhU="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.9.tgz_1513781495745_0.7548268481623381"}, "directories": {}}, "0.13.10": {"name": "@types/puppeteer", "version": "0.13.10", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "abc5d2a9e8edae887973044250d7a9ba1586083bb76fce398703aaa6d2af0a54", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@0.13.10", "dist": {"integrity": "sha512-1Qm7Co5YTmopc3GVGSFySvsZOqQCMTSu9u3neyEase8lFPUT5CsuM/FU9x3CaQwaWHO6sy9Kk/OWHt4ctyL33Q==", "shasum": "bde8846bc9758bcdcbffa3c98af8f6bff4622d0d", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.10.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOTxXPUbrV87lUN9TBizT8bm61PLuaCznWrZ4WoSz5AQIhAJaqHUxk0EBOwPCtQ7N8iWq4GnWLkVu4WDcZY1mo0pBO"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-0.13.10.tgz_1516738004960_0.19413356436416507"}, "directories": {}}, "1.0.0": {"name": "@types/puppeteer", "version": "1.0.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "7d31a2cf2292ae85c49047af98b41837e87d347a06a4b7d8c6a555980782820e", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.0.0", "dist": {"integrity": "sha512-uciD8VcIvPSdPw09P2zSnYuuOv2llK3lJOdtD7RMZOQ+a9FvcyZkx2nzBLYG1qoUlxltHi0RixliZ5MODDBATQ==", "shasum": "3e8f6c6612ed3ed7503fce4c7b6ef4a191d5e931", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1Rdtu1aZhH/yC+e/C+raEzYyUmIGl/Y3MDXD7Qa/m4QIgPnM9Ui+KKjkydr9TtZQMPa8iS7+zYupukTlvbj0hXtQ="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer-1.0.0.tgz_1516738096288_0.45672242995351553"}, "directories": {}}, "1.0.1": {"name": "@types/puppeteer", "version": "1.0.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "4d31c23ebbcace54d820e89fa3862d6ab33375d34d704b81c0eccdcd4ddbef62", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.0.1", "dist": {"integrity": "sha512-Q/e1N1LoT8Ko5Nh4wEBdFbHTLordYLWiZFG1rxu7E6h9mh2W6p2hcDuZ4O3vD1svwBced/8iwiig8krufX6WHA==", "shasum": "3591f19b7716cd50856515068f2519ae182c3dd7", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.0.1.tgz", "fileCount": 4, "unpackedSize": 45463, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCo4xEwf+VEHWE2J8CMFooWlbzFXdqs1LuSqvB6UPtGawIhAPK8ipkJSu37Ir1of+DO9hz7MfxxHh1j//laOZS7eh+G"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.0.1_1519860209947_0.8087442118525383"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "@types/puppeteer", "version": "1.1.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "4a3936e10eb08ab230999f10f845b58129fe0e6959171f043563e790d2e5f9d3", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.1.0", "dist": {"integrity": "sha512-9u12fHr9dPlaVHxti9YLDWxtO6Omn43fl+9vFxOgXS5LiMLtY++duAibD6GcKwPhFiuUsOaDokc91epH/E9dZw==", "shasum": "83cd9e053dd1b8a6bcc694abeee7f8543735c813", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.1.0.tgz", "fileCount": 4, "unpackedSize": 47003, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA/SJF8IHJIOeO1kCFlEwtl0OyBaPp2i0+/u/T5N1CmlAiEA9//DaiVN8bFvDK0rr5uKkjVQNNi+EUgYJ5TN0NsaeEg="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.1.0_1520296726015_0.7689671004765963"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "@types/puppeteer", "version": "1.2.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "2081ed34f46fd58605b48096f4feb76dc13812f8d26b23b4adfef4d7fce0d4a9", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.2.0", "dist": {"integrity": "sha512-X7izHwv/f/J7EN7bxawwemnE03FfSkm6bv18UGimsfIS4ylKhiM/AVp0CjFZhRC0L0HgCQC1v4i/HBGIgbi3Yg==", "shasum": "99f1c05115e98254dacab856851878aba643cbde", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.2.0.tgz", "fileCount": 4, "unpackedSize": 46396, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDcBUzZ6DibsLrq0n4rmoRs49M55feBfvwCqzmij2+1vAiAQVNO55Mp4JMJ7U73/mpggvzyiQLlTVyXWIlXvjb47DA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.2.0_1521238282380_0.3184426742127662"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "@types/puppeteer", "version": "1.2.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "20ee5e7ba45f75cd852183879530648c7a41c66f7938aa57593ca6b3689689c8", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.2.1", "dist": {"integrity": "sha512-Cd4AXl7sqUPn3zInZhMrHQsS7JbPHdM2b+1c26SfM9SIBFirANhZqTJVrIddwI40tug5/MyGLHOOdHYY3OdH0w==", "shasum": "00d663f0d6a4f9b472094c18414a00a4ea925655", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.2.1.tgz", "fileCount": 4, "unpackedSize": 47931, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaatn+o55nYtrmJPxs10EiM8+r9DLDDDmGEIXTF6/PtAiAoptQXbb36MnvNWRV1ZiSsRagghqOUhQRxJi98dZn+fw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.2.1_1522779337961_0.6531793864633184"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "@types/puppeteer", "version": "1.2.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "a7b59838b9524fe23b7fbb8112a62f97dbacf87895658d86e13c5af28cb7020c", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.2.2", "dist": {"integrity": "sha512-kq+QoX8XUkZ1dCybas3wSt/m/WdIbaXcHLixj8mb9gcSoyQE80TY04SyXC/hBOrZii4c0h108B6fF6712FgobQ==", "shasum": "d6df9df96ebf804951ce9dd1a0fb153dad34099a", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.2.2.tgz", "fileCount": 4, "unpackedSize": 48056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa379pCRA9TVsSAnZWagAAtcsP/iGPP+uApRQESoBblc7f\n65K9jpuPT4EuHqFQ8pgUDzp7il66aGkTFi/IdzUwLC5P1GGMyhMoWL0dAtyI\nDBpxT7o0ARVtw+k5CvxChEli6M0/+atGpsKjrcmXSu/Hjrao4dp++hz0eTR6\nQYfLQGPFC87HJSrLGmDSbC1zoS/FI8gpSxyDxlzIrnK6ffXehy28+OPmU2hW\n6VxhQ8SX28FmVd5rvb75Itz7K/wuoR9HSbQGtsTIcqe94Fg7lYn4BfJgn9nK\nNbiQEJJx3x6lP/zEETlB1G381ed/+FoHoCEe0mf3attkftdLKLW0Xyw7/oqV\ntHbp4gCTskcFZVKgG7tYcYGJ41OgxYL4gCfxNWMwF7qBxQlpXZzfAlIeBlxU\nFeFfy0T3VxWbZ2I3FkgMGogavba60m9CM8kcJr3LYVfPpwyvkg6GpENhusoU\n8fWajhTQeFsFvjrwwf9x3Neolk+V3FVawPUnthzBlm6XQ+YSwMoXoptco4v8\njSDkQ3s3MxA30YlFPRxFTP4OJpqh1R3ewZcfUQXAzabwbG7G5JzLRG/VTwrj\nJ6pyw0e8FZ6W59nebd43gCaVfuTqUSBcpoQ1cTp/dkIosENK2k5SSlcHH93L\nL8pNRq7ZpW/D1zYma3EnKRaelXex+H0Y6U6DtW+nS4S3AESQAVt2CF+zVgP6\ne0ay\r\n=/fPK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDqtX737M9ZR5stCU/aJi5WAwTMtSQmdWyLRu8TsG1CjAiEAifSeFgf9Veg5ELah+Ay0lNtBJoqHPfVdpbR/03p77hE="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.2.2_1524612968766_0.9535055468412001"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "@types/puppeteer", "version": "1.2.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "28c1aa437fed6ef6ae1d667e864272220e39997e5455ca6f93ade2115bf14470", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.2.3", "dist": {"integrity": "sha512-YM9jqT9aN1p8n3VyBP0dPFYD/O/kCsZGBAaZSiNQMgsvgPKFDo6C9REsyyBERYbeqs9kFr1oFjFatlcBm6uS/Q==", "shasum": "d4d31f5c789932db6f918095f4a139805022d0ed", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.2.3.tgz", "fileCount": 4, "unpackedSize": 48368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4lfTCRA9TVsSAnZWagAAkjAP+gOVqrm2AlfV+gFRwe5v\nq82BbeM76BEfgLrOaL4OyRfusqLsNP/fjKVhcQEtDuKkq2tdeLJRhVBX33dx\nKtBfb84PwFIAxg4Yz0C3EfO+hLj3iI9YCu1gY2ONIVhQ/lgcPVIeVkJIVGiu\nJ9GkNduMvfRe5qc5E6zdltTeI7deikJgHPE2g5zsrbPPWhzd3k6wyAWXlNF2\nvBUx+upOvqDuOogMK55mbnl7F5InjBOpCHLOx6uDNc++Rf0shY39Xq+8ZQdD\n+LLP0mR31yRwBvEKu9DCNPvXxh1zv4bgojtzFnR28QsvRys3j6YQu3krKjHY\n3cZVe6ZtIhyAWimPAfOvrrkSyB5CuTajPs3W4nOsae69t3tWmwCAf9jGK4Zv\ncKl/cERg7E3mDn2Noygwnx3mf37K7WS5MHTE5eHueFo0UdZJuzuyabJaXqn9\nuunPx95lzTR9DayrNOR2Ui9/dY7DXE5+O4efmXyx1g97Nq/FU3v9xweUnDuV\nA0mCHtHA426f9Pyoix73e9VFan/YObzpVX/QUMQOFAedmgkHhFgEwQq+0elm\nEC0fyXNDsb+JMtuHXddWY+N0loA4+oT/KT0mGZIBThptUmQA9MXWipZoBrkA\n8I68ZavN4dbVTYGQEFrBRhyWHSgqgPnRdxfXsS2usPjud6Ta/oGhN6ixq4FX\nCzcN\r\n=57R0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGH3dJQi8BOH1L5+KxD+S8QbhIhfCdeym15Tji1zGZQtAiAQpQp+TwP0btb/v8kI1a+ybZ85bDbN90yzETQSCsXIHg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.2.3_1524783059162_0.818301643274449"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "@types/puppeteer", "version": "1.3.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "14eadb9768f221200433f8e7c8d31601f47cd9acfa6de5533cf2c3896b91c690", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.3.0", "dist": {"integrity": "sha512-kp1R8cTYymvYezTYWSECtSEDbxnCQaNe3i+fdsZh3dVz7umB8q6LATv0VdJp1DT0evS8YqCrFI5+DaDYJYo6Vg==", "shasum": "dbee1fa65e24b6ad628e4a35867ad389faf81a5b", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.3.0.tgz", "fileCount": 4, "unpackedSize": 49655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6JVhCRA9TVsSAnZWagAAQtYP/i0OmcxOnrUUF05TrrZ2\nqFkFdkt/j+HrszuLXs2Y9fkqJQ2umlOxNcYnzjaMkcoPeK+DjphAeGxmghVx\nlzQaETFZbjgYyef2p4Cyujvo/adnmCOxz2AmzKQYdSF0I72uD4+QKRWXBAIc\naRk0jPn4MVNDgrtmuj/ovqThkwCxZZhbLCCSU3LaZ+b9OIfYoQyZ75HfDCvz\nI7dSA7oPCu+a8YhlJbr7Z0CQYLZHSyUsvwbWYm8GZQwAWT4t7eczY9DxX7yd\nCKwGQiUDtm9f1VZMYhdF0HgARhwXGm6S6CizZuxBDiBVQozrA1509tT7OKVa\nSGjeI3Wt3IyMTNJ5aNHtO4JhFoSxt1tw4nOwYfYO2njM7goHh+F3AdAoHxUM\nMbAbMVOwQ5NUfQ087Sg/ArlOEIbyYBCvWa7am/u5peON9B7Ckm1It9kvTf2W\nCmSLENeojw1+WMdgAICjpjlmi1+1kMhckIegvw8XOkvGMmn3O+VLoWI808V1\nAqzf/YjzaJwg/28TxhFZ6+jSpoh7SO3yF988v7e16ic9vueoTsYdz7xGqNDH\nROtaHSBlYcmnYBWWkHAWpZXYdifst+cVxKqkrRXDuuzxcERGN28VOoKS5tvt\nRUM3qT/vGMiF2Y/sVpvVnLDRylNofK51eMFLCtDcvcUJc3K7dOunw2Rf/Nuw\nn6LY\r\n=197x\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEMygKbJAlF94EvTq7VeIiCp21R/seO9V31W2S7sI/sqAiBdktb7+lHOOw+7dVKjzwRpotlU+aEitY7RQ51rwlmpOA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.3.0_1525192032699_0.9268076783469772"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "@types/puppeteer", "version": "1.3.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "03063648e442bdda80390800a33eb4bc8cd2f4c61d45a569bf2939ce01db8307", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.3.1", "dist": {"integrity": "sha512-+mJKsb2XM/F4JuLCress3oPDH5UPcj7TMted0HBByhNGhIe0M5yzkGLsedZmuvRcyaBPU0ifPpFR8/GTxmILeg==", "shasum": "3b147a36b13d5d5c818b437b7da4681123f859ed", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.3.1.tgz", "fileCount": 4, "unpackedSize": 48736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7PPvCRA9TVsSAnZWagAASmYP/01S+z8YhqCRWZQiTZE/\nidKh3nYt/fISKs9msi9dNqz4gf5S/1rHVxO4z5HXlikF3hL2QoAqN2yRU3ZA\n68LHcG5q6PbQ4QnvV53DR6AawaE4ddIjCMZuo0y1ZfcNwwLgwOFk7l9znEWI\n8J9SWmpzMNgTXpvXGu64zOXUIvF0GBpnFBe0GWSDdbKXobrY4aAZYTFdGrDh\n27WwsIM51yiFR8srd1JwI4nD0SxSAkPT0yk3bNKnWMrrr0aw2PNrWYCNi0te\nXX96LnWc15UISDbcvz7hUxGdtWg5O3bET31hY+Wn/kSbGn4XZ3xoRv/jyy66\nHo8QMG94I7fOYxXrjNXOP4NQo0ENkkvHzz7LKVZXA9mrukGZSu0YFnLiFJ11\nOeRcmvuwsWjH6QfqyN8zEHpyT7CjlwXEErEbjS5i2Tuy83Xis/UTiFGaXjMr\nW/NiCURDH4IKQwdVf4umON8hf5OAeN9+5qDYvvCf10RjEkZ4d0yFY981DLDI\n6cb/rAuTwhnI2/9cdHDrZBnkf85TNVLZzypD8A7nr5W24st+G17wCe8TgxNj\npSOJpA9I1msF9QdHd+3xD/sj0a0C78PgjcxrRD17gBF1dJQCJZBVyKToTaA3\nImYbaop0n7xp5n/uJa78P3qGAkXo9I+qbBLuCzYs71tLtPJtCKJ/5LNTGjuI\nnRc/\r\n=WMH5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3Fr3Y9rka4Rcni0y8uRTOrCBW/biJBAOpwk9EJBKqpgIgLDlV9kCv75G2PF50ZOiK2RgzMJWo2/A3M7clRH/y/2s="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.3.1_1525478382688_0.9236306782480495"}, "_hasShrinkwrap": false}, "1.3.2": {"name": "@types/puppeteer", "version": "1.3.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "53e19d1d683bcbe7125a0256b993b6f6c7ddb547cc69269413f1e2a1f5bf76fc", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.3.2", "dist": {"integrity": "sha512-65sKlK1LsRbpRpt8DqfXLPghWVurSi7UGsRVAivlILxkCmP2/c4PP1U2bNIOAEStb8soE4dYmjzt67BxZLgxEQ==", "shasum": "1f163da956bf4f8004be695754931d9fe11f5b27", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.3.2.tgz", "fileCount": 4, "unpackedSize": 48873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8iHICRA9TVsSAnZWagAAc84QAJ45ZMhSSWt19iw2E4vY\n5slYPoeGMVW1NvuetcWt8JlaGbk8LiD5tPcdSy3gCw8Np7uJpL+y/HgucvCj\nDzLmxLbnMJ5RVovczWRuH07uffAub2MGzbdbGbXzsrvvJbGOPZ81ayvhBJyG\nBTZ65qBV/70JmeajZLiMvktSvNr1sKQLbjAjpYmhLQt94jvnB0rosmiwNot2\nOt+ju+AOzY9jxzVdwlh92TZnNR/XSj2P5cUYBsnzzLDd9NEGLlAXlWK4/4Ab\nMsCjwMw9ssLR6YTRWY19RElms0uzsOlVmG9aL7uaqQO2Tlpa9GuhS2kCfDuz\nARD3qn6JUjPWks+nh115eiFwE11nXTmt98gA56+xUv2NUJ9JQSVyeAlhnsl7\nZNsQ4BwqAHVCqp3DKFRvZ/mksvJm2jl00ZnN7Dr21S5U3QDPyvZTav+9kpGI\njmiXufRgF9wQG0dw/0h8G79+VWrs12UwXsI9ZZOchm/7AlB1ojW000hYtzvg\nRYazexYlTPV0IjVkY4RTQEk1rSWwyfjL+lpzcVgvHBnWBDjrWbWe+05yBw9M\nliFDGB3peIhXmUfgGkr05U/jA8Zn949xF+d0JEAkXC/dZu6jbqUoG36ISZUk\nGGBvsKBE4J8EB00+worX9XKNyRKJAdQsx3iI+tST/y77rs0WpzvxmzHMxBdU\ncQnS\r\n=0C4X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpARLo4Y3D5XGgE0EkkDVsB3m0bAOhOhO3oDVQrBdiuwIgKB3rOGbH1cQIH0jr6Yb5h0ErSGj2uKYOPNNSFlwHP8M="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.3.2_1525817799902_0.7127285127278411"}, "_hasShrinkwrap": false}, "1.3.3": {"name": "@types/puppeteer", "version": "1.3.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "afa177804566a2b89eda003188df6333dee302797abebb140e2f6dd9b298be08", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.3.3", "dist": {"integrity": "sha512-H9vf9ptFIGPdM4GemwiorJfyv/RTdALaBOz5WMByBnBkjhiE2Vu6x3J6LJ3N5EyRX2BSsePJb2JJ6SoBE1idBg==", "shasum": "d3b462263f496bff93c126cac14962f067bfbf22", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.3.3.tgz", "fileCount": 4, "unpackedSize": 48644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGZvrCRA9TVsSAnZWagAAxvcP/ihqWCIO+PNbEFccVBG8\ndkgmuA3vNdUCAvygPmzCTzfB/CeFLg5JYQLsixGWwAkhonuDn4RyGD+7RnRS\ntJ9FjUYbwdiZx242EdIMebPOYqe3oROcDJ7f0hreBcjdTlMe6/7sD4SG6rtX\niVBoUyOk8f9cD3HQ/D/+bHEa0YXccCUHe7glDENYBcmCOV2MIajYcx90klrV\nPIAsmGc+Wo1sA8V1ANRrckIOU9C7GidxA8U08LnivobuHQekatCouj3hPGrX\nvrhagdFqCTP7KRoq6yhRxowQjQOd8P9fHuKz7RAyu+Owgcd0zPsurCOa5wSw\nfe8wtC3O/0X5fgJKfE8l8QfSfdBXROBWXYfUSRC68RZkNOz8AMssNmlItF6h\nqKWmHa6UE9lFQEFFn4WYudAcJveYdF2UcUoZr1ZYXrSI9krE0KjiDMI4dz93\nDA8cPbuEmWq+ZAELPZ6rneGlc3cWbWrwwoEevAWoKX1P2kzlGP9bgbUtSXaN\njyMZbneeEMcmSv1Nq2CeEIceyLE7ATNw9fktb4w9jRQR3kHtTUkjCFKyjnsT\nI1DGSoHkY58YhyWnmsOYRWxhSGaTq65m1YcJA88St+hXDaGIWXSfgEDXzcPk\nvEa1YF/rRYNC69WNl/hSo+9Jt6BapR1f3CZbr5UsviPML2nuatLHHkx+/6Oq\ng5Gx\r\n=DqPk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZBgxETc1QVh10y3vSYWuw9SP8Zh7tMlnvjxBtWO1r+AiB0wwYBqTQFrXJnOTwxZNZsC//RwqfrgMuXjqXQe2dbPw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.3.3_1528404970658_0.984383947716267"}, "_hasShrinkwrap": false}, "1.3.4": {"name": "@types/puppeteer", "version": "1.3.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "22dd33b8eebac4e299bc9ee516b90aa36962a59beeb60d62c1dbd3dffc442d58", "typeScriptVersion": "2.3", "_id": "@types/puppeteer@1.3.4", "dist": {"integrity": "sha512-WI43+i2SZnYAm39ASmeDxhSR+czDIBuEqTNftg3V+bgTlqsbdmj8n/p9/95wWd4ipsnb/qD6nrvBekEvWh8UKQ==", "shasum": "6fff43968792924ac2b68744641566e6acefdc20", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.3.4.tgz", "fileCount": 4, "unpackedSize": 48753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIXBNCRA9TVsSAnZWagAAYmUP/RPUR23dpgKGiT0Dbt+A\nFVEESVsD4bbP31wTN/VbCx19zeHTBl8hKIumZAXmHdrmrwnQMN2I12Lt5BPd\n/9jqetteo4gUmWvOlfE0O0ojPt9W2hm1Z7G9evbawjLlc2ttY+irde8BeaOh\nkOJoW1QvVUm51Ad+OF7EsYnNSIit7NrBbwd+4RQIYuwyVUKkJcuq+Ub+I/dz\nbhdo0iitEFHCQjn6G0iU1F0oF+E+SDcKE11Nhp62/ctCcseDecTsD5lZC5VB\n3WBWcReF//spQoe9Khz5jrUSTLtyaMnik8CvYeuK0CC+CV2bmbronqoXzHsU\n1ZBVvbC82BhHPkyJJ+33PQ9gW3fg0nf3UPtJHP11OPNPicVkhkGfbT10z9NA\nBSo0q5GDoaWTld57/g7SCrHqtT31Agqt7FRxgCQhypCiOhFNXKajY3beSzAY\nq6hDSxbMUwRM1S51prAft/e8MTbR1KqSDq0Y3cuES0m7Zob5DKIjnA9zQRnT\nL1FaJLhZxZFE6+Wa+v5qGtt6t9pNwumNZdoEPyHae/tPCVUbVZ+lQayUYTps\nKoKkANd3tuJLlp2SnmEWNpxvv4yIi02Vwxu1kvItcDVfT1PdD2FsN3S5+Ct7\nwm6EXhde4U7ejioRIDBYTRl9WKlYypXn5Egrdb4kYH3+fXY8dqyvx3N33Fch\nVpZ1\r\n=VOtD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3YeeUx8VX+MjmiIVxQfq98pKa2vMiZp14PWwTjdDOswIgDMa0r6HjPpn7FXWwLK2lpGeWQrk2WV8Z8LV7KcpiZP0="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.3.4_1528918092723_0.21459462385590755"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "@types/puppeteer", "version": "1.5.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "c8f81ba32befcb0ef238b9fe01bf3831c506284a06deced67030c50ab8d52d19", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.5.0", "dist": {"integrity": "sha512-KNQzIbHKn/yOlCH3BXOBA+3OUTZRmLkeCMQOHSid1ytrCKNks8ut6Oj0eeUeiOdOoRcW264qNh5kjH6LIoxeCA==", "shasum": "95b6feff9522d3a054ed09b49798e7232f24d558", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.5.0.tgz", "fileCount": 4, "unpackedSize": 60767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMZNvCRA9TVsSAnZWagAAlQoP+wf33Zmkh8+BpGjnttfb\nvwS7J1sK77UT74zyF5sKsCtRNHamtWfimPYrn65AQmuXtQpllDdsP1xbOjxD\n9yZWOzO7iZ6BsVewWrl43rDaMzFaZ81rCALB0qmHwmZEzuccFTZp1MPyIuFH\n0okeii3XHYSTg53I4J/qx8Iy/Nf24soO5ApaB9RsCPEfqMCMU+Mdfut2+GtF\nKicGAfIZT5CFy95cGKj+D+D3sqjOsAlGlFdWefVgFdXvWCzqiE2UYhjkT2yd\nonLcz/amfwr6hSSCKpcF2WRYl2ef0jLKVTtnfgn3O1UwmpyZTbJrWRwEjHIO\nsoc+Y8tHw9eSAuQJHHKkNrZBZBm6LNMQilf2Wbtka5vlyHAM8Ky0QTKuzFvc\nwXjjHxS7Fq7cDpdUNin8rXK64Bj51IugF83Kxs+gsmC5MAU28qKwDiG5p5HN\nAxWFbNz5/ybtpAoe8j2mGDXJbvC619JzVnjZwQx6s4so+l+x3GT/IXBU4rDs\ngxz4QIM1RQNFgBPZA22GGXRTEj2wCvwiLYavwwWQtgcmYZYJv3ZN/XRSJPPg\nqyZ5SO/nMqVEW5zekAO+lHLXOnNL7EWoIWkP/77fLrFBWHUmKCWSUawqBBhG\ntDcRwWgifo4+8ZIVKgvv1KwbOmyX0xbJJcambTFOGuD0Zu3uls/Pw/h/KZjf\n9D4Y\r\n=Kapa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5+BCr8Dsz+VVExb9hCNjgb1ctgoSmuVqfi1hREKqSEQIgYGm1gv10odWYYroOvuQ5aU/EYz8xOvUO7w3sYyj37I0="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.5.0_1529975662955_0.3380839926714361"}, "_hasShrinkwrap": false}, "1.5.1": {"name": "@types/puppeteer", "version": "1.5.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "7aa422f0179a92a0d231c6d34806b046d6a97fd18adb57f6625a20f78aa5acb2", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.5.1", "dist": {"integrity": "sha512-t++RBCHekDaTDrz6TcMp8obfm9Kd6CVJuIcilUMMJU2haxv2zX/forXRKmBtZtspJTmuhT9rMysNQgj/Drilag==", "shasum": "8e924528322e184d1cd171f33fd2c94329bf059e", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.5.1.tgz", "fileCount": 4, "unpackedSize": 61040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUpXuCRA9TVsSAnZWagAAq3oP/2lneUC0ehIkcb+Jn4P3\nRwapnt4P0b2rCtI6hAl9MdIufNPC0oaap6Fm780yRTD8pJq50s8ztCQeJngl\nqf3EFA+WNVg33dzO2XTd2Ng7yyd1z6RWdN43oeyXHznTBIpMHRoIY0ltCUuz\nZ9AowoYAQ16pSkfJwfT2efEmpFqspMbF/YXWG0p3V+9ujTODrsFtTKghJ1zW\nLkf7LOtb1uOjH7xpG5XFYgVezZ78PLaDzpnaF0ulYeHP5Aw0zO9+pRexpXpl\nsbjQqyePyZwwW2ynNsUcwLTS7qxOLAGsmP70ABjT7LdlpvjvfJR/bSMKNENA\nZQBwt3avan+Il/iDTNkPR4Pl3huTfFF1tKNTn5VQPPZY0n0KxDXMV1Nkbw82\nq09fpy5i8nV+ukjGoifyb74a+gCCgk+p+yXLpC+NisIcc4a0Dqnw7uChUTro\nl2XIP3uUxAAWOwdNH4nJEm3HiMQL8mrnMvnAqvGGbyP6q0Jpq2+knRPE15dl\nkFbzf8IxuX1b74rs/leKRXEPUK6D0srpKTUPXgxX6hvezcVfd54LbFitCirx\noS6ALTi+uELgNvmIym1GD1SvTRU3+a8EqCq+fYW9ZWEKu1tn7CGEl2X09zA1\nDQpLy8XrXBfjzL+9K1bow2R7mw71DT1SYa4qiBaRHsPVYCjA+3EbR8UPG87o\nE47s\r\n=fITO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAffwwvoJd9qlR3pAhTOFXejKuDGSQMwtq/mtFRXFmArAiBhmYWk3/IM2BbNll4IvPU2MbwwsR1KLSL5NjSQNaY/EQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.5.1_1532138990645_0.8054490248836688"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "@types/puppeteer", "version": "1.6.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "99dfbdbfeded4c9f91b03fe67b0c8f7613322ed69f645c49f3ebbca03c62db53", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.6.0", "dist": {"integrity": "sha512-I7M8S3bmgH4WozaPWMbe6MxTQ7P2jyWLVhsV5ZY/K3FkkG9Knei99783dlCtQS+rlkbhDBgBEjFhl7UoMr5/tw==", "shasum": "3dc6613cfbbb5e91443f5137fb4831973857ab7f", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.6.0.tgz", "fileCount": 4, "unpackedSize": 61351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX51mCRA9TVsSAnZWagAAJioP/RWOcUJ4FDmnxQq7X+3p\nizpo7z7HDscMsLjy4yU9/4uq1PQn9kTb0G7X4XxS7cHIs70AlPlv+baHwEIJ\nA2WocrBrwdIceBkEJUJtRPF0djlYMxlBCvdBq4iEXQrN84m7uLbP2ybMcJUt\nSEvgfO2cbpFKZ1pf3uCMyG0ONUaiQRhIVD40uaoAvHDBmM+UqyWQ1GIl+pLf\nvT7hL3d4K211+qTTXrl3E5XMxxxmcS5HplKceUpR2eOHyDKrDzaqc2LjvNaz\nHA1uCRXSXwebzClHFUwlyn3SQ7AuL8MoANqaU1egAidZfmHwQCdiQWly6hYH\nBv7K8tQnOzT1vh0qzODnAivI6I3FIK7msVkpccrnao7uiB1VGNBraVrmCqTO\nASug6oYc51o+xEsgm5atF+A3HZrS4RDoPhkAtDj1qWjFncGkf/XaLcHhSwsf\nesDHnzvziRk8bOhaY4xZz1sDMPxKHTnYlYvBeNhPlB6rf0QtEUXJ/dMV7M1f\nI2gRCn0reH0A2cNr73gcyAPCbVP/P6EW/yXAUtHNEHT6gcmCFZlawGF4XNQH\nMkUaJAkE8+vf8HxJjlWKMd4zaBVJFnznVVY1YBn1+F/6dX8eGOCX30Cepyge\nSLe5nSWDBdBD4w47LD52uVYVccpaAmEtArJltbNb0WCfJaEyvKsDXqTOTIdz\ny4vO\r\n=Lvpo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDa5oVKpMMQb86bfVUH1Smpj4f7ZlMOrLUkBZBBH8RcJgIgLt6gim7BVxTy52hVuj8mEXB0W/nPgBP0+qhIe+j5+Ig="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.6.0_1532992870480_0.0883253047670236"}, "_hasShrinkwrap": false}, "1.6.1": {"name": "@types/puppeteer", "version": "1.6.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "7eb0f8612c59cf8b9735764f0ccefda765c90d9318b4aae84db5e046f06194cb", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.6.1", "dist": {"integrity": "sha512-7SAb1vNU5e0VP8BwcYdjfVKGjbwzOwSu9NvtjBHlZtjnKXA0CmN6YpK7MT/jyQo3aegI50XpHmMaG5EYD3ZqTA==", "shasum": "329ed0329874fbd04e6d8af3d53a2430ad87906e", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.6.1.tgz", "fileCount": 4, "unpackedSize": 63094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiaAlCRA9TVsSAnZWagAAp6MP/3IfF3hA6mPMnqvUiqbR\n/jSURpPB1dS6Oh9syIU3HyWRD5FfAWAEbMstn7gna4A/o5mBYpIjfINv8MO2\ny0TrQWBEJjnszX99hNwnk0jYM8RvOVPXu2UYbTTUmBj5s7/oypkggIaOIyup\nUODwlzSAbfojBxeCGQ5hDgHjCeP0KwYQcMABONbe6KZhzNL6oK50MbeH7eNN\nCPEJcFMtizwsF95GpQ/u5qivG7wEGDp6e8k2T+wxzToxHzLoegsbDi1ihaiA\n3io/PULZQqnsGZQNnfmHM5fhczD7CzBXg+bm9v0+0aVZDmbn7CXfpqkrE06y\n2d0UeieIIiF+xA9CQ8ikcXXWTYjKkzEV83HzO9rQaM6QMzBKW6Rinai9ezrD\nPV5H3R3f/TAi3Emsis979RrlQD/T2ClXEct+glb9PQU+1mNOyFl4QK46noy7\nHUA2NXIfYV56oY/9y74Wj2QELE5g2baO/aUNhsiRDloPizjN+vRw4n4NFLRY\nnPm0MNmacVBBsg9JphCh/L8sXTUXxw+MJ1447W9fAFpCtWMdBaUmhgWlLc1g\nCN2zeLwTSYHy9Y/tP0RzgkWMr7OSgScEboyZjwmyxd3fmCEdpIXwVLe1aSzr\nxThlikirBgxPmzBfMyYtXqAzXNQQ0pYx5gF8Eud8cHouRKOi/SN/y36w5DT/\ntZn1\r\n=10qa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6YrInFBzW/Dngzh0hln3EPoWmp3WXeFj4dI8/jy8gxAiEAjwfxsY7pZVHG/rM7Rx2Im7fDYCKiCEkpYJTkjtOZXg0="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.6.1_1535746083434_0.027370869183720004"}, "_hasShrinkwrap": false}, "1.6.2": {"name": "@types/puppeteer", "version": "1.6.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "1145d1645663fc826abb90ef1eab38e0f5d4f91626d9087c79b201b4dc89043a", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.6.2", "dist": {"integrity": "sha512-7dDV8LiOFwnvDnWhBA7KFmd/BWwP8c8ac3X7/OXst9Q5JXg0qGpmNkEKh24XOxUbtWtVUvma9ZCb6ZmVjvioJA==", "shasum": "9b4ba40a67abad3c729a021ffd259d929d00c5e0", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.6.2.tgz", "fileCount": 4, "unpackedSize": 63879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjsleCRA9TVsSAnZWagAAfS8QAIH6SP+4Y5TIVCAGQXvE\nwtVZ3iGTRc9S+c/rft8slireKN3G9OmFyAg81R5ZoUy70IbTQRA+Q+Tyryt5\nh61CfoBxogpYplPltDxnP8YT55RSh2ks7bJVVqJ7LS82cIsPM/ys+Xf1EfV/\n86H+f/g/xLVH6Xx8fUEO1WwmtkQtxjtmmlDlEmp2Hhs2HEfR8FB+8OWdHGcI\nTo03orU+Kx6jbjHFX1ueQGKe1z87pIPVtt4dH3HxUEPdhjL8fG5xdzZWpu8E\n0MzjOVcYkVXBWiWzgq23R+QtUREqQ1556g+pBpemEvRy5ZKIkfP/eCnYSayr\n9IfdDn9zZE8xssX0WxTQetePfviA3+Kxm6FOtem7l+O5IUrPq7Kubl/PQDRi\nG3891hQhik9/I6y2e9DHOvarIpwIDmN5xYu264HzKDb2rvFC7YD02fX3Xlrc\nF3Ff/FUbViHYyE8ChltJ5qt7DTXvEd10ckJef6HLEPaFoLlFZuQ2vBhrum40\n6h2XrAjAhmafllY01Ul7NFz1KKknGYYJHVlnv4MtbJoWCXu0JUncMoqKCXHb\nLu7yT8QcDfJfuOyoFyRCtWR8SMYR4usyQlnTQgmQtwIk8UM3GEfLtj4zpqel\nQUXSvyICvMV5NhAEt+vPRQio3VsfsOUEhhqIUjeZwnZBEG/iYlK1p29rwd9Y\n2mTO\r\n=T93n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqb23chlPP4AEpRxziQcM0sn2NIITusMifP22CC5rfaAIhAJFgIGBL8gjmkkbMxOCmuKbeApWifNHQ1dk/BGbycCax"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.6.2_1536084318143_0.8048494359127296"}, "_hasShrinkwrap": false}, "1.6.3": {"name": "@types/puppeteer", "version": "1.6.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "265f1fd35ddfe931c6f96c648c10b8a767f2b4807a136c364edf45d8fe13df39", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.6.3", "dist": {"integrity": "sha512-LckvRjv9yE47H3zg1w8zaA0sxtW7EAxVGz2RsPMIZkTtVfl8M8/8o1GvD6xXEH1H5JrbAUjL37EhRMFi4Oe+Zg==", "shasum": "13b4a70ae9997b7f6b6c9d6771d2aa34c710b809", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.6.3.tgz", "fileCount": 4, "unpackedSize": 64043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbj/nUCRA9TVsSAnZWagAA59QP/jgRepPA120hVum4gQqb\nBySUHvFnB06BBNxifDmpQnxGfROtWksZJLGQ7wOhiYCBhxrZoAK66SzZ868F\n5qjXJ6gk9+OnNHGU4Aw4t/k0U8Sypj6/gIlZPplcRom/5PcyX0fMAfdccR4J\nPaTX3k8IzSBIiyo71f2Cy1KeyNaQNp8yv+oD4QZ9LkBlRDs5d0DOcB0eZOKv\nCl6jSCVm/QlIKnll1SiYk9yPVJYka38CwDAEuqw9qhmiIh4SIBUqtJjYsrMK\nH4N/kJXDPICCreamTa5xKup2XCj7XJPfNIRLwfOIGAI2HcLgRevAmZycmZXD\nTZSJ1ZkOYBdt/zd62QKp9BUfWmKMJmCDQNzb2IghdTyVgpc/e+I7lQm440c4\nL6Ar+wz7Z1eF1pBSfnJkz9yyozBw3qp+VkRDYU3tJv0Hwf7/iG9EEmmpsKc3\n2tfzFNguhDz0p3nUX4ebIdOZe4Gxq0yt6hNdJe9fq39+PRC6lvy0kdhM+99L\nDuFTgZHY50lmlK3XO+P5OBJo48pV/FTuu86u0ihk3UFCHiSIp08uBTLqNl0I\n/SF2/4OnsmF0jM8izcvD8+wucbTLIyISVxs493lUqhCwvn/6mMEhCLEoYQsf\nrB+/lifgBSx0J8JGoIZWejvn5yLoXm6f1yFhT4dv8nDiP+zRk8BgxO8SoINz\n1/jz\r\n=XdYo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEnxrpp+pw+RdQX0QDsa3lGY1TlOgm1QgVeC73C0JThgAiEAkobnIt5mYt0P4h2aTsdsfTXUrHqycmZjPZsmyVY1HvA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.6.3_1536162260194_0.6909210407574207"}, "_hasShrinkwrap": false}, "1.6.4": {"name": "@types/puppeteer", "version": "1.6.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/events": "*", "@types/node": "*"}, "typesPublisherContentHash": "addd22a9ad0a299a580e52e4114a243ab36eb3c69cff06180e5234bafaa4b4a7", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.6.4", "dist": {"integrity": "sha512-oYHF5x9kJk3oA2zH5KmaRWlyitdVyqBixd5Dl/rsONNWwovLkSl6NzJHWGX586uR4QujT/Vtr/jsEcDUNrYO7g==", "shasum": "c3b27b72241c782053f21e3996edad561e31db9a", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.6.4.tgz", "fileCount": 4, "unpackedSize": 64521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm/QeCRA9TVsSAnZWagAAl0MP/07Q3xeRcLPzfEsY4RbD\nEAuEIaKBnNJPDCGhQcoVWHBAQkttHb8CYg4X8JFbNy/sCICGdd1o7cma0j78\n7I9pMvyTn6PD6Qm8Aedurixcv4OyCyefs0XJUD++wlSXplSA+Epllv9pTjUb\nbAytLxi4LGVuFG22/GO4/ffuNcuDdOtRGnfzzlv07VtCIFOTMz4pLmZf/l1m\nVksXYhSD6CtkC7MifqWi87BzOaSGDj1klrXPGGX1a/L4gNTk+UbSzMFoobT7\nkkhcf8QAmRFj2YcHOxCYm5nHiC1QlaQVLokHLUFCGrugYItq31b2o38FNl5S\nMyTJLDsmz1oc01chNCprhlOaDqrSfg8YE9L8suI862qQjNYzH9u3lND32/Vt\nOoYu81RIzDri7tFsbRYXeWVPbuHWdnhFPbRaHyWfq406f5UxA0V3nQRbQeAN\ndqL4FGPCXFj6QmEOPFf3sqEplXNJu04UXv4UNE12GLeitkDFWOUgD+QmZBBv\nampA0NQ2VS60vcY0H/BvgYPlcR/1dx4R4+ZpGdxBlIyGWBmBTehGjJmna7W8\nB225xDNUH9T4w2P/1PsAtGiDEf8AaMR+j2v1CAgt0kJO5UQA1ISu1lVpiQ73\nwaCq5555HiEVLaQmV+KE/tLqFgRGZ+0zYYwsuBc8fAJu25vIDVZX/LYL19MK\nRcZl\r\n=stfM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID61K0Jr3e/gcceFpJfcoyEX5F3wiI1D2vJDuy2LufIkAiEAyisoQH8UAd8EZo87xGzfEOAPSSAXDDVkbF3zYSobVkA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.6.4_1536947230175_0.6625328650320392"}, "_hasShrinkwrap": false}, "1.8.0": {"name": "@types/puppeteer", "version": "1.8.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "505a6098dbdf863e2e95c57b214022dfb9c995262e6e866eaa5f503f86f00b75", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.8.0", "dist": {"integrity": "sha512-YR6aEjBY87BNj3k8wrtieU39ljyVAceKqO08fI9EMBDkYu5Sp6vioTKiAIsrNqDyB0u249lqLmmhwwq6qLNqww==", "shasum": "b414871bfeaf7a20976b955f3a7546b81091d0b4", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.8.0.tgz", "fileCount": 5, "unpackedSize": 66936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtQRyCRA9TVsSAnZWagAAV3UQAJxt8+3rBCHnR4Hm/49g\n4r2pqh2N0hDStmXm4qqJUEJyV5ncZGUnOJUWQE/X4tsg4+hgLCr+mWEshzkl\n3h3r/XQtAD4VSr3IYy75MM2wPcLrxaH/EzliLpcqryPm/dfdliDh1z2ysNHh\n9gqb6DSAgrIthYlyuhazrHlPB8G9AUWq6XfKM7i5z4mcYGPDrUEeNWYN31dU\nqXO/CoW+eVdgPx4igniw4TYtfrUsKINCPB8JzddpEWmFWqj2/rosgsf0he/p\nYhAbWCUOixvVNY+iCxKVZ1uvRVl0YAiRH4utWmcGzAXPpAjszJlbUBNzhfpR\n0EGgzxb1e1PFYnC50MORLWu4KtDUQLpGBA1cjz9EgWiIT2ZYwb8542BTUNtE\nnJnk01bKLzi8IJX4fy5nO9Qo0JIN9DYg1Fz492U7lVUr9KENNRI4ovk9xQ7r\np7DM6zQqkrfau7ERTuei3Jhh9srUOFRFGQ8Up8RTwe8G1NH0+H3atUEu4KId\nI6nS0Jw215xCi0YlVGnQ+tY0Q9oxJsYmRNNWQ+eXhx9lpbhPR1Nbxgzcosi9\n/iM2bEwgx4/nYHBOkJDqmuzoVQ5gu9Cv5xcbhQt468IbPXIs2hf88OodtIbU\nk0FA0q0dXJrgoYEfAvVvYuknc8ps2wAPrQlpPQGzAZxac3CV08isPUGA2GT5\nWz9D\r\n=IR8N\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD77QsIc5ayBOZbqmMoLGILTFDP8yfgJGAo5e4SB6huWQIgP1drO3l+fIyR0FymbaO1t4fN9rSJB+XGi3c/21XJUjA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.8.0_1538589809510_0.4919185928972085"}, "_hasShrinkwrap": false}, "1.9.0": {"name": "@types/puppeteer", "version": "1.9.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "de0140b3420bc524ed6160fe778364b571d13b4901c353d10472c46da3d70aa5", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.9.0", "dist": {"integrity": "sha512-ZP3wL4Lqpgu4xZbz/0vQhbIZnllNEawVW3oTpEXyJg+uhf/YQgL8oeTrfoUwj9JXE9UUe53eqmgLl29/d2gjqQ==", "shasum": "9a4bcca1902259e2cea6e6be7b4a540aad13785e", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.9.0.tgz", "fileCount": 5, "unpackedSize": 67403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbt6ohCRA9TVsSAnZWagAAUmwP/A2O9z1e/HmjxN8UzAkA\nR6Dq3AxCF7+OSmqxswJQNu6duo9sTECN66yKNiIJ/jWlSrGTSkGNhej4YRVD\nBnGZqbOilJPSaaOMIGdFXSkSyzekxvQWYUsdlXfaKyptOm+ivy8GNOD4r5xw\nmE2IRexX4wiBhFKAVjyw6joepCXsRzIoPW+NGpANjBFDiaNPwbAbXkUZSNqZ\n4DURaorP33eeLdfkAJdmGbmXPt0MZUCOugDXR9D5UP8HoOjf2DXWN0OjZW+G\n7DAgO9btZpWWvKbqDJZ/xTfdf7+cwlui+uGlJJVAWkAiXT482gfAxCS8ngu7\nG3WUtk6LFoaYiETt1k6BFnz/dS+jmCbR1DWegktEyGydH39Qkytq6EDEZYxr\n8k7L1xSTMWgzSDIKfrYMPrRBbkJvggKIBzGRxBKFRVLExJu7nDLMc7XgZx6j\nnKl8KpQloOwzERyqoAjmOZVvAV2Ae7N3PfISwkD07Q4C8ccI4X4SaUpS5onQ\nyU2zrpAFLFZE9X6g7tzsdaZxd1iY/ocdwjTTuYxcBexV7a2ffe6iHTYOra+C\n5h03pI9b3+kqBwoP6ke4LRJB0br73mx8upDmKs3yAr4l913JieCADTOhWrGf\nQyKHUshPz0c9PCinov4hMtrCBFg8zir7hp8PwH8/WlM4f/oBwwA79YlyXpzx\nA8zM\r\n=UqfL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAo13ZBuYQvMFqNiZcNonl/4z+4XGPS+WGlZPIg3tnbNAiAN3p822nOuntRbqg+2LHJMKDpVi1Q/yRt/c6isl58GDg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.9.0_1538763296916_0.7898813785889709"}, "_hasShrinkwrap": false}, "1.9.1": {"name": "@types/puppeteer", "version": "1.9.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "1fe1a0517ae3984d82da3cb20c33d8bcfc03860d0770b436446af2cfff900f8f", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.9.1", "dist": {"integrity": "sha512-oEDHOxH398JxzMdSaylEVSsbI1F9cHUOuD+3Vk51F9/KQOPlO4cJAlDCHSNqG/bgAaaw0+T7iKvh0vHTeexf1A==", "shasum": "a2499bb7eff5da8e9197f8407205e7b28bece29a", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.9.1.tgz", "fileCount": 5, "unpackedSize": 67420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1N1HCRA9TVsSAnZWagAAlhYP/2aXF27d+Wi/axYxPiKQ\nf9Hojhh4ib5aPUu5kTHKw6bXXf8zPetTwce8eJL2vQkVyGNb2VXgihvojCv9\nK8KeXfIDHx7QpJHVBIC6nbQvErWqr0u3c6wi+n5sjC0/mQePH8jGrPQwuLLU\nxjZkKHDLL4qRzBY2Ylc0NGgI7sZRWRhOTw4GS/0doRWwnTh18pS7ylYPHOOg\nSCBoXd5PCiTUpmZ1MQEGPZXPjZVG7oyKxXgehwvombDwRMzot860KLb2EAlW\nfELaOGu+r2nwhLuJUGwb6NXkkvOabOSkm3u/tc56hoxWcNtNnieTlVBp9CyN\nVgWHSQ4N7YX6aw2fHTboItWhYXSqnQKj8IsOIweIvO1a9WaQoCLtaIAOvGH4\ntiPGO4JKjMpw3ksjo2uiLxTJVEF93qF+1YwnPYR+Uu4aZaef4RirNZzaEq7+\nQxFF6o2t6oxb9Vl5jGnLqrNORpimu/okO78TkQ4SkFYcPE1tIce7PugiWkmJ\nrVgHWDpzekgpQY+HDe7x/DFBTYYKLCdbklv0htobkaCU0M165bC5aNno/nC3\nczWJef+xy7REe3qaYuNEHMrBe8/hwdo/YfcIzjMwdQam1BBkH7XsvbYId0AF\n8mG+M8HpXvAwu48iyaDlsWWsXMCy44OBFKclk3twiGEY22pK6CyKprKwbX3a\niY/x\r\n=GdC6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/j94jcDwqF+I9aPziYV1UNUzXZrxpyOq4F5oKQjTpsAIgVzs2L3sL+sf9jnS2WSW91dxkS+NBr70Gpy1JajK8fAo="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.9.1_1540676934986_0.03727049085616563"}, "_hasShrinkwrap": false}, "1.10.0": {"name": "@types/puppeteer", "version": "1.10.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "748d2b9559d34ea7bb0070f5faa7a35c8eb2669f1f308276c94bb3650eec62c2", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.10.0", "dist": {"integrity": "sha512-qrDx+mdV3jj5GYVW9rh8upVt+Hu6xUKerOJE5ko7bSm3aagqKybhwSsQvLqaZJzNs0vbyvtX0wTgs/H/ZvORCA==", "shasum": "15d5389d6d5ded7bfc0b06287e85655994f8badb", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.10.0.tgz", "fileCount": 5, "unpackedSize": 70495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb43QGCRA9TVsSAnZWagAAXdMP/0FYFSO7zdT7GSKf6HhW\nVj2CSDaVJFnST4aP7nLW8Cjw4rSL2lXVaEMSCpk14wkglra7IzPKEVEZ3czx\ntZdqEI6qCl9WV2mr47IArTVPLIfLDJlaSoLbHi1lgLMX5JZCCy3nO0lVe2fn\n0Jb696o8KL54SGrVMFizkJ+mc+QEWgtHAP9+XnzF1A9EH7UnVxHjTmACP3lZ\n+HRY/ScskzYhW5waoss5mg5WokV+QeCLo8KYQrXCj3WTY6wV2teEqQYfzg0E\nbfsucBEzyewP9Zbe1T+tU+7mbdH+J1oufdXFFu9+9ZxS5MKlgadtcI3VEvRu\nSLfybzKOR8bVEF/Jv8ksEJVY+uhi+FZxaFIgdpNlzjB39MVGF23GTUuzRzBY\n7D4oliyoa7jgsanjhOXAgvcZdzLGF45KKx3jL6tgCLQB9iVV/kPCFEqfKrZ9\nYMyzwiZDzlq44h9j5hJIH0lfjg4TPA5UfNlemKmgrlXqGfA6oS96x3MnmegP\nlvpIbndXforz1AuR7l8jHzTYWpgzeA1rGUz+ClgseoZGDJcN9yHlh/LQ6G+F\no6/huY1GKAx0TZzQ3LrhvZO7xSx0VxAiBpDTNlrCWfZCNSsi/i3AMySyXOxZ\ncOOvyxZweNs7EToPZmXnYoA9pEaiIW2yoIamexPELMgaFOzDJmLRabE/YhNb\nXMuy\r\n=jQ/L\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE8OnslrCKBWQLPZEmLa0kMXFuB+MlIDdps20PRkMvZmAiAJ7YUfvYJnz6+oX6e5Fj3yTPHuLaOADoeFWWb2bE3UOg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.10.0_1541633030045_0.7277199684149391"}, "_hasShrinkwrap": false}, "1.10.1": {"name": "@types/puppeteer", "version": "1.10.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "66dd4180a702daf8e7a0c91a0d240b6ec943c7b4b323cf0adf1fb30cdead1836", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.10.1", "dist": {"integrity": "sha512-f4nEFqkiPapSRWTxVB24A6a47bMCb2RJd5nAkiTPFwlsONl9hmUQ+baKYtDiDXQ2skZiWS+83bKu2OD75EdsWQ==", "shasum": "03b3fa9f4591ccdda70970bf13767681429de99a", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.10.1.tgz", "fileCount": 5, "unpackedSize": 70498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/tzCCRA9TVsSAnZWagAAPisP/jlNU5ouyKJ8GWIHbWH3\nmaOOPW1jv3RHc/BliJiT3QfSXIQ8xtejSLq7MgF2d8pxnjDPMR6llNJd40px\nCPYVoFiVbrtyj8gMu+RGkgANik7gmGxjOtZCiMSjEn7dMEko3zH9GYMRyHlv\nbupr4ruBKYtPR6rhDiSOsqYo66Psn2i8WdWy+259MxerjXE5IgkcvP/Vp3hM\nzCkIGk3B3rV+Stv/KEDs6tNxOeEit0INCgT3I8rjnVlU7+i0A0VtTQsl8pe9\ndQgqjGTm3lHvYBD6rEw6JuD3tIWDY3ZyLwGsuge9c4B/bJJZwiILlCa87FMx\nwKaKlqeYGYWAFRRoi+xj3Ix3dXyA3vKYBq/09ZNxY0diQNIMyH2ZqcFraSF3\nENKqIQAJVUJ1pFJwW+16jH2TR5XEXzSYoYhjDjp+Yv2SC5YRPeSeEcmcogms\nDw3TcO41EdfIPkATiPnxIWp+wxkCS0D1EN6xkgyQrVWRH70+hkLUWxtGx7Qp\nTthRTKCgqOn7ST2beT3c1TzBwVcSsPCUECC+qK6KaFc+UvP22Ss8LlGqQBjM\nFxS/N2gMyLs8SrrZUA4tHbyaAN1MSPen4br3DdpTl1YK4Exp14gpq/K/YaKv\ndZ+Ivzh4kxrZNz7oRX26HWDp29VKS5aSewA5FM4JWNXK6tljIWOwnG63om0q\nxrCa\r\n=Gczn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBrXalHQs0ePwmZgGEP0todfluGntsRi4cgrGCMkO5IyAiAPhdfE0DpJAvVomhyPpJr7lb2gDm1R3la35ocwht4jCg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.10.1_1543429313844_0.837327695246658"}, "_hasShrinkwrap": false}, "1.10.2": {"name": "@types/puppeteer", "version": "1.10.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "e4926f566e35e25327523a375ff78a70dd51a0f535f7b0ef67eb2f44290a3b01", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.10.2", "dist": {"integrity": "sha512-xVGTXVqZCHm+XyT65bPwG9WKNJDoB+A186D9IrZVOzFxalacR+CH3mKsWd5jF2eb4mJ5QWjix0fH/KjDUGtdVw==", "shasum": "508c36b4b3799b117142d2d2af2a39e32040845e", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.10.2.tgz", "fileCount": 5, "unpackedSize": 70496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBYJ0CRA9TVsSAnZWagAASG4P/iu7H3bFsJXOnjELvWI7\n7zybi4E2ei47zd39EI/4Q2FkTAsID1mHUS7OpnsaotmpAmfo8miFXUgaEmzH\n5hcnc+CT6uyjIg2LHXZmwEsy3bGXu0C0iFfWYfBNJtrURVbYk+2LRxhj75WD\nl6WMtg7QognUHBNdSI0DHdSLvJyn9ZEnRqVHedDd2b2VCLqkFh0Q/9fKOAfT\nbFEnI+4Kq42+41gDzpd7BdyoVOcZQ9AEPJTGO6hcppBx7hxfcBplbttaPMqO\nyx+u3TX/iT7O580kIZoKuLYq28mC5a2sDGoyCGF+pbb2BAorlwWBubvA6WsF\nwK3sLjUXB8zd9N0Ig4QVtDKimLABUY33/UdfA4Wgg+kHGoOWPfcd8OJ8CTmt\nLJ79So6myRUfqy22y/Z0Bedg75H8baDUrB5kM5oQP4dUAqrbfv6dCghn/2IW\ny6Wd0MgTn0t/gP9PTrIcFU/hfXDVQDgWXW8/ch8PVR84CWrbuYqoK5st99Y/\ntBQsyuLk7Sg315F/DsWk0ZyN+DSt4qIsXvC/lcpxe1H6JEgAa1OKSZ7Sm3A2\ngksiBMkRIyMDpS9C1AssGWjemBsUluUPtjZ0t3KFxq1PNbV/sWKU+52fvr7/\nQxt/aCuDjm83JoJarml4zIw7wrHRmeiEB4JwDzqpMbbEFdMnplrnSIJZRqbT\nevJb\r\n=LnCK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE5dfIL9/yrXqmPdMibKhSicvw0SIYaqsoo6UgXRbQcxAiEAnTEga2YFAeN+47EYEwDgpJKbQkyjpzqlooZFRCYaD7o="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.10.2_1543864948024_0.47077520907161174"}, "_hasShrinkwrap": false}, "1.11.0": {"name": "@types/puppeteer", "version": "1.11.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "c76fe19365d25dffa73319437b47bbef90468a0f9458734cee3b6e511d4663dd", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.11.0", "dist": {"integrity": "sha512-8CqvJRq3na1Eba34P/Syv1e1+ggyPiitF65bgq1r0t6kURKqSOnIt5G020aVorHoFJfeueJCYQ+ArRa/bGoIYQ==", "shasum": "15feaafd70689f76f5c897ac9afec5cac22ce6e1", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.11.0.tgz", "fileCount": 5, "unpackedSize": 70253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBvGZCRA9TVsSAnZWagAAEusP/3LBHSEWz969zlnS8koi\n7fzgwb3XqSH1RknJaDMsZkon7ZN2T8zwXyLmwJhWK6203XiqDbbflVdZcEZP\nYx3xLqN2w01s4ozhoBzqd3s9WjxECkEnQdSu7Vp+Wtpxy9llBFzEoWaRRzUH\nkxRjZMuDXCbR/DBlbdmgzCHraYIImYm8pxl8U5ED19e3l68gBJL3EJybhJh1\ngmPCFy1xWKqskbXV0kvphxonN6pH1ulB7sZsbXLEWDsQTHTNWQy2k/c0fwHm\nwXy/Ll7RD1Mag+R6ueXjmJ6LAvADhSCK4/i19UIbHSK/jAjtFBvdvANP/ZGp\nDfRe4woUJL84gzzQo60NfXR9tXecyRDYkjWIrBlb2EyJMFYYaVggIpY4Hc0m\ndSqnrxG8fiub4NiVjQFnS9lbUWppsTHYv1FZsBq/RBtf1xpQAaBi0RwtnhTc\nx1CbqC9erxxjbaUQIvynEC72q9QzrZPSlesFrlVSrv9W5rqHQntT/n1n88iV\nvChE9W6pRD91DrmiquBn9mu9y3BRz79MnNAQT6hiTOfzAba+XIIfRx3RkG4h\nNtQBW9aBBXFovyEFz2KrUZs6RR/9Sq/AJpTWGusbbaBqFyJ8EbE/9xihpOXu\nXPtUBiwIiFDBfNnS1R13M8Atw8idkPzeptxWtPMlBehNigpQKWUi3UqHrcES\nYpCJ\r\n=NFja\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/a5UzhFAyCln0BvpaMiULPhTfG2qivfDqkt3eP9UnwAiAL9/VA1EQOLbCPEC3bbpMig1mCjWaNFuYdWLHxba7qtA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.11.0_1543958936890_0.19372063118832417"}, "_hasShrinkwrap": false}, "1.11.1": {"name": "@types/puppeteer", "version": "1.11.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "38ca13846fa6baab835fb252c3a876aff1384026dbd7fa165ebfb01dbcd1b506", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.11.1", "dist": {"integrity": "sha512-IvrvZfWjITUH7q4WrM25ul9xlIeLM3Oh+hV2FL7xQQSroVf8mX3lMZaN7XEsw6Bdfp99Qm7I4GcD+ak5+wIEfA==", "shasum": "f2fe2e08917af2b4dc4b03bd2b838c05cb9d8410", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.11.1.tgz", "fileCount": 6, "unpackedSize": 70631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCCEOCRA9TVsSAnZWagAAM7kP/0TJJXlBO1M0i3yts+Wp\n4ZMiAyMV+EWJCnR0D5ba6XoObiI8kdveZQsIpve0tRkH13lycpSQyq8yXf67\nxSkzuOnAyhk4OYfZQYxI3YdxoeDbTVdPDx7PdvnZbkjAKKYngJr9fMg40Xd/\nOJo9fZQdVzrGr25rQe/YlAh2N/SPwg3rcdI4lpw/vA9dG5gGz6ndu4EuPJEM\nazTHBEtSC9JD5sajGw4ELDS2ilNUC0MZeRznE8ommYVDBbywmQAAQuhUR7Xl\n/7xbITJ/xmao4fI8IlCyoJKW73CRfZJBMcb0EtB4CB8U9fzcCSimUzLuuBAi\nq20LexKDUwmGih6Ry5zBjR7Eeh7rpdB7ZsqyVy0LRBm7V0Douh+1CmmR2IRg\n0lUe3ZKmRcyCRNqdkHIUFonQZLxbDtBIDmc6Vbl1mUuOGFCqOOMP7J3PLx26\nGgs7heC9nc9cmadiIzxMddh5m8rIyi4EAwQiWX2jSh+wojxgWHWnSL2iwsae\nwYRM2RpyFgXtASscnx4R9iutLmMy2I7meR79n1l5jRbvt5jfQAcc5LoZxYpX\nt/NvyId4G/K7IBbbA8QAasXI1YGH2rljxN8fjPAz+dz1Z6L85AQXjbLBcT+W\ndyp2W9rcXYNltzRWqHf0YsHUVdbWA2Mx0kQMiCAqEvmiwKv/SiOVRaDiZbnk\nsEHN\r\n=FVyc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKyMozjU91u4ADmtlF86a4H40LaOQUGxQOHDH8m8rOwwIgBWditpbyiHCLyeU4YVvNBU2+wzoZblhGReYcEyz6jWc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.11.1_1544036621331_0.393284024240498"}, "_hasShrinkwrap": false}, "1.11.2": {"name": "@types/puppeteer", "version": "1.11.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "1230a6f901fadb972cf77742cc17dd27576b468295f7d00fde124230828511a2", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.11.2", "dist": {"integrity": "sha512-eHBXpiZz0+PrnvHID68OjwcBjTbK1V49sa4lxjq+jiEe2eaLbRl2F77icNg0ewiB2fgdHJUHBD/9ubjfKrvTEw==", "shasum": "2d68f2d187b3f0d9a47f89f7b0abc7d77777928f", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.11.2.tgz", "fileCount": 6, "unpackedSize": 70832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR1a5CRA9TVsSAnZWagAADa4P/Atiaof4Bw2iCbpCsMXo\nJXd79TnH4qmv4s0/0VBsZYsQWPRPScvWShzIZSXq4F5lff5BNCqOFtzdFOsy\nABptCsMSDG/nA3m8TDa5bqt3ZCY/y7aNILRQRPbfbOimr694I+oXSfGbXOc1\ntpzX67S9BdkfXFd9oGEKIWSKJvbHTuBhG0Jqln7zjMpuaQdlOfpqTUCWz6w0\ncxrhYbh+unLYL74sZHD2OuuhgsU8HpzkrhAsub2gn+KbGMz32Kxjf4ERg56i\nnaA66IPxNyjg2H8cuSIPMBuOcHwzi/tunGN0YzMlyjgVg1sW0zOAsnZ6hWty\nqTzpDDWo3Yu5hLhwHe8/e1TrkxNospxhoebFFIi1Q8BKW0jJwIIC8GZnkoXB\nEPz72jWkgP73K4UqpFcIOKOkrx/9UHN6a0OZ+TVE78fs6gez5bjlahfJ76Hn\nl0rudOq+eB6J52QuA76xSMO0icDYEhNEcAnmdr26APRYHU7yLoDD9SdzXMGD\nPuxTqzX8wCqT+cFjfHK+KYzm+f/dWMdvmHGMy1UIabMRjsVucdBctJLZRFhg\nXjoI9DM7gDr8GmuP+4Sz+p377P1n9KNATmow6kM/5ESc/Rj+hDeD58Hpu6GL\nGhp2ofcNeXyssyVZ6isCWwjM3TFpFek/ueT7xTjRmsLLHEv6/UN9KqC9hpyV\nxZ5U\r\n=U9MK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuAx2qzz8hMMWejRQTXnhzdrpldkBqZhi3DFpplghILgIhAP/cBV4hcfcZPHa4u9PLrsVQvsteeZT9ac+535n+WRZA"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.11.2_1548179128734_0.3288512453140604"}, "_hasShrinkwrap": false}, "1.11.3": {"name": "@types/puppeteer", "version": "1.11.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "ba6751a0a76371baf68623f78cbc7385b2aee003b74964fb33e3dde6cf80d67f", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.11.3", "dist": {"integrity": "sha512-i/kNectDkLqU4y8FfeMAdMv4KGVVCPUVqp+bfa5+teq0JdoAwplu7k6t38+rpAAbEmPSywf6aqWbg9JbUDJqiQ==", "shasum": "c90e860b085ae19da9720d0fc376bd430cb32b7a", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.11.3.tgz", "fileCount": 6, "unpackedSize": 71363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcU9r6CRA9TVsSAnZWagAAnuoQAKF/vgU8xe8lssk2Sblg\nQ73pOR91NyFce/ciJNXoTKuhkUl7007nx7WopSn5RBXijMDWzdjD2SsmULg5\nm8c1uPTjKHxLfGvP0lgs8NJnwz0YkUCJ8AyyDQFi7mCz90UZJhiiN5Ew+cw+\n6QZYZx4FYCPRLP4b/MgmKaHM/zCV2OgG97cq7KguiMnJyPNU8DmCPSCirQEk\n1cFalmyB6QGoFlHUxxoWl9jLmhyB7b8IhsJmF5hW+kxzQexHW3+jBftObZeg\ner8z0F5PbaI3Y3Wmmi5Uw37SQxOYX6ywDtyFepNoBPOkJbp9ybQx+LS8XhZ2\n4J9+WONAgkc7Wk5JwBrSPfquY6KRTrMQMrQKlyS2psQaMMEsEffvtAfqrAqz\nGh2rk0xh2sABgAk3Q/C8P3alPWkcHuWo2CODw/8aPi32Db50L4/QvbtLJhQc\nWDSaYV6XbF+gyH2jp0bjX7ljYmdAZ+ATMeQ77WCyeWdmkNmSuzovybRHrZbf\n7JQBPNeiNwNUzc5n7PRTH7Cd2MlURLJtCzcT+W8/IINl2A4sFJjsH+d8rUuM\n5QcbckbMqSaKuBZsOEHSWDFU6kl69kDfzXq90nR8C+6Hs2i49TdkCR/dsf9s\ndTfQuNBlGoD40fy1FRaema1UHP6/Zr6mV4gWWZIsByCqej4d5Ya3qltpuwAF\n03S/\r\n=yE71\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqwIOvWN5MujbBl71072lUNibspuamrlR5BEepp7jH3AIgbRtgVJphgbv4criQqKtmzFcsaUX5zpyEI60n8uJ1yUA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.11.3_1548999417723_0.7840101213046138"}, "_hasShrinkwrap": false}, "1.12.0": {"name": "@types/puppeteer", "version": "1.12.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "6efe35ac5d5379b0f97522477a9cc9fbb9b56443eb4f0e0c06dcd54236974f3a", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.12.0", "dist": {"integrity": "sha512-chymwff4/kbiHWkG/ZqkyLboFWMmbO5gz9AozDKqdv6T9ca1ScI1jsK5A7vM5hp2qoxfADN/UEOpMDXaA/L0VA==", "shasum": "0bd11f1aae4130b164d1759e69b8376a75d034fa", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.12.0.tgz", "fileCount": 6, "unpackedSize": 72952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVO0NCRA9TVsSAnZWagAA7GYP/1s1N6iqnzUF/WIjwIBq\nVCcF+fqXAxDjEenK8eVk05r4pOCwkVFsHjh0beviwpIeVNs8ti3DnNGGlyJO\njHJs3rkBIY5iAiujzEDKg3yjjQ3Z3Vd20cN9zJ8ct7Ag5VJ0RLRsddxxbZcA\nzY05m8L+FpEoKZyGkyWk6f1UHBz4K1mHoIBcft8uJtMIsmT76coA7sw0VduC\nI1p1fmBbSNHC4D+Y7NbXQn8VaRFT+8JFw+EADGpajv1F32aAbDsmIUvjYYcM\n3wnlU0SNiCPmJze1oUGK6wV3CvPGwHhm4SdFoFvVS2wx6ZC/6VKXXHy6nqn9\njobP+mqvOX8B6brQ15Z1TpGOcoNqdsN0FzyifR34RcDKfCHPV8RDvYtVnXBX\nOI+e41tnVy/ebBm543EVUzjt7mBulc+Sv6H6T1IgP9SG8UJjbycWkxz0wjsj\nq6+speGfl38hH6Vdx5/uXTxqiyr+a8ytrfGJ/id7Q2DibQaeGr2xHTT1vGwz\n0M3umsvRXR/9LgQZpygcsLAhLBEeqDUkgue+RJnIKje6j6W0dVN8zQtzFJXg\ndtcys4cI8uXsyzO1RYEymsFTQ6nazjM3ltKarqVSAtjB0U9H4z9/kPslgnVq\nTvZWbf9B5UVAFtHO80B5speyX9tXhlVcmsiGCuvTJEbCXIMlA41SPLXD3ev2\n2IJ6\r\n=69wF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2xXkO9Onvdc8gkkHWEXZ97Rk99REg2dQmj741MsjBsAIhAJ2iwh4UAuu4LneQEAgRf7IsM7W+YD/ybOLvxhkHPheI"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.12.0_1549069581288_0.14300586887579514"}, "_hasShrinkwrap": false}, "1.12.1": {"name": "@types/puppeteer", "version": "1.12.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "ea87e91d8b75bdf7a0ca683bcdeb1d3f17544e851eaf1bb5a687517f2d8ee09b", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.12.1", "dist": {"integrity": "sha512-6qpe7XXM93iWh8quEP8Ay516Vmfc2r+ZAxFH3Mt6fx3vzmZz+4Q+hYxc9PxeEIXJhWLAAPYAgAiM/vLHEUwGpw==", "shasum": "f61f9e0da45e36cdbbeab3b088995a8a2cad47a4", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.12.1.tgz", "fileCount": 6, "unpackedSize": 73096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWOXwCRA9TVsSAnZWagAAp4wQAIaV+lOsPoWQkOqrv++d\nQkYNX6ShV4KOFmOpEpWMSDA1ItB6uiSWzUoEoPNlxzSka05+NroMNmAJha4S\n9brG13c466foH+3xIp+vuskYBxYrENrPn67L0LhGd+mV72UJjiKEJHhHUu8K\nj/3i5LeXAqXTCI9QVMHWHRDERaJ3ELjATBnr8zlOlM8TPMzH/OSybu7MwBQk\nPQL3sjwRAV/2owV0+PQkhSiRj9cF9BvIfvVQU4B5xBhSJAbKa0/qRYtD/vd5\nJj/UIux+hoWmrEzD5RIaER/3hFrj5vcXCUvkG0nq1xSXqQlCpfjacZ6DszUm\nJffBx5j9y4OIft4PU7PG451Fdcn1Gs9m6P9P0zqn2FAqmquXt3Kp6D9npRXX\ndRvhvl9F4ffPmIiWC/qSJM4MkgglUP9mIrYNU5CoGcvdPkQ8y+uOahaKHYhh\nyaoVhvZZ7v4e7mZ3z/VHnRR89wbeFLR6Lsz1gZciIDFia+YGnkinZozZmuYl\niG2QSaJSBOwvAT46g+qTSwnKWgWJlconUV06FikG8fOXp+bBzrljPRRc6l6E\nU60XuaVzCnlNUs6ddOkFZXRyxJ8RaMeKtclcy49cdCAx56ycZNWZ4DjJE0yV\nJ+1R5z4JV+XlA1dZDAiRF4HPt9Mu82VnNRD55VuD9jIcfACxm9JmFF8NMqB3\noAwg\r\n=/VM9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6z3nObKPdtbS6PukeF6wDQ7Lem6xwuyv4D2Ng4rzZIAIhAI9+ARdqKvAMJYGRJuKVWC2D2xoIIPivy7L0bnZf5oJa"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.12.1_1549329903797_0.48633260698666403"}, "_hasShrinkwrap": false}, "1.12.2": {"name": "@types/puppeteer", "version": "1.12.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "53373f4ca92a35da5b84cb9d9fcd704266c6a7f80582c69d9dd93b187ec2111e", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.12.2", "dist": {"integrity": "sha512-EGfxdNxqsEQiY6Ce+4rRD8PsDD18b5FWR+J/gWX7J/UKNRk1Pi9bircx0oADMNF2RdbyzmC+7TU95nwnerFwlA==", "shasum": "5e3c8ab57fe3e0c9d75d8deeb6bf290b57321dd7", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.12.2.tgz", "fileCount": 6, "unpackedSize": 74743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgVzACRA9TVsSAnZWagAAMgcP/jgHWbkAmYCcHDW826QP\nri3h63kQpGoPwPRWGS34L1ao3ZVqWdkPYSic1ocQqw7jb2W6Ybyn+esLqPJF\ni8vCQXQc11f44epfOu+wCxcQUydrZJ3oQr/egsBVORHh8VsEkY3U4boK/yzN\nUdjljmknAhSqOO0eMYteQpJ+y6HGryDCSn2LSsGjKs3a0+Mcf/64UjApsqjR\n1rkbHMaU6udqlrJptWu4TJ8xuDOV3X5FXYH189Ow1fwEU4n1p3hspaJxX2ds\np/TKdBFQlx4QSSaRw2BY87Kxcw3ZT6KNVKhtgVqbGjqYqR2MnhLAONZ9Ooi3\nyr29OtU+ckC4M8xD7KNWom4giGgMtZFkJW0atiXMEkcaHKnhEo2+q932rj04\n3IFkpQFtC/djQQDyy+ARnBs/lYzFefH2iy0KoldSB/o3Kekxaag1kqynj7yX\nusp5Ja1Q6gDJb7zJpEDNABzijnNvPEtm1lPwGaupQLpE8jW5t8J+HyKDkZcG\n9qndCpc5HwuIspKJCVHMRHK0R54ju7iX03PitUwz2EDJMJcPsKd5GPA0hRy/\n3N5mQu8bmgnpFZ7FIePl6UP9Y9Gd853FSW5HfKGztz1shJvlgmJYL1nPrhtd\n5+44uyOFgcLIzm/wXL5uoCjCyRbyfAce33P/Po2h2VzIKZt3g7ht6511N6Sq\ndagS\r\n=nrHa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/r+V3hxw5teQ+tSJoeQSlnJE4HPQRLfKpJwVR0UvUOwIhAMtDXWqwC9KZCNkj3cbrU+nIwCFHX8jiOXKTCvVxyry3"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.12.2_1551981760256_0.8335451666029419"}, "_hasShrinkwrap": false}, "1.12.3": {"name": "@types/puppeteer", "version": "1.12.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "648beabeab68454ede9d061513430971f4f8a684446b62c177e882addebf0c12", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.12.3", "dist": {"integrity": "sha512-mJtUPdXqB8THRwiHPbx8pkGYi+8IPf3dMuwJS9hHpr59BwkuLDkkEJ4qMST0k6TbOUXp+wyMJii30ouSkoEtaw==", "shasum": "1309882d368ed21004dfc4520864fdafcf126277", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.12.3.tgz", "fileCount": 6, "unpackedSize": 75690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcip84CRA9TVsSAnZWagAAwxUP/ROC/Ddl/dtFdGGMTZfa\n/O/bXshpgbPxKdiGquZAhQgfZNdxajk9oLhI5iTfOZ5Gpver9oNG1jco21f4\nlvOxEINukpp/o4mIOytkDY69sy30SuH+ZO0QMGQaIVcXwYnrpvXaR7XwsXLT\npXgLtc8GBmCxrUOh8oD7szDRnnuddp6QGWgyFFE4S6Wyg8+yt5Xh5BxoZZ31\nxIXnBWzziSFCT0m1d/4VT/9ld+oyDkedfKIXRhU+pXY/dCXkTtevAYlcGv1Z\n+E+BwRWTIXoR3vE8QuhmipSbba3m+XbeCkORt5984m99i5n9QzQW5fmOQ23Y\nzi5Matqhr6fFLFfxPzLMYECFFYuhVjJD7EQt0cfOml3/sElL8+lQRyzneaum\nwrtL+8QZPu2EbZW1bRysJmlC81Mus03IskAqa703gCTRlsvnA0mP7poNV4oN\nkzeXEwn7WU+V18WtRioY7Bec8c1nHKl5P9vVYlHQy3atrGFWxys6z4OglqH0\npr5Mpu6SnEFnEMHRRVXYihwgztB+ofydoGICLKrtgN/DlxB+mSWuzROvLgFD\nenNDBvCG6vWWTqxBk3jXl6AAuQNKt3SujttGAmap1CxVuRnWUjPngRtWTqmX\nZVt5Wva5hMz5i2gJxGst2xH7IwVkS6UQsXcFnCP2+EiVoosqiKoqcyAYrKAu\nnIKo\r\n=UfzX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClguiqaM3S0oJbUS8hngJ514xLYayA8KRP9dfoNvm3wgIhAJLmPXZ5yuWYEjMgmIFraTeZfc3d5ePULJrytNDsHnH8"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.12.3_1552588599648_0.7528392427105319"}, "_hasShrinkwrap": false}, "1.12.4": {"name": "@types/puppeteer", "version": "1.12.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "40d3647b20498a687247976c4f33b55a1b31fc4cbc1678404935ac0bb172daef", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.12.4", "dist": {"integrity": "sha512-aaGbJaJ9TuF9vZfTeoh876sBa+rYJWPwtsmHmYr28pGr42ewJnkDTq2aeSKEmS39SqUdkwLj73y/d7rBSp7mDQ==", "shasum": "8388efdb0b30a54a7e7c4831ca0d709191d77ff1", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.12.4.tgz", "fileCount": 6, "unpackedSize": 75690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcy4x8CRA9TVsSAnZWagAAuHIP/i47EmxvCVFs2tTkA3Iu\nZakuy0jdTeWsM0uzaYkuIZQt8u75b6qm34VqILxxDgHLjJykivEZ2rQp/K27\nb7B9xqNcnvBt82yhr083DYRMgV0Fz0+2dziPBjJI/td8X3EnCXQFliXQcC5g\nXgSYfrXuTV1rItWiczbkoJoX7GDWtmvtN3ryqk8WIaqL6wVVYSpuQYwep8lh\n/VoI3PmnSRnPOOSSRYKn2bVfvR+4NklxgQr4fHOSxVVeSi0OfoH5YzmeXDWH\nMryLTkWFr2kODcl/2qktUPssf/bsV2Bxrs+WqEf+LLBAEOr5tOZXmMyzn7sT\nmnl8xslyewaln8vxAHws/5ixAq/iL6JqCn0NgLnd24M5k2rlWA6IFvUmLtDW\nP8IGWBaNdofJpRe58XRQ0b1QUKB2wnCs1x8p0CKQ/t65pmzut/HSUq3o3L4W\nCTd+5n0f7hrxWjpVa45EuNEolWtWa0xatXrj//k7VCvyUlHvDMc44yTj2rKa\nSnhZEluarvWk3aaK4yJa0vfiZ1Ztbtmd4+GQZwTnyhZcgMrFw1xhRigLqq5F\nZ/jfLbC937U1puxKfN0unoi5F3ir/Q42PgSG+kDU+mErPu9cSRXjaa4p/u5t\nCao3Li6jGaRZhnwySqUMEXYAiwGcZPsDfkyli/EegH+XGwB6OmAvCiVXuMig\nkTwR\r\n=ur8g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSVUlhpyNiEzyn57VpdWALzX9rT4QsrNMFd/a/DTrcUwIgUWAql6YKJBMWeBQLUDX0Mdhsxj4MDMZHKUSzkRJI0p0="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.12.4_1556843643608_0.2231247589742591"}, "_hasShrinkwrap": false}, "1.19.0": {"name": "@types/puppeteer", "version": "1.19.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "520e2d272e3a06c186bd9edc3945a73c3b399c7b48a92c4b7cadf7a4699a6512", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.19.0", "dist": {"integrity": "sha512-Db9LWOuTm2bR/qgPE7PQCmnsCQ6flHdULuIDWTks8YdQ/SGHKg5WGWG54gl0734NDKCTF5MbqAp2qWuvBiyQ3Q==", "shasum": "59f0050bae019cee7c3af2bb840a25892a3078b6", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.19.0.tgz", "fileCount": 6, "unpackedSize": 75263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdOfwcCRA9TVsSAnZWagAAheEQAKH0s59dZe/ocren17zk\nF81fIbP17xWHiVjrTaJmBA64rpiQIiQEilqG0NB89MFOCQ8e3ULrUYLX7Pp9\n6FeGjBIydy19FUqzcG2k6DxThaPS94oJnp0tU/XiN2zlQJkeSoPPrDDadTSH\nzUnfZh95ytVwNiwiwNnNPQLK370qUjhEOvhMbx062gD//O6job2EXaPaOEcY\noEgLvMl77V9TcTfWgfasSfEpFTE0i64ZyRyBTppr0wMa154DkFT1KIOdeRwQ\nM4b7B5ujHZGpIKDfcIc+qbMLErDP5RHNyrRQ2MNNdimtbwQqvxDOABEtA1PY\nALhRBoFXVoXIDsTMIMFuFjCzfqs+/4V/Um1zGMWv/esYYOoFY+8i8ESKnX/Z\nk22uHCjxKFo9RE8I20Xj1J1p7coLNMG0R1tbHdSkXS8CTI0XPqJmFPrmUiRj\nSWUsCugNdwAUSYbhNLX5Q4Xp+ERfoj4xthuqBuXa8xrGa1t1YEsjt0uSBZo7\nXgSBYq1Hb8c0hEm88lBwXE34N6i9jUb8ii8oa61FjFgThzwYTLLNHN/+fjil\n/WbgQin8PIKta+hEc5LvgT5AXD3HsGrjceo/mJu59LP2/CrjxW6V5HhPNnEF\n8HKgbm0FqNPjpgOMvwuUC13v23m6PeuYFanAlT3qufAxLz06sxEUFTGe4bP/\n/GSh\r\n=XlVp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHezHUnTnhGentVSiP9Se3L/95I1Hts7ySSMDvemXnlPAiEAhsHI8WuocJZoIaG5qN0/9YhSrw9rAnABTN0TtUQsHP4="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.19.0_1564081179225_0.13042233843244788"}, "_hasShrinkwrap": false}, "1.19.1": {"name": "@types/puppeteer", "version": "1.19.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "63df4fb55cc75a2161958dbb7eeaa009e63eb7475aca55593d13379aef856a3d", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.19.1", "dist": {"integrity": "sha512-ReWZvoEfMiJIA3AG+eM+nCx5GKrU2ANVYY5TC0nbpeiTCtnJbcqnmBbR8TkXMBTvLBYcuTOAELbTcuX73siDNQ==", "shasum": "942ca62288953a0f5fbbc25c103b5f2ba28b60ab", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.19.1.tgz", "fileCount": 6, "unpackedSize": 75272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUbpXCRA9TVsSAnZWagAAGuMP/1iBZ+hF7WRq6mRH7mls\n0VMez62xQC69xHnDhCrYCtp6PUV1QPSUSs1N6yzDMTEP+OPQ3RyT4kyrtrjG\nFaCqZWfa7uDsg6K2nGc1YVTqXUr/beRKClMq727hCgb8DeMBbtsFzfr7d4/X\n82C4IsHEPiTdhpMpykXgd+hVsJYhFyyAB3SG5ZAQciKUCjTsTSvriQBcS4SL\nT2RmGDnI/axugnmyXiG266K0YyANzMrdE0h6RsdMm5jaF/RE1u7NVhGw9NEE\n6PO2x4oGHlIZ1rwMb8joVzLcQj2/Rd4ABQSlYxB0tduK+tASBKlEWOtbNhdz\nQgSE/gmmsp77Pbc7cBNrfe0xXMPNLB+1WfEvk1ll0pCtwPsiqvutt0lBR737\nO6wwwCTA0H8pv0xauDA/g5YP3f9CR+2rTx2h7W8uQWzFTbkBOZZydkzKZzl4\n8TxhP5vM5K35O97aL8mqY+6jpVkktLGwLteVmaLiBSNrjOLswwg1lepCMHHf\neXJ+pR4U+V854fA/lsGHL/SVoC0OfHe8VjeBnNV16ftUuhHNcdpuJV+vaKdv\nr+EXS6vhnXpZ5YhGmqcmIotAi69WDU3qsz8kBN7MuuApe247SgwrWGaIY7sz\n4ouZYn+Gqal3ObUv1rVCnJhP1wsPAvrrH1IrGh1r1BEHfRJFI4/dtZjuL+KV\nR6cV\r\n=dI8+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICcLC0Dl3Ca1oHvkIiDVnLu7TmJKw0GVwGvhEH9FnmydAiBdR2RHVP9UpM7aQLi3N2NFPYhQQZ+db+4NDQdjw0EBrg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.19.1_1565637206700_0.9594267371740579"}, "_hasShrinkwrap": false}, "1.20.0": {"name": "@types/puppeteer", "version": "1.20.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "caf559bbed19b770af4d4ccd4f13015e7faa7efef44291103f35b254c3ec2d2e", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.20.0", "dist": {"integrity": "sha512-V3nWu/ENNW7LzHumrgf1D+HnIEQHE+nHThq9MTPmGjQC75SVlWsalCp1OaZFaDeuUgWOmQPIBhSLz0cz+Hlz8w==", "shasum": "8a09062a744ca1e2f0607468d810b149ab7cf97f", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.0.tgz", "fileCount": 6, "unpackedSize": 76120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjjtbCRA9TVsSAnZWagAAY/QP/iHND9P4tO4vxwh3smI0\nh3bSeekgNmXZxhSNdAslMp3wniYjE8xmx7JHl1yvBgIF7ZP+zAHHXTEHhWrs\niUW/BD/k8wI5zS5WxSYDUWXyfatuQcBOpfXwliOc8n4lbevbDsWfCcsWygJo\niSgBr3hf/OoIUI7S9BTlLRylwQfJru0Lz98V97XJbIniN0H38Y85OybThELx\nshTNBXBQrCzoMijaw3ONr6VG91dY4otrI4/jspo3lM6Lbru5LOETf+twvbR7\ncmEYcfHv3ocfLnGhuOAWraNxqL8roikJ6SUSbNnQk4ntRfhXoik8Jihseg0d\nkzMrpVnLeS7hKlSsm2k++VVBWb2LGdhRleGTEF/947BmKL2OdZZHMMwtRS92\nZAgPEijgcCuPdGQFxuvzgbASCin/2fJDkVDdUmjNRvZYh9LMsRPFQ6JICXrt\n6TdB+9kA+rBfv9lAU1V9LMCDYrXghoIEc6HHzU+WLDFnbiBvhpeCLA7ld5ys\nNF/I/rWTOp+ALQbxP7qNWbcccwaBu5YVVmrI1dAbANspgdh211PXS5hoIvBG\n6nc7mkZKs7btMVJ69XIV+0nzB5G2CVkCNCkdZLEJZMAfHJqJbIZ6U16sBWmn\n8OEMGQ+T+UGH6oCM4YXFJq+2zg0SYsqLec6T9ZyJoKMMDG6fPk49G66FkL2z\n+OYa\r\n=dmiN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAEvJ5riaZqxV7ZTPfprpillDhPcc7IzT3spZtiuo6avAiAp5dn1kX6Srft+Cy8AofNlGWVihg+fl0IsWL15EUEVbw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.0_1569602394714_0.9913090579851156"}, "_hasShrinkwrap": false}, "1.20.1": {"name": "@types/puppeteer", "version": "1.20.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "853837c6c6a73223164710e9af0248028b8529518adf360965ed871f51d52195", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.20.1", "dist": {"integrity": "sha512-F91CqYDHETg3pQfIPNBNZKmi7R1xS1y4yycOYX7o6Xk16KF+IV+9LqTmVuG+FIxw/53/JEy94zKjjGjg92V6bg==", "shasum": "0aba5ae3d290daa91cd3ba9f66ba5e9fba3499cc", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.1.tgz", "fileCount": 6, "unpackedSize": 76402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdloUmCRA9TVsSAnZWagAAMrgQAIMIF0rXFza8XFOfPPHz\nxfYXSgVs7Nl0Q5yb2IUUzTLoQX3uPxbfKx1Qjscm+Uj0xZ/i+fpwv0o9tLbl\nNsUeV2TF1UK9NWMTvoxZ6U5U/6LCRax6ZdIKzlnZnZBH9HKBXPcc9NBBq68L\nqgF3veXiycctGBBgrnS4vawfX9boEFKz8ZFQ/xSoYxUENliVT2kXugARIt3g\nlD1tzaYz5Cknp/WVKFw/cE6mjELETjI1pUR0YeiVCxJQJE48KsDpWMILMqE0\n3PWfQZlxK7BdZEmVLIqxWIhHhj36WsA1J4buwMYjjmhm79oGaWfBnH1bBely\nrKA3M8gIl6436n5c8DU3nKOUMA3Pu+4n++YufCyLPyf9vIyIa6rm3oxpaQBf\n6Aw1bOdO8aEkohAdtSSdxfjZkYzeyvYDBs3ztzahc7qjL+MwmsygmU2EoTnl\noI7pwrvQpjH3J43BBac/0qhKidMLEOOG+JVhL6grVw5i/4hVkWGBRFJwHiev\nHcbSpY5XJRYTgcEH9P526ghkoRErLWBpy1DCIjw27otl9u5ditFPzk6F/HWH\n0ot2VFZhuV0i0cMYe+S0CEReCEx46a3tEIB1caQVnR9qt0ciJpyfEyiUEGHV\nGsx0SahK59U/gb6J2ExazWcDZxcdhfpgWTuAf7JNxzn4p09yd6MWEGvMKC/m\n1YiQ\r\n=TF3Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFbwaMcuMtMSo0ghLDKOY3e7bZjR/TTUZVCuiWt/JhsnAiBy4wXPPXHM+IxKbTiYA5FdL663mZgGTI4V/na+IqcR7A=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.1_1570145573673_0.6079348883791333"}, "_hasShrinkwrap": false}, "1.20.2": {"name": "@types/puppeteer", "version": "1.20.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4edc959f2e40be8db06997abbeb55bb674d6b0face638731f341205f579310b4", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.20.2", "dist": {"integrity": "sha512-oSFCtftHSfVx8K9XPdNNYs79Zt4pYJs/0NP78ltuGCB25zS3UNGJSiypBfbhbvRC5Dcsh0k1R5Z0i8HHtqQUPQ==", "shasum": "b123551fa868b78f1c65ef1cfc0dd5ea1f24234c", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.2.tgz", "fileCount": 6, "unpackedSize": 76676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn4+sCRA9TVsSAnZWagAANQwP/jW/D0YMo+QQxGzT79Kr\n9tx0QYD9xVtULBLqe7Xhp8/yYYCNRfxQhmzJdxr2Fu89/gyDVZ4CHwBmS/LL\nXyQTzSIn6oN//5xK4IhaWDfxJmhHZnlrWPoNEFGzPdIHJ0d1hJ6/xeN6xTGw\nhoUvbsX1+G24RtlTFH0MIUaut0TyvuI32Sf1UIlxMewyHS3kSt/MkcYW/3lq\nur2eYtEzs0aycO8puIfElzaf4U0hBsz4XaNUGt+Y6tqVEgWElhEK63/w32Rk\nGATNrg0IWgcP+9zIOu3oU+mORZy2lZCJ7U2fRu+HWfWyII3wHPODXbaHR9J/\neoX7LJbrlln1WumkKy6uSYIBStxY447Yz1Q6bhWxBVJr38iRuQIXRNQ9DWeb\n3eQIbEE8k7W/aCB3Ak5mlXf4FsZIkjWjHPKjVcqT1WTrGpTXuAX+oE90aKHJ\nX5ii0x/h6gfp53DYzmoUd0gekYY6ghRd/Pe8sHkouUJtwFNyZUX0u009+yBP\nD70Wxtpu/n/6Zc8D744Tm/7e+jOEW3jb680ighwxhL3F5h3BAr/o56DZhJWs\nqxPmh8LQ00SJ7kGOpocThJ+dP628Sooa6SOsR6AjVctXwc6wgFyikK7CWglZ\nQ9CWB0FGw8IFEY+bvq8ZJ/GKYTHVn91P4SmbxETLha9Ep1yMITIdELIVheuf\n+M4R\r\n=3atw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmn1FrVb74gEGsem+aFPvvfmA+d2C76cABTABqlrxnuAiEA2ib2n0jP17+YPEhL9fwVPKn2wFQyH1dL5XjtMRulpbE="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.2_1570738091748_0.5024777751147866"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "@types/puppeteer", "version": "2.0.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "d7b08fe7623231c548ecfe6da93a199bdfc53cb1fb60f16e07e646346d7e2dc3", "typeScriptVersion": "3.0", "_id": "@types/puppeteer@2.0.0", "dist": {"integrity": "sha512-QPHXIcaPcijMbvizoM7PRL97Rm+aM8J2DmgTz2tt79b15PqbyeaCppYonvPLHQ/Q5ea92BUHDpv4bsqtiTy8kQ==", "shasum": "82c04f93367e2d3396e371a71be1167332148838", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.0.0.tgz", "fileCount": 6, "unpackedSize": 77366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1Ij9CRA9TVsSAnZWagAANZcQAJYYBq28ePoiKr2cd7gv\n9JwmcxzrvG1KAoP+m1H2rvD1955TE6JtkXXCfwyC8T9/qWQZ4C533ED5tagg\n3NcKxcGjpW9tA00itf2y4Sido/iHUi70hzIgc3S1UR1HFYHLCaeuQE378gyJ\nlftavB7fE8cX4DTqXJovkn0Albmt2IsQDDkp+wmGoNJJ1WHiZIDrLPfjcrZF\nM+bwEZeogpGyuf5fVyCzuiJDhyixaNhKPZekigczl42+rUsOaOVLu1SL59kJ\n62OZ3IfeSVb1quydmSQZUbZXopvz/urn4TnZtDEStHmt24jncJCDXdIz2M2E\nZpcrRhlCfzrOBJ8mHtVMI6x8CTwU808S3fzsDpSTzIb2lMVrehzb6Q1NTdaL\n9QZHHodu9LXsTffn8VsoyqDOVM3T8fE1diXqLl3phZ+tLCOLj/f/lpjuDocp\nxSidJ1bqmtuBmRdU9JjKVqBQQa1yBaCXxtGU5Ab7sEaxWi4jluklS4gAelzh\nu7uNHdWB4vumRfcbX3+TSJNFpERS1xtRBDw3f9K/4KP1UdngtpMkEkzWGVWC\n5iHq3feVdURLGLtOJKq1iiCJG0OG4Y29uUQgYKKCPozNabv9+Do3AppWUbMU\nyW1cCByD7SnGVJgeqkPdaP6Jr4DPCkTWazm6HS7omY3Qy1Xj0KU4wbu7/Rhb\nVnfL\r\n=udk6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC81GIM4A8UTrvwV2TEIpdYI1fMwK+ToIuSevkqQRqLfAiEA7tnm9SeG2WCEj++Q6NEmIxrsk8hWxKUCECpgFuFlAnc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.0.0_1574209788925_0.36410871622434593"}, "_hasShrinkwrap": false}, "1.20.3": {"name": "@types/puppeteer", "version": "1.20.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2cdb5680dbcdc1c0edfa8ab4d81ecb1b4d0b8ad9e1728e4c7ef2a0565e705924", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.20.3", "dist": {"integrity": "sha512-U1H7E4wHDsPe2s7wa2fpUD4kPYmu3n4hYRmlFK4WgKQxXE1ctY2h9Exely8GXs7743gLvrnzuX7aJuyG0SEMIQ==", "shasum": "9cccd94b649237136c02a41c21f959d9db8e3b41", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.3.tgz", "fileCount": 6, "unpackedSize": 76730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1IkRCRA9TVsSAnZWagAAbPoP/3O2XM9oaDH2xgqvZ+Yh\nXdibRjOGmZ6oaL7IHNzutQg6Bn36+wZUEQ7dbtoCV1XG1joNSfkySMH/vBZi\n9VyHEoS2Ah9awN1J8mLGorgzDDB4/T8WlwH8TKUCTXKFNAMoh8zT8/q/MgZ8\nkTnPEqY8h8Su+GMg2J0t+KOo+uRSqFK+SCz4s2qtuwMdoASr3Y7IVLyDvlP0\nd85o3Edv45Vy4r2zbvvFiaTM/1ExXK6AeSHFoYX/UT9viipCt9p1bi4q8OKO\nOUw5Y26r5wTwL8nR6H2KYY6jmJWZtOy2Zp4FbMmtUkF7qqtOnE1R2Q4IBtBX\n424XbxZVe2Lo1ksHE9O75LcTi2WJmhJmdjEhP8zSdGVf2YnhUe5IQJwoGwXc\nU5fSlLjoPXrgdTeJvgeHwQ60Gld+O2OICxfdiXq33wWCCkqkoYVsFuaczZTw\nTpunvpnQGcbMcMg0zXxaxSUOvbxoVkLC4EJKmIsKacldWG12u+oHcMlIWeUL\nDLmxUKMF9lINA8AWR98wKQUqnm9e1kaiy1rtUn7m78zskAnVev+3CUuGN7Gp\niGzjLkzHG0/nt8oEozI1fQ6WXv8xmsAcV1AofDUGyyPmRMUk7M3Rq9VOKU/2\n2+lg9k9DJi4+zuGWEr5IrJjGJdH1TxHwxTBO4nMTp5gRvIqXITx9AcB4XrSW\nUx+B\r\n=c5FM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+pRY+wWXFSUECXIN6OQ91rLhRlGOLWkMOv7092U3pDQIhAPonJhCG9jCVRZAEPsVgrZxmQyL9py/kmE2+2dop2IHQ"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.3_1574209809018_0.06093045226341265"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "@types/puppeteer", "version": "2.0.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "acee53d9a1fc3588847e8d86ffb9dd74defa1514d5376cb860f304102ab39789", "typeScriptVersion": "3.0", "_id": "@types/puppeteer@2.0.1", "dist": {"integrity": "sha512-G8vEyU83Bios+dzs+DZGpAirDmMqRhfFBJCkFrg+A5+6n5EPPHxwBLImJto3qjh0mrBXbLBCyuahhhtTrAfR5g==", "shasum": "83a1d7f0a1c2e0edbbb488b4d8fb54b14ec9d455", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.0.1.tgz", "fileCount": 6, "unpackedSize": 77433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVYsGCRA9TVsSAnZWagAAQKsP/3dTUeM+ZSVkDOZzvmS6\nJOMpXbF4cvYzLzJr9oci1rJV4zmTdeASebIQDIytSt3NraYuspk9HfOPUPKG\nXdJgnPCRBDMpm8VV4+7mQrlSBZ00/SxOtsURS5/bTUecIDCienHg98dkrvhH\nNEaQMZeuRWSnVQLfyhkKumkpSteZ4xycnAFITlL/14+Y2nHC4dSkclzjLwyS\n93XT0mgzUibjMdwAW24IX/X8aHIq7hwsAGVz0sqA6sX1IbZ5+TMVMI2LOwcC\nXbW9RqD4EWEaeWbP7WHjw5DXNbi/nKfc48WDIZJzvvYGxpuRY6hwG+2NSABv\ntsYpJtkq9FNo4D23mEFs0vGy5mQnTwjItS3bH35nAh+Ot5SADGvIZLpEaxAV\nQ601X9CVK5mJ9L9fg4bOtvhHEYtnfWeF6XV0/DK/9mheMma58+cQTPAsi0a5\nNYR/hUb2yqCvoOxYKjMvs9csvCOdc4l6S4OeljUC4ttJ5XmsAeCGuza5DPIB\noGUgBTyA3kEV7nTIvrzb2dl4zUUBHHC5ZhiOcMmMzYs9XWlg9yUnKUv1XDI+\nKZsYTibybHrEv2Hvt/UIJpKVQTHBWSEfmTHnpV0/30CK43FzJECPhNwg6wLi\n3yxnGUPKK4CxRX9HTSY09PfS5M97KV+gU//Tb50weSQyXgAuBqCoAU0MvpMw\nBA8g\r\n=Z6ZD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWBe4vMU9Fa/9MHiG/Y4HHQq8u6X9F4j9Kq4s7fzJS/AIgBPt+qbM73uKbSlYrjFU7VdtZ8z39Y+c1I0UfnzEunis="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.0.1_1582664454344_0.12996518285308922"}, "_hasShrinkwrap": false}, "1.20.4": {"name": "@types/puppeteer", "version": "1.20.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "c6699ee8dafb4158dd3bbfd26c845fe90e66d7bc775c6bd5d325c21266bd5bdd", "typeScriptVersion": "2.8", "_id": "@types/puppeteer@1.20.4", "dist": {"integrity": "sha512-T/kFgyLnYWk0H94hxI0HbOLnqHvzBRpfS0F0oo9ESGI24oiC2fEjDcMbBjuK3wH7VLsaIsp740vVXVzR1dsMNg==", "shasum": "30cb0a4ee5394c420119cbdf9f079d6595a07f67", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.4.tgz", "fileCount": 6, "unpackedSize": 76797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVYsbCRA9TVsSAnZWagAA4o4P/2L7hk4PhNfP4XbvQ8O9\nMtjz9q5abPI9PjVcBSYyreuTc3VCWSF5Aav3Xx9EieBiyXvNYaSXcDLhpye/\nfz9vgX7ciuvo7+q3P5SIiY7AFrkV9D0AHzJDrqUHpMjDMEvyNxHlL1g5vAwz\ncSnITFpddw7b0qN0z4/jcgZljzxIqsJH5bDHd+a/xo4BKNodfd2W3LLBuPr+\n1nOzBV93n+4KysF0IYPlJSifch6JK1wjgBsrAbrzWDgjOPvgDtLgCqoD/eoJ\ndsdarNcc/c6IAkEcRhwWusy5shz6V94SZOuf2tm/mjSfqIpJdH4BdOY0JdwR\n3hmHl+FMn4SkM/QmxCh+4ot5sOP9IJAHsywbVw/xNZmUUJyvb/rXYHvPZIxd\nBtqQ81Pt2zKe0yxzURKQXAfFehI+YTjYBRYp5jGVhxbBi6UseWjbto7ApRto\nNHoQwtNqk1sjbqH1DZq9uxrmVAH4PCVLO8ggfPUmP1VpuvMRroYhClh5sFui\npykW9HU2N1Z5QJg7gNByW1tPKI4PuBDzQOTiuU1VVSBj8Zo8FSZQPYJAG1V6\nY/7LO8dAqCqpc++Hl/C9Q5I3LlpxYpMsh3HDRcM6ve3pykIFM59HCrLw4cpr\nfdgckwhgHQXhoZK4+RqazIYKCOpdUnP3FlOkBOrsn8ddaRm8i9mwgONZmd17\nHdde\r\n=p7nf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDN4wOl0UTQbwlnEBWX90SHIhlRoIy6h3B4KMBijO4cQAIgSPij4TSFe1x7KEKQZJbw1BfYlt395l0KnfgFFxPjVvo="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.4_1582664474827_0.8668205161163636"}, "_hasShrinkwrap": false}, "1.20.5": {"name": "@types/puppeteer", "version": "1.20.5", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "b6c94d4c63f611f243c3259eea2848a5f81c29de31d3cf6c3bcfd1a5e4f8da59", "typeScriptVersion": "2.9", "_id": "@types/puppeteer@1.20.5", "dist": {"integrity": "sha512-5INeY7d3RNvTEaPsFmx1DDjvu9P8iI0GVSFDop4p4F15I1oQGxclw4IPPhINRhl3jTq9OMoHl3sXe+fGYVyJIw==", "shasum": "1f2350dfc78b1830060cb9b3e4d9d57c4c126710", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.5.tgz", "fileCount": 6, "unpackedSize": 76862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD6JCRA9TVsSAnZWagAAN90P/06gKkiiH2DIICQ0F5w0\nj1e1O+aL5tsv16yoNqoqWOu4J2a/ZF3z017ji9KA5I3ooBdyk3NVrLkBL5hr\nT7uClOpHfzSTlIynHfHHWMRM/Dwjw2/0/RxCfP1Z+KgKpqSFX/oPm9nulsmN\nTR+AH+CtQqdaJx77ILQTp198CN03Mv3izHvBpWeKphSY4GP5TV872vP3xjmD\nJajqze7iE2jEKpRBUatQvVy0Kp8MpCrqm5Iar8sIy2v/0GzSUyBvJFxqGmXB\nZrsCaG4jcxUCRSFBqxXygwhzbKpiS4uuPVWIPuLaHwJM91w21o/tXDCPgtgf\nYNOseozCxtd0xY3/MLqBr2r6rQ7qBOspm7gix6oTQtHlccYYcUGJtldMkrP8\nKKPfHgtR1XpQUIzVG7dFvIlmRyEbg3NGX69P92yulpkSqgqGFvhiwFour4x6\nQzgKB6MElYeiDcNIIdSCaWCN9jEehlX1qXS8ElF1xI2VX/9gYRRQdGImBgAG\nBXTc840IxNuWmjc/+07LeLNG58TbxWOxGulupdRNEH+5Vw3Yv/0U9tDMsDl0\nz+TeVXQmVAUdVrqP0W/i6ynyfbw4t7FYhWgN3JpBFrHEIVG+rMdmIfXR3oo5\n4GgW+kzo65eG0bVTW74/TS8gboQimvYMoWfM8fPU9iXOw1SCkG/YtkKkmn6v\nnW40\r\n=womC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFCmnhWzVQ+wtBevDSD2pE5MxNHTzw1m0K48IvuQQd8GAiAS/DCNnFy3hDwo3qQ6pCqRjeMnpwhY2KTeQ/NEIHNFzw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.5_1588608648445_0.07474164982852738"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "@types/puppeteer", "version": "2.1.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "663c5b9a996f411627755e13e6b4116c8d460a08ef425597ceb29a4c392d94f1", "typeScriptVersion": "3.0", "_id": "@types/puppeteer@2.1.0", "dist": {"integrity": "sha512-QIRQXl0VaSgnwOZ1LwxD321Tfb1jLOzCWuF2BrwjEkWq2IhxSicPOddUywLV7dRSO6mcU4sWKRdoGdci6gk0Aw==", "shasum": "31367580654632f87f86df565f1bde0533577401", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.0.tgz", "fileCount": 6, "unpackedSize": 78015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeub5FCRA9TVsSAnZWagAAOU4P/0+58SSWqm2sZAid3sCk\nPGRyYsPWsVBHVg9T1KsYWGyZd0RdLL4pvfQpJLd88FbR23Y0KRlH2pYalK6a\n+zSySCYnK4EJsrTsB1ttSNsy41C0SXXGx2MrZ6D9DnQGEkdL4FZF9IQo/kF0\n+nj91RmicjpEUbCLmepNGkUJNAy4OqQwdHc+WbdEg3PKpuA8XJv2qfWRsQXk\nTwFGXdbuUuLAABR1pzerK2X9UXJ1Q5dMGRdGDRCAjqBovr2kOTHC+8VKGmnS\nxOTdDaAoFaOtGgBTxghfTcw2ag3utquP8zkwaNvzRejdGAM3yTPVPKY8sJcB\nFN2YgA3FhWeMnXTjV0HEeOahZQKv/P4tPKq/pwiBPofGQQMlYx24RhoMbWl8\nH2XxRAOYNsH2AGoLbMIHLDkP7MxkqSFeOzJ8Av5P//Vp5XPfAugLgetTiCXP\n5+Skt8lb64x87BeYuWUWjUADH9FFOhQs7/e6ySTm9QmpiSxE1jDf8Z/9jV5C\ngFrw6aKurv6Q6e56r+lSC+4NFaZSHkfnJBZznpZRsWUak044mgBqwrtc4xSN\nPh9L8UjXgdmuPxyptaqgNpTkoUQ4z8xtMqhwhdHDxroYLyC7p/N5cEidXi7A\n+kHvVfsFVm9OrD0Jfbdn5POONk2TSgurU8K0/uYc1/fjjAgrdJ2Rwgt5Wa6a\nZXSw\r\n=wMJ6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC/1SMf/qnQzovECOtnWRvzlZsr7SjQiEYb2grdPHuOMAiEA33CklOgspSYvdymIjGMcymjIDgx0uDGIUUtDsZ+xZgs="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.0_1589231173238_0.030091403917154036"}, "_hasShrinkwrap": false}, "1.20.6": {"name": "@types/puppeteer", "version": "1.20.6", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "387ced1771dcc44376a66b524010e4f0067e18fb75642029780b8301084c64d7", "typeScriptVersion": "2.9", "_id": "@types/puppeteer@1.20.6", "dist": {"integrity": "sha512-ITkr6Z2qvCiDZFB3Y+VYt+uOzqxitr1HehAn+t/PoNNZAsX+LnovMaqFYg99pvkP18AvZdAqKNMhd8JaQIEWUA==", "shasum": "660c5888f6b88e657cdfa4aec5962e6f65efc733", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.6.tgz", "fileCount": 6, "unpackedSize": 76862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeusnkCRA9TVsSAnZWagAAoLsP+wTQ30Fp9FF54LPQdKhw\nlp56qcIcbT8X5BIxB83drUYiF1jQKIZ51IfvxDL/a1nidAgLUzILfBl+YTId\nMl4tyw6VpLMfH7LTLBOrPFJ0kMipeWTEciE0RWTfUx6vvg4N2vuocZT1LeEt\nxvx348txOZbQ/i2pPCFdqd109OKUJFZ5+Qom8cmnYN4Gpc9bJQsPlRei/iT1\nFnbw/Pg2O6ziBgpssHl7o+RAKgrrvTSVeFWcrwDQ3Mo4Cig2s7DhcBdrtAps\nYxV57flgphuH1HmUsMZpstVaUuD6rCQy8wiXjXngZt/fq62v94XVMMUZZfq+\nhpccAX1fbqM4GJz4mSGMlpbvVMeEZ0iLtoAqPr5v+/PX8pAwTUp6tL1QC9z0\n9oF46B14FtCr5+fjE19IOTswQ9abIG9Oe8GOJduoW6wsstMrwQ8A0DDEcmy8\na3Vn/UWXEZXuITghVeoL1/RYKHSlbEri78S3ulCrdkulQus+RFo3P448PvI8\nRTMPBedTxXj1cBT3gGgRwJxNSADv/D6Z2CiE8QFNRhFiJHsV1gA+Z27zypvw\nIKQRzRb2LVxgKTWjIBj8QeFtv2lcFsBNB9HNUxSTmP/Ypil3OP0RWE/JsQta\nkT2cjykgGbXN1gVznI8Xx+1n5QMSL3NdC0IMDjbH/tQYJRI9X80qNbLoHcJZ\nW6TH\r\n=gIPT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGHcnjZ+yRML/87OrHV4Ra4fhmmBPMckoaTaGJW2oI7bAiEAllOCYUCyHvNOcQIGm7KCQ2OXYJ2NnBlSpiaI8MI+bgs="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.6_1589299683772_0.5962918690384409"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "@types/puppeteer", "version": "3.0.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2e0bda566bb02fcaa5b1b81ec8392fff2019dbfe59830575b0764c38126cb760", "typeScriptVersion": "3.0", "_id": "@types/puppeteer@3.0.0", "dist": {"integrity": "sha512-59+fkfHHXHzX5rgoXIMnZyzum7ZLx/Wc3fhsOduFThpTpKbzzdBHMZsrkKGLunimB4Ds/tI5lXTRLALK8Mmnhg==", "shasum": "24cdcc131e319477608d893f0017e08befd70423", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.0.tgz", "fileCount": 6, "unpackedSize": 77997, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexCIPCRA9TVsSAnZWagAAfccP/Aorb2VdEVNBBC011lEd\nRFJmZj8zDp3GuzDkWWbv2cSehdBpGJFmng2t5v2jyCixKJ4yJSOxGP3fkEpV\n6JDdHJvs7Wq/fEVrNPtaUVQE1FFSznKXYKS4x7VBbxCxUTR5rg+pNmKbfWjb\neftrXGNBmBVXYn5p4BHgyoZUcGLalPM8DRoQna5O+kLM+RgqNHeaZsJ3IkBs\nyt0czluFMQ14dOibISit+5La7h05KfcnS28pwcMU+gNi/1LIXVzs9O5kQtFJ\nJp0E/Txim6yZG456FVKgiesObtgXxNcp6p9ANTMMyl8S2JTQkm/C3u1veJRD\n6NVYcGdx99zrHQvwx/MDUjMsltiR00FX7cpHZmMGutxuMOZLDrpVot6alr+6\nxLUFuBeieZ1CzpkZwU9Irp720c422KY9aRcv89+8uz3ErdrKs9eGU7cfQ7XB\nzhbd01RJn+DvQhRVQiAupPoupIjn6SY7HoIBdplJ3oY/MWGuy7i+L827yOKS\nkqDJyCF5Gfajpka/xw5s05CbrwRV2Nx1D9t8A1bPH9Qk9kBhAvxdL8BsPB9Q\ntnWEFuSmK4X7hJ5Rwe6OHh8GUfn6FZOMaQzb9BPQoAS+Gmlo0rF2WJGnRAaz\nv5qqzoUaD6naiBcrYn64D/FEqVPDLHnTm7BOEi/0ViAp2K7/oplT7ogqFNg2\n1yuD\r\n=CXv/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID25QIN15bC16CNEYlgPqqdMG+S8ABCYVMaRKItPAH1KAiAvMPSdK/Otu9PC7dUB4d43flEIYpNOWj6jRqWJLxJI0Q=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.0_1589912078894_0.8566456489528143"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "@types/puppeteer", "version": "2.1.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "f9f9b8c4ce98f3b7c5d27e75e1856bf628c51a86f5cc5cf978995244b534647b", "typeScriptVersion": "3.0", "_id": "@types/puppeteer@2.1.1", "dist": {"integrity": "sha512-FqPZvUtnpTGrqbHvPUn76pvVcBPEVEqZftrdOjr6YRkaaxkjKQ8dQLNaQBjER7Lvd1Q6+0R0XR+N3tYGWBSzNw==", "shasum": "dfbec9de3db4328ec9b66ab2cbb1875033bc22f6", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.1.tgz", "fileCount": 6, "unpackedSize": 78018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexCImCRA9TVsSAnZWagAA7tQP/i0/7tyloxTG6NF7G9mf\nmsz/Jh+8DZjX5IwVk4txvlMOumDebGGU42/OQ00UQ7rekcPuLny0Al+rdKPC\nl97GBIkiZOFrucD6AhWqRMo3fTLeFgu7jgdRtKx72X2+hHRrDA11zhQUt1NQ\nrzox/7t03llMKoe2S3VN4iobiem9L9JUhxxb7ODzOhFb64verVOmP8v4UoMR\n92gYtOIcUoX1INiW85qicdcnC7Cz0YETftkPWetShzzfTVBtCO/oPJxXGo7V\ne9dJKjd4VdZ65bXmb0Cbg9JSe99wDTA3prmH0eeXn0MQLBzS7zn9ihPeq8Tt\nkxk5aYMnNh51ChTGhwG336GaGqGgM0mwOBS8npH1LRybLvn+o/PdTvff5Wwd\nk4X8c5VGv9qRyYqZhBxYLDXubCw1eIv3sasalk+DbQjlqFiaRxdyqheOYmr/\nXxUSdgwAaCA+V1nbNmY77YuQb7JRHw8sc+NZWvXz+7dsyx5QpQw2fsaHvgyq\ntIYRtJrnhVosSWBJp+KMfHjx5lRTbRBnz7qaHM2F/nNYeMpNFCgFcPk7KYY8\nuMy2KXUI1Y/MFBKGMRkBLHnS57llhvBpT0EB3w0sDzqkaayi6VAocIALsVsK\nrlhob9qfZZifzkHP5o5FkbMTSb4X/P94cbfNA1wq+njbinWp6wndT2xa+MKV\niHSu\r\n=KQV2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETPMhGqW6Q84qiwghzvfA5mULDMyVu21PLdSWTbjLrzAiA23+SeutbmDzqwxOGgSLJpLizXlPgbS85o+aIzEviVqA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.1_1589912101833_0.6219244535168869"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "@types/puppeteer", "version": "3.0.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8d97b43c0cae03b787e0d0a7c3d19b0b078732b4c2ccf988964392dde12f9e1a", "typeScriptVersion": "3.0", "_id": "@types/puppeteer@3.0.1", "dist": {"integrity": "sha512-t03eNKCvWJXhQ8wkc5C6GYuSqMEdKLOX0GLMGtks25YZr38wKZlKTwGM/BoAPVtdysX7Bb9tdwrDS1+NrW3RRA==", "shasum": "053ec20facc162b25a64785affccaa3e5817c607", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.1.tgz", "fileCount": 6, "unpackedSize": 78015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe960JCRA9TVsSAnZWagAAOAkP/3xY7/4IHWiRbBbf/1Hn\nmPBaPU/T4WLhjf5BhY66hTpoz8+O5ZAB70JSjxSekT11cbpol8pBPSjlnSjm\nIzD6BpafJPeZpbDRnCrS2WudK3/ycnHHSSRq+YXvvM+VqrnXdsd8FhzcErdI\n4WVaMsCFPVXxqoEZOzNhbcBjoJp0sdOYDp+Pp91T4OPgmuPVujh2Yb3u2QlR\nmeD68OaN1S6UBzpNxFKjXExf0l9OQNMYPilpfmIx8AH9VSshzp99/75k9j9P\nS/QZ0DPb+9FqPGbQzLFMFYS6qEmUupzEpg9P63BavK0RxpyZTh/JDJOCAT0T\nwWHFCo/Lut3BJB2/AqhSI0VMSS6UtTqfSgZ9x/D/vG9kPdey5oRuaLhaT6Rl\niIPfFpQe45w5vGYP5m8A1ibDegTD6FXrJvYMHS8UgIqrUTCh/i5Rr489xBGN\nm5gYUicBD7zl7Y/a34mYfxeAvFN1TLJzldCiamjpy9yTkb+NRH4YPtklEEnU\nm7KvB+Ac6m71KyTuHxHrkm/bdD31ZqSYaiFbAO1oM1zbeAC1MphsTjPkFOqQ\ne2B7aki5yfq5QdPjWjjjpcZud/xsglcG9xMUTZuwgJsc5Fac47tNjoWM8xRw\nvkebxF803+5iD1/TUTur5HZPhLJa7UPGiiGzOG1Zzod29x4pzBE5gpvfKqIj\nADi/\r\n=YkoX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9vl/Zpd/34r8D/VzjL428uOUI3/CPXBV7NZdlnZLisgIgO6f04cIbxWqcXZvTcoJQJ3hp+ZT1GwQkIY2K/l8Qm38="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.1_1593289992678_0.5616348027122942"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "@types/puppeteer", "version": "2.1.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "04db2e3b51c50b070244fc7fbac59a77f260ec55a5adb1df97105990a591241b", "typeScriptVersion": "3.1", "_id": "@types/puppeteer@2.1.2", "dist": {"integrity": "sha512-rNPC<PERSON><PERSON>+PuHR11au5Qu36TK63i9ppcdvREyEUd45jGc9Yj4AsoLpiCm22ebwDDRSzNc1N2+XOmaa5i6ZSW8FOMQ==", "shasum": "75b870c7f8fe4bdf76c3bb30c997c3904611d839", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.2.tgz", "fileCount": 6, "unpackedSize": 78018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSDCYCRA9TVsSAnZWagAAmtMP/1Lx3Acpa9lRXnGasMni\nibINVUusSBfRXzbAARGPGR+oL3qCvWG3XgkSIDNi5PxQqvmeJVZV4c4aEuIU\n9XND6//UXTIwOzgLkP1tL/qVHGYq2WA8yk5sdSdbQfldJagkLfAbxdgwtxwl\nbFGwrWDqqLJYPWDucQBuN79gjcJwiqaRtRCUYc/v2BZe+lcPn3hEMSD0QpV8\nIun574GhqVVgJNpXeiabu7PM6EYKJHJ6jXSx9Z2J06idlUfAe9nsoK4uswG8\n5roQ4cFmdV7QubNwWgEA/I9OISnXym4QeEO5khLYFo939rC99ge018X8Vuga\nc6WA9zpNERL6rkQLZpg8HCgGbhPB39+MPH6NG9yqX/hokXdwcymbbcwaXuRP\nkGv7O2kq0VTZBUx5p7ZiZgG6vUvIyswUV2LiiQWWYcR1PF2ZLCbEf6V1lzEh\n2PppEHyXvyves1yScKsactN5VE54wwW8DeKJCKLUaK0edVaWLAxgG/nZPBbQ\nDKXnL65nYi1QvK3EdO1pehRu3TneFSjEyFOmWNAZeSm0FnnmZH2QY7CtCz1p\nc8GtpBJ7MEX1f5u8BYWxI100lOf8WGMfQ3/TvJs7B8c+No22PfJXGC0Q9i1e\n1exrFHVqZfWyP+fFgSyegS2/1EUnX93IvG4EdZHRYvKJPkswTZM+kUNj1CKj\n+i5V\r\n=V3ed\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhmGj2/vRaBP3xik8SVbVP8cRqzgU8ae97cr/U+0jboAiEAx6SCuAK0/tQhtlhg6bk5daNh1vNKIx/eifNJ6iW+Zzg="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.2_1598566551800_0.12127881010326003"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "@types/puppeteer", "version": "3.0.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "5bc4ea80623e07e431099060e1480a18445a5486842565787d8d0579a352540a", "typeScriptVersion": "3.1", "_id": "@types/puppeteer@3.0.2", "dist": {"integrity": "sha512-JRuHPSbHZBadOxxFwpyZPeRlpPTTeMbQneMdpFd8LXdyNfFSiX950CGewdm69g/ipzEAXAmMyFF1WOWJOL/nKw==", "shasum": "20085220593b560c7332b6d46aecaf81ae263540", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.2.tgz", "fileCount": 6, "unpackedSize": 78016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV/ICCRA9TVsSAnZWagAA41EP/1lmBLBvD2r7hbS3S1VC\n7Rxhc848dkfnAQHeENe2m1JJSx0cjk3hIcNDnb/7pgsMr2ta3DIDaJ85zzyk\ng2D9g/CVUOrMapeZVh26kOb4OYX67Wd8vh6gsHY0fUTLOF0HMEPtYmhb7ZHU\nmyxmSX6aF307JZ1/nMAw/JAOU5vSh11gZwfQk9kLlnrqO8tVo5yhf3ruxaA8\n+hCHmw5c2m8h8L1xOpBonop4msOXpLEd5gabOQEjKcXdUv2juxurCycX54DC\nZZKMdj6+PyBlvARTH/2UMidh+PqXyyo7x4dLbOE5FuaumBh5NFUCKTvvqXJs\nIpAo2Bu//nu3scibbR4ZsTrKfHs2A6yTNvVD6YX3qyTjfg7Bd3ifhuqP8k7I\nHPOXdlH9igskTvgJ+5nT/NtLmAhsq0UfKxKx/g2f7Wizejjk+jIpvbN1g/VE\n/vX54aFBUHJWtKjE9ut8YCk69TXO0YRxpGohxLWsoaBaSNPM2IlEgNHKrcav\naN/DFxQRzJ9d74lXIYdf1ftFoAWpt2JOK9ykyfHTBK5AtE3V0v+J6uB6ZyFd\nd+YnUouSGIbOl0NfUC00CvSJr+r7flmeNZwPYsc27iIYyu3R+qnYgx9r9ygt\naRjtD6fkP2Z843uMqIR+LYmmcHrrHvpi1o8I0zsASnXArr7Ha1HC7uY956WV\nN09E\r\n=zcDU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaQ/wpWAFKJh39FMcOiyKxhIzMjbJyArQNUk8x4Gu6aQIgflq726WLzzvG5e4mvUQwX0lb7BatduSGYDoOq1cJzi8="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.2_1599599106163_0.40405203454017835"}, "_hasShrinkwrap": false}, "2.1.3": {"name": "@types/puppeteer", "version": "2.1.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "2432b619541384456b2efcb37556ba8d5528f687b340564cd11d643865d64b2f", "typeScriptVersion": "3.1", "_id": "@types/puppeteer@2.1.3", "dist": {"integrity": "sha512-ZYRfnd4oj2fxdGA+uNn1cBQYfBJK4xQLIKmG7ONoCoyOhksk68e5mzFI/zgvFHSxX0Qf6MITb+IZlHFGS6ipLw==", "shasum": "6f9963ad4f70b717f2cd399cb738149db6f70b21", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.3.tgz", "fileCount": 6, "unpackedSize": 78019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV/IZCRA9TVsSAnZWagAAz6wP/jtTwZ7XzViPWK/vy5Ve\nHD3tfcY8hB1+4U4XqtTMefzCN5jPnMDWlmgv2baZvOG29wXYMevaMHr9bdes\nMbPLhPZEaSweWtTSgxEqqhzKJc/pekKVLgWU6tNz6sOCZ4jm5Zv1ZjheV5Uh\npXO9g0QZuSbPSRYYn4iir/ruIpiIZhkpQ8bH+ysaMROUhXjmOgljeKTWkimX\ncdmzy+3Q40MnGtoMfz/ZiR5wI10x0DA6AT+T6mobCJj6Qb56LR4+Yc1wCFhN\nRKZ2wEBZ9zH5gpdkWwAB4S1Jm3hMIgP5B4ltFQUwlC3ABmXdxIhHi7xDIA/H\nQL61+rRlJWz3TbgH88sZw4k4pMTKPvNaHDT4ozp5/x++GtNml7wvyrG4Lg2D\n678S1DRhN4rJD6fqV6C5tDvxY9QwO4hfA58csBKyDldYpsVmkJPEBv2XvKF4\nDF5QBz29wCb+Hx43Px9++UtavOuEOOQpwufRM1VNPnkwXQ2WlKppowJ75aEY\n84H0RI0GpqkAp+iHzAicgEYqta0FRpxguAOzqp5B5bw3BhyqlXfYxCknKeoK\nz1S1n34SAw+y63/je3VvMSSGaWbs6k7IQb/pOfEA41OjqIOAP0a2EaxJR4JZ\niGpeKbeR6DQY/tQe/HJEoyiFrmISO1hY/I9YQaGwKClk9ACeLUchI3j6sE6Q\nwVlH\r\n=chG9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6e5G03eIayptrhkDlChnXWaCVSyD20HKwVkXEKO3GvgIhAKPGf18+z6YDs/rYLFGJUyUqippBNVkHQfFPcejJjvTM"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.3_1599599129358_0.8361456019180826"}, "_hasShrinkwrap": false}, "1.20.7": {"name": "@types/puppeteer", "version": "1.20.7", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "97f0dbda562961bfc734111f515a78d8edd6e16facc8dcaa1a76246ab5cfa8ea", "typeScriptVersion": "3.1", "_id": "@types/puppeteer@1.20.7", "dist": {"integrity": "sha512-LCfP/Zf/y4I/hG8ARR8htPYa1wpLpUkysJo9TffmQssVz8c1b9uDNU4benDHSldiz7HVAMek1DCWz7KbqEUg3w==", "shasum": "31fb4274f0c6ec2e90ed8473616243f15a808017", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.7.tgz", "fileCount": 6, "unpackedSize": 76863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfV/IqCRA9TVsSAnZWagAArysP/AljlDUzYyuYAtyZDUBG\n9Y0OJD6NATKH+tXvGK+NVmVyIMquaITi8um6VKL1ppIKUSPIJYPeDPZbPCje\n7y1gv2I9TkWozhICV/GnojhMyejhyNrIUnXNGIIHmkA1nJgXzX10bpdJ7U5t\ni1rCoAIMc4kH/JIeRn4b5M9ekIoj4ae/XmV6VVYQjbYRrjGPvpsaZ0h1FtP2\nvsFnRYBtOITJpkShZNSbZNnBz7YJiL/xzu5qFbVn3WxwEE6uEEsI9e5knMCS\nXsEWYrpHcopc0+ul20qoZFXkia5dPcGMBD4HFdq2nS/hTr9S96aKqKy1DnA1\nnBGzmvfg+QJTK86CnYicgb1+pnZZtITnlSGuJfovvBJ9SuD49h/ksRlZg6Zk\nF3pkAWUSbUEAKRkbJjepYMCWN5s3Wt8ZlnZ7cNRiPqAv7T1N8cS+LAJ9Xl7y\nK56L0m7odBhjYJeg0FYHvTimLQhe6GfDT0Dn1zBO77OBQIdvXIkPEnI31Jtp\nxTxeZm+Cn30uDFM2Pv5ylj3XBbkELssekA3H/DoxiK83mUv/nFxpRvZCJ2vp\nBPm5ioaY+7HHyLAifWJRAKp6004DHD3VkLM/zfzDtO2PoAT1VO9f5CsFRtul\n/WOtr8GScjTQ2hTk1zAppmHaKAo/e3sJ1O8yib1x4Ehf686rupFgiybMtI8E\nu+Fo\r\n=Ei/G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB336rdzHMtr0Fdp5DWLK1n0TJmVhlTaNOTRQKl3QMLZAiEAw4MW+2TXvdLu86GKaty4HtIeaNnRNshtkto9j6HRHrg="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.7_1599599145861_0.04466070945117284"}, "_hasShrinkwrap": false}, "2.1.4": {"name": "@types/puppeteer", "version": "2.1.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "374403bd4f732e91a34aad791dd36709d330f6531e4a255bdce8a5f34d11d806", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@2.1.4", "dist": {"integrity": "sha512-IlJgM1wYKhRTuk5kT39KXCB+O1SQcCKEQTmjC2ilKnc2c76BpyKsdAO1kgaXRp5zeA6uZ5m4X2fdJsX7+u/iKg==", "shasum": "758677e5e87f0b0aa80b5e53e28a1a457543cba5", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.4.tgz", "fileCount": 6, "unpackedSize": 78184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaJ2fCRA9TVsSAnZWagAAq7UP/3fCd/mJ6+U8ld12Q1PY\nDs/MjDg0CeDhHRJfVP+MB5OI/aLCgSgOs974aYNGTkLyuOFg0eUhMx6ZO2jf\nZGd74WcsiB8ilLV5+UMdwb66VshHT2EcfEVLXpDzOs8EiRvLMYjnDlDTQndi\nQ6yvlalThh8IHxgrQfGD9VYw2vr1sJR331cko0MjoI/kX4We+RWSuW4mfrI0\ns1NieAAYt9C4BYo+tva31f8VtR9RxeYDQ7XmfvrzCxdA0ddg1V77Yt99IbQl\nHs0G0XFPuS7x5E4L6+ebkz4G5nrkW5Itu8R8/sOZQG+TA3KFo0hCEZmXa1Mf\n1esyd0RC9/v7xuXxxOjakfA7QBhLYG9u79JuN0Au4CrMUciMUy5CfB1RRaQH\nyoZD2DXbkfIg//jeVuERD27lNsM0lti6039COYaJWZA/w8KGsrqxPh2aWD8q\nmVL82rXVHoKFUQzduhAG8EmNOA5iQI6rCdITeZIVXr0MXDUXUgKci+hsxhQM\nA6sF1C+2pBKPOLau/GlCpscWVAhRw6/2vxiJ99rNRMmb3dR2HyDc0Ss1Xdbb\nSw3qggZD2P4R8r9a6K/fZ2aXn4iuR15ZopkCXZ8F5QzV8WMePavNfytzi5Af\nSKMWyiQlXFh0kywwj2P9oij1A05TlfQOMqURR+OUf73uAxvbAGCmslhc8edY\nMhPh\r\n=mZsb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIChclBkxEved071cEdD9MsbAGXNPQdVAbUmtVhC0r8eqAiADKF8G+H28jThdLHM6CXJ/NNEn4KigkuNgpfQFsJb+7A=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.4_1600691615386_0.533362570284841"}, "_hasShrinkwrap": false}, "2.1.5": {"name": "@types/puppeteer", "version": "2.1.5", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "dda910839132a69f582c1d0eb5772107d4aec700555a34bc1b92b09ffbd9d819", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@2.1.5", "dist": {"integrity": "sha512-ZZKAcX5XVEtSK+CLxz6FhofPt8y1D3yDtjGZHDFBZ4bGe8v2aaS6qBDHY4crruvpb4jsO7HKrPEx39IIqsZAUg==", "shasum": "9137aa87dca21450820bfd6fa4fe747bc8176381", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.5.tgz", "fileCount": 6, "unpackedSize": 78770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcueLCRA9TVsSAnZWagAAjA4QAIJXhs/gYCulAobjO/tp\nmq1+b9z1k/IQIM7aIQjdrsPrP7YKPbxwjgCTh4l3kINSXpRbzgd5dv9zmikQ\ns97LPB0C/UVoSl8EYSmzIvDPWaMHQJYgOoTYQpWHimi0jsXPjLlD+qfDq28O\nMYtKpbtc2SC3bNZ0JI2VoUMxLSBbWYsZvBbEuriyS9q4wXhhQSybpThslOLU\n7mhhLNwGCqrGj2N+YGrGTa80nbnwbrMSI8zZDR/mmyC6HYgFuFYbgMueaBmE\nMVCkMOeZxtpXUHmaOuoJ8aKdfCmwb/WH9cVASqB720hvlkVxYMGPCCPYUrYu\nSg7FZpR+e2AKCVFLYzEE/2e3pzic7rfcYT7FTAhRi2MjKsxtaTUV6n0VWxmf\n1EYG/Qu5nodl63sXmul3vWCJliNtxMuST6GESwrrKvtM60Md/pS9Z2ySnmS+\nR5Gw1XvPKrfuqJWIO7yOywDYexoSzYG+mXjIFmIGgmusPNPVwQ5zRZW7XfUi\n5yRkWDisHU3MBGUE6gxonors3+PcpEhQkO64QY/JaLW0McmUlssW2iSjeoD8\nP3+5Q3npBnQrYACkcZae4a0gh3k/dBHBFM93rDCBpj0rXCJyPFXB5E9goEuK\n5v9jpyjJAha7wh5zfy+1ghQsy+nOxZ1Sj69/eW2PYgmJbLtrpaW8WBNsGSR7\ni5sy\r\n=GEo7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDn/sYqriQNAhSYShFehFLoiYrcblaH64bUqaAj6hh/wAIgYERGGSSuV1T63RA4R+NBnOGg3tVMUVuOlOjtirsH2m0="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.5_1601365898676_0.7003333656375144"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "@types/puppeteer", "version": "3.0.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "b305932afdb2c9a75a4abe5e317bf4da4f5d3c33fc36604473e98644a9dd9b6d", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@3.0.3", "dist": {"integrity": "sha512-JDrg4N5hzKvtZsD62DV6i5h5hxKtZF+Q79YmGIacAVyJNcG1OWR0ty+e6LwqGWaoEt5lPeTtA2Z7OuTjUg6rag==", "shasum": "db0066e294cb9a1572d8e25fc68705e6d270ee8b", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.3.tgz", "fileCount": 6, "unpackedSize": 78583, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnC4hCRA9TVsSAnZWagAAC34P/RYHexNTbO5YjlmjtdQZ\nrZ65HVIYLWAL0ltNaDwih3YuhMXB6W+Pyljw5tIUF/fKMUdCbLiAX/FUG0NQ\nTh9RnCr7Kta8NXNy9+CQH9NShwL9/yxNq/UQereGrPs5wb8gCHHwgcHkZrOY\nM4TTjgPyeYCrk/rbdyvzwI8QLqFlheL/7fsnifh4tLfSWMBrpgxAriCjPhpQ\npaNGQ0qbXLaev1nexqRQnLTxwQ97KV3n7+NebweCJ0PvKLgnG7MYBK2RpsJ0\nC6nSE5VrT73vqP/RH2Y85Nv8+nJGzh3ROwmX+179j6SiOGEPwQAAm26GCdrU\nt1F5CuyZRyn/wNz66QAcpG8KdFBw8JNow1nqFp/Gk5H2mZT0h7XguMMroBH/\n91FSyN6QdNHJ3e7+xfWErHren2X6Ojxt/vznktngG7ZiDaiQJ9WvtEyV9Oc3\noRtatyFBIXEl14nfqvbHLXUPIYWYEpjshWqH3zef6pMSXV6QMW/LNvJtsHNW\nQGN05o+1j8oBiwiZWPvk6WFti4zdx9TuuVvz/I58imqwrXYefOOsLrTnEGKa\n6HFU6wtbEK3fS2BGRRsMaXJ9vppRU1ExS7ADCSWNHnKhcdpbx5PrlKSfLpz4\nQhuzoKrOjfgnpPra0cLTQhj4uzS2anFEd+jSh6sT0KF2AB3LXKHGdQcDXa0B\nYqAV\r\n=0+8C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZQxzeOydkU204kO9YnE3tqIKsvfb8USOsuBg2JWVuBAIhAOCmN2NY8mB0XG/mU+5tRSDM5SAYDrEyiHh05BT2uxeo"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.3_1604070944797_0.32366530182301"}, "_hasShrinkwrap": false}, "3.0.4": {"name": "@types/puppeteer", "version": "3.0.4", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4c08b94915796ff72331f7e2f2c7cfa775a1c7db12e4755586a014595d6a5aee", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@3.0.4", "dist": {"integrity": "sha512-wmkBg1Wn/wwgGDl9IrxwY9nJ/Hfv/YHFAqy6Dch9KYDSjA0lif8PA7j3Ls1iOfKWEMRIbHkpWIPoiw4r9Vq+3A==", "shasum": "ef42b97bc50cc8813a8055f93a092b60ccd27251", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.4.tgz", "fileCount": 6, "unpackedSize": 79383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnP7rCRA9TVsSAnZWagAAQVMP/RSq0r8HeGQoOU2nhyRS\nAJrYvVIIogBc0gfWkYWp18JY9IG1OM5JBHIuec+0zcyO8aU7Uzc9v05yka4I\nIceFzaj8EAqq0cC4ufp5IeIk9itIFgz9VYXmT0i/4cjxylPWto/bpeH0Y6qs\nR45MLKRTZsdY4kblEY7bHIUh9ehT6Zc+0RHo8JPTPL/PhBG4fXf4VU+v7kIO\nyvfdPhdu+cfd9UQaXOETkIBrme3WYckKx1P+itZrdoH/RqrunXoRLbdOvZGl\nO0kI5iXw7w0XVQniznXh7vS3WeGYWyJUcR233D011V4JfgbCK8j14NxrBUfz\naqdT+1Natg3dFFkfKhg9SpfH0QP98iUIqxWVQPM2C5sZ4Xj/6nOrxRUWy/UC\nzlVYIHpTinEx2zBlkzXzZFqISVNu6SZetZBBv+MPWAUuaRVOHH2Lvm2DzV/T\nxpnL6J8tivGZyjdaNHUkLC9cQKZIOB+2BUqX6rPX68cA7YLWoJxUhjgKR13x\nWr4JGAme+ZauSTKBIbAQ5miJPPrx27xDuZFrGHRVFr03TmXfbT/e1sG69rKV\nmx6D6sI/TGDrA7vxHcHEpGwRBQKbxErK5vaVAPqZUTIXMkTYYB/ZIXmbIlfc\nehcYTkkGQp2ZwQjY/1Xbc6siay/WcZjj31/HqGFmI6sOKjveD9lHQYPo8DFE\nTAYb\r\n=ZBjd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCatsCvaw1Zt1quYIH4tCRcweHATdaJVbSrywdLkf2JfgIhALb16purWXtZdWvynbVpataAfLNv+okyHIQzp/TM8MGB"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.4_1604124394511_0.22690139751619953"}, "_hasShrinkwrap": false}, "5.4.0": {"name": "@types/puppeteer", "version": "5.4.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "82d3f279e37fd1592855c3afc397937d8bd1fb582b70a2db8d2d8831b1f57f93", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@5.4.0", "dist": {"integrity": "sha512-zTYDLjnHjgzokrwKt7N0rgn7oZPYo1J0m8Ghu+gXqzLCEn8RWbELa2uprE2UFJ0jU/Sk0x9jXXdOH/5QQLFHhQ==", "shasum": "1ef860bd7a9dcf0c4633aac8c0ec21f75b431868", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.0.tgz", "fileCount": 4, "unpackedSize": 88362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoMskCRA9TVsSAnZWagAAMy4QAJA19A3+hcMkKhulBLVS\ndqQWywHozYmL1INRnRNmmYliMhF4NSa8jrBrW5WN9pcKq/HVOhMbvUIvQJ/w\n2I8OVhp4rxpLBIMhLucb+NzfPLHRLTNsaWfws1ezQSPDAkzORLfUAzNfveul\nbpY4GV0qcrYL86lyz1UhI9cKDbcy+m/x9FbknsLdNQsDt+I23r9+MfAlE2Zr\nyss9Cc43qsclFgX2IpPVquWOaMvt71YvBDh5vONUoVIDyPyfd9egyInb3WHl\nDsmgh6uwh8BdTjUO/mEYMdB2cNYaRk0bDmaBnX13WsCw0pPnjXDaIm0VykX8\nQz+3q5HEi8dkmMULfLreh1jjsMMt6T8FtkRP2m1AdlYfJtz99YHT5D5Mfhpg\nWjQp7dHJfGbMOJP6GUWn19m83hTbur1Y3hQi5LSjPSeEp7QHnlWIeOkjq33a\n3tHjYWsf2VUabPcWmksxbWkNNiLGhaiba9ig86j1nXtHhhHYsTBpXBQdB/pA\nS0xKA76NWQPhZ/3M4B6lKG9cE/eaOgNuKEui7AOTNTg1JWdHXi/TD8SOv/cW\nVV8rb2gjjyyKTx5UEYgYr46+9ksh3AnHN0Z2kuSDJExk4w7yg14f6F2dylv3\nADCvz4gZHgHFzzWVf/2RPfc1CvpBquipYwPMT+GVuzDrCxlqVv4qxuoMo46i\n4Y8c\r\n=iPY9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZeGLBXvb/TlJbFqFP/N6o5cMq+Up5ivFYNUU2jlu85AiEA4u3fUSQkSgCM5aIuuXdoKnnWwMWgSEvsPgfYg99RzhY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.0_1604373284289_0.04292165179830776"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "@types/puppeteer", "version": "4.0.0", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "8cbbf85d6358a5ca8a2ac97f0e5f979d0210a3b8d6c60cf0ef88e2443bbc07b9", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@4.0.0", "dist": {"integrity": "sha512-5K2wOaTmQ0bUz56zH4Lhwuj5PeNlRzgHcAJ+soWqaXmZuIoZW+i1/My18wms9P+Z6YbXWweWX75BCJUMC7LKyA==", "shasum": "8148ff79d0198a2f9440a8d5e052035575d9128b", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-4.0.0.tgz", "fileCount": 6, "unpackedSize": 82334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoMszCRA9TVsSAnZWagAAPA0P/jXtxj+9sqKakEIbdx43\nzfQdrqmA1o7E41mvtvYjlpl8BIw8Y5QK869pXEBaJcUpBEdqDIcTiCWRj2HG\nzLDFqAAwCYulCcoXkx+K1Kc4qb6BvzHXZn5OWK8bQPEQQ3ZlMZxEGnUnp0uJ\nZ2eTiNYetoVHk20nVmssqZ4+nyT3zCkVs13wGg+Gk09e814Y5dzdc5HBA9mV\nhSjNz0nk4IHhi9oyAZltVlpatGHDUHB7PFPd33rFVvpjgPiOYl+ZNAJ+lyNI\nHiuZKg+BwCTbPmrAdAVRWy+VgsWtny3G9f12NR0x9s6bMQEToY+l+8caVrYb\nPBMtalMJBZgUVrYSmo4bwg28HIqMxrBMldd8UkmGBh0DH3SKl9ESW880JAId\nNHejGLYlHPFFE/shrcMLN6e6GVxWwQVVx5AUVuO9CddbFW59i/5ztGyZif1l\nN5oyIqwN/pU0YePSqp+nex6puvrvPlP9Nrsp2xMO9kK08wzDsiCPdp7mnbwh\n3HHxusR4XMz+ovQp13JMwV0Goa8E4hVwJZ6JCUXD0nMcoo//eBpwOpPSESdG\nl2dhXsnqZIaJSW/6JXF542gcPjLxZ48dqTsxwVnPcLRWucwE6+DfI5EiO/Fh\nTq4thuKL8psgoM1R7ezx7EOaXzVJ1a3VpM6MjkelVYmIUYv2uUJoAwIzbWf2\nVmuK\r\n=KGiA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICHCt1pscIkAf/CVYiNJmXvm3T8GpBPjo7a0DvneFFHcAiB2iqc/RVSk5UuCtP2KSSL47dofL/GpzLEzWzIirUg9zw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_4.0.0_1604373299408_0.5558077781658193"}, "_hasShrinkwrap": false}, "3.0.5": {"name": "@types/puppeteer", "version": "3.0.5", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "17ba4e6cbe4416625655c4770a870a962709115ac76dc9f6ea18c510bed8eb6c", "typeScriptVersion": "3.2", "_id": "@types/puppeteer@3.0.5", "dist": {"integrity": "sha512-NkphUMkpbr/us6hp1AqUh/UxX5Tf2UJU94MvaF8OOgIUPBipYodql+yRjcysJKqwnDkchp+cD/8jntI/C9StzA==", "shasum": "5ef5d023f45c0dfcc82e97548891b11b6ce868fb", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.5.tgz", "fileCount": 6, "unpackedSize": 78019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoMs8CRA9TVsSAnZWagAA1NMP/0xrd508Ndu36kIWem3K\nSTSWjdGFC3K9QYpUVVRasNXi5hyrpckZotYgx6eO7xndx+sgTTo+oazfL3za\nIDVuDKKJQX7MtFYzDZD1MPXxWkX3KtEUCnIE4PFJ0nxMB3cSMBPq8kmTA7tq\nfyL33ZnY39JX4y5cycQ7kxwcNqhDpRH8+luwZOd/MCtJ+vTLYut8cVmUgzVh\nQpOKQfEsEW0rgwnaDqgHOuRN5RzP9efKrjwGhLOBWqHoBPrtDjRYpSePWi5k\n9yOOLaue6JQ9N6AD/UOxRYiIP0l+iyHM4URPlH69eSZIcOqwj3qthp/rJSfJ\nVIQ7Ma6KRncIwPqgXNMuY2qbFJFeTs1DMN1AhgJKwfQ//HMo7ckuqa3NjAGF\nvibTK1T4aZnx0QajnFOEg38BMmJmugMHsFlGu+Vcdo5OR6/HFupRkj1DfgP9\nRn48CbB+BBC8sijJjBJRaIq8PjjGPA/Q3B8tjaZC4anv0DSXtZrClMS4cEBS\nusa3JQ2zD+omgefM1xmcjWfin+29XfMFfkZqcedgTPMjeAHx9CTP0f+t/lSC\n8lwjBLiqyIc/MNqFdQ2F4CiNcouLk6Qwu+6lPUNdzMBNyyf0kVHxva/eP2gF\ngaVCSGLVQlHFOkA3z7hXwJtDCxU5EhTcfFQEp5Hzlt+FOfrRu5qQZLmD50g5\n0Wgx\r\n=xaEI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyM9EJbbni7YDHeKQU0kp/2vWQOmoVp2bsbOvtBqo4oQIhAOpR5CKoEXjI563DF1C041Cc2UR/6U6IocMBEca82Zsv"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.5_1604373307669_0.7771624073039642"}, "_hasShrinkwrap": false}, "5.4.1": {"name": "@types/puppeteer", "version": "5.4.1", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "e8426cff70a7fbfd3a13590f000d4b7fa1ec3746a3a5abdc8f79bdc82c9ab5d2", "typeScriptVersion": "3.3", "_id": "@types/puppeteer@5.4.1", "dist": {"integrity": "sha512-mEytIRrqvsFgs16rHOa5jcZcoycO/NSjg1oLQkFUegj3HOHeAP1EUfRi+eIsJdGrx2oOtfN39ckibkRXzs+qXA==", "shasum": "8d0075ad7705e8061b06df6a9a3abc6ca5fb7cd9", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.1.tgz", "fileCount": 4, "unpackedSize": 88332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyZkHCRA9TVsSAnZWagAAlXAP/0cOQnK/ovAxb7ojruia\nNKESrjOEYyC1vQxQo5sMntTn/5iPzhHXisiiTV4YNA8QnPiDkWMnPasjFye+\nLkzZvSqnEZGLGVL8v4Wcgsvq5UrbtIKRjsTf50WQHg1qSpw2ZdtPBO9qn2Kw\nUC7IskDsr0xfVj/46pqLWN7BEqOxcDwKtIwpROZLH5objY62ZQNaglKiHxcV\nJAafsss1dyeTtF9TMbcIxUey3sB1ql6PW8XY8z8SPT4T/F3zRTVmnMNop3E/\n/doInz6NM5L2zwupOGpHdrUiFXTial6mp+FXzzrwSaj4kkxDylszFLMIBhww\nzF9VbhRXbXv8kd8trVYb9yAdqpw6ROlJLHN7havLfIp7lXhcQljY497aO37R\nly8zDNozpfgD7dCxv13yRKDBjsHO9sfru+UOuIKCmHmLaz1NT0F9WJ4yIw5y\n9vdEB71ZBNcMBqXME9hDLW84Dyu5zE7XdWeoQAc79AXhZ09jf1hoWPkiIWtZ\npW9Jf7NfLzilXyk+uSejmxSaf5CwUaon4amfOshuqaKoJd6VYAR2nBrr01AJ\nYDtgpX1UoCuOhXHyKNOQnOXUdB/QLXjI03Nmp+5PfJliFRbFuKBKt5unbjU1\n6d27Ri8MA3SvJQuG3I7hgxGirHmLY47TZveQvdEUhYgKmYcwoWqdThMh2fgv\nYVGL\r\n=OxPX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBv+ZlOZnOAYBvFhkfnyGuShyJW9KLKFnFeKSU847DJAIgPu0UHTQoOTbBddjHFDTDEz6NEAoO27jCTqRWRLo2znY="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.1_1607047431323_0.9700063279483784"}, "_hasShrinkwrap": false}, "5.4.2": {"name": "@types/puppeteer", "version": "5.4.2", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "42ffcc09fe9a44c94b91054770cedd4bfe52265bd977a9d960bd667535a17da5", "typeScriptVersion": "3.3", "_id": "@types/puppeteer@5.4.2", "dist": {"integrity": "sha512-yj<PERSON>HoKjZFOGqA6bIEI2dfBE5UPqU0YGWzP+ipDVP1iGzmlhksVKTBVZfT3Aj3wnvmcJ2PQ9zcncwOwyavmafBw==", "shasum": "80f3a1f54dedbbf750779716de81401549062072", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.2.tgz", "fileCount": 4, "unpackedSize": 88704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz7cVCRA9TVsSAnZWagAAaBIP+wU3X2BPzTTRfdWngDqu\nUzEFK1oSztkLp8H7GYHjgqmmyntJ/velaFA/h5tQQ3r+c9NGweoM+cCvJ2pm\n/I/cQlxcDRWf4hIvQh1f0LPTMCViLdqZvXpztE5Yc4g8v0qZFmbikMYfwazZ\n0FZo4R0Afe0gjPvfJOwSxrlVk5HE6nhVb7Ynodn0dXuU4LM3h+5UbbZZ2wY6\ndm5FJzKQt7/oGJZomdTqllFXDrUXh0nUJ4GzTVrbDw2vq7xaiabKdK1r4Q5g\nf5YJj31MgiL+Z+Z2UfJqKnRhSJyvNiCW3puWhcymyii6r9DT7zSb6MR0uRks\n54GSba+8R6ztC4V0uwKUEQXf5YvsDG/y7BUY1VzYech3shDOLMVcjfEIuJ6r\nqxzm3uEgMBgGkQBnR+qpeRHAtThgWwem5fOcgb2gYdsyNtlTdwz8/sSs1XPW\nDL0hyU2StBrqFqa+L5iJOFUpSHvvUx9NzncjUhxwugN2EzsWXV3VboRfM3aD\n8L9tDmI8DAZVrveuGDAKK+teejc6IK758fRabQg47wea6eMtKs4zylCEZ//t\n9kAB1PpFjXZHs4UjzJAzEFQZHo5wm5TKiKVoXGPddNX5vvMFgsSrcBy7gY5y\n+I7WmSCz9YWJhM2d2WZIPkvX9VDqnVYj+GPxrT7rs5oh4KV+zv/7/LR7UaER\nLTRQ\r\n=R2UF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6dJsNp2u2vsUeRPJwRMIbiIARmPC4EcGxQ3u49rdgDwIhAJf8Ak5VcVlEIEJ+Tule3VF5zwNAIG1hytzSvys3vzHW"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.2_1607448340686_0.3274615731863031"}, "_hasShrinkwrap": false}, "5.4.3": {"name": "@types/puppeteer", "version": "5.4.3", "description": "TypeScript definitions for puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "d60698852c506a0a05c6665fde8774ef20e65c6c92b8fd665eb684bd21c1fb2a", "typeScriptVersion": "3.4", "_id": "@types/puppeteer@5.4.3", "dist": {"integrity": "sha512-3nE8YgR9DIsgttLW+eJf6mnXxq8Ge+27m5SU3knWmrlfl6+KOG0Bf9f7Ua7K+C4BnaTMAh3/UpySqdAYvrsvjg==", "shasum": "cdca84aa7751d77448d8a477dbfa0af1f11485f2", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.3.tgz", "fileCount": 4, "unpackedSize": 89256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGzuiCRA9TVsSAnZWagAALXoP/3pZVU7XWAHjuBWv5p1Y\n9j4YnFruVWl+oDxzKI+DJX4Jh+sm7Lts9PDVBVMYcpfkOGvYICnjDqHcA8Kl\nEMugtGjOIbegC9Pt17kYGgknSSllvyrMKbJBcxtlbSqq9B5jm1QI/sIddFIL\nJ/WiIZTOAwUAYdeI5MtBvka1/p7TY5booAEiyG9ETFJDswsuwb23b2t9ChEv\njF5BztmBTfDiORGivJL+MIkUyOKXL/jHh/nAiTuvjZUUFbnHwQb4t1hkJOLr\nJSnQ46kUQwpK18QlgHq9xd7xBxpHvw3FTW2p+zQWI1BaqIn+mKqTrFqwXvIo\nURO/Hl8/buc0MojzWDlU0GFhlPFGCA3SvTpGUQ3dnyfUH9m/OjZviuL68WSq\nSmUiK89l5gS+F314KEr62dz8l65ZbRGTj2DxTspUSTJlKx6ojKvPWH2dd2y1\nQLi5Q7TgrSMwB93PP+zgc4vxLS/7M7BOzEk2ns/7sQUlnJ/Q/lHHlQLFjESt\nxSn/2/oh/5hyEx7b6jOTJHw+JZ3ztqjNFCCpMWpqeRHLyWMeybpuJiGldC8d\nZG5CfEhiJfEBm2ARcA+l+583M4HPnatDxAz+yRdgxgv2Hlz4sc1gBNWs0uBS\nSDN/9uWJNmAMtuIRBH8G/dXMdI407F+rLwHFnr/9/pSeCFg/Pgrq1BjPnlvG\nh0i6\r\n=/JUB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7Z1ocNh0C70a3Pv1ogqAX7QpffqgpPPrs2k1FKxibKAiBAKf7jCiPPF4walemVcJokFuwD1rdD9WU7s3C3RFVk6w=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.3_1612397473885_0.9008193621040934"}, "_hasShrinkwrap": false}, "5.4.4": {"name": "@types/puppeteer", "version": "5.4.4", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "928c922237407c1662753dd845162761d112332020abd5976b7312c429873d94", "typeScriptVersion": "3.6", "_id": "@types/puppeteer@5.4.4", "dist": {"integrity": "sha512-3Nau+qi69CN55VwZb0ATtdUAlYlqOOQ3OfQfq0Hqgc4JMFXiQT/XInlwQ9g6LbicDslE6loIFsXFklGh5XmI6Q==", "shasum": "e92abeccc4f46207c3e1b38934a1246be080ccd0", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.4.tgz", "fileCount": 4, "unpackedSize": 90798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eIhCRA9TVsSAnZWagAAzAAP/3ayRyZtoMti1ziZ5T1i\nHYU3ShPggZGwtmuF0ApBp8NAAIkAvOlALlIadrLAvtMiqVLZlMDcp7fp5s5r\n7DrhrnSPT4wKWLeRk+PqBfhLbAT/GP+KkbZJ9W1crV8/P0nNhqtkiBwxarbi\nlFV4SOoNjuq6qaenThhn2hVTglGH286az4MeaRO+N0OdfXYqN3CMLn+myEVX\nvrxs2rpJBnjDjPfao1/aASDxnPLwZ/TfAJOvDmaS2EeSixqgdSYmxg56bDzk\nSIbzDo91aEXsu0Py/7td8FF0hqz6ANmh7PbvS4IW44tPOVIkrC1ESHz/Soau\nAV0SGGXwnnVmPOGPk+pIBidJrwHAmy6jDbzPtFiknfYgXkZLLILG5obMjmyS\n32S/YOtrPEpKkA0a4YxKP+OYOQOh4zxyRmV7d9PuBnRcur0+cDmBljleE+PF\nU87BjSlZSD0Iv8Jb8SdSBTGB40Psd++OA/7sR+5skwpjSHWTiW6kAgG5R34v\neOvZWyUasOOzYbj11tjaLlHoKL/laybZLVrwBvF3yMQUyAMbuqND1DC6MF84\nNwfqZIa0j8vuzamacEFebj/Kp9iPZ41in4m7k8oYy+kOOMiy2G8S2GlxkzPr\nilw+UyxZNn/zUCBFwpmWo1l+uAjCgzy0GAMsX6ar09MPXQ+5U6eAoQ9icWCw\nublK\r\n=PbR9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFxpvEsdedfzciXBnkhvWz+lUp5+9io7LwFs8yprddP7AiEAwqd/TA21j6u9veqa9iJoXrkbUgeu1bYvA4/m0q2f+IY="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.4_1625678369490_0.7487771770921292"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "@types/puppeteer", "version": "4.0.1", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "0b04aa904a62dbe1a1afdf868ced118fee0f8446c0994b63c13328e9ea8643e0", "typeScriptVersion": "3.6", "_id": "@types/puppeteer@4.0.1", "dist": {"integrity": "sha512-AgoyBI8JCmtbqlvU0qkssgCSBj+S3JCH6xk4+KLr1D46S/ER5IwqHpK3Qy30BMab736FpVD2ELVWkrtqPQg1/w==", "shasum": "c5964485b90869a9eb68e2dcffc9c7c27604a722", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-4.0.1.tgz", "fileCount": 6, "unpackedSize": 83788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eIvCRA9TVsSAnZWagAAT80QAIVFkMY+I5DKZY4emNjf\nLFxspYhbJClu2NaTumlUIeLi7/gc9boUuYYiwSK7w1uBBD+vrT3lP6BuS52D\nKv7gniz19y/tpSIF6xFLKuqvsAmKxhl4yEGGCoM3TXPEGUN/HJivZmbNE0ol\nnC7esBR0pkuvagmbHTs41CJWk8I2FOaSDpZsdmIjV/CEwsH7AEpn4NNCIHpD\nuQdWcm7tvSHKofvhzPmqa0l0jZKX4Gp6txLTDwUWvjksH0TTUgzfhE3Q3Am2\n2b0DBKKCBO2RBZvP7hmPnEIQDwJwzRTKGdzBhL1c7qOr+CEmsGFaVruM3emV\nqXAfv+8tMkw+8oCi9UClCG/qcoKySAE6atKamgDhnhQagnmotjh1pf3dv7ub\nJaGd/YxANH6TRJbk39WXpS6IVtfBM7Glj+8kB/BYV6BmxJxKolTTpNIam1QE\nSs1pyKYGAeE331s1W2NgOllFGTFU7fVKEC60WNaj5dTl0OOrfs8Wa45x/91t\ndCdMglEKQBL1ArtYgwtAVnElvGBDy3NfCn9XIbXyRuoLVzd9mpVuwy3hYdIR\nP0vCaFWc5zTc6Ne/Xapfyz+Jhk3e9aBRjuWfeT70aksCSZ4uBNWhSYLd2x3T\n3VhlcMmzRzTUnscuF+I5St2Tk6Pwwx1aLD1MHR3qLjwFQUolMAg0Qc81k90l\n814y\r\n=wfv5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEH/kPUT1VBRY0d+aWFMBIHDWIC9mUDwnWWngnbfLXDvAiEAhL7UX0x8m/m96+Lq4k4VzCEoMlpi6rqcHeraYIAdNs0="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_4.0.1_1625678383288_0.3036817318699976"}, "_hasShrinkwrap": false}, "3.0.6": {"name": "@types/puppeteer", "version": "3.0.6", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "70a46dfddde28aa0e01082118e1dbdbbc352aa7021be8b560f307f705800d775", "typeScriptVersion": "3.6", "_id": "@types/puppeteer@3.0.6", "dist": {"integrity": "sha512-RAKTyvL2MfxEUCTNVERoCypGWtxT8xX38iK2pCF2lQrlin9fHEfs3dkBsqt44xvtXP52WoRAo7uBzd6SNt9SSA==", "shasum": "3939196de07ac0110335a14027f71fb714a10ece", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.6.tgz", "fileCount": 6, "unpackedSize": 79473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eI2CRA9TVsSAnZWagAAdU0P/1ux28QbgRHVrwiaABVm\nFtXcBCJlkTJS2zDy5gbhYct0zn4jNi5DoPMdac4uv+RjtKzN1FgLg8v/phSK\nf7siITTMLswV6oOGOltgSjaiEjMPGmfLf3Zxqaou7bBfPv/MFRQudVUyi5vu\nFiIjFTNWzl13++Slrxh+FGSZdVxPDqLaJweVAXhnE75Wghb907vZPZD3vqln\nXhCzJNjb1KrJUlACeDfnoRpievafG149Vp0Va5SB2RFSVM1vbZZkJfOj5tfd\nJ4zhjxVDZOf0quE0frl0MyOSyfWd6ugcEr1mQj3aKtd6WNaK1BUrvyY03qFU\nT3tK1HebieBmwEx543xKdRP6rpYlbyzCP+95Hslq7eKUiLJYfy1S9PmAee8J\nUJYYzVWv04TyOgartfbzvBdVWtHuwgHP26MxQsiIkFBvCTrObuzHyuatn68t\nIoWEqY5PSGsgdhzyFMDvjyEGQl64vXy2M3wYYY1DLtgzonqlrbSYcePZrCaG\nOI7xMImSF/Xb23v7Zyxao8kF4cqVNGWDa+ExBzElTD8aB8FyrO+9xAOPZ75F\ngDCBPCVZ95NRcLUuyLKI8KAz6/JnGq7/7S649O6jCOjL3l4MwY8kHIFAF52u\nnPJk+q2kvyB1IIZ4b/3LK6RNyqrsF79A+yXK6Af4cIYaga97kPceoeZsDoFS\n8+wA\r\n=ArpW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9BFYNn2zaa0R3VLEfV81ExUMkB9pL7wC/paQVg9htOgIhAPzdSeW29ElkayuQA732fo1n3fsYnqGZ+jWYK2KRpekV"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.6_1625678390220_0.6163398771076065"}, "_hasShrinkwrap": false}, "2.1.6": {"name": "@types/puppeteer", "version": "2.1.6", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "39ec7f7668c07169a14c67e2fa83cc419ee18d664fbfb200cb780a4c2c7e135f", "typeScriptVersion": "3.6", "_id": "@types/puppeteer@2.1.6", "dist": {"integrity": "sha512-6+BPcokDgLouVh2gtYZB2coG/hhVenm4FcBEEHaNYvg+IZ/6tiRiyONHefEDo23CLSPyujgsA5AxMDoZccRrpQ==", "shasum": "21426c263fcd58cb98b81874c75fa8597a43f1ce", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.6.tgz", "fileCount": 6, "unpackedSize": 80248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eI+CRA9TVsSAnZWagAAZ4YP/Rjd9YuGiYesuB+8/5xR\nZbAAe2vREXKrPpwmSlqcMTO3tz0Gxq+R9VmdwQ92+TGBax6QBtL1s3q8VGGx\nkiA7yET+W8MQ3IUvUhYJ5ontY72+VDGSl/hT2PSc7dzrxRxoAiWkrjC8OZ+g\npBt3/gqP2tt7khgCxpds6bf/taK5zmlnJQHPi+0avoTpHyAFMHwYslx9LB1X\n3JTy2Z+Sjt4jphiPyf5tTGXVOApPVBr0XsWMcAj+wY17hOAkVj10bcTuMsX1\n/Fu/IEjWEx5UI1vlzjxhVDschpElPWPfknqO7aQvx1xkMEG11gfourCLh2de\n9ZFd3VyvKdofGB2UALK7fA0JkvUHgnyQHb0I6HMhfY96p4z8cQU0UTEdkEK8\nRBdvzGKyAMchEd8hDORFpJ7H+GE0qZYQPvUq2h3o/oYzIRm2G/2TFXRG7Jtc\nw/+m6ZlMnNxexYqf2sySz4TuvHJ6af+nR8bSbExtHevu7lyDqjWhqvhfDVMi\n2oXbKVh/HnvUgH5S2saJNhVptBwdXEJxCvjQfpb3IZ/JjNmBWYdlfmgQ88YW\ndf3V+lrw81aQ8aA+0OFkT0dZHnLPJjUNvNbrKJZvSbiXgLXNQvlatqW0V1zU\n5LHM32DGmtfNQRWnUBpZyCy28igsFdSOgujXg/kNVvVVFtaqo3imCSF3JPbL\n1djs\r\n=JGAx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVbtFB5MHjbU/1e/ryj4Ycufbkbtrr37qE0lpQehQBzAiAFs2DS7wNPv7eYVZFdJWddA8Fwc5m9SUb0NHdpm1I61g=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.6_1625678397832_0.4155034371123312"}, "_hasShrinkwrap": false}, "1.20.8": {"name": "@types/puppeteer", "version": "1.20.8", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "817d6dc5483b44ea82de2d6fcc95ec0c3451d5eac968d62aa86f472a5fef0f58", "typeScriptVersion": "3.6", "_id": "@types/puppeteer@1.20.8", "dist": {"integrity": "sha512-yJZzz9NeEmTxRGaZzUxUtBIEAoVXTtAx40mG8K0eDPwEeWyuxXKC7Lredxs6uNcgbvMDc8xzYy4v54jbbpoqrg==", "shasum": "fadbf64f7ac497e9248297beb6ed6a01705c0918", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.8.tgz", "fileCount": 6, "unpackedSize": 78329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eJECRA9TVsSAnZWagAARqEP/RPu2tGlvZSq0zGYGnD5\n/17L0rht2sxrMpuNtYz/KwAyN44rG1JN+76mISUEEMsBeNkVilV7+G9wMlp5\nFAN2RLS9uGvxFMNW99hSh6PvLp6OubV57bIzyKmPy37VqINdD9ymKmcrWvD6\n8Pf/pi2G6Q8EhreCVqRJF6pUkYmWYz/CVwxbyu4hl7X+shK1c2JD79bbSry+\nmsXsPVUbfkqc55MVGFSXHsmks0x0Blykp66c5j8NWQz8y+WwrWLeuhxMwmxc\nMeF9z5yeVDPVQfl2zRXQtSpIg74a65Ol2XL0dHZVgK9QZl8xvAAGqezYOtk3\nfqharKv/QeM+Dc6i/jDO/KHRzMIZvva7J1OjwbDAppWxA/1axysAKt7a+Rui\nwMpuQRzU+zwdhPoqXFXKsoI/SJCI6cPwDodNET+apViODfvl04HLAzZAach+\nS1VIau1AflpnulMJekz3s4mu9IzvaTgEh/Iw0rmD7WNfo41Xe0TzJj8EB/34\nF5qS8vX3pYYXWTRa+fDHJcGZUZaM6Wa0OW9yOr5TU26FV+v+5/1BoQGKcDiP\nbapVp76+fDHDMsoMRe3jr53mHnR/pR0uc6NhJ/5KhGz55s8JgDLT1mK/Xz2o\nDtL9F4LaBDyGAWtCVCnzp24TKDpDqA5WExMr1qdmSV2uu+TJsIp7jBfw0e5k\nUqjT\r\n=b4o3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCBVuM03BHQzAUzLSq66cuQ3w/FdgJtLygb6kP4SyM/JQIgORDvWh9M/xZ0m7oKAVLX1ymi6WsUrH/FHbdxLCMYfZM="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.8_1625678404585_0.5643182980885308"}, "_hasShrinkwrap": false}, "0.13.11": {"name": "@types/puppeteer", "version": "0.13.11", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "1ae2134301919608470437b7550da1be65507babac4aeeb3de0c4538e8f2255a", "typeScriptVersion": "3.6", "_id": "@types/puppeteer@0.13.11", "dist": {"integrity": "sha512-iGNaAibbMZ/Cv//tI9b+U6dUgrmbC+6HHuvPG6jIct4lenbFDoyskbMyC0gww9hTI1ZKBTzWqn7V5ORBf1Ozsg==", "shasum": "ddcce1f6421baf03fd3abe100fe93395fce2291d", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.11.tgz", "fileCount": 4, "unpackedSize": 43142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eJLCRA9TVsSAnZWagAALVsP/jD9uCjV1PwPwHeWimFV\nPoHq/bw3FCL+zUc/pKopWN6bwdsUXk/1mIYxDgRj6J/+/bBXd/5EY8LtE2YK\nGpKt2k49g7bFue29BqAlmIsbsCjzThJUyeq+APB71j4uXuv/Is3PPcriQzZ4\nHNaO2nsqRUQaH/otz3wQlBlRYqgV9q0DxFJxycwvj6LnQNKBjZtfXO+AJ0X+\n0NVr77Xuk/u43pB0Jg1Se8GVsiQg4JTm1a9Dcdjz4EQuZ+VU73Bbz0eB26hX\njGo13uowHexGvlcfYk1GagKUQJVghMID/F1xysyJdYGz9ArmxheTNMzn6MOm\nRw559DBd4PwxPgBYNwhiaFI/s4SA3IG//9Z5fxKp3YGV+/C1Nmr1HHoDHCa6\nL6/YlZo+A2fsft5R5iQV87M2ZGj0Bsl4tadYUBMSBpSK8eMKZcmMwjPby61a\nMBOtC9f0rDadqCjAnyu5f8rV/xoVThJheYcbzMzTH8I4q6mZPl5X4k+rlhO+\n2BItNvuHovnoRSDc4Wd6ZFNRAHe2brx6YU5xHVLkl/pdKQN2WYhzmNsDTIFx\nYcgiy+b2sk+oIQTp4zo3QCi5AMF3UfFTbKjlFoScopKiqS+YPRnGVOipAmLl\n+kRGPzkYc2Lr7MrkYSF3hzCs3DelPQJ6mX644q3UEcFSliCvw9ySNvlXhfX9\nsl1b\r\n=8KhX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDP4B4RrzeA9JIifCPjlSUTFjuBZAybg0QytJ2O7J7U9QIhAJQ8UZUYu/tQUqGEN6Se9LZJSZuMDyO49goIbfYnKkpI"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_0.13.11_1625678411389_0.7721460780121334"}, "_hasShrinkwrap": false}, "5.4.5": {"name": "@types/puppeteer", "version": "5.4.5", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "ec7d4660293d843cf4e01fc1b9dadbfce3b186f17d6fa4e111f08c2104dcfe1f", "typeScriptVersion": "3.8", "_id": "@types/puppeteer@5.4.5", "dist": {"integrity": "sha512-lxCjpDEY+DZ66+W3x5Af4oHnEmUXt0HuaRzkBGE2UZiZEp/V1d3StpLPlmNVu/ea091bdNmVPl44lu8Wy/0ZCA==", "shasum": "154e3850a77bfd3967f036680de8ddc88eb3a12b", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.5.tgz", "fileCount": 4, "unpackedSize": 90978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFr0CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7BxAAlHd1llBE9CgRN0CQt6YMX33bMs6fbDGXtGQZXENwdEWWOpxi\r\ncFFZQhO+1WQU31ofbCC5Frl3+qZqTZvakl2ihbxHjMPzBnDk/EhmElNl/alo\r\nze3ETSCobUPBkfkhbZKO3CX+4QY6NqCkI3gHI10wZROiTCTSu6dj4SNTtJxq\r\n32jMQ1QKBN5Q52s58SILHyTw5BMDYPNvJ6NpTSZUcamEsnXvAHkw3oAMIzTK\r\n0uj8B5xlewtV81wxAlCsylaRLhNSrbVmXNsU7pVtfkKr3dOq60B5/sU4pTMX\r\nCorGEmrKne44euV6yNjJMETzPyeRZw9BqN15/c0EY4X5tx97GBUO2pdREDjw\r\npmB9eHSpJTYTDUzUtCF+d5M/0OZOVgB38mcFxY4GBgeQOQVHrdrjaAsM66wQ\r\nNBUqXvbguH+0w3Hej1KqCXOJJGtoT739TUYofUyfahLxCOjFp1r7C/z3v/j1\r\nh3KdGfiqbjbmmGIGnukwx+rB641LHMh8zIW69/gidocPVEn8H2XRERKZjdTw\r\nk+XnJbr2v3A61Eg2+RjnXxOCsZPb5zAxIoIWkMZ1KZQnjBPlo9jTub7ctDBD\r\n72bqM/XxWNzWRnbkGO02YW1EPUWQiGud6bbfnUF+vvrtS/P0mhWx0WPKJkvs\r\nOa44NpPAH5ImgdLEfykX0rMNoZ/C2Imrec0=\r\n=LW4p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEaa42Wqx9dRgOY+UnM+zQEbDMhyVBR24xiducUXFiESAiAitR7rXakFfBh6+zFcRIcVbrWjYZzc2wKB1cl4XzVU+Q=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.5_1645657346237_0.978452639652375"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "@types/puppeteer", "version": "4.0.2", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "4b1732e19faac11fee0c7e822356999045627d4150f66f5cf5f0633aa63c0656", "typeScriptVersion": "3.8", "_id": "@types/puppeteer@4.0.2", "dist": {"integrity": "sha512-LOjNvVmJR9X2K7/hUJlt1VHss4VjNOLml27i21PJfwdQLGxxXq47mPRqcY54LR1J2IoFdyM0WFYddWFhFM51pw==", "shasum": "7d247e75b2200380723f55a2f98d2d2d88269626", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-4.0.2.tgz", "fileCount": 6, "unpackedSize": 83968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFr0NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjexAAjcaYRQmCn/29C6kqcaxPuzI3WhzfCaeUY0IlEawluzkdccby\r\nbI+8UFckUle3ZwdoXlJRimBhRomOl02eFv28wcaKGNGWwHbkRTNDZcAK5jYS\r\nB9LAu+hrWVknxwk2RGA7dEbOAGuuAbMDe1RhYSQgyFJz2FFqE2kbKS2k7rVU\r\nccb+YkbYjX7vye4mL8RSbipaCm2vhlJNMnp1JCMt/sZIVpWtU1uDP/NjiKkI\r\np4YgpkKDDlybYP2w0xQiqSWk8xxCZnXq5GNltQvzqp0F6mhFej/q6ydNA+k6\r\nsvSJ469r2fWjHMtFofHr7VIsSl504E/cT8C9JI6c0A9Em/h8rSvdewFZDM80\r\n6bcryBcgRFtjp6lo2agom3xJmDIZXbGlSNfYmbD+MOIy1xXG5TBvJKtVaDgD\r\nS0q8lm2tG+mkzD/fzcj2xw6MKD/LoxS8cGmHCJ/2qxXLjwKjbT08UJ4Ec+Dk\r\nmwHRLX3yJltH7wx0YG6P7ebSvu+3bc5Hr+ZbEfjqXMUDBsntpAWE1Mn0+LMk\r\nAWce+FysLx1eydBotNoZe3JN2wdW3t1w5BMiqc68baJ6omny8dW+OQA2G9aA\r\nWJWf0axFkwB2IcWtA9wsUbIudDcbuN+ayDTih0YGtMNZ0O9M4qgnCzAKHWjj\r\ngpBbqykzorT6bTdDwyMU4ecoB8BwKDGcOTo=\r\n=k/it\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC999N5Wdq5MsSE0hFhtWUua9s2IKPBce+oWQDYOpk2FAiBPI98GFKrTYDLTNS/jruiVo4nAo3gSU7j5Fzr7kp+YnA=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_4.0.2_1645657356945_0.6143199295627229"}, "_hasShrinkwrap": false}, "3.0.7": {"name": "@types/puppeteer", "version": "3.0.7", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "df3888cbf264fbd354728135c744e8907430880123ee5c56cecc293deb434bf7", "typeScriptVersion": "3.8", "_id": "@types/puppeteer@3.0.7", "dist": {"integrity": "sha512-fwmQHrM+CbJd/ZUD4IBXmEKMF3nPMLQL+CaTSfzjo/ayElg8AIzFLcJZ81nCFR47RxLJRdAuSydtlky7N1cwkw==", "shasum": "43e5baa3a2d515d895d5a01b50b2b523ff76b966", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.7.tgz", "fileCount": 6, "unpackedSize": 79653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFr0PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppoQ/+NwQSNiPUpXlUS2kCjaU7cZWxBGRPw3sezM45BCVkPm9lkVgS\r\nsSyM6caudSGGcsIWw+bW7pQ0Xnf6USVvqi2zlrq3klJFgq+1QG2OOiIex21w\r\nQavsMJ1jvRTm4tOaGdoHr+72/BKjK0ouCruCOMYu1rp4EfF9tlRhS8CGiB5n\r\ncZZ9yJ5rdKYXARq5z67jdhj+Am2l/fI42cjKfa2lmh6viy1loiyXbopqtMEk\r\nlx4v811447YMHu/5Tbk+nojsaxR9Yy6PbD2nfOH+BbZug/E4PPN/xc2yFlM6\r\ntcJHTv2g45rQ8Kl/SbrbYzQKeK9lpdGgjeb8Dxc3iwmHjhnu84RtLA0PEOUL\r\nZcAbL3aDHfebrILtnQzGLDTLZtT7iEnKfxBXxV6lpfdV8zPz00ISiJ1KP7rk\r\nideLUzfyXP2WN+ZMekJnfMSZ08S46eAF/t+gSVCYe/iSUvUFP5bUTmo12pmE\r\nVI3eWrdkAMbYfZjb765RfEz2FzTqTp6EPCD0vOSNoIHmh9hWxF0Fz+miAr+o\r\nE/P5qduWqeOB0Y0PgMuhBuLOO6ZqUX5HEIlfhMgg1ZsO+QAU7gC4NY3qq/Hg\r\nKrsT6T+kKoweDaP69Uh6+hMmyHBgXz9Sl+xJtRY+wuFssrjNsJ2dBLRz6eJg\r\nf6fjnKphyi+gmldnyPfKWVRjzmf4Ov3AeJo=\r\n=diZk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjqzfyrWv7MUYUKeGNED39QBjuto9NF/+S2XhxGp082QIgNNaREoL43Ljc55gBmQ2mjjcsSAF3HUb5UngBV6MZ1w4="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.7_1645657359402_0.42538442942928056"}, "_hasShrinkwrap": false}, "2.1.7": {"name": "@types/puppeteer", "version": "2.1.7", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "de0cfad165de45ed195b15cada386bc6a26b363ccbf61529f7834319df1fb532", "typeScriptVersion": "3.8", "_id": "@types/puppeteer@2.1.7", "dist": {"integrity": "sha512-Y2FfdxGxGMEPXmN1Dc88b3OjdoXzR40x6xjIXhMauoZQtpe0obd+uqeP3fCmSWJOxEwHfJEb0BuWnEt5t7P2GA==", "shasum": "a2260675f87bb63b935735bed59b4a41fd9a812b", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.7.tgz", "fileCount": 6, "unpackedSize": 80428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFr0RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFbhAAippbnoFNzUvFkxcHcfI9+nsAg1OvN1ae/2DQN87cw67ju2z0\r\n/VX7owM8W6ajVkE7hBak7jLZQLRRSDu1OH7Buw8W4NqYxFOQgni0U735v3jl\r\nFLdn6BZ/BHX2hVga8h5xVq6ayn5a/r0kZ2lsVDkDlLmHLL0OK4cXOhKIACAq\r\n/tk9e/VqOAU6J8w4O45lfSpXHv6eZABxA0TfDFe5TBQcek6MmusG4xCvZWpR\r\nVFmPNJ4JD7rPqJZlUe3zJXkFKJdJBTpMWA7QSH/qfhQBczHy76ozEhw8SM8m\r\nPMdkHIQmeI9BM8GFTycsQdYytDk0qmISizkHBlBUAX4hhTYQPDx5j9m/izqV\r\n+Qwr9EjBIeJE/JrgiLItCK4Z6pQTvYPR4eEqyuibEIu8xkRajZlhdaiA70nY\r\nEFqQvcOynW33Kyls3E8DMc4QIEfKFVUwOBNTsp2a7qhG4tV8xEWMwW8+uWnu\r\nacb3eRz1lYyq/9Iw4HRt4XhIr9WccgzMoRPm6Fnq5MDqieSa+3R9J1kFo0cF\r\nItzj9x/5JXRAKFuxDhlyG94bNBxqvd/zzpCHdU6jQisRHVfspVGhw6mwkBWu\r\nfDXRIXw8gcrGsHAlOh1rCedgmtcv2zZHvyZs9Ov1FN/oSMgoka0g510rZBMp\r\nM38TEEslsSUX+7awoxA8hCU5pI9A5QL2g9M=\r\n=8UB7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwLfpxrGTcEV53mVS2EVgf7ysPHTpeS+vzQRp1ObQoXQIhALhx+R1fbnET8zdhNfSv62NJ4vLRAIyFqNoR0LM2Lc8w"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.7_1645657361146_0.21135038101970638"}, "_hasShrinkwrap": false}, "1.20.9": {"name": "@types/puppeteer", "version": "1.20.9", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "c3e6ae730d65513ddf62394527135bb2e93df22fac479cc983206ff18af514c5", "typeScriptVersion": "3.8", "_id": "@types/puppeteer@1.20.9", "dist": {"integrity": "sha512-AkGPQLSk6omeKW8FWMQRJTTTcEYVEtlMVEDTFkwD9cJFjd8pYTOszk/deiFnKjV23OO6K5EPQA7y4xKYqd574Q==", "shasum": "21386839dae3ba4a94eff857e720bfd3c94a871f", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.9.tgz", "fileCount": 6, "unpackedSize": 78509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFr0TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5jw//TLIZ0GnH+7Bpm8EDvGkHCunnC6Xx+HdH8+E5kEGOVIzYhP0Q\r\nQpxHqnOrGA4NXuO4ml3ZttcBHLR75x9GBYKy4ZmC+0g8E2Dy3SByCSSy8HYB\r\nVqf2vJOIC2tzp/6x9z5v6jOn4rBf5tB83p09INzJnAa81HXYuGm3R5eAfU+B\r\nBs/w6+Zib+HJPmhlu/t4DAKKW1BQe0YRjrN0qU7TyzbYfig2tyinHUtfRtht\r\nS1pcRDDPzFIqSQOvIK+oZGYiFwy+sGDGY8tXNEpr8LBEz+nM2gWXCzH7JquD\r\n5JMRNKY9HTo4ZRqW9zMLz28G1VOhrDGArD5VDcuV09IZERacKJYLcKL6d0dy\r\nLG6ioxFV0QVpTGcGsSAFhza7JIqpvhX9JlF/S5WdGrgR4JF9HtzROAnvEiIc\r\nJTjBPsYBLtYUagREtn3syRaLEd/CQrvkXrONmW+KDnskdT9Rfd3Ge/JYEu7w\r\ndaX+6PGxV/i0hXB5riQmQ0mRrK22bjWC79kMsT0VsYshq8fwsnace0RSTsrH\r\ngGNpect5WgY+G/F82NpiNVrn0ChK13tioS4ThqU4opQ9Jogd8z3Kkq8EdU3x\r\nRCnzcFg4nov4U76a5Rlxm1bZwvU1rxNJB6INKmWm5HdMS4meivB5MtcoLU7+\r\njNhYUDsKtbEWjWn1Qd4ThAXvmmzawhUdt+I=\r\n=qih5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF32idRYy7sicLL/9kX+P3jJwkRXKGaG05VpmrfjIrs0AiAncmQxjJ5W08r9UIhNaIT+/YnrQzV8V9HeZjRZQdnbcw=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.9_1645657363749_0.11302767757921028"}, "_hasShrinkwrap": false}, "0.13.12": {"name": "@types/puppeteer", "version": "0.13.12", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "dbaa605007228b7ce9124ddb8a3874f6803c711c81174cedf476cd381147070f", "typeScriptVersion": "3.8", "_id": "@types/puppeteer@0.13.12", "dist": {"integrity": "sha512-KIO3toPLv5WoqeWFRNUkPI+mmGa95ice5YhF0LwJUVhyVOzCXOUNt7F96xvEwAWLS0etmhz1iL+d4aQ9CkgGAA==", "shasum": "f4e8adccacfb331db240de83a07266b26a18ab10", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-0.13.12.tgz", "fileCount": 4, "unpackedSize": 43322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFr0VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtJg//bdWWXBNTpC2Gc6i6oI5ywslEjhs2lJxeBKpiIcbB7xWyHGz+\r\n66yoxCQL4P+vzfpYWhOU+4ADm2jNhIZENVAH0WESk8Un9XOf/h1Q/eGm1cG/\r\nioCx6RNE3OjV43pg/aA/YI1RtFe2SvoYbo6vt2nfQ5YrOE4P9trj0m6/aROH\r\nrUsGcdm2jZzMvwIRnVlqJzRrL8svRj0wXIKa5/kfjneOtiLN8Q/A6686JEVE\r\nBiAwCR7PHZ35m08bnYNlQnNA72teiGngOYqECgtdAKUbBZfb0p4rQHojfXnn\r\nRY2TiYuls2MFe1WZTeBwySAlfYX+UBohu5vL9RCxXJ7ZMQLlGmTIhgiZrzA/\r\nltygZx/nRhPph3Eykv44CDnwzqsc0luwjX4oS9O6sV1CGcdhaBX1tmrCiS+S\r\nDEuyMDV3HVD5LhCFICSSQ7bnPUDoS4tLmnncYErqcam1qjpQzVvwx8kWdiL9\r\nYOyKk55iKbLMzgKJbDf3v0TkPPDwdmviWho+WcxW/swU4M6aH7sJHj/enXUP\r\n+GTXGGvgqmscIVJHKmznaMPyTxq4K3FmvcyIGrmCr+U7TAAznKtWAV9qlB79\r\nmP9OQhq9K3weVDm1r00cl+QrhrarioWKrJMyb8ZMjx1QrgXnm23wTbngy25k\r\nl79JcTBXNjtHXP7/Rv/G5qopQPnLh0oLdQo=\r\n=kQ04\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHTrafj1EYXpTGRJVx7nlFHhHo6yQrAhgJyJvJ83mRoHAiBdTbubPkuLbgABrAkj1oMywuj/Bjj14tXtu/D4rDiYpw=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_0.13.12_1645657365152_0.7535709781107427"}, "_hasShrinkwrap": false}, "5.4.6": {"name": "@types/puppeteer", "version": "5.4.6", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "66fb35a9d369d855ef18bf3fef3034128a8596d499b1c2f5d4161f204d8db0f5", "typeScriptVersion": "3.9", "_id": "@types/puppeteer@5.4.6", "dist": {"integrity": "sha512-98<PERSON><PERSON><PERSON>s7+/GD9b56qryhqdqVCXUTbetTv3PlvDnmFRTHQH0j9DIp1f7rkAW3BAj4U3yoeSEQnKgdW8bDq0Y0Q==", "shasum": "afc438e41dcbc27ca1ba0235ea464a372db2b21c", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.6.tgz", "fileCount": 4, "unpackedSize": 91038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClsGDE1JmNnkY9oIeYkJOMU5UR4ABh6/OoR6zv8o+yfQIhAMRynbUr98r0QxHslc6WArvd6sbPcjrQ71ii/wiIQ8+7"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaE/GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYmQ/+PmdzeFntrUMyy4HVgBD7Ikva9Tb6MOwgztH1mc1zZUSQrVTR\r\nQ6Kfqa7FW/eExEwag+XqUZZs2be+49NSCg0JEqo0IXmpEnW8MgAXG+cUomH8\r\nMgz7IkbybQB75PlFtBIPrOkLcmy3VBRWlBrX8JZCS2FFKgtQOCttzGjgx7LG\r\nnymAhwmL1fWdWSoup9nLxRoWVzJumYltGWh2XSDn2+uMUvvnB0hSDC7ad5JK\r\nvdv069oTwfSBF1xaYJYGgFMKunCkTrUZsMeUDtJpkG/TBqnbxkp+G2Z7+xos\r\nyJ21yA2QjDu9c5Pu7d4MUFfv1UqBw54XIK8LI8pWTjmv7OB34rZLEIxrTkIf\r\nQij5uSA933CYPHrij8kbQNgrBJH4SsGE8AS9pDsdPWxZeup5hh8SvP5WHCuq\r\nno6X96FWd17gXSIxuzm2jT/yISiImyKcBGxffpbF3STEDf3hIvSZGATtkww3\r\n4T7lWbVG+eKBZPKyWC1os7NgMfNtQU3kKhfc47v4RY6nq9yp+crHNMY9kOz4\r\npidDk6bhp6UbmZNU3VhGyjnCM/7OgJJ/2eVwIj+NR1ww1XGRAEHBxZAPygYJ\r\nms1Yqa0DujjDX8ksvBdVi/1GArjN41Ib+BAyUpzePa54ssw1nxzU8ImJ1FiF\r\nxY237rHVsi7fl5tWsrg4p0YPb/TJcnAAAGI=\r\n=1XgP\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.6_1651003334602_0.5573083240848744"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "@types/puppeteer", "version": "4.0.3", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "7554ae86e6ba28a961e218d226a1c6011b3f8a5b7c5ba3823b06c4ab5e4e48f3", "typeScriptVersion": "3.9", "_id": "@types/puppeteer@4.0.3", "dist": {"integrity": "sha512-SZp55F61SFKigTtNvHfoArL0T9Q/mqEBg4FvmkeE7KHhKBmBGIjwgYcNDDMkrBWbq78OjAhYXbzABmvhuZxOfw==", "shasum": "d05dafdfb2e22e598d9dc68e1a72dffa2d4b168d", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-4.0.3.tgz", "fileCount": 6, "unpackedSize": 84028, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPGJojQrci2XQ8xe3tm1uM8F87m+YbTUsH5WXxaVxvWAiEAi1iouFCm9Sj1ONt4/kmbYh/fArSB0TRkOfKb1SZz4rY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaE/NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/qg//RrnMOP1qqfoLbQZvIIIR2vw/R137IT0WEUmu/Wc9XiRzncdi\r\nB60EdrLvzTMg4jJYNebgACANkA1plLhA5sxL6IMfAfpCkkUmCmdtt9OeW2V9\r\nfSCgAxEf7B+PUHLumtfI8mXmtFNKTLeBPjkgmW1uiOkgfPu1HtYBdXUtMgiX\r\nFqMl+huNuXv+Nwkmmpx7B2hnXqeCBfnhqCGOPyr9VSkClu3TiriPy7Leh+cR\r\nhwMXjx7JKMqtBoeGbAtPfThpLf6/l8iVxuRbQcLkLJh12Ue71gt+bmTFI+ht\r\nXzGgq9yQBXLaB3tm7jVSIqd3EhjtHuOFJH1Zm9OAh5qhsewcA3i8lAJJDVnf\r\nBLIE87dVtPN+FQp1AhveNHBHm6wPl3VDKCKXVPqcUPZTMAa1vazA4zQFknu/\r\n0XQ8oWJRR2s/2kWhIVwNQt4vaF4K7U7hTjR7MNNRr16TxnJH45V6Yr2VOm28\r\nRObFbZG2sqbjtyhLVTmnlMjhoEu8vlF8DCUk0HahGozqnOc3zBEKdrNfRsq3\r\nwyhMoksaNW5IhMucjckYKd1/ggEvbPElNpdw0w3S7thaK+bqen9Qe+tmQ4Ow\r\n5GBo5jBh4g9lGsBGHlkMZ8TZt/HN/pK28N3qCr4o8zsdTbp/wPNrZwSwXbNt\r\nAVYZGymudYksDvyqlf8ZDI6ooGMTGc+F4hY=\r\n=IR7S\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_4.0.3_1651003340974_0.7997086387751318"}, "_hasShrinkwrap": false}, "3.0.8": {"name": "@types/puppeteer", "version": "3.0.8", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "d84925f0b75040d9fceb4f49306cb18e471fa0ed591ef6d99723ccb216c3bdd6", "typeScriptVersion": "3.9", "_id": "@types/puppeteer@3.0.8", "dist": {"integrity": "sha512-YmaPF3KLyDNJfTMlbONOWsAfUh9j0up8xA1G/uyz4c8yD80C2VUM5OTjf+sAS89EN6A6gas0oEzJThICf4OUpw==", "shasum": "f5431d58bd99d6ef9b6d93f0cc5a0f344559b6b3", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-3.0.8.tgz", "fileCount": 6, "unpackedSize": 79713, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJSVK20i5zopJ9IMws3MdoM82t6SMm1PrtHQYiHUD1gQIgKlJaLr6463jYdpuA2egQ36a4ROgeeKblzJjzwdFhSoA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaE/OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpl7Q//Ysy1UFZfBB2E97njAy44lZYmhUVEpNRhqfJ+FiHJtL+IEN9z\r\n2qXn/cb0D23LrDx8/idNvl4w7nwrB7aTZWMT0tWoXkKOEzt0J7ODAoA+d1rp\r\nZnQJBisXcIWarIbSQdBi4wkpS++LFZBzpDsvssKsn+fPrr0L2fQO2xdnLfwj\r\n1mHTjMpR5Azipjl5Zi7LOnYTHDN9sdnCk4PETb5Ch8MRAVTlGYqSfme5bHzE\r\nuyaVR8+4iCsmmF45HRE7E3Uej0STpskLJ/rpWfisbsLD4QPc3rhTkjIPPtOn\r\nJym0Tr4OtCgjWJiJmIaV16R4D3GKhb9vHCVChTFS+c8uoMg9rvjaugycvrDz\r\nwuLAlo3LE+o9Pl59SJ42DJdVgo3c09K4ItTMoitU1axcollDFbVsCdXVAZ0h\r\nAnZ4t95y0lTcgsN9fAC9i4yV8Kdh0BBYZBElAEBtFeQ9MmoLQJ0Jif2EDVP3\r\nk69L75//M5Oq/xIee8O2hXSUP1Lk3iyRl+OTJgPzHqIdVy/GCnpWh/4CZtCN\r\n/Vg7ddGxpr/PI42z8JlVP+5poJadI+46bGPY084PETaVyHN+pTDKDzDgvGlj\r\nm0UAqmvBPvOvVIZJg6hPOlv/9tDHTSeQgclqyn4bzZQw3i0cvoxbl4N25YnL\r\nh2/5m+ubwl8PX446pUSixv5STL6MWXl+XQE=\r\n=F9ew\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_3.0.8_1651003342571_0.4189223975412353"}, "_hasShrinkwrap": false}, "2.1.8": {"name": "@types/puppeteer", "version": "2.1.8", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "6520cec9cd717aa6f70b91951fc4ac280c66b352df52f0b4d28efb5b4d360285", "typeScriptVersion": "3.9", "_id": "@types/puppeteer@2.1.8", "dist": {"integrity": "sha512-sFyFD1yIlBwg8jpCbmi4ngecMI6Uw6iEKyUUglFXOiaZQHTvE8oftAWKK7E5sac1Uce+7uR5iVKWDLKDxmjcSA==", "shasum": "c880b35b7d34ef85806deca7032d959ec5af8498", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-2.1.8.tgz", "fileCount": 6, "unpackedSize": 80488, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBiJw8IKpDeZOUtinBomOF6nWHx/xSjb7soq39CgR1/MAiAlSA1eA+WyApBqjja+3dvAzwsInKjx9aoXrH8mtQTUtw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaE/RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr98A/8DebFUcVOpYPMnvH1cZ40WKLTWoANncyaP0BlmR00p0Mh+m9s\r\nXqbtKxwa5HBgd6a/9sQsxdM/s5s/yrkRV4LVkff0iTrP68EuaDG1leT7Lzso\r\nkXIsy5LRDuZTzj4BNO3S3Cb4T2gYmiMp7leWj3O+/oFbnQko4mKVc18TfYES\r\n6l06npmYtJxelHDOUeh8hTIQYW4SwNz5T/CnZxugrrHR76AFLV2BlNxmDnCr\r\nYd/XwGEibqX57I/YLm78ysTXkRB5EDUe4dCT7WsHGfTjVUretBzXkB7omMdW\r\nrVTa5S4c0L/aa5GxnozMOo09eeqd87JUoX3NmcJW2X0wzYGnYrda5tbOXXC4\r\ndZ0bdYlBzCULWKhpJgmVr3ivRJqON21bUYO4hLD2EoW0+QsvX3N51EEJt4kn\r\nkZwqQL9QyDqymqofQ1WJMic1uOwFY5tNArukg9i5aMCUQ+WwPzfWdTTFH+Q0\r\nL2tUrlcJ279Q5xFKP+yWhYrfEbloPq+B0Rj5tRJ9af1qUbyyfZONBDzOGIc0\r\no8rEndKKtgxK3KF8XpMerOIXfC4ZvWhoRXAjYAomYhOuxqm5VOhEWOun0Son\r\nGwJm1ZpfoCeIPrZ1hIT63gs6L7TnlnxXU9BpcsgHsAfNC5SErjXXq0pYQGPm\r\nQs4nZvBbAMP+qqSdcUH9tp81L3oSFxCU1QU=\r\n=64DA\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_2.1.8_1651003345001_0.20204670701981597"}, "_hasShrinkwrap": false}, "1.20.10": {"name": "@types/puppeteer", "version": "1.20.10", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "f3bab9c36ac127a92313a76c3ab3787ea86d763e9761f4396f63a2c49beae289", "typeScriptVersion": "3.9", "_id": "@types/puppeteer@1.20.10", "dist": {"integrity": "sha512-3tlzJ9+z8rMvBDzHPv4Q4LCgBwDRvuoMxThkr+BqcbbNjsBkL/enFf6m+MDJke7vJT4i22ojB8yb3UpibiuBWg==", "shasum": "dfb83a8775e22fbe81eeeeeb9ada69c1e6bad3d6", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-1.20.10.tgz", "fileCount": 6, "unpackedSize": 78570, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+7BXL1vjWzOPCclOtJ9msM3yUfCa62on3DgNwYIErwgIgYcvCPZPPJCvR+Bs+4tzC36F4S0kIjD2X7HjdzOMSShE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaE/TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphrRAAhRFyaIt9ZSjHbcdBKZv4IvRwJQBorpDx6DW6MXnuo/Fy960d\r\njDEBz55ImQZoVkLMnVl0yndMMSxGL7uExP7XuRKz0lOGsmPT/yfylhkGE3sw\r\nZoshKyXkzwg4xP+hb9jehqJFeN9HObW7Ao4JhClVZ7JWLTwUfdQ8sqnbS+YE\r\nqIedq8bovbmSrcrz5kBE0nFb+07ButKPlXDTW7luRapt7ULk9iacf8qaUUj5\r\n7FYzrd+I82AwXmu3j1r+T2UxzAbctZOgLCSsSJk9rc7cgEPOz8SgQA9L878X\r\nTavg9wqH0pbOiwD3EUeZ0H2029hDX6QwUeb92XH4phP6YZCXqJY/hyW6Sxdm\r\nk4ubegBZ0H2X3zazHNk7PRFUZSEuAm98gw9yjtXA0+6eUwCrSSXD062DMN1/\r\nrZYpgReK72j02mho37IpRYE2muq61EnSwNHhsebRBEGM8FhdvlpoabUB93zH\r\n2F43JLnNC4zgbRocpfDzbS3KgJZFNcWJcl38gcWO7DpDzmlCw4jyVnTLL/Vw\r\ncDa/cD5eJxSlCHDAsuUUfQuQvcM3FZAET1iz/cHBIcKUq4da3JmCikEqA0wv\r\nujYnNNgiyMTeXfEhAwl+5KIEyRRC58mnB4iQ1ZieaC/HvStEbZIiejJZBqxb\r\nxTt2caqXFYnoop6xDHn2F/Z0BC13/HPCocM=\r\n=9TWl\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_1.20.10_1651003346782_0.793137710398663"}, "_hasShrinkwrap": false}, "5.4.7": {"name": "@types/puppeteer", "version": "5.4.7", "description": "TypeScript definitions for puppeteer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/puppeteer", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/marvinhagemeister", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cdeutsch", "githubUsername": "cdeutsch"}, {"name": "<PERSON>", "url": "https://github.com/ksm2", "githubUsername": "ksm2"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Serban<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/JasonKaz", "githubUsername": "Jason<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dave<PERSON>well", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/angrykoala", "githubUsername": "angrykoala"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/cameronhunter", "githubUsername": "cameronhunter"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/1pete", "githubUsername": "1pete"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/puppeteer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "95aedcc8474e6ac01555a79f35a738e9dca706cf12f79f03742b1352f6fb5989", "typeScriptVersion": "4.1", "_id": "@types/puppeteer@5.4.7", "dist": {"integrity": "sha512-JdGWZZYL0vKapXF4oQTC5hLVNfOgdPrqeZ1BiQnGk5cB7HeE91EWUiTdVSdQPobRN8rIcdffjiOgCYJ/S8QrnQ==", "shasum": "b8804737c62c6e236de0c03fa74f91c174bf96b6", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.7.tgz", "fileCount": 5, "unpackedSize": 91049, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMghlmwlcoIF/frz3DKKlYC/TdH7y7e+AbrulkGdmDnQIhALZG/Zr3F/A45w0tRkWQl1WwCIsEnQ1MueXgqAe7xqtL"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRw9zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpY5g/9FQpqvl75N2jbCLR0gAeE+ItbDRprrBFSXyz9sDpu90VNNV1q\r\njlgr7/z5lJyfamq3gFGkKzpRiXOl/Qm51UGIGsNvqctDOJaSG3wsihdcQA9h\r\nWeGpSmPxBGbZnQWzwDer7z/Cx6e1dmwkJnC0esZYv+3qT4EiwZS9Xe0mf/k2\r\n5m8fLo7Yw5eg0iD/8X9U2SOCl+KQjs30jBXi19v+MdVurrY6upP5QX11nWtH\r\nfUc7k871ZdbrHBXL6zSll4ke4ErHbFfZjtr47pYW9IxqrifMhUgqjULSHNfS\r\n14V4f8jYaS01vbo2QGNkzbmZWU2tU4eXn/4B8fCUqK7EPpAem3tR01FTaiPa\r\nVg+ZOmu1WOz8ccotTu62949CJX2/eqedLAITiTb+oBoRclOBV7LHY79O7Bai\r\ne0F2mLVL+fQxW+H/bnns48cpWFThwjLGgnzLYjb1hweTw6qmocWl+rxADXv/\r\nCIgDahFw0ifttRBswUu5g1wATjSmh5Uj31Pj3A4GvX+YqTZwOXZfuRHJc97g\r\nqsexNassbi9PNuD/hGYOzfaMiqZ1G3df5ca8xIdcn43NkGTJ5WDwFsJxaBSh\r\nJz50Tno55ROsCxeT1B/wGH9hkAP+NJhYMFOb+ZuIq0dT6NhRPilTcYwxbnyX\r\nyLHcuazMiZ6RwEv7+XnIz2cMe+e2o7F4Uq8=\r\n=MB04\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_5.4.7_1665601394962_0.4884105806437744"}, "_hasShrinkwrap": false}, "7.0.4": {"name": "@types/puppeteer", "version": "7.0.4", "description": "Stub TypeScript definitions entry for puppeteer, which provides its own types definitions", "main": "", "scripts": {}, "license": "MIT", "dependencies": {"puppeteer": "*"}, "deprecated": "This is a stub types definition. puppeteer provides its own type definitions, so you do not need this installed.", "_id": "@types/puppeteer@7.0.4", "dist": {"integrity": "sha512-ja78vquZc8y+GM2al07GZqWDKQskQXygCDiu0e3uO0DMRKqE0MjrFBFmTulfPYzLB6WnL7Kl2tFPy0WXSpPomg==", "shasum": "6eb4081323e9075c1f4c353f93ee2ed6eed99487", "tarball": "https://registry.npmjs.org/@types/puppeteer/-/puppeteer-7.0.4.tgz", "fileCount": 4, "unpackedSize": 1759, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2YtQhVR2YO4I/Tsc3+sqsB3vf9CROXoFGKv0cYRKagAiEA1v8Ef6yD1rHTgRNh4t2GnqncDUECq4ltB+L6NOoJXfg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYttgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8qBAApNvF6KkjPhJDHzc3Mp5iSLzLkT3//55VpH5Mj5g8LnXBhU/t\r\nfEhriwePbuEZJnwefJJ3lPHxn+j+jXQsMXa09TzlLdusRxe9Hc4Vkd4QL8u8\r\ntSW1bOkAV2paZg1x6/1CtfuEiXx9y+4hUPi3+jz8FVgAkdvB6IMg+Gexax6m\r\nzYr3zcTNsnpJqNbwl1H+lkwDPgAJTsi4bO47lCJCUYK29JXJXlje8uPPpzLf\r\nj5YYlSfWnMRHo0NcO276/5PpbdKap9dErV9hqU1RJ56j7VT8W5ZHVJ5ZBArw\r\n1yFypwMHBpK1nFLfueQAEwTnbpyoY/yhBH9e+cB1yFJ9896Ixb9XyJul6zK+\r\n4gt2wzzIaPEXXEniDg8VKpyb5qT2p1393AjFlHDbJmS7CzLCNEMyQau6CngU\r\npMQkETu3LzA5AdatQDBAWY0kO27/7aLvnRkocv0zcisPJCwwqIBeRHLKkHEu\r\nGyPjnphwNKz3YhSQUWfcNSWprl1SAfo2nQxkVWIwLMvbDpJ3ubVdbskDMEbT\r\nAvPDsOGmxF2xolGg/6IY1jLfsbqG426eTDsFSflSAiFxhthpzhfNHnAtyp/Q\r\nKSAwsRBKNuK6ZWB8grA3qrWMzHPq9Pm1BDqR/LnbudyTL1DxEZM9PJ9+erKf\r\nGuSa5MGJLAWXTvD5oFAUm8WjNhGe878id30=\r\n=RODV\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/puppeteer_7.0.4_1667423071883_0.16442332991784592"}, "_hasShrinkwrap": false}}, "readme": "[object Object]", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "time": {"modified": "2022-11-02T21:04:32.152Z", "created": "2017-09-20T22:08:29.638Z", "0.10.0": "2017-09-20T22:08:29.638Z", "0.10.1": "2017-09-22T17:58:12.587Z", "0.10.2": "2017-10-04T18:21:30.113Z", "0.10.3": "2017-10-16T20:12:03.424Z", "0.12.0": "2017-10-17T16:10:51.917Z", "0.12.1": "2017-10-25T16:22:13.733Z", "0.12.2": "2017-11-04T04:17:27.311Z", "0.12.3": "2017-11-10T21:33:28.114Z", "0.12.4": "2017-11-13T15:14:28.901Z", "0.13.0": "2017-11-14T16:32:25.922Z", "0.13.1": "2017-11-21T18:22:24.915Z", "0.13.2": "2017-11-21T18:43:10.548Z", "0.13.3": "2017-11-22T17:17:58.477Z", "0.13.4": "2017-11-28T00:31:39.168Z", "0.13.5": "2017-11-28T17:05:15.843Z", "0.13.6": "2017-11-29T00:56:51.192Z", "0.13.7": "2017-12-06T20:10:49.880Z", "0.13.8": "2017-12-11T19:17:00.275Z", "0.13.9": "2017-12-20T14:51:35.835Z", "0.13.10": "2018-01-23T20:06:45.022Z", "1.0.0": "2018-01-23T20:08:16.385Z", "1.0.1": "2018-02-28T23:23:30.067Z", "1.1.0": "2018-03-06T00:38:47.266Z", "1.2.0": "2018-03-16T22:11:22.424Z", "1.2.1": "2018-04-03T18:15:38.060Z", "1.2.2": "2018-04-24T23:36:08.874Z", "1.2.3": "2018-04-26T22:50:59.322Z", "1.3.0": "2018-05-01T16:27:12.811Z", "1.3.1": "2018-05-04T23:59:42.775Z", "1.3.2": "2018-05-08T22:16:40.109Z", "1.3.3": "2018-06-07T20:56:10.759Z", "1.3.4": "2018-06-13T19:28:12.818Z", "1.5.0": "2018-06-26T01:14:23.076Z", "1.5.1": "2018-07-21T02:09:50.711Z", "1.6.0": "2018-07-30T23:21:10.615Z", "1.6.1": "2018-08-31T20:08:04.659Z", "1.6.2": "2018-09-04T18:05:18.252Z", "1.6.3": "2018-09-05T15:44:20.328Z", "1.6.4": "2018-09-14T17:47:10.371Z", "1.8.0": "2018-10-03T18:03:29.706Z", "1.9.0": "2018-10-05T18:14:57.117Z", "1.9.1": "2018-10-27T21:48:55.083Z", "1.10.0": "2018-11-07T23:23:50.166Z", "1.10.1": "2018-11-28T18:21:54.047Z", "1.10.2": "2018-12-03T19:22:28.247Z", "1.11.0": "2018-12-04T21:28:57.037Z", "1.11.1": "2018-12-05T19:03:41.789Z", "1.11.2": "2019-01-22T17:45:28.827Z", "1.11.3": "2019-02-01T05:36:57.848Z", "1.12.0": "2019-02-02T01:06:21.449Z", "1.12.1": "2019-02-05T01:25:03.939Z", "1.12.2": "2019-03-07T18:02:40.469Z", "1.12.3": "2019-03-14T18:36:39.849Z", "1.12.4": "2019-05-03T00:34:03.783Z", "1.19.0": "2019-07-25T18:59:39.516Z", "1.19.1": "2019-08-12T19:13:26.883Z", "1.20.0": "2019-09-27T16:39:54.913Z", "1.20.1": "2019-10-03T23:32:53.901Z", "1.20.2": "2019-10-10T20:08:11.874Z", "2.0.0": "2019-11-20T00:29:49.102Z", "1.20.3": "2019-11-20T00:30:09.146Z", "2.0.1": "2020-02-25T21:00:54.470Z", "1.20.4": "2020-02-25T21:01:14.954Z", "1.20.5": "2020-05-04T16:10:48.585Z", "2.1.0": "2020-05-11T21:06:13.354Z", "1.20.6": "2020-05-12T16:08:03.899Z", "3.0.0": "2020-05-19T18:14:39.056Z", "2.1.1": "2020-05-19T18:15:02.097Z", "3.0.1": "2020-06-27T20:33:12.796Z", "2.1.2": "2020-08-27T22:15:51.922Z", "3.0.2": "2020-09-08T21:05:06.279Z", "2.1.3": "2020-09-08T21:05:29.548Z", "1.20.7": "2020-09-08T21:05:45.992Z", "2.1.4": "2020-09-21T12:33:35.537Z", "2.1.5": "2020-09-29T07:51:38.844Z", "3.0.3": "2020-10-30T15:15:44.972Z", "3.0.4": "2020-10-31T06:06:34.714Z", "5.4.0": "2020-11-03T03:14:44.464Z", "4.0.0": "2020-11-03T03:14:59.532Z", "3.0.5": "2020-11-03T03:15:07.967Z", "5.4.1": "2020-12-04T02:03:51.546Z", "5.4.2": "2020-12-08T17:25:40.841Z", "5.4.3": "2021-02-04T00:11:14.158Z", "5.4.4": "2021-07-07T17:19:29.619Z", "4.0.1": "2021-07-07T17:19:43.446Z", "3.0.6": "2021-07-07T17:19:50.428Z", "2.1.6": "2021-07-07T17:19:57.936Z", "1.20.8": "2021-07-07T17:20:04.724Z", "0.13.11": "2021-07-07T17:20:11.717Z", "5.4.5": "2022-02-23T23:02:26.393Z", "4.0.2": "2022-02-23T23:02:37.157Z", "3.0.7": "2022-02-23T23:02:39.585Z", "2.1.7": "2022-02-23T23:02:41.294Z", "1.20.9": "2022-02-23T23:02:43.861Z", "0.13.12": "2022-02-23T23:02:45.271Z", "5.4.6": "2022-04-26T20:02:14.884Z", "4.0.3": "2022-04-26T20:02:21.128Z", "3.0.8": "2022-04-26T20:02:22.778Z", "2.1.8": "2022-04-26T20:02:25.223Z", "1.20.10": "2022-04-26T20:02:27.049Z", "5.4.7": "2022-10-12T19:03:15.232Z", "7.0.4": "2022-11-02T21:04:32.068Z"}, "license": "MIT", "readmeFilename": ""}