{"_id": "bare-fs", "_rev": "58-4dff6b304302d6ba43cb8b8fe844be2b", "name": "bare-fs", "dist-tags": {"latest": "4.1.6"}, "versions": {"1.5.3": {"name": "bare-fs", "version": "1.5.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "45a3da38bda5a3518e9d349369a6289f65c4f1a4", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.5.3.tgz", "fileCount": 8, "integrity": "sha512-Jt0SBw7//n8DQYbsr5V4WILxwzrCl4vLH2t9sCkEBtkoW5G7WQmP5hXqW+tlCjOev1Wk4dztkiLqLbnCCqPmXg==", "signatures": [{"sig": "MEUCIQCCi58CynhXZitpMoL8yhG2MLY5PV7wjigT2f30YvC31AIgH/SUlunp9oASfiC9jER4cbA8WwaDkQin2Pgqm74TjY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61691}, "main": "index.js", "gitHead": "5fe8d1666d93e35a9f7097990b932ab429c72d02", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.5.3_1684225498194_0.9635209851495024", "host": "s3://npm-registry-packages"}}, "1.5.4": {"name": "bare-fs", "version": "1.5.4", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.5.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "2b49357cf382c79db3b4ffb545c24e9b81b16cd8", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.5.4.tgz", "fileCount": 8, "integrity": "sha512-kpm6bhQQXJn5ccUXeQoXsk3CgDfMeHg8TkJC1ab9I2ePXO0wqh0Ha795uV/wUPLN9xLpR9yMSBFQqp4+W4qbbg==", "signatures": [{"sig": "MEQCIFHpbr7lEtLqiWq7itFJXgUrcQgj7rD9QSEP5Pd00bwsAiB1Mz9A0adNvUPz+PKpAY0BltSVoOS8ObCgRLp0e7Kqgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851}, "main": "index.js", "gitHead": "2b08b0c18e9b77b6538a573144bd783cae82df9e", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.5.4_1684755119658_0.33889382335386053", "host": "s3://npm-registry-packages"}}, "1.5.5": {"name": "bare-fs", "version": "1.5.5", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.5.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "e5ccf14e7ac76dbdf4219bc34886ab94415ea4fe", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.5.5.tgz", "fileCount": 8, "integrity": "sha512-jHojCHhG1GJSj6Oput6hYuHut1UuekCUieFLjHE+OYY9iGh9KvHbRL7467EwWPlgZS0Z4DGf5UXmDQPxpkW5ZA==", "signatures": [{"sig": "MEYCIQDOfBv2UMot+A/+AvW5quI+2wY37jwt5V3v6S+Pk2JEQQIhAOj2sP+Dh9HHwv0/uZJnEoVh1lLDECe1TL2KDbGlAK8J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57983}, "main": "index.js", "gitHead": "9e897f15ea4aaf39dea4d505d9c99dbb9e4d2230", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.5.5_1684844957811_0.14114184177371625", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "bare-fs", "version": "1.6.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "c615ace66ee9af19e721adb2b7d973815faaf23b", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.0.tgz", "fileCount": 8, "integrity": "sha512-9lQIYBsfAzoJriP8qWQV1V04yPDiGTOBlpWtPuW0QVniC+GVWZAt2ekVA6ugdDGLIpCf24Y6kYk5Oxak0fi2fg==", "signatures": [{"sig": "MEYCIQDooiU6+fNa7wuR4f+G3Vt2Ix4qXklYPA02hz0MRRgl9QIhAIg3YfNH/TIDE6UGP864iDqtK8MJVvWrG+Eq/90p96Yx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81993}, "main": "index.js", "gitHead": "32a069d313ec54d9367a4fb4524ad85adf766233", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.0_1685006354602_0.19560090003747455", "host": "s3://npm-registry-packages"}}, "1.6.1": {"name": "bare-fs", "version": "1.6.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "ac12260119b781e15dd2c8f26290972ee944238a", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.1.tgz", "fileCount": 8, "integrity": "sha512-ldBKht1WQ+zPOuvK/XMNb9G7DA7ucR2BFH3Y1YUE41nLks8/a133vk6EX/K62AOY8rAhAbAShz9JbDiFe1K9PA==", "signatures": [{"sig": "MEUCIQC6KUAE8UhJZkulppynqvwafbzJGdEh6NlpvcLSgyJwXgIgHkI+0zCh678L76pn8Rqu1IC+V3nDexa3RPlBiERo0Rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80781}, "main": "index.js", "gitHead": "8f75adb15210bc90e2d52e14657404c09753e914", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.1_1685011074877_0.857345743237258", "host": "s3://npm-registry-packages"}}, "1.6.2": {"name": "bare-fs", "version": "1.6.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "a95be8e8340a0a850f1b32b7618ef30ad5fbcfae", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.2.tgz", "fileCount": 8, "integrity": "sha512-3OlrE0XHU4/wKOmxpa716E/4v+s+CSw3XXqx1obbfm3bCW/cLcqAGGwraZf4rAcP5ah4UKqurhLEEGg9sE+B2Q==", "signatures": [{"sig": "MEUCID2SzlfdCu3Gy8z3InKZ2P6ukSl8pUugZ8SfuG+81S/qAiEA65rwlKLJrgMySb1zaeYfGTfQV/Zg5jDONWLhePKXNkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80811}, "main": "index.js", "gitHead": "3a78e3528247f8b5daebfc478c240d02d5f35675", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.2_1685011249752_0.6871198818331201", "host": "s3://npm-registry-packages"}}, "1.6.3": {"name": "bare-fs", "version": "1.6.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "c443f118764927a41478f9e847c76e40e0a0eb00", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.3.tgz", "fileCount": 8, "integrity": "sha512-uKA7Fryf4uE53MCV1Y6s+zwcl/u8jGtx6H4nFYQqLiF8wot4dOCRT+x4k3zB26c2BfvEDIpdrQpg2yhgTqqCTA==", "signatures": [{"sig": "MEQCICt/GakcGNsiy0ttuBrIL31zj8lRhYXRtjLiEmtWmGU0AiA+ePCH6IAmrEdB6PWscu4y9Ss6Y7vUO9UnmYb6jXA18w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80987}, "main": "index.js", "gitHead": "a70b4d34400e96e5568f8195d3d668558a398c58", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.3_1685371749842_0.019956536771236655", "host": "s3://npm-registry-packages"}}, "1.6.4": {"name": "bare-fs", "version": "1.6.4", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "31b04774e31eba9974dab06d60f411abd4da0a1a", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.4.tgz", "fileCount": 8, "integrity": "sha512-lRJcTQOvfH43eCNiy91pS/kISil3AzoKQVdAlGsM8txWWGmINDot9+Uc1LXyBHtL+mdqFIbFqbV2ZEswGOftNA==", "signatures": [{"sig": "MEUCICCZ9xpu991rJY4O5F7Dezszqe594se4Q+Uyt2kWKjIIAiEA743KZG16NHzQoEhM1gkoQ0Vh2F+/5Zqx3cXPXTrtT5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81083}, "main": "index.js", "gitHead": "6d30a3288f1e961af4ce864b9f0c99a03aa8dcf0", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.3.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.4_1686579937691_0.3452261560021028", "host": "s3://npm-registry-packages"}}, "1.6.5": {"name": "bare-fs", "version": "1.6.5", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "5d9fae2679c41cf82d97fb624da96411ebc9c7be", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.5.tgz", "fileCount": 8, "integrity": "sha512-hIp6UXgb1fxwDCTv6J9+HHAJQ6GBvmJ8TQBiCUSGSoobUGRogvxdbbN0VMxlUO54MdIy4Gfm/JtZGdqKG0L6BQ==", "signatures": [{"sig": "MEYCIQCNWHlZ4n0PxNnuYuB/nciBU1nlpooRZ7s82JDXJs9mGAIhAL21wj+MtmJwmRk5kb3OcxYl1dfZAHGsc1EkyNq4zvFI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81268}, "main": "index.js", "gitHead": "827104718c2084e133098990327d430c13b6746b", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.3.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.5_1686646740249_0.8844004914474801", "host": "s3://npm-registry-packages"}}, "1.6.6": {"name": "bare-fs", "version": "1.6.6", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.6", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "91b3db486f73101aec7a39ce1f5373595727faaa", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.6.tgz", "fileCount": 8, "integrity": "sha512-UDsJHeMzvfLN5td//RrdQhOUiB0Kh9tp3J7VSJKlZvLL4h5Wq38KuT8LNj2aaaun443KI2kanoPEKX8NlB2n+A==", "signatures": [{"sig": "MEYCIQD6CLMgtrT/mVLunMiJVWU1d+vBcJ+NdYfsXtbta16qfgIhANOnI1rGUDRkN77CL53jrI1YvOTuvtWyEhtS9EWJZm9R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81268}, "main": "index.js", "gitHead": "13fe8fcf88d80c7316c3cab422c105b3a30336f9", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.3.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.6_1686648497817_0.9606496872127535", "host": "s3://npm-registry-packages"}}, "1.6.7": {"name": "bare-fs", "version": "1.6.7", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.7", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "a7ec70b26746b670bccbccdee37182c52ac0f877", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.7.tgz", "fileCount": 8, "integrity": "sha512-WGXXhF2e0fD/0zT7wyFg0BIJYqhgmO5rPvpAcTerPhxCjxgf2kVrp8UGs0fpxmGcxgd05b/qL/UFd9nehkCxkA==", "signatures": [{"sig": "MEUCIG7B1Ye/u6lnK1VtKABFIVPt9CdQ0caaZHpronC3UNrdAiEAqpfCLN6ZfJiebsxTQGuTlKvp8UI4fJNMFWhQqGB/Ms0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81183}, "main": "index.js", "gitHead": "df8f995f620a7561194ffa53807dc20e388581d5", "scripts": {"test": "standard && bare test/all.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.3.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.7_1686654008989_0.0988780785343839", "host": "s3://npm-registry-packages"}}, "1.6.8": {"name": "bare-fs", "version": "1.6.8", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.8", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "b4b1c7b99fb6e08703e3ec3f805ec5f328b57f26", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.8.tgz", "fileCount": 8, "integrity": "sha512-QUFfLoKbMi4KjhZCA6GgkEL074cwe8rRDdBV3Jsz8LlMzHYRNn5pM2Cvu20/oP8WQj2tLIGzRPMZe+4IWcHQhQ==", "signatures": [{"sig": "MEUCIQDrTHysB7ZtpvpGkAgGTW0HayNgtn40aaslpRAyq2GdyQIgd0IoK2vVs01jDWWykbbrNoF2hNYceV+wWLpF/rvJoyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81124}, "main": "index.js", "gitHead": "dccc19a590ffbcde846fb342dacd6a5949e1b7ef", "scripts": {"test": "standard && bare test.js", "install": "bare-dev rebuild", "prebuild": "bare-dev prebuild"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.8_1686750306658_0.4116628518325176", "host": "s3://npm-registry-packages"}}, "1.6.9": {"name": "bare-fs", "version": "1.6.9", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.6.9", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "f45200abccc75ea6b89444f86a298799ffebcf98", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.6.9.tgz", "fileCount": 8, "integrity": "sha512-mZUD5DwE2UcQqUw7V2w/ZfcAclmIihkxvY3h0cCXQmXQHW5x6M2DbqO5IBP+XFZ+pCxcO1YyBACke7FvXzTwOw==", "signatures": [{"sig": "MEUCIGnYpVOd8gM7NDoipAi9Q78Mr7N8ukuzeljeFDNf9CZPAiEA8tze2D0ZtVxtu5Qunh6uPHcuJv1g5ZmNITDxmw9sKN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81052}, "main": "index.js", "gitHead": "5bdde9f1d978f331ec58d82e992f4971ce4857a1", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.3.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.6.9_1687436514380_0.21649398156776112", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "bare-fs", "version": "1.7.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.7.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "72c0f55967d5946e7d1d889f2457757ec24df22e", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.7.0.tgz", "fileCount": 8, "integrity": "sha512-4fj2JSKFa3M6HdNlLjvOKc8kRwI4MH/YmWO356cIkN03YHse1Vc1c2YlWxTF7cim4Yuss+KNLXeHdBEfsGg9nQ==", "signatures": [{"sig": "MEQCIDyZWcKD3tpp28PBuF/TTD3v5GEbCn6qosRtdairLmoeAiB0RQ3QxOL8KEtl3yzZgzeeJdbWpVidVE2Hz0z6CFIRpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84236}, "main": "index.js", "gitHead": "541139d000babd30b92a28ad8be96fe2d9c3c45a", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.7.0_1694700252919_0.9513306671988697", "host": "s3://npm-registry-packages"}}, "1.7.1": {"name": "bare-fs", "version": "1.7.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.7.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "6c9fe359791081bbe5bf71c55d5961eb97d2e2da", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.7.1.tgz", "fileCount": 8, "integrity": "sha512-Yr8+WYAnI0SSTJ9MNZTBm5/7j/0yv46gURP/FcGmxA+CxyBM1XCYJ6QjZkzjRjby9B/b5lIH1FTTfd05iWJBAw==", "signatures": [{"sig": "MEUCIHi9ALyneEDjjrEBFQ+pqnpYn8ixYNneFaZQwD3OXtGVAiEAw89Wl8c653oF8+527n4ryi+uMSp6KNUiO+RU3jRrjVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84724}, "main": "index.js", "gitHead": "1979b2a0e815e73487911c0f3bfc6c9f2ed85653", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.7.1_1694708425505_0.43991083030579436", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "bare-fs", "version": "1.8.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.8.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "391512ef32ecfa9b21563c32ca1e2fd11a214925", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.8.0.tgz", "fileCount": 8, "integrity": "sha512-hloBFVv/w71Emga1O1lRg8D3qcnB8+jvAL5tyxyXwElk0Wgl6xCAU0zSJkkSTkMrcvUFf4IG+eT7T25K96H41A==", "signatures": [{"sig": "MEUCIQCobQmcEQwsEXXOPStbLg5gL47waFsy3P87+YzexRFdDAIgObE34Zq5+OUiNOUmS4G7/IuIDFgl8qxa2Zf0p3PUxDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90683}, "main": "index.js", "gitHead": "96555abf50229533f651e1bdfa2a9a75492515e6", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.8.0_1695302713536_0.9434518069722511", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "bare-fs", "version": "1.9.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.9.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "8f8cd86a4b9b97c5eb756fe99ba76b72f124fc17", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.9.0.tgz", "fileCount": 8, "integrity": "sha512-iqgIQq1iJ6ele2m6z7LOWGA+HXQnPy8S6zEt0rTBAt7doFicERpizqFxsu2Y+dk1F/fRDjtZRUq/MSzJALzcsA==", "signatures": [{"sig": "MEUCIF52Hz0xxrOX8Ctp9oVBxazpPc0OBaqDNcBYRFDsHyWxAiEAqYczveBASRKstOKMtFxplckbP2PWrIozfoYhe0cFiIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94172}, "main": "index.js", "gitHead": "cfaea3e5e7320a15d3a9b65ea6704c1cf9c0a60f", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.9.0_1695644800537_0.25122889506664503", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "bare-fs", "version": "1.9.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.9.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "b86007f6cb92ec38ef7769b11e656ddbe8cebaf3", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.9.1.tgz", "fileCount": 8, "integrity": "sha512-oeT7fMOg5jkenBCL3kPftRUauCQPFDE2GqCS4t4i0zHusJilGv7TLbV1hf7cvhz7NJX8LADwIkj0WuzulZxOdA==", "signatures": [{"sig": "MEUCIQC4x6nmd2FPd7xUsMf39jFniQx9jShhLcfDbR+j/87DfQIgfBJErRwMgLSqMslrW9uijlP7nr4/EP3sgz5sHps/0iM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94132}, "main": "index.js", "gitHead": "c151a3499ad09d92c692935ba4477d75c894ae55", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.9.1_1695667940936_0.31936064499869166", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "bare-fs", "version": "1.10.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.10.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "5780cf7605048db1edd82ea0e97071a98490ff1b", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.10.0.tgz", "fileCount": 8, "integrity": "sha512-/LRcjbnxyitFO5Sc16HMKckpDrJOyUgP20l3zbFsf35KRlCGt0Uu9riznPUrk/mAnrS3chRigsRjjSsOAXjsUg==", "signatures": [{"sig": "MEUCIQC7uoV5JRoerLBSegiB3pCrWPEBbYvxwJg+NNtEeIUeRwIgPahQ9V8KQuUz6ukxga/XRBg/C/Au4ZQ3+5zhH1Rlb/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94046}, "main": "index.js", "gitHead": "431b8c17cb8507273de71339b0613e7ed4171b2a", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.10.0_1696273342350_0.25130902513396225", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "bare-fs", "version": "1.11.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.11.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "cf3e60480de57761b483139f02c4f5c508fb8498", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.11.0.tgz", "fileCount": 8, "integrity": "sha512-iBJqNwVelT/mCXo6MGg2LZ2eGbp05xo2Tu6atEfUFrVWxzjo8fse2jCMNyoskUW7vFmXh4hPfg/1nUQseSYcJQ==", "signatures": [{"sig": "MEUCIQCbacaIAGwVbS/qy4Z9Sub80iYtTg4B0zjliyRpjpE9gQIgSXWLlgg1MeR7dGWcIbx/DIsm51WoiB2V2b9hirQqbTk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106122}, "main": "index.js", "gitHead": "ce157c71ff3f1832de110d20d508923b1d541e29", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.11.0_1696946465433_0.06688141909114798", "host": "s3://npm-registry-packages"}}, "1.12.0": {"name": "bare-fs", "version": "1.12.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.12.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "a45dcb0aae3e32bea323601ef85123da13274404", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.12.0.tgz", "fileCount": 8, "integrity": "sha512-KyuoIW750M8QrdOsYij619hQohaqeXW2YbNu1SMsvSbXNdyWC7oREEpWqMiuwyJpG3qwHlLQqwMWtYRaPacNbQ==", "signatures": [{"sig": "MEQCH08zClR3R+dP2UO+hX5peshPjZayhe+knlIVdgH8kfwCIQDEDMgb4Gq1FhRdn6xiZoaVwLFQmQCHoyBbMDfC+SeD+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108863}, "main": "index.js", "gitHead": "d97d22809b31edd260396df2faf02a9b9585ef56", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.12.0_1697188967790_0.8510391907047643", "host": "s3://npm-registry-packages"}}, "1.12.1": {"name": "bare-fs", "version": "1.12.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.12.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "fd0a10446dc438ac5757967684c50b8c8fdd3616", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.12.1.tgz", "fileCount": 8, "integrity": "sha512-a9nVXrdVapSgptHXxN8b0lXxH5fUo0h+P8RX4yZ6m2j0MUVrqOlrIcAnT58xkKfND5RdkaDiR/H+c8bHSo4yPQ==", "signatures": [{"sig": "MEUCIQCF/0SIZF8cqb8ieiacY3z4f9rlTpWNbw+Q3gx/0TVtsQIgUQ282xGNGRceQUOuZZmIQvMLcBsIw7ObfauG4ufWcKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109027}, "main": "index.js", "gitHead": "d4f0acfde48f59c287822b9cb213777700ed5dc6", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.8.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.12.1_1697722426622_0.7151318432013258", "host": "s3://npm-registry-packages"}}, "1.13.0": {"name": "bare-fs", "version": "1.13.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.13.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "d9f5e18da9c433dce2674c4155b047325ca51fa5", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.13.0.tgz", "fileCount": 8, "integrity": "sha512-OtwORIwVlBd0BsOCtTdNT1WVaD/WcEcM9AlM7gPp/RCNUd9c2KMjvCxfot9Z4a6uPmMvnBJ/OW7nywK+a7OSDg==", "signatures": [{"sig": "MEYCIQDi1vzXQ8ohiCJjPfYWzqTyjrVrw3HdfBjqx6Ptin8sNwIhAIF+V9XattP7yJyRpIxh+h0jFzGuDYa7Gi/hJwcUK4w+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117141}, "main": "index.js", "gitHead": "37523b1e61a925751876d8f756e5f2ffcab55093", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.13.0_1698234865027_0.04797600759254195", "host": "s3://npm-registry-packages"}}, "1.13.2": {"name": "bare-fs", "version": "1.13.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.13.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "564554bd45687a478376ee8e3b8887e65077b005", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.13.2.tgz", "fileCount": 8, "integrity": "sha512-rJEYkP3mvtakftHR6t+FBrEkkKDWQFfRcHJFsrC6zr6u9F3+b5Wp5RcBuPYWy3lRQvaT9O2PqyalJqQaI3OXvg==", "signatures": [{"sig": "MEYCIQD1subPEExSPkIB3R7ppTPo46jJfov1kge1QEZv8aqJEgIhAOkKy9Pq+iBUnRiNDvKIox1A3J5unCVeGQlDVfk8t/xX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117126}, "main": "index.js", "gitHead": "a912595582ced461d4cf53855640136ffa6b3dc7", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.13.2_1698235374884_0.576066301684113", "host": "s3://npm-registry-packages"}}, "1.13.3": {"name": "bare-fs", "version": "1.13.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@1.13.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "6cf535b188df681fce8d4c6d440b06ff949f3cda", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-1.13.3.tgz", "fileCount": 8, "integrity": "sha512-5kdJyMd67b4Ybv8oQbXLd7LTYPaVHdnBQKWcZJcdL2T3KOkTDcvQ9vi1gOSl2u03lJ0ZRAsyL2ar4u/MShL8/g==", "signatures": [{"sig": "MEYCIQDLdH/z9VT0GtYV9sAytDjpfS59sFxiUKZ3nM1vi/6+mgIhAOYgY71va0l6xd6aYsQFeXMkZBtCebPDgWi2O1ItX5Yj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117151}, "main": "index.js", "gitHead": "47b7e08d575ef684fd320710cbfa4c3ca4f7badc", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"streamx": "^2.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_1.13.3_1698235511172_0.3310845182970492", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "bare-fs", "version": "2.0.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "056b7d63cc7544b6d40acf041a55d87330d4a9d6", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-JEvXF+BBpKKR4/auvhN7mJVQDfTCuLXYaJdegEP3jcUe4kOVszQmGY0zKMyo5OWGSIPoxlI79SaiaJP6It4uDA==", "signatures": [{"sig": "MEUCICwwAHtsCK/rF2PADjwOR/18o/JXyECLTUg2u8h6UtjEAiEAkAEekTDm4ngREq7dpbLgZ7PAwutgAg2x7fxX3SYsRZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118250}, "main": "index.js", "gitHead": "a2b1f733a7d1e014f7b85caaf48f0851c08c109f", "imports": {"events": "bare-events"}, "scripts": {"test": "standard && bare -m package.json test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.0.0_1700135488333_0.048251123778547456", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "bare-fs", "version": "2.1.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "f12c607eb8e984620de27de68b26e05d020f4580", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-qQC9eSmZEzi/VI6AbYA+DnCT0KVlETuNSXQkS0gGVqKT0IiNoNlNMaudwL1Im4o2+UyZ2TAr9omWCwgunHweDQ==", "signatures": [{"sig": "MEYCIQChtyzCuhXunfEsnEnc5a3YugDfjNcm9QiRZAr8Phkg2QIhAKRi028uUkYyLBtnkWmpUe6jy25fpZuzMiaBTr0Dbh5Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119081}, "main": "index.js", "gitHead": "7ea78466bb4d62c53ad7208579288d81f24501d4", "imports": {"events": "bare-events"}, "scripts": {"test": "standard && bare -m package.json test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.1.0_1700578480630_0.6608648185477837", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "bare-fs", "version": "2.1.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "e55129d3863371785dcd9e7d2aeffb1d58842cdb", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.1.1.tgz", "fileCount": 13, "integrity": "sha512-2MzJP2ZpSNvYjRf21iO45vrgs7hFh0K4YZpzFHN4THS59IJT9yotfrUKQ/LGf2PS+KDnuQV/oTRu4QDcTbgGQQ==", "signatures": [{"sig": "MEUCIFm/21WItoHW1ITe+hiDOsdu8pfD0PZceouulb1AgvSBAiEAraLJeq1BtrB7iAfTbMl69lpuq7ZCwmMSSRR8AiTRkKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 408521}, "main": "index.js", "addon": {"target": "bare_fs"}, "gitHead": "14ee244938fbc097ffdc19c23d25458a82f397ad", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.1.1_1706879024546_0.11328476039266899", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "bare-fs", "version": "2.1.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "59294fa0ba7596c8b8a8a735517f28f8e5c1fbcc", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.1.2.tgz", "fileCount": 13, "integrity": "sha512-+BjcnjHwnPfXunVnp7TTHP/8PkQZdMAIuJ8GyqodOv+F7hrhvqWX1oIaac3klLPSJ6wKRWVfoL8+w0HMrs1/OA==", "signatures": [{"sig": "MEUCIGjzYkGa7yTFpE5gJTcL0KnYr3e8KRyftb2BoV5VDw9NAiEA4gHt5bMpOVf7RLuSO2rnPz4pkJLSkB8W3qkTlGvCQQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 408499}, "main": "index.js", "addon": true, "gitHead": "9bf9ab979c9b63b44ffaa2be27c2ef1da020012e", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.1.2_1707472990302_0.16266871894702084", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "bare-fs", "version": "2.1.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.1.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "92e9ed679af898b2e7a23523113f79113048d75b", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.1.3.tgz", "fileCount": 13, "integrity": "sha512-Oa7F0QJV7We0mtKq7Tk246uiBrl7vun64cPEsJOEwv2vHjnVL9yO7aH3643aSrd4rXfVe7yhJ9LHZywQQAXKFQ==", "signatures": [{"sig": "MEYCIQDmSWB0ZsnqccS1DzOeqq83stD9nNRiE8wlh9qTr2ocmgIhAMahxlPlwAj4HkpyqEMg6AFTSYz7+oVITKPOX8yDge8T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 408889}, "main": "index.js", "addon": true, "gitHead": "fdd3179feb6c4becf988fff101f9cd35bf6c74f7", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.1.3_1707497884791_0.43353506975318434", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "bare-fs", "version": "2.1.4", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.1.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "b99989422ce92c91cfcfda4bf758e66000e7827d", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.1.4.tgz", "fileCount": 13, "integrity": "sha512-B8lsuGP8a+KDYWX/0EBCjMBXCah4Zq7CD6MXeNbbNQFXHcZXmwIQI8Rip3EosrkJUXrTS3l9UooHmmYFOQT4/g==", "signatures": [{"sig": "MEYCIQDMBPligdoSIVm2KblGvEWF4lDuGCOPKH9IUZ6uQDtHUQIhAM0LBetn5ybnFCixZTAWOAl1rLR6GVTcY1vVDVlSujDi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 412985}, "main": "index.js", "addon": true, "gitHead": "db62083ba360c6a24fd8832b8a3e4438e444bf37", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.1.4_1707813960652_0.27892338395647487", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "bare-fs", "version": "2.1.5", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.1.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "55aae5f1c7701a83d7fbe62b0a57cfbee89a1726", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.1.5.tgz", "fileCount": 13, "integrity": "sha512-5t0nlecX+N2uJqdxe9d18A98cp2u9BETelbjKpiVgQqzzmVNFYWEAjQHqS+2Khgto1vcwhik9cXucaj5ve2WWA==", "signatures": [{"sig": "MEYCIQCmNOmy7ULwOj1wACCZZedOJ/BIs2wHC5OgrRcKcIonsQIhAJ24SRS273L6o77zNq2VTT5mGy86A18UerssHgbvORRG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 413025}, "main": "index.js", "addon": true, "gitHead": "b40f0db28997f2fca3c50f5bc3374000c5fdfa57", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.1.5_1707814002070_0.7096643209949052", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "bare-fs", "version": "2.2.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "e23ab841be0e08b29ad2933281ce4ad5ddd60384", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.2.0.tgz", "fileCount": 13, "integrity": "sha512-+VhW202E9eTVGkX7p+TNXtZC4RTzj9JfJW7PtfIbZ7mIQ/QT9uOafQTx7lx2n9ERmWsXvLHF4hStAFn4gl2mQw==", "signatures": [{"sig": "MEYCIQCj2ny8vdNQsOZoytzrgp9XhDds6sAEGI7t0pi7HIq6oAIhAIgkZ4mWXs0XxbYVb9z/4LfVHqsideWG1KMwsudlFph4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 414716}, "main": "index.js", "addon": true, "gitHead": "7ad01dc2e6f6023906e51305739db7357115a539", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.2.0_1708683946878_0.39233114744290676", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "bare-fs", "version": "2.2.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "c1985d8d3e07a178956b072d3af67cb8c1fa9391", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.2.1.tgz", "fileCount": 13, "integrity": "sha512-+CjmZANQDFZWy4PGbVdmALIwmt33aJg8qTkVjClU6X4WmZkTPBDxRHiBn7fpqEWEfF3AC2io++erpViAIQbSjg==", "signatures": [{"sig": "MEUCIQDOWIDS6LyVqB87KkUvWFXwgZGxlCZ1mMuB4Xzahh+yAAIgcStPiBPiuQmI3m0KxPnCqOXg7Bwb6cE6J4q7Af8J1rU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 411459}, "main": "index.js", "addon": true, "gitHead": "76148bd0c664ccb15ea3ccd326bd0c8bd554d877", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.2.1_1709224243496_0.7544838735264565", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "bare-fs", "version": "2.2.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.2.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "286bf54cc6f15f613bee6bb26f0c61c79fb14f06", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.2.2.tgz", "fileCount": 13, "integrity": "sha512-X9IqgvyB0/VA5OZJyb5ZstoN62AzD7YxVGog13kkfYWYqJYcK0kcqLZ6TrmH5qr4/8//ejVcX4x/a0UvaogXmA==", "signatures": [{"sig": "MEUCIBzdkYfc9O/adH0s0NdTZkioYe9izAKJDQJGgZHDJWikAiEAxOuiLfB8e5fz1grnhthnyZ3CC/ShVS3oTRBlYaSpsMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 512323}, "main": "index.js", "addon": true, "gitHead": "0f95020602028e028237f59fe8d3d9613ea8ba31", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.4.0", "dependencies": {"bare-os": "^2.0.0", "streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.2.2_1710329996771_0.5579993223002402", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "bare-fs", "version": "2.2.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.2.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "34f8b81b8c79de7ef043383c05e57d4a10392a68", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.2.3.tgz", "fileCount": 13, "integrity": "sha512-amG72llr9pstfXOBOHve1WjiuKKAMnebcmMbPWDZ7BCevAoJLpugjuAPRsDINEyjT0a6tbaVx3DctkXIRbLuJw==", "signatures": [{"sig": "MEUCIF4V6mfUq0vDXlg4HtM83aKp9O0Zvng5jPTUr9whTvAKAiEA08qP5GHA9RO8AxTh08fCG2jj9UagRAuJ5HGzEJGHKPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 512313}, "main": "index.js", "addon": true, "gitHead": "44249d6299e650d3e577e71d04e65f83890a7358", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"streamx": "^2.13.0", "bare-path": "^2.0.0", "bare-events": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.2.3_1712170002513_0.4914262506557572", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "bare-fs", "version": "2.3.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.3.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "0872f8e33cf291c9fd527d827154f156a298d402", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.3.0.tgz", "fileCount": 13, "integrity": "sha512-TNFqa1B4N99pds2a5NYHR15o0ZpdNKbAeKTE/+G6ED/UeOavv8RY3dr/Fu99HW3zU3pXpo2kDNO8Sjsm2esfOw==", "signatures": [{"sig": "MEUCIC0jsHfZlAKbjaPecBS6ybZy+nCB0pzkg9e8tJGkH2oLAiEAmkPRC4sZdJcKtZ9QSlIIMsDohg1+SlDhh+zRJ0xZDys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 513491}, "main": "index.js", "addon": true, "gitHead": "e6ca8ae7370fb377e705d74c1b5ce59fe62a6986", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.7.3", "dependencies": {"bare-path": "^2.0.0", "bare-events": "^2.0.0", "bare-stream": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.3.0_1713956992571_0.8443990986836623", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "bare-fs", "version": "2.3.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.3.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "cdbd63dac7a552dfb2b87d18c822298d1efd213d", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.3.1.tgz", "fileCount": 13, "integrity": "sha512-W/Hfxc/6VehXlsgFtbB5B4xFcsCl+pAh30cYhoFyXErf6oGrwjh8SwiPAdHgpmWonKuYpZgGywN0SXt7dgsADA==", "signatures": [{"sig": "MEQCIG7UtXiPOlMhWKR8V9jAGa/rjc5nSJBQOJckqNTlg1euAiB8V2Fd5/DUPGDuNpgmNg+PKBBbpz65b60zBcpJcQPF7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 513506}, "main": "index.js", "addon": true, "gitHead": "9d8aa5cf29d7395050c9184ea7b7f4f124d245d3", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "21.7.3", "dependencies": {"bare-path": "^2.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.3.1_1717401978738_0.23224100113208257", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "bare-fs", "version": "2.3.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.3.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "389c637fae43b8b3b9033e00e8c79da99d1ab14b", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.3.2.tgz", "fileCount": 20, "integrity": "sha512-Kcq/FG3lhspzGHK+Q0IMfImuFOmaW/jFofBAUJuuG7H67879JeaPUppUHhgLjJKenfxiO6Ix2AGSd47Pf7mRxg==", "signatures": [{"sig": "MEUCIHls/X6CkjZgjtNsxIaB5Vev6RTSmw63HN04gneatIBuAiEA9Pl3CW/NSRpn74ab6KN7PwhyOHL1hVsprCBbeRqO740=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 868982}, "main": "index.js", "addon": true, "gitHead": "b989171c22d29294ee80e166eadbd04aeabadbec", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "22.6.0", "dependencies": {"bare-path": "^2.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.3.2_1724918504834_0.9311258946049761", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "bare-fs", "version": "2.3.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.3.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "0f66b203a72b9a632e9b5f2b6619d0ca19b758af", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.3.3.tgz", "fileCount": 20, "integrity": "sha512-7RY<PERSON><PERSON>+vZVCyAsMLi5SPu7QGauGGT8avnP/HO571ndEuV4MYdGXvLhtW67FuLPeEI8EiIY7zbbRR9x7x7HU0kgw==", "signatures": [{"sig": "MEUCIHrpc9j6UA50hh5X0w5r9TPa221KOqj0XhU+YNhw+wC9AiEAjm9uUoXygZ5S46VZmS0xrHWZ2ozkafqJiLQjSfGMLiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 873590}, "main": "index.js", "addon": true, "gitHead": "4b271612b480c5bdd6feff356816dc2571d40d1e", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"bare-path": "^2.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.3.3_1725011840678_0.7299036491583146", "host": "s3://npm-registry-packages"}}, "2.3.4": {"name": "bare-fs", "version": "2.3.4", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.3.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "339d3a9ee574bf58de3a9c93f45dd6f1c62c92d2", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.3.4.tgz", "fileCount": 21, "integrity": "sha512-7Y<PERSON>itZEq0ey5loOF5gdo1fZQFF7290GziT+VbAJ+JbYTJYaPZwuEz2r/Nq23sm4fjyTgUf2uJI2gkT3xAuSYA==", "signatures": [{"sig": "MEUCIFAi/1y8DSx6+epJc1ott98OKFnb1to0embKOMpLLps/AiEAidNepvCIUclofiDTv9xyPrbuO/Lt4iR/CC27nhifNQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1021446}, "main": "index.js", "addon": true, "gitHead": "cb29fbd96d7ce80c4d50ae4a46e764d6ca8b9926", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"bare-path": "^2.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.3.4_1725973009750_0.8913709206744758", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "bare-fs", "version": "3.0.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@3.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "10ec7b4f60d0538b628e4ddbe5d333ff830cec0e", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-3.0.0.tgz", "fileCount": 21, "integrity": "sha512-dTU3wvWDiF9Ixy0MSkZmDnbZD66ZfCQ6Dn2THnelvRqYU/JltuMePdlN5QP9/bVybOWzSLxFoeEggCIsGflAqw==", "signatures": [{"sig": "MEUCIHOJw6nuIF042N2IsThc4PgjGcbyUScjylO/qzrd1uS1AiEAqC9fD8rSTQgA2+fL0LX1J85Lphhp1STNfZRdeq3KhZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1021446}, "main": "index.js", "addon": true, "gitHead": "81a8cce12bf3582d617a41cf28a5d652543c12b9", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_3.0.0_1726042122300_0.7031094411110363", "host": "s3://npm-registry-packages"}}, "2.3.5": {"name": "bare-fs", "version": "2.3.5", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@2.3.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "05daa8e8206aeb46d13c2fe25a2cd3797b0d284a", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-2.3.5.tgz", "fileCount": 21, "integrity": "sha512-SlE9eTxifPDJrT6YgemQ1WGFleevzwY+XAP1Xqgl56HtcrisC2CHCZ2tq6dBpcH2TnNxwUEUGhweo+lrQtYuiw==", "signatures": [{"sig": "MEYCIQCpw5cOct9kW0V2lyVL7G3XKJMgAkxLJPAC1SePtEoWYQIhAJaOyut1it8MPFf2Y3bE3GetJF8lvhuROghd40TcOD2b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1017230}, "main": "index.js", "addon": true, "gitHead": "e0d012033423c28869bfa355576c22355218b9b3", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"bare-path": "^2.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_2.3.5_1726215881660_0.5021725865230604", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "bare-fs", "version": "3.0.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@3.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "2a7424fc07d339ec7aeacab3cb68f79114dda0ad", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-3.0.1.tgz", "fileCount": 21, "integrity": "sha512-/C3R6lbKUvBIWoLox8w6HJ1ARDsYunM+4z5+ZE5/MbuIFaNBBeJPKSI6xUTLS7iRrjCdKQ1wj4XgePdzUxtPVw==", "signatures": [{"sig": "MEQCIEAQuUFLsFQ3xXWWfZn/t+LGCcR9iVxNHZr5/QcEmmd8AiBJ4mlXUQFhS+4YNElSBYXvr1zZphqIi5M+C4B9TeGh+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1017230}, "main": "index.js", "addon": true, "gitHead": "71fcb1a664347b5b7c7dbd54a758c6afbb1f0879", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_3.0.1_1726215901462_0.7569115356855254", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "bare-fs", "version": "3.0.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@3.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "eead322798003125296989e5cb15be5c3e9d639c", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-3.0.2.tgz", "fileCount": 21, "integrity": "sha512-QWw1LQkaRvhZYbicfPdY7srt0Q5MO6BAQnomvBylVdgg+xAVse4sbYbNVtN0RdX6A6nimrykipER6NJDcR939g==", "signatures": [{"sig": "MEYCIQDDxVBjhuJnMDHvw4u/8RWM5LjQ2hxHJ9XTAYyhtuU5PgIhAK0z9U6LBAcAeWISoP+hIOZ9XiuXOR3XSCElz4iMMiNu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1025607}, "main": "index.js", "addon": true, "gitHead": "76137a58f5e492d5f9d698debc50cecaf67910ae", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_3.0.2_1726513055630_0.18739207291317572", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "bare-fs", "version": "3.1.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@3.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "e48dcf21a65e9e9a329cc9281c730ebc05754acb", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-3.1.0.tgz", "fileCount": 21, "integrity": "sha512-8qGUva/yp75zBZjxvfJ60b5yHK55UYHgQYJY+HtTxM/j/N2wVWh36Ao1kCU0KEukcmKp6WDnxSz0lyAd9YRhzg==", "signatures": [{"sig": "MEUCIQD+KTIzFPA+3FEtxEHC7w3w9UTJ0JgThpsHiRzRRvPoIAIgdNCbp+tHW8V2x7xy/JH40Jqya713bwKEUTht1pA1LZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1045750}, "main": "index.js", "addon": true, "gitHead": "59d4dd362eae9fa8ba18e0575ee2f54b01cd451c", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_3.1.0_1729168667254_0.8367651448405811", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "bare-fs", "version": "3.1.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@3.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "8438ecb47613b2208024b4b0aec488ff8aaca119", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-3.1.1.tgz", "fileCount": 21, "integrity": "sha512-AhJ8tNQxhYGfpYERMKshGXG5CJF7S2JpxYAGTa1+Bd78m4TKeMqj9UqmW8Z2ooAYeP0idv0fi/AYVomS9NqKRQ==", "signatures": [{"sig": "MEYCIQD4rz3s8pfgTnlLY7a2QCoJvq44ZYKxmVwa3Xr2qHHmkAIhAJqqY28SQswuOovCjfafNvg0EBF7LIdJTLLjrfnWLNx7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1098956}, "main": "index.js", "addon": true, "gitHead": "9857b66611c06b2ab1107b1f011376abac026641", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "22.9.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "standard": "^17.0.0", "cmake-bare": "^1.1.7"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_3.1.1_1729500372902_0.8181258675073066", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "bare-fs", "version": "4.0.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "fabc383c49450bdc3d9de0a3bf2ba2decff07d30", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.0.0.tgz", "fileCount": 21, "integrity": "sha512-ze6hmwEcRtUC+r1d3qgXkI59JlOoJBMpFSc60Mx312/EL7XVeMXvehN05H5bMhwaadgtCUtTpMsQXuOxd6dtKw==", "signatures": [{"sig": "MEUCIQCP9bXDnFd1u1UvLRz6STfAjjRN3N13f4k1+o2P/PQEgAIgGgJR9s5eSgUnjFBi+cxaI/MfE/q3Q20BCx2mS1ePs54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1101624}, "main": "index.js", "addon": true, "engines": {"bare": ">=1.7.0"}, "gitHead": "857f8f1334801b11c3b8a6da22820ec27fc108d4", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.2.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "prettier-config-standard": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.0.0_1732651211806_0.9945045878054708", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "bare-fs", "version": "4.0.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "85844f34da819c76754d545323a8b23ed3617c76", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.0.1.tgz", "fileCount": 21, "integrity": "sha512-ilQs4fm/l9eMfWY2dY0WCIUplSUp7U0CT1vrqMg1MUdeZl4fypu5UP0XcDBK5WBQPJAKP1b7XEodISmekH/CEg==", "signatures": [{"sig": "MEYCIQC2YjGgbusiluV148rfC46p94OkYLcEWF3hTUlwKGJFlgIhAJYqv0tgaOGrxTWGOJycNdO4uaxNmLaTIdDFaj+Sg3mx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 826117}, "main": "index.js", "addon": true, "engines": {"bare": ">=1.7.0"}, "gitHead": "e9b79ab74b2b1f2e40bd12191c7d62a9f1b6c0a1", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.3.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.0.0", "bare-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "prettier-config-standard": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.0.1_1733841949888_0.915564786146593", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.2": {"name": "bare-fs", "version": "4.0.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "a879c7b5d9242663ef80d75d6b99c2c6701664d6", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.0.2.tgz", "fileCount": 25, "integrity": "sha512-S5mmkMesiduMqnz51Bfh0Et9EX0aTCJxhsI4bvzFFLs8Z1AV8RDHadfY5CyLwdoLHgXbNBEN1gQcbEtGwuvixw==", "signatures": [{"sig": "MEUCIFjFukxBfvNhQL6pxRXTQAEHeeG6eTFkveUoHer/e5YeAiEA7VzfoeQVlW1UnkQ9rlk6HsFo3bGQUF8EnA9mEnodrxU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1038085}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "261b1c596f428610e8d275dec3bb4089430ac9cf", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.10.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.0.2_1742804076906_0.5859061115457174", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.0": {"name": "bare-fs", "version": "4.1.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "b7c7c5c8e2b4f8e3bd320b7d20effa7f6f7af9a0", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.0.tgz", "fileCount": 25, "integrity": "sha512-65ppKEeKvQ3G5G0IWbQymyzZ9QsWMAwpOOSBD/r9wGpQOGqgwe8vSjLLpKy08/M5f4V0pXbsTQQpLXwtJGs8Ww==", "signatures": [{"sig": "MEUCIQDFGNZXouYstpR1fKCAAHQPZCK+fETgAU18bEXww59fEgIgL7+EAv0s5WN1jPRtCxQoCchm1j0Jmyb2jA/KYGiqgJA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1042598}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "9199da43b302588834815a9e68953af5211e65ff", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.10.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.1.0_1743669590311_0.4739508927977385", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.1": {"name": "bare-fs", "version": "4.1.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "b969b8d692bacbae07421b56bc6b069ec104ce6c", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.1.tgz", "fileCount": 25, "integrity": "sha512-RNZckVKB69J31g0aY0x0Alc/RYwRLjlAsR8u8WQGhPBD8CdlUC4nJ6IbR6V6MA4wlIKt1vCoGhXK7wP08xrfsQ==", "signatures": [{"sig": "MEYCIQDfkPx2pi/Fw71BuK3AAZq0e+fc2IB9X6tBCinxDtgVsQIhAKM37wyOv1NLwco3uc/MFlbS83GNUlHwrQy/Y9kE6aq1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1042640}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "e680aac201c65200bec4c7ba112c8ba6d68fc1ec", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.10.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.1.1_1743669686017_0.19559842436826713", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.2": {"name": "bare-fs", "version": "4.1.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "5b048298019f489979d5a6afb480f5204ad4e89b", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.2.tgz", "fileCount": 25, "integrity": "sha512-8wSeOia5B7LwD4+h465y73KOdj5QHsbbuoUfPBi+pXgFJIPuG7SsiOdJuijWMyfid49eD+WivpfY7KT8gbAzBA==", "signatures": [{"sig": "MEUCIH7kqLEESbWL7MpMwRza234Pa6i76zaIXvSbEk1LZpbXAiEAhzKUxNlu5fvUUT4QNlVnAzcgTYaIeVr1FaU2MHYZqWE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1042720}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "6d07709dcb46bf149898f33b4b69cda80975cf5e", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "11.2.0", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.10.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.1.2_1743669892796_0.42754463118954966", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.3": {"name": "bare-fs", "version": "4.1.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.1.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "9b23313163d844e807aa6eafb4ba82b5f070eefc", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.3.tgz", "fileCount": 25, "integrity": "sha512-OeEZYIg+2qepaWLyphaOXHAHKo3xkM8y3BeGAvHdMN8GNWvEAU1Yw6rYpGzu/wDDbKxgEjVeVDpgGhDzaeMpjg==", "signatures": [{"sig": "MEUCIEHs0LQJunDWVAtQzU6/qCmra+DZKnQacwRpRzIWZk3nAiEA1tuHfDScaYCvNg5gdns1qjbUGkix/NTD2tQdyWEV1f0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1044354}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "220e5f4afa92506afebfa5760825174706c2ebec", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.1.3_1745583969221_0.7591022735628234", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.4": {"name": "bare-fs", "version": "4.1.4", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.1.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "3dd863925f29843e64a9030839c9cb3155714d40", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.4.tgz", "fileCount": 25, "integrity": "sha512-r8+26Voz8dGX3AYpJdFb1ZPaUSM8XOLCZvy+YGpRTmwPHIxA7Z3Jov/oMPtV7hfRQbOnH8qGlLTzQAbgtdNN0Q==", "signatures": [{"sig": "MEUCIDvVzZAFA3K4pPpAdhUndNw/rlCLWl7xqP0Oy45jiZvBAiEA/fbw7+B8vCBZDkKi8ZU4QE3BkyEGd0J1kgKcHHez4lM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1045202}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "6df1976c85114c1aa50527187af10b88c5eea1bb", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.1.4_1746089093099_0.94121533538788", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.5": {"name": "bare-fs", "version": "4.1.5", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-fs@4.1.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-fs#readme", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "dist": {"shasum": "1d06c076e68cc8bf97010d29af9e3ac3808cdcf7", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.5.tgz", "fileCount": 25, "integrity": "sha512-1zccWBMypln0jEE05LzZt+V/8y8AQsQQqxtklqaIyg5nu6OAYFhZxPXinJTSG+kU5qyNmeLgcn9AW7eHiCHVLA==", "signatures": [{"sig": "MEUCIQDbdK1XMbGMcQe5TI8jNb2Ex51akUCYvtoi5cruLBzo4QIgReV/COiDz1uDlBruQ7RrwKTKueH6dvs8qdfV56huH28=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1042644}, "addon": true, "types": "./index.d.ts", "engines": {"bare": ">=1.16.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "gitHead": "7f02214c57d2fb59d46c26ca1446959750724daa", "scripts": {"test": "prettier . --check && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-fs.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Native file system for Javascript", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"bare-path": "^3.0.0", "bare-events": "^2.5.4", "bare-stream": "^2.6.4"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.1.1", "prettier": "^3.4.1", "cmake-bare": "^1.1.7", "bare-buffer": "^3.0.2", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/bare-fs_4.1.5_1747126364391_0.718471812475634", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.6": {"name": "bare-fs", "version": "4.1.6", "description": "Native file system for Javascript", "exports": {"./package": "./package.json", ".": {"types": "./index.d.ts", "default": "./index.js"}, "./promises": {"types": "./promises.d.ts", "default": "./promises.js"}, "./constants": {"types": "./lib/constants.d.ts", "default": "./lib/constants.js"}}, "addon": true, "scripts": {"test": "prettier . --check && bare test.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-fs.git"}, "author": {"name": "Holepunch"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "homepage": "https://github.com/holepunchto/bare-fs#readme", "engines": {"bare": ">=1.16.0"}, "dependencies": {"bare-events": "^2.5.4", "bare-path": "^3.0.0", "bare-stream": "^2.6.4"}, "devDependencies": {"bare-buffer": "^3.0.2", "brittle": "^3.1.1", "cmake-bare": "^1.1.7", "prettier": "^3.4.1", "prettier-config-standard": "^7.0.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}, "_id": "bare-fs@4.1.6", "gitHead": "0c52674e75bcee78e476c75f93b42a9df07a70f9", "types": "./index.d.ts", "_nodeVersion": "24.2.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-25RsLF33BqooOEFNdMcEhMpJy8EoR88zSMrnOQOaM3USnOK2VmaJ1uaQEwPA6AQjrv1lXChScosN6CzbwbO9OQ==", "shasum": "0925521e7310f65cb1f154cab264f0b647a7cdef", "tarball": "https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.6.tgz", "fileCount": 25, "unpackedSize": 1046126, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICQCPokhnkDi+NL6N3ZL99yKh2+wHliWQFYW3f7L8AFWAiEA4rpVxxuDykJ0bnZYN0Xs+ftV+JZAJ5MhmrqRH0irZpE="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/bare-fs_4.1.6_1751628030671_0.5001768060113021"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-16T08:24:58.193Z", "modified": "2025-07-04T11:20:31.141Z", "1.5.3": "2023-05-16T08:24:58.385Z", "1.5.4": "2023-05-22T11:31:59.806Z", "1.5.5": "2023-05-23T12:29:17.979Z", "1.6.0": "2023-05-25T09:19:14.820Z", "1.6.1": "2023-05-25T10:37:55.073Z", "1.6.2": "2023-05-25T10:40:49.937Z", "1.6.3": "2023-05-29T14:49:09.978Z", "1.6.4": "2023-06-12T14:25:37.893Z", "1.6.5": "2023-06-13T08:59:00.458Z", "1.6.6": "2023-06-13T09:28:18.005Z", "1.6.7": "2023-06-13T11:00:09.139Z", "1.6.8": "2023-06-14T13:45:06.875Z", "1.6.9": "2023-06-22T12:21:54.672Z", "1.7.0": "2023-09-14T14:04:13.222Z", "1.7.1": "2023-09-14T16:20:25.714Z", "1.8.0": "2023-09-21T13:25:13.779Z", "1.9.0": "2023-09-25T12:26:40.737Z", "1.9.1": "2023-09-25T18:52:21.160Z", "1.10.0": "2023-10-02T19:02:22.551Z", "1.11.0": "2023-10-10T14:01:05.684Z", "1.12.0": "2023-10-13T09:22:48.091Z", "1.12.1": "2023-10-19T13:33:46.891Z", "1.13.0": "2023-10-25T11:54:25.239Z", "1.13.2": "2023-10-25T12:02:55.206Z", "1.13.3": "2023-10-25T12:05:11.458Z", "2.0.0": "2023-11-16T11:51:28.526Z", "2.1.0": "2023-11-21T14:54:40.793Z", "2.1.1": "2024-02-02T13:03:44.750Z", "2.1.2": "2024-02-09T10:03:10.466Z", "2.1.3": "2024-02-09T16:58:04.943Z", "2.1.4": "2024-02-13T08:46:00.796Z", "2.1.5": "2024-02-13T08:46:42.256Z", "2.2.0": "2024-02-23T10:25:47.021Z", "2.2.1": "2024-02-29T16:30:43.678Z", "2.2.2": "2024-03-13T11:39:56.962Z", "2.2.3": "2024-04-03T18:46:42.739Z", "2.3.0": "2024-04-24T11:09:52.806Z", "2.3.1": "2024-06-03T08:06:18.986Z", "2.3.2": "2024-08-29T08:01:45.033Z", "2.3.3": "2024-08-30T09:57:20.887Z", "2.3.4": "2024-09-10T12:56:49.966Z", "3.0.0": "2024-09-11T08:08:42.581Z", "2.3.5": "2024-09-13T08:24:41.872Z", "3.0.1": "2024-09-13T08:25:01.676Z", "3.0.2": "2024-09-16T18:57:35.871Z", "3.1.0": "2024-10-17T12:37:47.606Z", "3.1.1": "2024-10-21T08:46:13.123Z", "4.0.0": "2024-11-26T20:00:12.059Z", "4.0.1": "2024-12-10T14:45:50.158Z", "4.0.2": "2025-03-24T08:14:37.130Z", "4.1.0": "2025-04-03T08:39:50.585Z", "4.1.1": "2025-04-03T08:41:26.324Z", "4.1.2": "2025-04-03T08:44:53.090Z", "4.1.3": "2025-04-25T12:26:09.420Z", "4.1.4": "2025-05-01T08:44:53.326Z", "4.1.5": "2025-05-13T08:52:44.604Z", "4.1.6": "2025-07-04T11:20:30.943Z"}, "bugs": {"url": "https://github.com/holepunchto/bare-fs/issues"}, "author": {"name": "Holepunch"}, "license": "Apache-2.0", "homepage": "https://github.com/holepunchto/bare-fs#readme", "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-fs.git"}, "description": "Native file system for Javascript", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# bare-fs\n\nNative file system for Javascript.\n\n```\nnpm i bare-fs\n```\n\n## Usage\n\n```js\nconst fs = require('bare-fs')\n\n// Currently supports:\n\nfs.access\nfs.chmod\nfs.close\nfs.copyFile\nfs.exists\nfs.fstat\nfs.ftruncate\nfs.lstat\nfs.mkdir\nfs.open\nfs.opendir\nfs.read\nfs.readdir\nfs.readlink\nfs.readv\nfs.realpath\nfs.rename\nfs.rmdir\nfs.stat\nfs.symlink\nfs.unlink\nfs.watch\nfs.write\nfs.writev\n\nfs.appendFile\nfs.readFile\nfs.writeFile\n\nfs.promises.access\nfs.promises.chmod\nfs.promises.copyFile\nfs.promises.lstat\nfs.promises.mkdir\nfs.promises.opendir\nfs.promises.readdir\nfs.promises.readlink\nfs.promises.realpath\nfs.promises.rename\nfs.promises.rmdir\nfs.promises.stat\nfs.promises.symlink\nfs.promises.unlink\nfs.promises.watch\n\nfs.promises.appendFile\nfs.promises.readFile\nfs.promises.writeFile\n\nfs.createReadStream\nfs.createWriteStream\n\nfs.accessSync\nfs.chmodSync\nfs.closeSync\nfs.copyFileSync\nfs.existsSync\nfs.fchmodSync\nfs.fstatSync\nfs.ftruncateSync\nfs.lstatSync\nfs.mkdirSync\nfs.openSync\nfs.opendirSync\nfs.readSync\nfs.readdirSync\nfs.readlinkSync\nfs.realpathSync\nfs.renameSync\nfs.rmdirSync\nfs.statSync\nfs.symlinkSync\nfs.unlinkSync\nfs.writeSync\n\nfs.appendFileSync\nfs.readFileSync\nfs.writeFileSync\n```\n\n## License\n\nApache-2.0\n", "readmeFilename": "README.md"}