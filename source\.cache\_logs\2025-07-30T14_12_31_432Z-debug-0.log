0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@11.5.1
2 info using node@v22.17.1
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:D:\Github\Kontext\source\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm install puppeteer @types/puppeteer
8 verbose argv "install" "puppeteer" "@types/puppeteer"
9 verbose logfile logs-max:10 dir:D:\Github\Kontext\source\.cache\_logs\2025-07-30T14_12_31_432Z-
10 verbose logfile D:\Github\Kontext\source\.cache\_logs\2025-07-30T14_12_31_432Z-debug-0.log
11 silly logfile done cleaning log files
12 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:543162368
13 silly idealTree buildDeps
14 silly fetch manifest node-fetch@^3.3.2
15 silly packumentCache full:https://registry.npmjs.org/node-fetch cache-miss
16 http fetch GET 200 https://registry.npmjs.org/node-fetch 612ms (cache miss)
17 silly packumentCache full:https://registry.npmjs.org/node-fetch set size:undefined disposed:false
18 silly fetch manifest puppeteer@*
19 silly packumentCache full:https://registry.npmjs.org/puppeteer cache-miss
20 http fetch GET 200 https://registry.npmjs.org/puppeteer 293ms (cache miss)
21 silly packumentCache full:https://registry.npmjs.org/puppeteer set size:undefined disposed:false
22 silly fetch manifest @types/puppeteer@*
23 silly packumentCache full:https://registry.npmjs.org/@types%2fpuppeteer cache-miss
24 http fetch GET 200 https://registry.npmjs.org/@types%2fpuppeteer 146ms (cache miss)
25 silly packumentCache full:https://registry.npmjs.org/@types%2fpuppeteer set size:undefined disposed:false
26 silly placeDep ROOT @types/puppeteer@5.4.7 OK for:  want: *
27 silly placeDep ROOT node-fetch@3.3.2 OK for:  want: ^3.3.2
28 silly placeDep ROOT puppeteer@24.15.0 OK for:  want: *
29 silly fetch manifest data-uri-to-buffer@^4.0.0
30 silly packumentCache full:https://registry.npmjs.org/data-uri-to-buffer cache-miss
31 silly fetch manifest fetch-blob@^3.1.4
32 silly packumentCache full:https://registry.npmjs.org/fetch-blob cache-miss
33 silly fetch manifest formdata-polyfill@^4.0.10
34 silly packumentCache full:https://registry.npmjs.org/formdata-polyfill cache-miss
35 silly fetch manifest @puppeteer/browsers@2.10.6
36 silly packumentCache full:https://registry.npmjs.org/@puppeteer%2fbrowsers cache-miss
37 silly fetch manifest chromium-bidi@7.2.0
38 silly packumentCache full:https://registry.npmjs.org/chromium-bidi cache-miss
39 silly fetch manifest cosmiconfig@^9.0.0
40 silly packumentCache full:https://registry.npmjs.org/cosmiconfig cache-miss
41 silly fetch manifest devtools-protocol@0.0.1464554
42 silly packumentCache full:https://registry.npmjs.org/devtools-protocol cache-miss
43 silly fetch manifest puppeteer-core@24.15.0
44 silly packumentCache full:https://registry.npmjs.org/puppeteer-core cache-miss
45 silly fetch manifest typed-query-selector@^2.12.0
46 silly packumentCache full:https://registry.npmjs.org/typed-query-selector cache-miss
47 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer 167ms (cache miss)
48 silly packumentCache full:https://registry.npmjs.org/data-uri-to-buffer set size:undefined disposed:false
49 http fetch GET 200 https://registry.npmjs.org/chromium-bidi 302ms (cache miss)
50 silly packumentCache full:https://registry.npmjs.org/chromium-bidi set size:undefined disposed:false
51 http fetch GET 200 https://registry.npmjs.org/cosmiconfig 482ms (cache miss)
52 silly packumentCache full:https://registry.npmjs.org/cosmiconfig set size:undefined disposed:false
53 http fetch GET 200 https://registry.npmjs.org/formdata-polyfill 521ms (cache miss)
54 silly packumentCache full:https://registry.npmjs.org/formdata-polyfill set size:undefined disposed:false
55 http fetch GET 200 https://registry.npmjs.org/devtools-protocol 551ms (cache miss)
56 silly packumentCache full:https://registry.npmjs.org/devtools-protocol set size:undefined disposed:false
57 http fetch GET 200 https://registry.npmjs.org/fetch-blob 711ms (cache miss)
58 silly packumentCache full:https://registry.npmjs.org/fetch-blob set size:undefined disposed:false
59 http fetch GET 200 https://registry.npmjs.org/@puppeteer%2fbrowsers 770ms (cache miss)
60 silly packumentCache full:https://registry.npmjs.org/@puppeteer%2fbrowsers set size:undefined disposed:false
61 http fetch GET 200 https://registry.npmjs.org/typed-query-selector 1300ms (cache miss)
62 silly packumentCache full:https://registry.npmjs.org/typed-query-selector set size:undefined disposed:false
63 http fetch GET 200 https://registry.npmjs.org/puppeteer-core 1379ms (cache miss)
64 silly packumentCache full:https://registry.npmjs.org/puppeteer-core set size:undefined disposed:false
65 silly placeDep ROOT data-uri-to-buffer@4.0.1 OK for: node-fetch@3.3.2 want: ^4.0.0
66 silly placeDep ROOT fetch-blob@3.2.0 OK for: node-fetch@3.3.2 want: ^3.1.4
67 silly placeDep ROOT formdata-polyfill@4.0.10 OK for: node-fetch@3.3.2 want: ^4.0.10
68 silly fetch manifest node-domexception@^1.0.0
69 silly packumentCache full:https://registry.npmjs.org/node-domexception cache-miss
70 silly fetch manifest web-streams-polyfill@^3.0.3
71 silly packumentCache full:https://registry.npmjs.org/web-streams-polyfill cache-miss
72 http fetch GET 200 https://registry.npmjs.org/web-streams-polyfill 95ms (cache miss)
73 silly packumentCache full:https://registry.npmjs.org/web-streams-polyfill set size:undefined disposed:false
74 http fetch GET 200 https://registry.npmjs.org/node-domexception 122ms (cache miss)
75 silly packumentCache full:https://registry.npmjs.org/node-domexception set size:undefined disposed:false
76 silly placeDep ROOT node-domexception@1.0.0 OK for: fetch-blob@3.2.0 want: ^1.0.0
77 silly placeDep ROOT web-streams-polyfill@3.3.3 OK for: fetch-blob@3.2.0 want: ^3.0.3
78 silly fetch manifest typescript@>=4.9.5
79 silly packumentCache full:https://registry.npmjs.org/typescript cache-miss
80 http fetch GET 200 https://registry.npmjs.org/typescript 594ms (cache miss)
81 silly packumentCache full:https://registry.npmjs.org/typescript set size:undefined disposed:false
82 silly placeDep ROOT @puppeteer/browsers@2.10.6 OK for: puppeteer@24.15.0 want: 2.10.6
83 silly placeDep ROOT chromium-bidi@7.2.0 OK for: puppeteer@24.15.0 want: 7.2.0
84 silly placeDep ROOT devtools-protocol@0.0.1464554 OK for: chromium-bidi@7.2.0 want: *
85 silly placeDep ROOT cosmiconfig@9.0.0 OK for: puppeteer@24.15.0 want: ^9.0.0
86 silly placeDep ROOT puppeteer-core@24.15.0 OK for: puppeteer@24.15.0 want: 24.15.0
87 silly placeDep ROOT typed-query-selector@2.12.0 OK for: puppeteer@24.15.0 want: ^2.12.0
88 silly fetch manifest debug@^4.4.1
89 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
90 silly fetch manifest extract-zip@^2.0.1
91 silly packumentCache full:https://registry.npmjs.org/extract-zip cache-miss
92 silly fetch manifest progress@^2.0.3
93 silly packumentCache full:https://registry.npmjs.org/progress cache-miss
94 silly fetch manifest proxy-agent@^6.5.0
95 silly packumentCache full:https://registry.npmjs.org/proxy-agent cache-miss
96 silly fetch manifest tar-fs@^3.1.0
97 silly packumentCache full:https://registry.npmjs.org/tar-fs cache-miss
98 silly fetch manifest yargs@^17.7.2
99 silly packumentCache full:https://registry.npmjs.org/yargs cache-miss
100 silly fetch manifest semver@^7.7.2
101 silly packumentCache full:https://registry.npmjs.org/semver cache-miss
102 silly fetch manifest mitt@^3.0.1
103 silly packumentCache full:https://registry.npmjs.org/mitt cache-miss
104 silly fetch manifest zod@^3.24.1
105 silly packumentCache full:https://registry.npmjs.org/zod cache-miss
106 silly fetch manifest env-paths@^2.2.1
107 silly packumentCache full:https://registry.npmjs.org/env-paths cache-miss
108 silly fetch manifest import-fresh@^3.3.0
109 silly packumentCache full:https://registry.npmjs.org/import-fresh cache-miss
110 silly fetch manifest js-yaml@^4.1.0
111 silly packumentCache full:https://registry.npmjs.org/js-yaml cache-miss
112 silly fetch manifest parse-json@^5.2.0
113 silly packumentCache full:https://registry.npmjs.org/parse-json cache-miss
114 silly fetch manifest debug@^4.4.1
115 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
116 silly fetch manifest ws@^8.18.3
117 silly packumentCache full:https://registry.npmjs.org/ws cache-miss
118 http fetch GET 200 https://registry.npmjs.org/semver 100ms (cache miss)
119 silly packumentCache full:https://registry.npmjs.org/semver set size:undefined disposed:false
120 http fetch GET 200 https://registry.npmjs.org/mitt 163ms (cache miss)
121 silly packumentCache full:https://registry.npmjs.org/mitt set size:undefined disposed:false
122 http fetch GET 200 https://registry.npmjs.org/progress 170ms (cache miss)
123 silly packumentCache full:https://registry.npmjs.org/progress set size:undefined disposed:false
124 http fetch GET 200 https://registry.npmjs.org/extract-zip 177ms (cache miss)
125 silly packumentCache full:https://registry.npmjs.org/extract-zip set size:undefined disposed:false
126 http fetch GET 200 https://registry.npmjs.org/debug 190ms (cache miss)
127 silly packumentCache full:https://registry.npmjs.org/debug set size:undefined disposed:false
128 http fetch GET 200 https://registry.npmjs.org/tar-fs 196ms (cache miss)
129 silly packumentCache full:https://registry.npmjs.org/tar-fs set size:undefined disposed:false
130 http fetch GET 200 https://registry.npmjs.org/proxy-agent 212ms (cache miss)
131 silly packumentCache full:https://registry.npmjs.org/proxy-agent set size:undefined disposed:false
132 http fetch GET 200 https://registry.npmjs.org/yargs 239ms (cache miss)
133 silly packumentCache full:https://registry.npmjs.org/yargs set size:undefined disposed:false
134 http fetch GET 200 https://registry.npmjs.org/zod 254ms (cache miss)
135 silly packumentCache full:https://registry.npmjs.org/zod set size:undefined disposed:false
136 http fetch GET 200 https://registry.npmjs.org/import-fresh 457ms (cache miss)
137 silly packumentCache full:https://registry.npmjs.org/import-fresh set size:undefined disposed:false
138 http fetch GET 200 https://registry.npmjs.org/debug 470ms (cache miss)
139 silly packumentCache full:https://registry.npmjs.org/debug set size:undefined disposed:false
140 http fetch GET 200 https://registry.npmjs.org/js-yaml 477ms (cache miss)
141 silly packumentCache full:https://registry.npmjs.org/js-yaml set size:undefined disposed:false
142 http fetch GET 200 https://registry.npmjs.org/parse-json 486ms (cache miss)
143 silly packumentCache full:https://registry.npmjs.org/parse-json set size:undefined disposed:false
144 http fetch GET 200 https://registry.npmjs.org/ws 486ms (cache miss)
145 silly packumentCache full:https://registry.npmjs.org/ws set size:undefined disposed:false
146 http fetch GET 200 https://registry.npmjs.org/env-paths 512ms (cache miss)
147 silly packumentCache full:https://registry.npmjs.org/env-paths set size:undefined disposed:false
148 silly placeDep ROOT debug@4.4.1 OK for: @puppeteer/browsers@2.10.6 want: ^4.4.1
149 silly placeDep ROOT extract-zip@2.0.1 OK for: @puppeteer/browsers@2.10.6 want: ^2.0.1
150 silly placeDep ROOT progress@2.0.3 OK for: @puppeteer/browsers@2.10.6 want: ^2.0.3
151 silly placeDep ROOT proxy-agent@6.5.0 OK for: @puppeteer/browsers@2.10.6 want: ^6.5.0
152 silly placeDep ROOT semver@7.7.2 OK for: @puppeteer/browsers@2.10.6 want: ^7.7.2
153 silly placeDep ROOT tar-fs@3.1.0 OK for: @puppeteer/browsers@2.10.6 want: ^3.1.0
154 silly placeDep ROOT yargs@17.7.2 OK for: @puppeteer/browsers@2.10.6 want: ^17.7.2
155 silly fetch manifest ms@^2.1.3
156 silly packumentCache full:https://registry.npmjs.org/ms cache-miss
157 silly fetch manifest get-stream@^5.1.0
158 silly packumentCache full:https://registry.npmjs.org/get-stream cache-miss
159 silly fetch manifest yauzl@^2.10.0
160 silly packumentCache full:https://registry.npmjs.org/yauzl cache-miss
161 silly fetch manifest @types/yauzl@^2.9.1
162 silly packumentCache full:https://registry.npmjs.org/@types%2fyauzl cache-miss
163 silly fetch manifest agent-base@^7.1.2
164 silly packumentCache full:https://registry.npmjs.org/agent-base cache-miss
165 silly fetch manifest http-proxy-agent@^7.0.1
166 silly packumentCache full:https://registry.npmjs.org/http-proxy-agent cache-miss
167 silly fetch manifest https-proxy-agent@^7.0.6
168 silly packumentCache full:https://registry.npmjs.org/https-proxy-agent cache-miss
169 silly fetch manifest lru-cache@^7.14.1
170 silly packumentCache full:https://registry.npmjs.org/lru-cache cache-miss
171 silly fetch manifest pac-proxy-agent@^7.1.0
172 silly packumentCache full:https://registry.npmjs.org/pac-proxy-agent cache-miss
173 silly fetch manifest proxy-from-env@^1.1.0
174 silly packumentCache full:https://registry.npmjs.org/proxy-from-env cache-miss
175 silly fetch manifest socks-proxy-agent@^8.0.5
176 silly packumentCache full:https://registry.npmjs.org/socks-proxy-agent cache-miss
177 silly fetch manifest pump@^3.0.0
178 silly packumentCache full:https://registry.npmjs.org/pump cache-miss
179 silly fetch manifest tar-stream@^3.1.5
180 silly packumentCache full:https://registry.npmjs.org/tar-stream cache-miss
181 silly fetch manifest bare-fs@^4.0.1
182 silly packumentCache full:https://registry.npmjs.org/bare-fs cache-miss
183 silly fetch manifest bare-path@^3.0.0
184 silly packumentCache full:https://registry.npmjs.org/bare-path cache-miss
185 http fetch GET 200 https://registry.npmjs.org/agent-base 92ms (cache miss)
186 silly packumentCache full:https://registry.npmjs.org/agent-base set size:undefined disposed:false
187 silly fetch manifest y18n@^5.0.5
188 silly packumentCache full:https://registry.npmjs.org/y18n cache-miss
189 http fetch GET 200 https://registry.npmjs.org/ms 101ms (cache miss)
190 silly packumentCache full:https://registry.npmjs.org/ms set size:undefined disposed:false
191 silly fetch manifest cliui@^8.0.1
192 silly packumentCache full:https://registry.npmjs.org/cliui cache-miss
193 http fetch GET 200 https://registry.npmjs.org/yauzl 200ms (cache miss)
194 silly packumentCache full:https://registry.npmjs.org/yauzl set size:undefined disposed:false
195 silly fetch manifest escalade@^3.1.1
196 silly packumentCache full:https://registry.npmjs.org/escalade cache-miss
197 http fetch GET 200 https://registry.npmjs.org/pac-proxy-agent 206ms (cache miss)
198 silly packumentCache full:https://registry.npmjs.org/pac-proxy-agent set size:undefined disposed:false
199 silly fetch manifest string-width@^4.2.3
200 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
201 http fetch GET 200 https://registry.npmjs.org/proxy-from-env 213ms (cache miss)
202 silly packumentCache full:https://registry.npmjs.org/proxy-from-env set size:undefined disposed:false
203 silly fetch manifest yargs-parser@^21.1.1
204 silly packumentCache full:https://registry.npmjs.org/yargs-parser cache-miss
205 http fetch GET 200 https://registry.npmjs.org/http-proxy-agent 217ms (cache miss)
206 silly packumentCache full:https://registry.npmjs.org/http-proxy-agent set size:undefined disposed:false
207 silly fetch manifest get-caller-file@^2.0.5
208 silly packumentCache full:https://registry.npmjs.org/get-caller-file cache-miss
209 http fetch GET 200 https://registry.npmjs.org/https-proxy-agent 223ms (cache miss)
210 silly packumentCache full:https://registry.npmjs.org/https-proxy-agent set size:undefined disposed:false
211 silly fetch manifest require-directory@^2.1.1
212 silly packumentCache full:https://registry.npmjs.org/require-directory cache-miss
213 http fetch GET 200 https://registry.npmjs.org/pump 225ms (cache miss)
214 silly packumentCache full:https://registry.npmjs.org/pump set size:undefined disposed:false
215 http fetch GET 200 https://registry.npmjs.org/socks-proxy-agent 229ms (cache miss)
216 silly packumentCache full:https://registry.npmjs.org/socks-proxy-agent set size:undefined disposed:false
217 http fetch GET 200 https://registry.npmjs.org/bare-path 232ms (cache miss)
218 silly packumentCache full:https://registry.npmjs.org/bare-path set size:undefined disposed:false
219 http fetch GET 200 https://registry.npmjs.org/y18n 145ms (cache miss)
220 silly packumentCache full:https://registry.npmjs.org/y18n set size:undefined disposed:false
221 http fetch GET 200 https://registry.npmjs.org/cliui 143ms (cache miss)
222 silly packumentCache full:https://registry.npmjs.org/cliui set size:undefined disposed:false
223 http fetch GET 200 https://registry.npmjs.org/get-stream 246ms (cache miss)
224 silly packumentCache full:https://registry.npmjs.org/get-stream set size:undefined disposed:false
225 http fetch GET 200 https://registry.npmjs.org/bare-fs 240ms (cache miss)
226 silly packumentCache full:https://registry.npmjs.org/bare-fs set size:undefined disposed:false
227 http fetch GET 200 https://registry.npmjs.org/tar-stream 243ms (cache miss)
228 silly packumentCache full:https://registry.npmjs.org/tar-stream set size:undefined disposed:false
229 http fetch GET 200 https://registry.npmjs.org/@types%2fyauzl 251ms (cache miss)
230 silly packumentCache full:https://registry.npmjs.org/@types%2fyauzl set size:undefined disposed:false
231 http fetch GET 200 https://registry.npmjs.org/lru-cache 253ms (cache miss)
232 silly packumentCache full:https://registry.npmjs.org/lru-cache set size:undefined disposed:false
233 http fetch GET 200 https://registry.npmjs.org/escalade 125ms (cache miss)
234 silly packumentCache full:https://registry.npmjs.org/escalade set size:undefined disposed:false
235 http fetch GET 200 https://registry.npmjs.org/string-width 124ms (cache miss)
236 silly packumentCache full:https://registry.npmjs.org/string-width set size:undefined disposed:false
237 http fetch GET 200 https://registry.npmjs.org/get-caller-file 138ms (cache miss)
238 silly packumentCache full:https://registry.npmjs.org/get-caller-file set size:undefined disposed:false
239 http fetch GET 200 https://registry.npmjs.org/yargs-parser 145ms (cache miss)
240 silly packumentCache full:https://registry.npmjs.org/yargs-parser set size:undefined disposed:false
241 http fetch GET 200 https://registry.npmjs.org/require-directory 140ms (cache miss)
242 silly packumentCache full:https://registry.npmjs.org/require-directory set size:undefined disposed:false
243 silly placeDep ROOT mitt@3.0.1 OK for: chromium-bidi@7.2.0 want: ^3.0.1
244 silly placeDep ROOT zod@3.25.76 OK for: chromium-bidi@7.2.0 want: ^3.24.1
245 silly placeDep ROOT env-paths@2.2.1 OK for: cosmiconfig@9.0.0 want: ^2.2.1
246 silly placeDep ROOT import-fresh@3.3.1 OK for: cosmiconfig@9.0.0 want: ^3.3.0
247 silly placeDep ROOT js-yaml@4.1.0 OK for: cosmiconfig@9.0.0 want: ^4.1.0
248 silly placeDep ROOT parse-json@5.2.0 OK for: cosmiconfig@9.0.0 want: ^5.2.0
249 silly fetch manifest parent-module@^1.0.0
250 silly packumentCache full:https://registry.npmjs.org/parent-module cache-miss
251 silly fetch manifest resolve-from@^4.0.0
252 silly packumentCache full:https://registry.npmjs.org/resolve-from cache-miss
253 silly fetch manifest argparse@^2.0.1
254 silly packumentCache full:https://registry.npmjs.org/argparse cache-miss
255 silly fetch manifest error-ex@^1.3.1
256 silly packumentCache full:https://registry.npmjs.org/error-ex cache-miss
257 silly fetch manifest @babel/code-frame@^7.0.0
258 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame cache-miss
259 silly fetch manifest lines-and-columns@^1.1.6
260 silly packumentCache full:https://registry.npmjs.org/lines-and-columns cache-miss
261 silly fetch manifest json-parse-even-better-errors@^2.3.0
262 silly packumentCache full:https://registry.npmjs.org/json-parse-even-better-errors cache-miss
263 http fetch GET 200 https://registry.npmjs.org/json-parse-even-better-errors 105ms (cache miss)
264 silly packumentCache full:https://registry.npmjs.org/json-parse-even-better-errors set size:undefined disposed:false
265 http fetch GET 200 https://registry.npmjs.org/resolve-from 139ms (cache miss)
266 silly packumentCache full:https://registry.npmjs.org/resolve-from set size:undefined disposed:false
267 http fetch GET 200 https://registry.npmjs.org/error-ex 140ms (cache miss)
268 silly packumentCache full:https://registry.npmjs.org/error-ex set size:undefined disposed:false
269 http fetch GET 200 https://registry.npmjs.org/argparse 143ms (cache miss)
270 silly packumentCache full:https://registry.npmjs.org/argparse set size:undefined disposed:false
271 http fetch GET 200 https://registry.npmjs.org/lines-and-columns 146ms (cache miss)
272 silly packumentCache full:https://registry.npmjs.org/lines-and-columns set size:undefined disposed:false
273 http fetch GET 200 https://registry.npmjs.org/parent-module 153ms (cache miss)
274 silly packumentCache full:https://registry.npmjs.org/parent-module set size:undefined disposed:false
275 http fetch GET 200 https://registry.npmjs.org/@babel%2fcode-frame 159ms (cache miss)
276 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame set size:undefined disposed:false
277 silly placeDep ROOT ms@2.1.3 OK for: debug@4.4.1 want: ^2.1.3
278 silly placeDep ROOT @types/yauzl@2.10.3 OK for: extract-zip@2.0.1 want: ^2.9.1
279 silly placeDep ROOT get-stream@5.2.0 OK for: extract-zip@2.0.1 want: ^5.1.0
280 silly placeDep ROOT yauzl@2.10.0 OK for: extract-zip@2.0.1 want: ^2.10.0
281 silly fetch manifest fd-slicer@~1.1.0
282 silly packumentCache full:https://registry.npmjs.org/fd-slicer cache-miss
283 silly fetch manifest buffer-crc32@~0.2.3
284 silly packumentCache full:https://registry.npmjs.org/buffer-crc32 cache-miss
285 http fetch GET 200 https://registry.npmjs.org/buffer-crc32 144ms (cache miss)
286 silly packumentCache full:https://registry.npmjs.org/buffer-crc32 set size:undefined disposed:false
287 http fetch GET 200 https://registry.npmjs.org/fd-slicer 211ms (cache miss)
288 silly packumentCache full:https://registry.npmjs.org/fd-slicer set size:undefined disposed:false
289 silly placeDep ROOT pump@3.0.3 OK for: get-stream@5.2.0 want: ^3.0.0
290 silly fetch manifest end-of-stream@^1.1.0
291 silly packumentCache full:https://registry.npmjs.org/end-of-stream cache-miss
292 silly fetch manifest once@^1.3.1
293 silly packumentCache full:https://registry.npmjs.org/once cache-miss
294 http fetch GET 200 https://registry.npmjs.org/once 120ms (cache miss)
295 silly packumentCache full:https://registry.npmjs.org/once set size:undefined disposed:false
296 http fetch GET 200 https://registry.npmjs.org/end-of-stream 120ms (cache miss)
297 silly packumentCache full:https://registry.npmjs.org/end-of-stream set size:undefined disposed:false
298 silly placeDep ROOT parent-module@1.0.1 OK for: import-fresh@3.3.1 want: ^1.0.0
299 silly placeDep ROOT resolve-from@4.0.0 OK for: import-fresh@3.3.1 want: ^4.0.0
300 silly fetch manifest callsites@^3.0.0
301 silly packumentCache full:https://registry.npmjs.org/callsites cache-miss
302 http fetch GET 200 https://registry.npmjs.org/callsites 119ms (cache miss)
303 silly packumentCache full:https://registry.npmjs.org/callsites set size:undefined disposed:false
304 silly placeDep ROOT argparse@2.0.1 OK for: js-yaml@4.1.0 want: ^2.0.1
305 silly placeDep ROOT callsites@3.1.0 OK for: parent-module@1.0.1 want: ^3.0.0
306 silly placeDep ROOT @babel/code-frame@7.27.1 OK for: parse-json@5.2.0 want: ^7.0.0
307 silly placeDep ROOT error-ex@1.3.2 OK for: parse-json@5.2.0 want: ^1.3.1
308 silly placeDep ROOT json-parse-even-better-errors@2.3.1 OK for: parse-json@5.2.0 want: ^2.3.0
309 silly placeDep ROOT lines-and-columns@1.2.4 OK for: parse-json@5.2.0 want: ^1.1.6
310 silly fetch manifest js-tokens@^4.0.0
311 silly packumentCache full:https://registry.npmjs.org/js-tokens cache-miss
312 silly fetch manifest picocolors@^1.1.1
313 silly packumentCache full:https://registry.npmjs.org/picocolors cache-miss
314 silly fetch manifest @babel/helper-validator-identifier@^7.27.1
315 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier cache-miss
316 silly fetch manifest is-arrayish@^0.2.1
317 silly packumentCache full:https://registry.npmjs.org/is-arrayish cache-miss
318 http fetch GET 200 https://registry.npmjs.org/is-arrayish 89ms (cache miss)
319 silly packumentCache full:https://registry.npmjs.org/is-arrayish set size:undefined disposed:false
320 http fetch GET 200 https://registry.npmjs.org/picocolors 137ms (cache miss)
321 silly packumentCache full:https://registry.npmjs.org/picocolors set size:undefined disposed:false
322 http fetch GET 200 https://registry.npmjs.org/js-tokens 140ms (cache miss)
323 silly packumentCache full:https://registry.npmjs.org/js-tokens set size:undefined disposed:false
324 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-validator-identifier 148ms (cache miss)
325 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier set size:undefined disposed:false
326 silly placeDep ROOT @babel/helper-validator-identifier@7.27.1 OK for: @babel/code-frame@7.27.1 want: ^7.27.1
327 silly placeDep ROOT js-tokens@4.0.0 OK for: @babel/code-frame@7.27.1 want: ^4.0.0
328 silly placeDep ROOT picocolors@1.1.1 OK for: @babel/code-frame@7.27.1 want: ^1.1.1
329 silly placeDep ROOT is-arrayish@0.2.1 OK for: error-ex@1.3.2 want: ^0.2.1
330 silly placeDep ROOT agent-base@7.1.4 OK for: proxy-agent@6.5.0 want: ^7.1.2
331 silly placeDep ROOT http-proxy-agent@7.0.2 OK for: proxy-agent@6.5.0 want: ^7.0.1
332 silly placeDep ROOT https-proxy-agent@7.0.6 OK for: proxy-agent@6.5.0 want: ^7.0.6
333 silly placeDep ROOT lru-cache@7.18.3 OK for: proxy-agent@6.5.0 want: ^7.14.1
334 silly placeDep ROOT pac-proxy-agent@7.2.0 OK for: proxy-agent@6.5.0 want: ^7.1.0
335 silly placeDep ROOT proxy-from-env@1.1.0 OK for: proxy-agent@6.5.0 want: ^1.1.0
336 silly placeDep ROOT socks-proxy-agent@8.0.5 OK for: proxy-agent@6.5.0 want: ^8.0.5
337 silly fetch manifest @tootallnate/quickjs-emscripten@^0.23.0
338 silly packumentCache full:https://registry.npmjs.org/@tootallnate%2fquickjs-emscripten cache-miss
339 silly fetch manifest get-uri@^6.0.1
340 silly packumentCache full:https://registry.npmjs.org/get-uri cache-miss
341 silly fetch manifest pac-resolver@^7.0.1
342 silly packumentCache full:https://registry.npmjs.org/pac-resolver cache-miss
343 silly fetch manifest socks@^2.8.3
344 silly packumentCache full:https://registry.npmjs.org/socks cache-miss
345 http fetch GET 200 https://registry.npmjs.org/pac-resolver 135ms (cache miss)
346 silly packumentCache full:https://registry.npmjs.org/pac-resolver set size:undefined disposed:false
347 http fetch GET 200 https://registry.npmjs.org/get-uri 142ms (cache miss)
348 silly packumentCache full:https://registry.npmjs.org/get-uri set size:undefined disposed:false
349 http fetch GET 200 https://registry.npmjs.org/@tootallnate%2fquickjs-emscripten 172ms (cache miss)
350 silly packumentCache full:https://registry.npmjs.org/@tootallnate%2fquickjs-emscripten set size:undefined disposed:false
351 http fetch GET 200 https://registry.npmjs.org/socks 180ms (cache miss)
352 silly packumentCache full:https://registry.npmjs.org/socks set size:undefined disposed:false
353 silly placeDep ROOT @tootallnate/quickjs-emscripten@0.23.0 OK for: pac-proxy-agent@7.2.0 want: ^0.23.0
354 silly placeDep ROOT get-uri@6.0.5 OK for: pac-proxy-agent@7.2.0 want: ^6.0.1
355 silly placeDep ROOT pac-resolver@7.0.1 OK for: pac-proxy-agent@7.2.0 want: ^7.0.1
356 silly fetch manifest basic-ftp@^5.0.2
357 silly packumentCache full:https://registry.npmjs.org/basic-ftp cache-miss
358 silly fetch manifest data-uri-to-buffer@^6.0.2
359 silly packumentCache full:https://registry.npmjs.org/data-uri-to-buffer cache-miss
360 silly fetch manifest degenerator@^5.0.0
361 silly packumentCache full:https://registry.npmjs.org/degenerator cache-miss
362 silly fetch manifest netmask@^2.0.2
363 silly packumentCache full:https://registry.npmjs.org/netmask cache-miss
364 http cache https://registry.npmjs.org/data-uri-to-buffer 33ms (cache hit)
365 silly packumentCache full:https://registry.npmjs.org/data-uri-to-buffer set size:43253 disposed:false
366 http fetch GET 200 https://registry.npmjs.org/basic-ftp 138ms (cache miss)
367 silly packumentCache full:https://registry.npmjs.org/basic-ftp set size:undefined disposed:false
368 http fetch GET 200 https://registry.npmjs.org/netmask 159ms (cache miss)
369 silly packumentCache full:https://registry.npmjs.org/netmask set size:undefined disposed:false
370 http fetch GET 200 https://registry.npmjs.org/degenerator 183ms (cache miss)
371 silly packumentCache full:https://registry.npmjs.org/degenerator set size:undefined disposed:false
372 silly placeDep ROOT basic-ftp@5.0.5 OK for: get-uri@6.0.5 want: ^5.0.2
373 silly placeDep node_modules/get-uri data-uri-to-buffer@6.0.2 OK for: get-uri@6.0.5 want: ^6.0.2
374 silly placeDep ROOT degenerator@5.0.1 OK for: pac-resolver@7.0.1 want: ^5.0.0
375 silly placeDep ROOT netmask@2.0.2 OK for: pac-resolver@7.0.1 want: ^2.0.2
376 silly fetch manifest ast-types@^0.13.4
377 silly packumentCache full:https://registry.npmjs.org/ast-types cache-miss
378 silly fetch manifest escodegen@^2.1.0
379 silly packumentCache full:https://registry.npmjs.org/escodegen cache-miss
380 silly fetch manifest esprima@^4.0.1
381 silly packumentCache full:https://registry.npmjs.org/esprima cache-miss
382 http fetch GET 200 https://registry.npmjs.org/esprima 128ms (cache miss)
383 silly packumentCache full:https://registry.npmjs.org/esprima set size:undefined disposed:false
384 http fetch GET 200 https://registry.npmjs.org/ast-types 163ms (cache miss)
385 silly packumentCache full:https://registry.npmjs.org/ast-types set size:undefined disposed:false
386 http fetch GET 200 https://registry.npmjs.org/escodegen 195ms (cache miss)
387 silly packumentCache full:https://registry.npmjs.org/escodegen set size:undefined disposed:false
388 silly placeDep ROOT ast-types@0.13.4 OK for: degenerator@5.0.1 want: ^0.13.4
389 silly placeDep ROOT escodegen@2.1.0 OK for: degenerator@5.0.1 want: ^2.1.0
390 silly placeDep ROOT esprima@4.0.1 OK for: degenerator@5.0.1 want: ^4.0.1
391 silly fetch manifest tslib@^2.0.1
392 silly packumentCache full:https://registry.npmjs.org/tslib cache-miss
393 silly fetch manifest estraverse@^5.2.0
394 silly packumentCache full:https://registry.npmjs.org/estraverse cache-miss
395 silly fetch manifest esutils@^2.0.2
396 silly packumentCache full:https://registry.npmjs.org/esutils cache-miss
397 silly fetch manifest source-map@~0.6.1
398 silly packumentCache full:https://registry.npmjs.org/source-map cache-miss
399 http fetch GET 200 https://registry.npmjs.org/estraverse 103ms (cache miss)
400 silly packumentCache full:https://registry.npmjs.org/estraverse set size:undefined disposed:false
401 http fetch GET 200 https://registry.npmjs.org/esutils 123ms (cache miss)
402 silly packumentCache full:https://registry.npmjs.org/esutils set size:undefined disposed:false
403 http fetch GET 200 https://registry.npmjs.org/tslib 125ms (cache miss)
404 silly packumentCache full:https://registry.npmjs.org/tslib set size:undefined disposed:false
405 http fetch GET 200 https://registry.npmjs.org/source-map 133ms (cache miss)
406 silly packumentCache full:https://registry.npmjs.org/source-map set size:undefined disposed:false
407 silly placeDep ROOT tslib@2.8.1 OK for: ast-types@0.13.4 want: ^2.0.1
408 silly placeDep ROOT estraverse@5.3.0 OK for: escodegen@2.1.0 want: ^5.2.0
409 silly placeDep ROOT esutils@2.0.3 OK for: escodegen@2.1.0 want: ^2.0.2
410 silly placeDep ROOT source-map@0.6.1 OK for: escodegen@2.1.0 want: ~0.6.1
411 silly placeDep ROOT end-of-stream@1.4.5 OK for: pump@3.0.3 want: ^1.1.0
412 silly placeDep ROOT once@1.4.0 OK for: pump@3.0.3 want: ^1.3.1
413 silly fetch manifest once@^1.4.0
414 silly packumentCache full:https://registry.npmjs.org/once cache-miss
415 silly fetch manifest wrappy@1
416 silly packumentCache full:https://registry.npmjs.org/wrappy cache-miss
417 http cache https://registry.npmjs.org/once 9ms (cache hit)
418 silly packumentCache full:https://registry.npmjs.org/once set size:11768 disposed:false
419 http fetch GET 200 https://registry.npmjs.org/wrappy 134ms (cache miss)
420 silly packumentCache full:https://registry.npmjs.org/wrappy set size:undefined disposed:false
421 silly placeDep ROOT wrappy@1.0.2 OK for: once@1.4.0 want: 1
422 silly fetch manifest bufferutil@^4.0.1
423 silly packumentCache full:https://registry.npmjs.org/bufferutil cache-miss
424 http fetch GET 200 https://registry.npmjs.org/bufferutil 126ms (cache miss)
425 silly packumentCache full:https://registry.npmjs.org/bufferutil set size:undefined disposed:false
426 silly fetch manifest utf-8-validate@>=5.0.2
427 silly packumentCache full:https://registry.npmjs.org/utf-8-validate cache-miss
428 http fetch GET 200 https://registry.npmjs.org/utf-8-validate 132ms (cache miss)
429 silly packumentCache full:https://registry.npmjs.org/utf-8-validate set size:undefined disposed:false
430 silly placeDep ROOT ws@8.18.3 OK for: puppeteer-core@24.15.0 want: ^8.18.3
431 silly placeDep ROOT socks@2.8.6 OK for: socks-proxy-agent@8.0.5 want: ^2.8.3
432 silly fetch manifest ip-address@^9.0.5
433 silly packumentCache full:https://registry.npmjs.org/ip-address cache-miss
434 silly fetch manifest smart-buffer@^4.2.0
435 silly packumentCache full:https://registry.npmjs.org/smart-buffer cache-miss
436 http fetch GET 200 https://registry.npmjs.org/ip-address 122ms (cache miss)
437 silly packumentCache full:https://registry.npmjs.org/ip-address set size:undefined disposed:false
438 http fetch GET 200 https://registry.npmjs.org/smart-buffer 180ms (cache miss)
439 silly packumentCache full:https://registry.npmjs.org/smart-buffer set size:undefined disposed:false
440 silly placeDep ROOT ip-address@9.0.5 OK for: socks@2.8.6 want: ^9.0.5
441 silly placeDep ROOT smart-buffer@4.2.0 OK for: socks@2.8.6 want: ^4.2.0
442 silly fetch manifest jsbn@1.1.0
443 silly packumentCache full:https://registry.npmjs.org/jsbn cache-miss
444 silly fetch manifest sprintf-js@^1.1.3
445 silly packumentCache full:https://registry.npmjs.org/sprintf-js cache-miss
446 http fetch GET 200 https://registry.npmjs.org/sprintf-js 107ms (cache miss)
447 silly packumentCache full:https://registry.npmjs.org/sprintf-js set size:undefined disposed:false
448 http fetch GET 200 https://registry.npmjs.org/jsbn 121ms (cache miss)
449 silly packumentCache full:https://registry.npmjs.org/jsbn set size:undefined disposed:false
450 silly placeDep ROOT jsbn@1.1.0 OK for: ip-address@9.0.5 want: 1.1.0
451 silly placeDep ROOT sprintf-js@1.1.3 OK for: ip-address@9.0.5 want: ^1.1.3
452 silly fetch manifest bare-buffer@*
453 silly packumentCache full:https://registry.npmjs.org/bare-buffer cache-miss
454 http fetch GET 200 https://registry.npmjs.org/bare-buffer 194ms (cache miss)
455 silly packumentCache full:https://registry.npmjs.org/bare-buffer set size:undefined disposed:false
456 silly placeDep ROOT bare-fs@4.1.6 OK for: tar-fs@3.1.0 want: ^4.0.1
457 silly placeDep ROOT bare-path@3.0.0 OK for: tar-fs@3.1.0 want: ^3.0.0
458 silly placeDep ROOT tar-stream@3.1.7 OK for: tar-fs@3.1.0 want: ^3.1.5
459 silly fetch manifest bare-events@^2.5.4
460 silly packumentCache full:https://registry.npmjs.org/bare-events cache-miss
461 silly fetch manifest bare-stream@^2.6.4
462 silly packumentCache full:https://registry.npmjs.org/bare-stream cache-miss
463 silly fetch manifest bare-os@^3.0.1
464 silly packumentCache full:https://registry.npmjs.org/bare-os cache-miss
465 silly fetch manifest b4a@^1.6.4
466 silly packumentCache full:https://registry.npmjs.org/b4a cache-miss
467 silly fetch manifest fast-fifo@^1.2.0
468 silly packumentCache full:https://registry.npmjs.org/fast-fifo cache-miss
469 silly fetch manifest streamx@^2.15.0
470 silly packumentCache full:https://registry.npmjs.org/streamx cache-miss
471 http fetch GET 200 https://registry.npmjs.org/b4a 84ms (cache miss)
472 silly packumentCache full:https://registry.npmjs.org/b4a set size:undefined disposed:false
473 http fetch GET 200 https://registry.npmjs.org/bare-events 131ms (cache miss)
474 silly packumentCache full:https://registry.npmjs.org/bare-events set size:undefined disposed:false
475 http fetch GET 200 https://registry.npmjs.org/streamx 133ms (cache miss)
476 silly packumentCache full:https://registry.npmjs.org/streamx set size:undefined disposed:false
477 http fetch GET 200 https://registry.npmjs.org/bare-os 160ms (cache miss)
478 silly packumentCache full:https://registry.npmjs.org/bare-os set size:undefined disposed:false
479 http fetch GET 200 https://registry.npmjs.org/fast-fifo 170ms (cache miss)
480 silly packumentCache full:https://registry.npmjs.org/fast-fifo set size:undefined disposed:false
481 http fetch GET 200 https://registry.npmjs.org/bare-stream 172ms (cache miss)
482 silly packumentCache full:https://registry.npmjs.org/bare-stream set size:undefined disposed:false
483 silly placeDep ROOT bare-events@2.6.0 OK for: bare-fs@4.1.6 want: ^2.5.4
484 silly placeDep ROOT bare-stream@2.6.5 OK for: bare-fs@4.1.6 want: ^2.6.4
485 silly fetch manifest streamx@^2.21.0
486 silly packumentCache full:https://registry.npmjs.org/streamx cache-miss
487 http cache https://registry.npmjs.org/streamx 4ms (cache hit)
488 silly packumentCache full:https://registry.npmjs.org/streamx set size:155255 disposed:false
489 silly placeDep ROOT bare-os@3.6.1 OK for: bare-path@3.0.0 want: ^3.0.1
490 silly placeDep ROOT streamx@2.22.1 OK for: bare-stream@2.6.5 want: ^2.21.0
491 silly fetch manifest fast-fifo@^1.3.2
492 silly packumentCache full:https://registry.npmjs.org/fast-fifo cache-miss
493 silly fetch manifest text-decoder@^1.1.0
494 silly packumentCache full:https://registry.npmjs.org/text-decoder cache-miss
495 http cache https://registry.npmjs.org/fast-fifo 8ms (cache hit)
496 silly packumentCache full:https://registry.npmjs.org/fast-fifo set size:13855 disposed:false
497 http fetch GET 200 https://registry.npmjs.org/text-decoder 138ms (cache miss)
498 silly packumentCache full:https://registry.npmjs.org/text-decoder set size:undefined disposed:false
499 silly placeDep ROOT fast-fifo@1.3.2 OK for: streamx@2.22.1 want: ^1.3.2
500 silly placeDep ROOT text-decoder@1.2.3 OK for: streamx@2.22.1 want: ^1.1.0
501 silly placeDep ROOT b4a@1.6.7 OK for: tar-stream@3.1.7 want: ^1.6.4
502 silly placeDep ROOT cliui@8.0.1 OK for: yargs@17.7.2 want: ^8.0.1
503 silly placeDep ROOT escalade@3.2.0 OK for: yargs@17.7.2 want: ^3.1.1
504 silly placeDep ROOT get-caller-file@2.0.5 OK for: yargs@17.7.2 want: ^2.0.5
505 silly placeDep ROOT require-directory@2.1.1 OK for: yargs@17.7.2 want: ^2.1.1
506 silly placeDep node_modules/yargs string-width@4.2.3 OK for: yargs@17.7.2 want: ^4.2.3
507 silly placeDep ROOT y18n@5.0.8 OK for: yargs@17.7.2 want: ^5.0.5
508 silly placeDep ROOT yargs-parser@21.1.1 OK for: yargs@17.7.2 want: ^21.1.1
509 silly fetch manifest wrap-ansi@^7.0.0
510 silly packumentCache full:https://registry.npmjs.org/wrap-ansi cache-miss
511 silly fetch manifest strip-ansi@^6.0.1
512 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
513 silly fetch manifest string-width@^4.2.0
514 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
515 silly fetch manifest strip-ansi@^6.0.1
516 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
517 silly fetch manifest emoji-regex@^8.0.0
518 silly packumentCache full:https://registry.npmjs.org/emoji-regex cache-miss
519 silly fetch manifest is-fullwidth-code-point@^3.0.0
520 silly packumentCache full:https://registry.npmjs.org/is-fullwidth-code-point cache-miss
521 http cache https://registry.npmjs.org/string-width 20ms (cache hit)
522 silly packumentCache full:https://registry.npmjs.org/string-width set size:61460 disposed:false
523 http fetch GET 200 https://registry.npmjs.org/emoji-regex 133ms (cache miss)
524 silly packumentCache full:https://registry.npmjs.org/emoji-regex set size:undefined disposed:false
525 http fetch GET 200 https://registry.npmjs.org/strip-ansi 143ms (cache miss)
526 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:undefined disposed:false
527 http fetch GET 200 https://registry.npmjs.org/strip-ansi 147ms (cache miss)
528 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:undefined disposed:false
529 http fetch GET 200 https://registry.npmjs.org/wrap-ansi 161ms (cache miss)
530 silly packumentCache full:https://registry.npmjs.org/wrap-ansi set size:undefined disposed:false
531 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point 172ms (cache miss)
532 silly packumentCache full:https://registry.npmjs.org/is-fullwidth-code-point set size:undefined disposed:false
533 silly placeDep node_modules/cliui string-width@4.2.3 OK for: cliui@8.0.1 want: ^4.2.0
534 silly placeDep node_modules/cliui strip-ansi@6.0.1 OK for: cliui@8.0.1 want: ^6.0.1
535 silly placeDep ROOT wrap-ansi@7.0.0 OK for: cliui@8.0.1 want: ^7.0.0
536 silly fetch manifest ansi-regex@^5.0.1
537 silly packumentCache full:https://registry.npmjs.org/ansi-regex cache-miss
538 silly fetch manifest ansi-styles@^4.0.0
539 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-miss
540 silly fetch manifest string-width@^4.1.0
541 silly packumentCache full:https://registry.npmjs.org/string-width cache-hit
542 silly fetch manifest strip-ansi@^6.0.0
543 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
544 http cache https://registry.npmjs.org/strip-ansi 12ms (cache hit)
545 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:43180 disposed:false
546 http fetch GET 200 https://registry.npmjs.org/ansi-regex 119ms (cache miss)
547 silly packumentCache full:https://registry.npmjs.org/ansi-regex set size:undefined disposed:false
548 http fetch GET 200 https://registry.npmjs.org/ansi-styles 124ms (cache miss)
549 silly packumentCache full:https://registry.npmjs.org/ansi-styles set size:undefined disposed:false
550 silly placeDep ROOT ansi-styles@4.3.0 OK for: wrap-ansi@7.0.0 want: ^4.0.0
551 silly placeDep node_modules/wrap-ansi string-width@4.2.3 OK for: wrap-ansi@7.0.0 want: ^4.1.0
552 silly placeDep node_modules/wrap-ansi strip-ansi@6.0.1 OK for: wrap-ansi@7.0.0 want: ^6.0.0
553 silly fetch manifest color-convert@^2.0.1
554 silly packumentCache full:https://registry.npmjs.org/color-convert cache-miss
555 http fetch GET 200 https://registry.npmjs.org/color-convert 136ms (cache miss)
556 silly packumentCache full:https://registry.npmjs.org/color-convert set size:undefined disposed:false
557 silly placeDep ROOT color-convert@2.0.1 OK for: ansi-styles@4.3.0 want: ^2.0.1
558 silly fetch manifest color-name@~1.1.4
559 silly packumentCache full:https://registry.npmjs.org/color-name cache-miss
560 http fetch GET 200 https://registry.npmjs.org/color-name 91ms (cache miss)
561 silly packumentCache full:https://registry.npmjs.org/color-name set size:undefined disposed:false
562 silly placeDep ROOT color-name@1.1.4 OK for: color-convert@2.0.1 want: ~1.1.4
563 silly placeDep ROOT buffer-crc32@0.2.13 OK for: yauzl@2.10.0 want: ~0.2.3
564 silly placeDep ROOT fd-slicer@1.1.0 OK for: yauzl@2.10.0 want: ~1.1.0
565 silly fetch manifest pend@~1.2.0
566 silly packumentCache full:https://registry.npmjs.org/pend cache-miss
567 http fetch GET 200 https://registry.npmjs.org/pend 132ms (cache miss)
568 silly packumentCache full:https://registry.npmjs.org/pend set size:undefined disposed:false
569 silly placeDep ROOT pend@1.2.0 OK for: fd-slicer@1.1.0 want: ~1.2.0
570 silly placeDep node_modules/cliui emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
571 silly placeDep ROOT is-fullwidth-code-point@3.0.0 OK for: string-width@4.2.3 want: ^3.0.0
572 silly placeDep node_modules/cliui ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
573 silly placeDep node_modules/wrap-ansi emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
574 silly placeDep node_modules/wrap-ansi ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
575 silly placeDep node_modules/yargs emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
576 silly placeDep node_modules/yargs strip-ansi@6.0.1 OK for: string-width@4.2.3 want: ^6.0.1
577 silly placeDep node_modules/yargs ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
578 silly reify mark retired [
578 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\esbuild',
578 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\esbuild',
578 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\esbuild.cmd',
578 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\esbuild.ps1'
578 silly reify ]
579 silly reify mark retired [
579 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\prettier',
579 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\prettier',
579 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\prettier.cmd',
579 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\prettier.ps1'
579 silly reify ]
580 silly reify mark retired [
580 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\tsx',
580 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsx',
580 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsx.cmd',
580 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsx.ps1'
580 silly reify ]
581 silly reify mark retired [
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\typescript',
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsc',
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsc.cmd',
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsc.ps1',
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsserver',
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsserver.cmd',
581 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsserver.ps1'
581 silly reify ]
582 silly reify moves {
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\esbuild': 'D:\\Github\\Kontext\\source\\node_modules\\.esbuild-UTvWHBZI',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\esbuild': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.esbuild-pvgJTcJX',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\esbuild.cmd': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.esbuild.cmd-pmPV4gwW',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\esbuild.ps1': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.esbuild.ps1-LiJ1dhd2',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\prettier': 'D:\\Github\\Kontext\\source\\node_modules\\.prettier-hK4Y7rE0',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\prettier': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.prettier-PtVSKEn9',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\prettier.cmd': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.prettier.cmd-vlTU0jcp',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\prettier.ps1': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.prettier.ps1-ZTQkJcUU',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\tsx': 'D:\\Github\\Kontext\\source\\node_modules\\.tsx-9uVYkXXI',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsx': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsx-vQW5Kb1R',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsx.cmd': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsx.cmd-7uB6jQ3O',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsx.ps1': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsx.ps1-ehp6gaKM',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\typescript': 'D:\\Github\\Kontext\\source\\node_modules\\.typescript-UidVfsJA',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsc': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsc-TRYed3ID',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsc.cmd': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsc.cmd-EGTBWLc6',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsc.ps1': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsc.ps1-xZaFpVjE',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsserver': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsserver-h6M5RsqA',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsserver.cmd': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsserver.cmd-Z0HwmX1b',
582 silly reify   'D:\\Github\\Kontext\\source\\node_modules\\.bin\\tsserver.ps1': 'D:\\Github\\Kontext\\source\\node_modules\\.bin\\.tsserver.ps1-AcFjygCp'
582 silly reify }
583 http cache buffer-crc32@https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz 0ms (cache hit)
584 http cache fd-slicer@https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz 0ms (cache hit)
585 http cache is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 0ms (cache hit)
586 http cache pend@https://registry.npmjs.org/pend/-/pend-1.2.0.tgz 1ms (cache hit)
587 http cache wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 0ms (cache hit)
588 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 0ms (cache hit)
589 http cache color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 0ms (cache hit)
590 http cache yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 0ms (cache hit)
591 http cache color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 0ms (cache hit)
592 http cache y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 0ms (cache hit)
593 http cache require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 0ms (cache hit)
594 http cache get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 0ms (cache hit)
595 http cache escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 0ms (cache hit)
596 http cache cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 0ms (cache hit)
597 http cache fast-fifo@https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz 0ms (cache hit)
598 http cache text-decoder@https://registry.npmjs.org/text-decoder/-/text-decoder-1.2.3.tgz 0ms (cache hit)
599 http cache b4a@https://registry.npmjs.org/b4a/-/b4a-1.6.7.tgz 0ms (cache hit)
600 http cache bare-os@https://registry.npmjs.org/bare-os/-/bare-os-3.6.1.tgz 0ms (cache hit)
601 http cache bare-stream@https://registry.npmjs.org/bare-stream/-/bare-stream-2.6.5.tgz 0ms (cache hit)
602 http cache streamx@https://registry.npmjs.org/streamx/-/streamx-2.22.1.tgz 0ms (cache hit)
603 http cache bare-events@https://registry.npmjs.org/bare-events/-/bare-events-2.6.0.tgz 0ms (cache hit)
604 http cache bare-fs@https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.6.tgz 0ms (cache hit)
605 http cache tar-stream@https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.7.tgz 0ms (cache hit)
606 http cache bare-path@https://registry.npmjs.org/bare-path/-/bare-path-3.0.0.tgz 0ms (cache hit)
607 http cache sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz 0ms (cache hit)
608 http cache jsbn@https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz 0ms (cache hit)
609 http cache smart-buffer@https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz 0ms (cache hit)
610 http cache ws@https://registry.npmjs.org/ws/-/ws-8.18.3.tgz 0ms (cache hit)
611 http cache ip-address@https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz 0ms (cache hit)
612 http cache socks@https://registry.npmjs.org/socks/-/socks-2.8.6.tgz 0ms (cache hit)
613 http cache wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 0ms (cache hit)
614 http cache once@https://registry.npmjs.org/once/-/once-1.4.0.tgz 0ms (cache hit)
615 http cache end-of-stream@https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz 0ms (cache hit)
616 http cache source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 0ms (cache hit)
617 http cache esutils@https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz 0ms (cache hit)
618 http cache estraverse@https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz 0ms (cache hit)
619 http cache tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 0ms (cache hit)
620 http cache esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 0ms (cache hit)
621 http cache escodegen@https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz 0ms (cache hit)
622 http cache ast-types@https://registry.npmjs.org/ast-types/-/ast-types-0.13.4.tgz 0ms (cache hit)
623 http cache netmask@https://registry.npmjs.org/netmask/-/netmask-2.0.2.tgz 0ms (cache hit)
624 http cache degenerator@https://registry.npmjs.org/degenerator/-/degenerator-5.0.1.tgz 0ms (cache hit)
625 http cache basic-ftp@https://registry.npmjs.org/basic-ftp/-/basic-ftp-5.0.5.tgz 0ms (cache hit)
626 http cache pac-resolver@https://registry.npmjs.org/pac-resolver/-/pac-resolver-7.0.1.tgz 0ms (cache hit)
627 http cache @tootallnate/quickjs-emscripten@https://registry.npmjs.org/@tootallnate/quickjs-emscripten/-/quickjs-emscripten-0.23.0.tgz 0ms (cache hit)
628 http cache socks-proxy-agent@https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.5.tgz 0ms (cache hit)
629 http cache get-uri@https://registry.npmjs.org/get-uri/-/get-uri-6.0.5.tgz 0ms (cache hit)
630 http cache proxy-from-env@https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz 0ms (cache hit)
631 http cache https-proxy-agent@https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz 0ms (cache hit)
632 http cache pac-proxy-agent@https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.2.0.tgz 0ms (cache hit)
633 http cache lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz 0ms (cache hit)
634 http cache http-proxy-agent@https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz 0ms (cache hit)
635 http cache picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 0ms (cache hit)
636 http cache is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 0ms (cache hit)
637 http cache js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 0ms (cache hit)
638 http cache agent-base@https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz 0ms (cache hit)
639 http cache @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 0ms (cache hit)
640 http cache lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 0ms (cache hit)
641 http cache json-parse-even-better-errors@https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz 0ms (cache hit)
642 http cache error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz 0ms (cache hit)
643 http cache @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 0ms (cache hit)
644 http cache callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 0ms (cache hit)
645 http cache argparse@https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz 0ms (cache hit)
646 http cache resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz 0ms (cache hit)
647 http cache parent-module@https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz 0ms (cache hit)
648 http cache pump@https://registry.npmjs.org/pump/-/pump-3.0.3.tgz 0ms (cache hit)
649 http cache yauzl@https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz 0ms (cache hit)
650 http cache get-stream@https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz 0ms (cache hit)
651 http cache @types/yauzl@https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz 0ms (cache hit)
652 http cache parse-json@https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz 0ms (cache hit)
653 http cache ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 0ms (cache hit)
654 http cache js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz 0ms (cache hit)
655 http cache import-fresh@https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz 0ms (cache hit)
656 http cache env-paths@https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz 0ms (cache hit)
657 http cache yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 0ms (cache hit)
658 http cache mitt@https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz 0ms (cache hit)
659 http cache zod@https://registry.npmjs.org/zod/-/zod-3.25.76.tgz 0ms (cache hit)
660 http cache tar-fs@https://registry.npmjs.org/tar-fs/-/tar-fs-3.1.0.tgz 0ms (cache hit)
661 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
662 http cache progress@https://registry.npmjs.org/progress/-/progress-2.0.3.tgz 0ms (cache hit)
663 http cache proxy-agent@https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.5.0.tgz 0ms (cache hit)
664 http cache extract-zip@https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz 0ms (cache hit)
665 http cache debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 0ms (cache hit)
666 http cache typed-query-selector@https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.12.0.tgz 0ms (cache hit)
667 http cache puppeteer-core@https://registry.npmjs.org/puppeteer-core/-/puppeteer-core-24.15.0.tgz 0ms (cache hit)
668 http cache devtools-protocol@https://registry.npmjs.org/devtools-protocol/-/devtools-protocol-0.0.1464554.tgz 0ms (cache hit)
669 http cache cosmiconfig@https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0.tgz 0ms (cache hit)
670 http cache @puppeteer/browsers@https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.6.tgz 0ms (cache hit)
671 http cache web-streams-polyfill@https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz 0ms (cache hit)
672 http cache chromium-bidi@https://registry.npmjs.org/chromium-bidi/-/chromium-bidi-7.2.0.tgz 0ms (cache hit)
673 http cache node-domexception@https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz 0ms (cache hit)
674 http cache formdata-polyfill@https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz 0ms (cache hit)
675 http cache fetch-blob@https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz 0ms (cache hit)
676 http cache puppeteer@https://registry.npmjs.org/puppeteer/-/puppeteer-24.15.0.tgz 0ms (cache hit)
677 http cache data-uri-to-buffer@https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz 0ms (cache hit)
678 http cache node-fetch@https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz 0ms (cache hit)
679 http cache @types/puppeteer@https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.7.tgz 0ms (cache hit)
680 http cache typescript@https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz 0ms (cache hit)
681 http cache prettier@https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz 0ms (cache hit)
682 http cache tsx@https://registry.npmjs.org/tsx/-/tsx-4.20.3.tgz 0ms (cache hit)
683 http cache esbuild@https://registry.npmjs.org/esbuild/-/esbuild-0.25.8.tgz 0ms (cache hit)
684 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
685 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
686 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
687 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
688 http cache data-uri-to-buffer@https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.2.tgz 0ms (cache hit)
689 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
690 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
691 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
692 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
693 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
694 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
695 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
696 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
697 silly tarball no local data for buffer-crc32@https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz. Extracting by manifest.
698 silly tarball no local data for fd-slicer@https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz. Extracting by manifest.
699 silly tarball no local data for is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz. Extracting by manifest.
700 silly tarball no local data for pend@https://registry.npmjs.org/pend/-/pend-1.2.0.tgz. Extracting by manifest.
701 silly tarball no local data for wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz. Extracting by manifest.
702 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz. Extracting by manifest.
703 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz. Extracting by manifest.
704 silly tarball no local data for yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz. Extracting by manifest.
705 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz. Extracting by manifest.
706 silly tarball no local data for y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz. Extracting by manifest.
707 silly tarball no local data for require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz. Extracting by manifest.
708 silly tarball no local data for get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz. Extracting by manifest.
709 silly tarball no local data for escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz. Extracting by manifest.
710 silly tarball no local data for cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz. Extracting by manifest.
711 silly tarball no local data for fast-fifo@https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz. Extracting by manifest.
712 silly tarball no local data for text-decoder@https://registry.npmjs.org/text-decoder/-/text-decoder-1.2.3.tgz. Extracting by manifest.
713 silly tarball no local data for b4a@https://registry.npmjs.org/b4a/-/b4a-1.6.7.tgz. Extracting by manifest.
714 silly tarball no local data for bare-os@https://registry.npmjs.org/bare-os/-/bare-os-3.6.1.tgz. Extracting by manifest.
715 silly tarball no local data for bare-stream@https://registry.npmjs.org/bare-stream/-/bare-stream-2.6.5.tgz. Extracting by manifest.
716 silly tarball no local data for streamx@https://registry.npmjs.org/streamx/-/streamx-2.22.1.tgz. Extracting by manifest.
717 silly tarball no local data for bare-events@https://registry.npmjs.org/bare-events/-/bare-events-2.6.0.tgz. Extracting by manifest.
718 silly tarball no local data for bare-fs@https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.6.tgz. Extracting by manifest.
719 silly tarball no local data for tar-stream@https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.7.tgz. Extracting by manifest.
720 silly tarball no local data for bare-path@https://registry.npmjs.org/bare-path/-/bare-path-3.0.0.tgz. Extracting by manifest.
721 silly tarball no local data for sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz. Extracting by manifest.
722 silly tarball no local data for jsbn@https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz. Extracting by manifest.
723 silly tarball no local data for smart-buffer@https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz. Extracting by manifest.
724 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-8.18.3.tgz. Extracting by manifest.
725 silly tarball no local data for ip-address@https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz. Extracting by manifest.
726 silly tarball no local data for socks@https://registry.npmjs.org/socks/-/socks-2.8.6.tgz. Extracting by manifest.
727 silly tarball no local data for wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz. Extracting by manifest.
728 silly tarball no local data for once@https://registry.npmjs.org/once/-/once-1.4.0.tgz. Extracting by manifest.
729 silly tarball no local data for end-of-stream@https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz. Extracting by manifest.
730 silly tarball no local data for source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz. Extracting by manifest.
731 silly tarball no local data for esutils@https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz. Extracting by manifest.
732 silly tarball no local data for estraverse@https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz. Extracting by manifest.
733 silly tarball no local data for tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz. Extracting by manifest.
734 silly tarball no local data for esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz. Extracting by manifest.
735 silly tarball no local data for escodegen@https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz. Extracting by manifest.
736 silly tarball no local data for ast-types@https://registry.npmjs.org/ast-types/-/ast-types-0.13.4.tgz. Extracting by manifest.
737 silly tarball no local data for netmask@https://registry.npmjs.org/netmask/-/netmask-2.0.2.tgz. Extracting by manifest.
738 silly tarball no local data for degenerator@https://registry.npmjs.org/degenerator/-/degenerator-5.0.1.tgz. Extracting by manifest.
739 silly tarball no local data for basic-ftp@https://registry.npmjs.org/basic-ftp/-/basic-ftp-5.0.5.tgz. Extracting by manifest.
740 silly tarball no local data for pac-resolver@https://registry.npmjs.org/pac-resolver/-/pac-resolver-7.0.1.tgz. Extracting by manifest.
741 silly tarball no local data for @tootallnate/quickjs-emscripten@https://registry.npmjs.org/@tootallnate/quickjs-emscripten/-/quickjs-emscripten-0.23.0.tgz. Extracting by manifest.
742 silly tarball no local data for socks-proxy-agent@https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.5.tgz. Extracting by manifest.
743 silly tarball no local data for get-uri@https://registry.npmjs.org/get-uri/-/get-uri-6.0.5.tgz. Extracting by manifest.
744 silly tarball no local data for proxy-from-env@https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz. Extracting by manifest.
745 silly tarball no local data for https-proxy-agent@https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz. Extracting by manifest.
746 silly tarball no local data for pac-proxy-agent@https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.2.0.tgz. Extracting by manifest.
747 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz. Extracting by manifest.
748 silly tarball no local data for http-proxy-agent@https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz. Extracting by manifest.
749 silly tarball no local data for picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz. Extracting by manifest.
750 silly tarball no local data for is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz. Extracting by manifest.
751 silly tarball no local data for js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz. Extracting by manifest.
752 silly tarball no local data for agent-base@https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz. Extracting by manifest.
753 silly tarball no local data for @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz. Extracting by manifest.
754 silly tarball no local data for lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz. Extracting by manifest.
755 silly tarball no local data for json-parse-even-better-errors@https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz. Extracting by manifest.
756 silly tarball no local data for error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz. Extracting by manifest.
757 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz. Extracting by manifest.
758 silly tarball no local data for callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz. Extracting by manifest.
759 silly tarball no local data for argparse@https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz. Extracting by manifest.
760 silly tarball no local data for resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz. Extracting by manifest.
761 silly tarball no local data for parent-module@https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz. Extracting by manifest.
762 silly tarball no local data for pump@https://registry.npmjs.org/pump/-/pump-3.0.3.tgz. Extracting by manifest.
763 silly tarball no local data for yauzl@https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz. Extracting by manifest.
764 silly tarball no local data for get-stream@https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz. Extracting by manifest.
765 silly tarball no local data for @types/yauzl@https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz. Extracting by manifest.
766 silly tarball no local data for parse-json@https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz. Extracting by manifest.
767 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz. Extracting by manifest.
768 silly tarball no local data for js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz. Extracting by manifest.
769 silly tarball no local data for import-fresh@https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz. Extracting by manifest.
770 silly tarball no local data for env-paths@https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz. Extracting by manifest.
771 silly tarball no local data for yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz. Extracting by manifest.
772 silly tarball no local data for mitt@https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz. Extracting by manifest.
773 silly tarball no local data for zod@https://registry.npmjs.org/zod/-/zod-3.25.76.tgz. Extracting by manifest.
774 silly tarball no local data for tar-fs@https://registry.npmjs.org/tar-fs/-/tar-fs-3.1.0.tgz. Extracting by manifest.
775 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
776 silly tarball no local data for progress@https://registry.npmjs.org/progress/-/progress-2.0.3.tgz. Extracting by manifest.
777 silly tarball no local data for proxy-agent@https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.5.0.tgz. Extracting by manifest.
778 silly tarball no local data for extract-zip@https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz. Extracting by manifest.
779 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz. Extracting by manifest.
780 silly tarball no local data for typed-query-selector@https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.12.0.tgz. Extracting by manifest.
781 silly tarball no local data for puppeteer-core@https://registry.npmjs.org/puppeteer-core/-/puppeteer-core-24.15.0.tgz. Extracting by manifest.
782 silly tarball no local data for devtools-protocol@https://registry.npmjs.org/devtools-protocol/-/devtools-protocol-0.0.1464554.tgz. Extracting by manifest.
783 silly tarball no local data for cosmiconfig@https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0.tgz. Extracting by manifest.
784 silly tarball no local data for @puppeteer/browsers@https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.6.tgz. Extracting by manifest.
785 silly tarball no local data for web-streams-polyfill@https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz. Extracting by manifest.
786 silly tarball no local data for chromium-bidi@https://registry.npmjs.org/chromium-bidi/-/chromium-bidi-7.2.0.tgz. Extracting by manifest.
787 silly tarball no local data for node-domexception@https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz. Extracting by manifest.
788 silly tarball no local data for formdata-polyfill@https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz. Extracting by manifest.
789 silly tarball no local data for fetch-blob@https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz. Extracting by manifest.
790 silly tarball no local data for puppeteer@https://registry.npmjs.org/puppeteer/-/puppeteer-24.15.0.tgz. Extracting by manifest.
791 silly tarball no local data for data-uri-to-buffer@https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz. Extracting by manifest.
792 silly tarball no local data for node-fetch@https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz. Extracting by manifest.
793 silly tarball no local data for @types/puppeteer@https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.7.tgz. Extracting by manifest.
794 silly tarball no local data for typescript@https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz. Extracting by manifest.
795 silly tarball no local data for prettier@https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz. Extracting by manifest.
796 silly tarball no local data for tsx@https://registry.npmjs.org/tsx/-/tsx-4.20.3.tgz. Extracting by manifest.
797 silly tarball no local data for esbuild@https://registry.npmjs.org/esbuild/-/esbuild-0.25.8.tgz. Extracting by manifest.
798 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
799 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
800 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
801 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
802 silly tarball no local data for data-uri-to-buffer@https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.2.tgz. Extracting by manifest.
803 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
804 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
805 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
806 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
807 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
808 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
809 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
810 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
811 http fetch GET 200 https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 225ms (cache miss)
812 http fetch GET 200 https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 226ms (cache miss)
813 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 246ms (cache miss)
814 http fetch GET 200 https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 246ms (cache miss)
815 http fetch GET 200 https://registry.npmjs.org/pend/-/pend-1.2.0.tgz 250ms (cache miss)
816 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 252ms (cache miss)
817 http fetch GET 200 https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz 249ms (cache miss)
818 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 259ms (cache miss)
819 http fetch GET 200 https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz 267ms (cache miss)
820 http fetch GET 200 https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 261ms (cache miss)
821 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 267ms (cache miss)
822 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 269ms (cache miss)
823 http fetch GET 200 https://registry.npmjs.org/b4a/-/b4a-1.6.7.tgz 330ms (cache miss)
824 http fetch GET 200 https://registry.npmjs.org/text-decoder/-/text-decoder-1.2.3.tgz 358ms (cache miss)
825 http fetch GET 200 https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz 429ms (cache miss)
826 http fetch GET 200 https://registry.npmjs.org/bare-path/-/bare-path-3.0.0.tgz 431ms (cache miss)
827 http fetch GET 200 https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 442ms (cache miss)
828 http fetch GET 200 https://registry.npmjs.org/bare-stream/-/bare-stream-2.6.5.tgz 449ms (cache miss)
829 http fetch GET 200 https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.7.tgz 448ms (cache miss)
830 http fetch GET 200 https://registry.npmjs.org/streamx/-/streamx-2.22.1.tgz 470ms (cache miss)
831 http fetch GET 200 https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 476ms (cache miss)
832 http fetch GET 200 https://registry.npmjs.org/bare-events/-/bare-events-2.6.0.tgz 503ms (cache miss)
833 http fetch GET 200 https://registry.npmjs.org/once/-/once-1.4.0.tgz 497ms (cache miss)
834 http fetch GET 200 https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 512ms (cache miss)
835 http fetch GET 200 https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz 502ms (cache miss)
836 http fetch GET 200 https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz 504ms (cache miss)
837 http fetch GET 200 https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz 506ms (cache miss)
838 http fetch GET 200 https://registry.npmjs.org/degenerator/-/degenerator-5.0.1.tgz 502ms (cache miss)
839 http fetch GET 200 https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz 529ms (cache miss)
840 http fetch GET 200 https://registry.npmjs.org/netmask/-/netmask-2.0.2.tgz 507ms (cache miss)
841 http fetch GET 200 https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz 525ms (cache miss)
842 http fetch GET 200 https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.5.tgz 520ms (cache miss)
843 http fetch GET 200 https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz 527ms (cache miss)
844 http fetch GET 200 https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz 524ms (cache miss)
845 http fetch GET 200 https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.2.0.tgz 523ms (cache miss)
846 http fetch GET 200 https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz 556ms (cache miss)
847 http fetch GET 200 https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 558ms (cache miss)
848 http fetch GET 200 https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz 568ms (cache miss)
849 http fetch GET 200 https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz 578ms (cache miss)
850 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz 568ms (cache miss)
851 http fetch GET 200 https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 581ms (cache miss)
852 http fetch GET 200 https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 582ms (cache miss)
853 http fetch GET 200 https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 593ms (cache miss)
854 http fetch GET 200 https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 606ms (cache miss)
855 http fetch GET 200 https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz 598ms (cache miss)
856 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-8.18.3.tgz 615ms (cache miss)
857 http fetch GET 200 https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz 607ms (cache miss)
858 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 613ms (cache miss)
859 http fetch GET 200 https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 616ms (cache miss)
860 http fetch GET 200 https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz 618ms (cache miss)
861 http fetch GET 200 https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz 620ms (cache miss)
862 http fetch GET 200 https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 665ms (cache miss)
863 http fetch GET 200 https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz 690ms (cache miss)
864 http fetch GET 200 https://registry.npmjs.org/pump/-/pump-3.0.3.tgz 692ms (cache miss)
865 http fetch GET 200 https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz 695ms (cache miss)
866 http fetch GET 200 https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz 694ms (cache miss)
867 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 701ms (cache miss)
868 http fetch GET 200 https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz 698ms (cache miss)
869 http fetch GET 200 https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz 702ms (cache miss)
870 http fetch GET 200 https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz 703ms (cache miss)
871 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 709ms (cache miss)
872 http fetch GET 200 https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz 725ms (cache miss)
873 http fetch GET 200 https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz 721ms (cache miss)
874 http fetch GET 200 https://registry.npmjs.org/basic-ftp/-/basic-ftp-5.0.5.tgz 748ms (cache miss)
875 http fetch GET 200 https://registry.npmjs.org/get-uri/-/get-uri-6.0.5.tgz 758ms (cache miss)
876 http fetch GET 200 https://registry.npmjs.org/socks/-/socks-2.8.6.tgz 790ms (cache miss)
877 http fetch GET 200 https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz 769ms (cache miss)
878 http fetch GET 200 https://registry.npmjs.org/tar-fs/-/tar-fs-3.1.0.tgz 772ms (cache miss)
879 http fetch GET 200 https://registry.npmjs.org/typed-query-selector/-/typed-query-selector-2.12.0.tgz 771ms (cache miss)
880 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 773ms (cache miss)
881 http fetch GET 200 https://registry.npmjs.org/proxy-agent/-/proxy-agent-6.5.0.tgz 790ms (cache miss)
882 http fetch GET 200 https://registry.npmjs.org/progress/-/progress-2.0.3.tgz 795ms (cache miss)
883 http fetch GET 200 https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 822ms (cache miss)
884 http fetch GET 200 https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz 823ms (cache miss)
885 http fetch GET 200 https://registry.npmjs.org/bare-os/-/bare-os-3.6.1.tgz 859ms (cache miss)
886 http fetch GET 200 https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz 836ms (cache miss)
887 http fetch GET 200 https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz 908ms (cache miss)
888 http fetch GET 200 https://registry.npmjs.org/bare-fs/-/bare-fs-4.1.6.tgz 920ms (cache miss)
889 http fetch GET 200 https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz 896ms (cache miss)
890 http fetch GET 200 https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz 899ms (cache miss)
891 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz 914ms (cache miss)
892 http fetch GET 200 https://registry.npmjs.org/@types/puppeteer/-/puppeteer-5.4.7.tgz 921ms (cache miss)
893 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 954ms (cache miss)
894 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 959ms (cache miss)
895 http fetch GET 200 https://registry.npmjs.org/esbuild/-/esbuild-0.25.8.tgz 965ms (cache miss)
896 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 1027ms (cache miss)
897 http fetch GET 200 https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz 1052ms (cache miss)
898 http fetch GET 200 https://registry.npmjs.org/pac-resolver/-/pac-resolver-7.0.1.tgz 1066ms (cache miss)
899 http fetch GET 200 https://registry.npmjs.org/ast-types/-/ast-types-0.13.4.tgz 1083ms (cache miss)
900 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1060ms (cache miss)
901 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1061ms (cache miss)
902 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1061ms (cache miss)
903 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-6.0.2.tgz 1062ms (cache miss)
904 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1068ms (cache miss)
905 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 1067ms (cache miss)
906 http fetch GET 200 https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz 1071ms (cache miss)
907 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1069ms (cache miss)
908 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 1087ms (cache miss)
909 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 1094ms (cache miss)
910 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 1110ms (cache miss)
911 warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
912 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 1188ms (cache miss)
913 http fetch GET 200 https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 1235ms (cache miss)
914 http fetch GET 200 https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0.tgz 1240ms (cache miss)
915 http fetch GET 200 https://registry.npmjs.org/puppeteer/-/puppeteer-24.15.0.tgz 1297ms (cache miss)
916 http fetch GET 200 https://registry.npmjs.org/devtools-protocol/-/devtools-protocol-0.0.1464554.tgz 1307ms (cache miss)
917 http fetch GET 200 https://registry.npmjs.org/tsx/-/tsx-4.20.3.tgz 1309ms (cache miss)
918 http fetch GET 200 https://registry.npmjs.org/@tootallnate/quickjs-emscripten/-/quickjs-emscripten-0.23.0.tgz 1358ms (cache miss)
919 http fetch GET 200 https://registry.npmjs.org/@puppeteer/browsers/-/browsers-2.10.6.tgz 1648ms (cache miss)
920 http fetch GET 200 https://registry.npmjs.org/prettier/-/prettier-3.6.2.tgz 1727ms (cache miss)
921 http fetch GET 200 https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz 1780ms (cache miss)
922 http fetch GET 200 https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz 2206ms (cache miss)
923 http fetch GET 200 https://registry.npmjs.org/zod/-/zod-3.25.76.tgz 2429ms (cache miss)
924 http fetch GET 200 https://registry.npmjs.org/chromium-bidi/-/chromium-bidi-7.2.0.tgz 2492ms (cache miss)
925 http fetch GET 200 https://registry.npmjs.org/puppeteer-core/-/puppeteer-core-24.15.0.tgz 3594ms (cache miss)
926 info run esbuild@0.25.8 postinstall node_modules/esbuild node install.js
927 info run puppeteer@24.15.0 postinstall node_modules/puppeteer node install.mjs
928 info run esbuild@0.25.8 postinstall { code: 0, signal: null }
929 info run puppeteer@24.15.0 postinstall { code: 0, signal: null }
930 silly ADD node_modules/is-fullwidth-code-point
931 silly ADD node_modules/pend
932 silly ADD node_modules/fd-slicer
933 silly ADD node_modules/buffer-crc32
934 silly ADD node_modules/color-name
935 silly ADD node_modules/color-convert
936 silly ADD node_modules/ansi-styles
937 silly ADD node_modules/wrap-ansi
938 silly ADD node_modules/wrap-ansi/node_modules/ansi-regex
939 silly ADD node_modules/wrap-ansi/node_modules/emoji-regex
940 silly ADD node_modules/wrap-ansi/node_modules/strip-ansi
941 silly ADD node_modules/wrap-ansi/node_modules/string-width
942 silly ADD node_modules/yargs-parser
943 silly ADD node_modules/y18n
944 silly ADD node_modules/require-directory
945 silly ADD node_modules/get-caller-file
946 silly ADD node_modules/escalade
947 silly ADD node_modules/cliui
948 silly ADD node_modules/cliui/node_modules/ansi-regex
949 silly ADD node_modules/cliui/node_modules/emoji-regex
950 silly ADD node_modules/cliui/node_modules/strip-ansi
951 silly ADD node_modules/cliui/node_modules/string-width
952 silly ADD node_modules/b4a
953 silly ADD node_modules/text-decoder
954 silly ADD node_modules/fast-fifo
955 silly ADD node_modules/streamx
956 silly ADD node_modules/bare-os
957 silly ADD node_modules/bare-stream
958 silly ADD node_modules/bare-events
959 silly ADD node_modules/tar-stream
960 silly ADD node_modules/bare-path
961 silly ADD node_modules/bare-fs
962 silly ADD node_modules/sprintf-js
963 silly ADD node_modules/jsbn
964 silly ADD node_modules/smart-buffer
965 silly ADD node_modules/ip-address
966 silly ADD node_modules/socks
967 silly ADD node_modules/ws
968 silly ADD node_modules/wrappy
969 silly ADD node_modules/once
970 silly ADD node_modules/end-of-stream
971 silly ADD node_modules/source-map
972 silly ADD node_modules/esutils
973 silly ADD node_modules/estraverse
974 silly ADD node_modules/tslib
975 silly ADD node_modules/esprima
976 silly ADD node_modules/escodegen
977 silly ADD node_modules/ast-types
978 silly ADD node_modules/netmask
979 silly ADD node_modules/degenerator
980 silly ADD node_modules/basic-ftp
981 silly ADD node_modules/pac-resolver
982 silly ADD node_modules/get-uri
983 silly ADD node_modules/get-uri/node_modules/data-uri-to-buffer
984 silly ADD node_modules/@tootallnate/quickjs-emscripten
985 silly ADD node_modules/socks-proxy-agent
986 silly ADD node_modules/proxy-from-env
987 silly ADD node_modules/pac-proxy-agent
988 silly ADD node_modules/lru-cache
989 silly ADD node_modules/https-proxy-agent
990 silly ADD node_modules/http-proxy-agent
991 silly ADD node_modules/agent-base
992 silly ADD node_modules/is-arrayish
993 silly ADD node_modules/picocolors
994 silly ADD node_modules/js-tokens
995 silly ADD node_modules/@babel/helper-validator-identifier
996 silly ADD node_modules/lines-and-columns
997 silly ADD node_modules/json-parse-even-better-errors
998 silly ADD node_modules/error-ex
999 silly ADD node_modules/@babel/code-frame
1000 silly ADD node_modules/callsites
1001 silly ADD node_modules/argparse
1002 silly ADD node_modules/resolve-from
1003 silly ADD node_modules/parent-module
1004 silly ADD node_modules/pump
1005 silly ADD node_modules/yauzl
1006 silly ADD node_modules/get-stream
1007 silly ADD node_modules/@types/yauzl
1008 silly ADD node_modules/ms
1009 silly ADD node_modules/parse-json
1010 silly ADD node_modules/js-yaml
1011 silly ADD node_modules/import-fresh
1012 silly ADD node_modules/env-paths
1013 silly ADD node_modules/zod
1014 silly ADD node_modules/mitt
1015 silly ADD node_modules/yargs
1016 silly ADD node_modules/yargs/node_modules/ansi-regex
1017 silly ADD node_modules/yargs/node_modules/strip-ansi
1018 silly ADD node_modules/yargs/node_modules/emoji-regex
1019 silly ADD node_modules/yargs/node_modules/string-width
1020 silly ADD node_modules/tar-fs
1021 silly ADD node_modules/semver
1022 silly ADD node_modules/proxy-agent
1023 silly ADD node_modules/progress
1024 silly ADD node_modules/extract-zip
1025 silly ADD node_modules/debug
1026 silly ADD node_modules/typed-query-selector
1027 silly ADD node_modules/puppeteer-core
1028 silly ADD node_modules/cosmiconfig
1029 silly ADD node_modules/devtools-protocol
1030 silly ADD node_modules/chromium-bidi
1031 silly ADD node_modules/@puppeteer/browsers
1032 silly ADD node_modules/web-streams-polyfill
1033 silly ADD node_modules/node-domexception
1034 silly ADD node_modules/formdata-polyfill
1035 silly ADD node_modules/fetch-blob
1036 silly ADD node_modules/data-uri-to-buffer
1037 silly ADD node_modules/puppeteer
1038 silly ADD node_modules/node-fetch
1039 silly ADD node_modules/@types/puppeteer
1040 silly CHANGE node_modules/typescript
1041 silly CHANGE node_modules/tsx
1042 silly CHANGE node_modules/prettier
1043 silly CHANGE node_modules/esbuild
1044 verbose cwd D:\Github\Kontext\source
1045 verbose os Windows_NT 10.0.19045
1046 verbose node v22.17.1
1047 verbose npm  v11.5.1
1048 verbose exit 0
1049 info ok
