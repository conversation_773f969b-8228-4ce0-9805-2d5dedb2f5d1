{"_id": "parent-module", "_rev": "12-02a327d00dd4f81cb13d2fe92ce50177", "name": "parent-module", "description": "Get the path of the parent module", "dist-tags": {"latest": "3.1.0"}, "versions": {"0.1.0": {"name": "parent-module", "version": "0.1.0", "description": "Get the path of the parent module", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/parent-module"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["parent", "module", "package", "pkg", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^1.0.0"}, "devDependencies": {"ava": "*", "execa": "^0.2.2", "xo": "*"}, "gitHead": "0ebdd4a7582d4cee30ca2151fbd592b4b3b20ff2", "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "homepage": "https://github.com/sindresorhus/parent-module", "_id": "parent-module@0.1.0", "_shasum": "b5292863a1e8c476ecf857e7d75c98920b24b8a6", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b5292863a1e8c476ecf857e7d75c98920b24b8a6", "tarball": "https://registry.npmjs.org/parent-module/-/parent-module-0.1.0.tgz", "integrity": "sha512-fkZFUUL8tSsJUm/WvAqyJLV/Aj9/jpnMXHy0leJYEUu2qw5FE6nJQI/bMVW2xbZySot4uaPb6cw4eY5zQn7/Ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZY8MMMirTy0zXEW/DWQMPb5mJzvc7SyPcSkiEAaQmLwIhALRon4A7+SwJ5LrpBeD6e96QRtcT2PsQlvWDYP+hnYen"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "parent-module", "version": "1.0.0", "description": "Get the path of the parent module", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["parent", "module", "package", "pkg", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^3.0.0"}, "devDependencies": {"ava": "^1.0.1", "execa": "^1.0.0", "xo": "^0.23.0"}, "gitHead": "21d44dd4d566218c6abd1b8747469756fba455d6", "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "homepage": "https://github.com/sindresorhus/parent-module#readme", "_id": "parent-module@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8Mf5juOMmiE4FcmzYc4IaiS9L3+9paz2KOiXzkRviCP6aDmN49Hz6EMWz0lGNp9pX80GvvAuLADtyGfW/Em3TA==", "shasum": "df250bdc5391f4a085fb589dad761f5ad6b865b5", "tarball": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.0.tgz", "fileCount": 4, "unpackedSize": 3865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIlHoCRA9TVsSAnZWagAA7j8P/iXA1TC9HTMKzU1Ye5X4\ntOtox1HhIfp7VwNnFVti2GytUXxmlfs0r8UaCfcIk8IBAARsvKDREp3LobS3\n1ZbX5st+42h+b2oycKQkUhBaN//PZhp/WVTrA9vzRz07JS7ZqEu2j5t0iznb\nBdavKmC6OfzY3B3rsDpHukc1uhWDnQJMFb9S1NVObKlTjKcIkAYlissa8eue\n10WvvQzdCNIZlxU2G6XGezAYw2N7HvdmoyCOrgC+jb4GguE57467rrI/My5u\nL1NqFLaeiaB0w5V+mb5O3lkyxFPVHeqf6NoeWn6oeoeZ6fOugJbq4+MSQ5sJ\nReYJfAfcNmTPyXwLZFC36F/zZVxztfLiLQAyx1IIEP0A9NNXiOCUhx1F6PA1\n0xePgwCP4TgwBH79di4DXvmNvzCFj3W67F1KPna51FQTkxKU4MdCvCsejiiY\nmQFmz0UkqiXeUfKp0eKmRF+GTx5d6LhHrhHStFU8xEcjO19nu22vEQ1GLyLw\nplNWVvBTUskpOgK0yxcbwSB4G/9xxTrAQYr5O39ND1tD8qoCDxeHbRCWj2ya\n1HGFBXCe5gAr5PV+RHwIfUwMM7tuoUQgEcpKC5IVppnHE2I2Ay6aC+/lN713\nn5zICEuseMRuuqJLesGXcvkqXN6W/3cO9hsjcFS9c+BAcpgncvY+8EaAHmjV\nvsWu\r\n=VW5f\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdHOsgZBScqqrxAKhdenELVIH1+pBigyWWzHZML/WLDwIhAIcTgCIn7BQ8cJO9E8ydVHPoWz12CaN/taXYRUgvP+m5"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parent-module_1.0.0_1545753063775_0.45941122354092845"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "parent-module", "version": "1.0.1", "description": "Get the path of the parent module", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["parent", "module", "package", "pkg", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^3.0.0"}, "devDependencies": {"ava": "^1.4.1", "execa": "^1.0.0", "xo": "^0.24.0"}, "gitHead": "48267d001c4d215ba21a701a6882dba30fb8d614", "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "homepage": "https://github.com/sindresorhus/parent-module#readme", "_id": "parent-module@1.0.1", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "shasum": "691d2709e78c79fae3a156622452d00762caaaa2", "tarball": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "fileCount": 4, "unpackedSize": 3922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnLEgCRA9TVsSAnZWagAAnfUP/34CM4qiVo4Qaylc5j9N\naflPKZecVGFTivIGy9F7I+dTMiCXRhABp0MaLq1s03KOHAgym2ZL89ItB+l9\ngeUhRz7hFcTm4sP+iEtBQ0D+vr0Q+arS7qoROTXJ94ybC0rC/+Hr9XyvXZRX\nW0KQYkekqyTn5AIH1ZN7o0zgnfapdTgz7zBhGTsICqd1jl3FFJLshsXowO1X\naBCErOvowwQKaTVWnU65cIuMKKN9g80jIWZVYu5F6B9VOUOV7moGY3C0GofN\nD/b2lSMlF8En4UbiSI1F0NmGwnbDplri5KsHyXAFSoVhQFp65VQBJjZU/DnM\n8S8zan/8jpAAxAw7cZZ0G911zDLh+Jo7HuB+xc+dwLbK8fONAJOVFkRLAdCJ\nGC/XcyZ5ftPTwmDbn3wLOdk8UZytBP/FD69NU4HTvPkG2SkKAbv0jK+nIJEs\ncaQGh42m7mFEhmiXBLJZ83UAFlVsyAej9s14GRet3fAqWhIbnzdv9boQwYSi\nTGy0kxGtaaa8G/WcBZyn+hGGPlakNn0RUV6ZhZ0y5zpaM/YPFS885Oj0i8Um\nhuwvIYCf6hlxcLylUI/BY+HLzp2W7qFABT+ib7VA3QHznyW21tyHqXGXWiiu\n9urgGmksS5dhsEF4vYzZPN80xAI4qXf5pIBMUzB0b1kvemyCGJ/9FCxUghlt\nqqUz\r\n=Y7oh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH0o9dG06lHpdesOZRHLalVMES4OwKx40Db5qlXGMeI2AiEAuT6JtVLbiHQhF0YM+0LOWNmKt3pqYZmksmHz8J1NZhU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parent-module_1.0.1_1553772832186_0.1578223180001468"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "parent-module", "version": "2.0.0", "description": "Get the path of the parent module", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["parent", "module", "package", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^3.1.0"}, "devDependencies": {"ava": "^1.4.1", "execa": "^1.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "1923bf8f724f7f2e77a2c893116285a8b0a47598", "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "homepage": "https://github.com/sindresorhus/parent-module#readme", "_id": "parent-module@2.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uo0Z9JJeWzv8BG+tRcapBKNJ0dro9cLyczGzulS6EfeyAdeC9sbojtW6XwvYxJkEne9En+J2XEl4zyglVeIwFg==", "shasum": "fa71f88ff1a50c27e15d8ff74e0e3a9523bf8708", "tarball": "https://registry.npmjs.org/parent-module/-/parent-module-2.0.0.tgz", "fileCount": 5, "unpackedSize": 4625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyFjVCRA9TVsSAnZWagAA/yQP/3p0SAYda/UfvdMK1J3A\necG8eD/cd8XMG9f7mcyvdOkd2YM3IPfu0capE6kP4Wp9TkZPnpMTxguu2oxe\ni+mssu1Dd/L8LMxxsbsU7V3EyX2QV0dGsiB91H/QlPRfr3WapnL660JTNaze\n0cQF9vOs8GfTUCrsm3IOxuPJHiAkxs4udS1NzgNsGh5custjt9tP5Dx8fu8j\nCFDzT6R38iEV2Cn2w0IIQzJBkhQ5XuKgiex8RMtvKRa+fWSSwkQlMRM+9QRE\n5x1ACxEbFiubXr0pG3eZt6gMR8cCoxZjqOfXOAS6c5BGunVDLDY7UpJnhQEH\nfgJqbpzTsXYtaoDN9O4U1UloGyiLYKu8bTigBBfsPc5d/L0vUtWK4kw6XX6F\nHZuqQibUlTcXWc17zKyb+QOtTcIs2LAOhd0YB+vNxf45whQyO+bwl+N3LAd1\nJz5qh/kUw4/0YtQZuTrqbl58F2/hk/zGPpS8rbF4Nk25pi+sdzuqMpGuXMhF\ngTMTg1SmgVX4O29hKtLqiTE8+1T/mQR25mm7MfNmM0go1nHUmEe6tzA71Qb1\nvBnVimAkAxXTKdd8p5KchKNpYo+hJ+OKPpdt8LEwZlGm9Qs+5+INtDwHUTts\nZP3FvhmRX3oDhAavOEBM7wtX6oQR8IROq32aKPJ8jn6KdrixER/htTKDqQLM\nyLn+\r\n=G19r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICmQCYnaIZ1l00dvvfSVtxPE9vaATS8BsGo3Lhav91Q7AiEA6uiYRdPyzE4+fo8zrPAUfML7VHHqUaqvxDzcyFYW2Ck="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parent-module_2.0.0_1556633813147_0.3310203047620397"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "parent-module", "version": "3.0.0", "description": "Get the path of the parent module", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["parent", "module", "package", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "execa": "^5.1.1", "tsd": "^0.17.0", "xo": "^0.45.0"}, "gitHead": "e99192a8f6870dde49947643c7324a3797515de6", "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "homepage": "https://github.com/sindresorhus/parent-module#readme", "_id": "parent-module@3.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-zJXn5iKdXXUV51iC7O4nHiPs/eWnkd0obrHDlOKQ6/MafFrxljinW6hCkr0UTsAGq7B+NXT2wIfLshcD+5RvIw==", "shasum": "4bddc5cc0584935189977bae3384cc66dedde363", "tarball": "https://registry.npmjs.org/parent-module/-/parent-module-3.0.0.tgz", "fileCount": 5, "unpackedSize": 4939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh22RrCRA9TVsSAnZWagAA0jcP/AlCpLLxFecwaCI5BD9p\nGVJ5tSYVZyEKyt++9zZmYZ9NkpOAzdjwpwofWXPto+C++msR5ZQPJLFmHuXm\nvqJT39rsizQkMW3lnSS3svWKHAk/MLkxtOOAEV2rVqNDBMcIgLqNR5/BjB3j\nEtf57OkNM0Gat7KsJWlWSe8jghktZo+fHeSB8+4p9H2i+ITkOGtv6K9janTK\n/XaZ4RWEFyxPZsWc6EUEZxDKKL0Oy52WglfGg6bnhdpiBfhhUC6BepUmpZDO\nOsk0uzUi44e0oSci8lYW5IP3R4gRZW0JLsLWu8t8dQmQ0SaMxzp8PlLGZja9\nmz+bdHhLJgsjb4RucIe0CMWWAm1g2Nz3QgAOkXf+0vW4TqoJmI9ikyaroYTM\nRsBqg2yv/vIXbv354htHnOwJyJrvJDiB5cF058pqT59PLnbhsHQm8Mu+l4BI\nxRGaJsLSHuT7gjdc8jkge//v/Oy/jXr3fvVMpcuVrARAhuG0hwVkOkoDu8ZP\n/N372RqZhxCwpQiySAXZhm+IpD97hZngGYXbJWIgCGUHVWjU787YO5aHQGGL\nGJUpFkD5kg7CKHI2IeXzqsjECPQcZxBul448b8ioqeNCyiBIIUvrqbZfo8KS\nxe2V15wOf3KpZ+4NyipBoM0xpPj6YoP+zvZmi+DHLI4cuF9sZ6WRPyui4aM7\nw0MW\r\n=WtnO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/Fj9QO37vURVKlLGmOKOuJUaPovPItL/wAywaz1RxBAiAWrnwe9itWtPuCZQkeCTYME3fh5C/vG6jjKX2dE/y4Sw=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parent-module_3.0.0_1633335216602_0.5745603950022955"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "parent-module", "version": "3.1.0", "description": "Get the path of the parent module", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["parent", "module", "package", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "dependencies": {"callsites": "^4.1.0"}, "devDependencies": {"ava": "^3.15.0", "execa": "^5.1.1", "tsd": "^0.17.0", "xo": "^0.45.0"}, "types": "./index.d.ts", "gitHead": "f579e1737e12b120650997e16fef12dbced4938b", "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "homepage": "https://github.com/sindresorhus/parent-module#readme", "_id": "parent-module@3.1.0", "_nodeVersion": "18.16.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-FX4TpKXX6CesSm1D9y5IcF0/KdDjP/w0c1AKqreGZne2QyWiPWHfoApMaJl8zvH3DTh+xtVmlLIUqSSoPYjqLQ==", "shasum": "bcba90b465fa3ba029fad2a933f4c683668a2148", "tarball": "https://registry.npmjs.org/parent-module/-/parent-module-3.1.0.tgz", "fileCount": 5, "unpackedSize": 4939, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvbeJmShR+CeYeLE4/JA30I9wLHEXJNzmTZn1Am+FKjgIhANXOt3jeDgiLm8vngKrM1DdDxiNjVm4qIFxDkoHrfvQq"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/parent-module_3.1.0_1694672794314_0.07498039719627103"}, "_hasShrinkwrap": false}}, "readme": "# parent-module\n\n> Get the path of the parent module\n\nNode.js exposes `module.parent`, but it only gives you the first cached parent, which is not necessarily the actual parent.\n\n## Install\n\n```sh\nnpm install parent-module\n```\n\n## Usage\n\n```js\n// bar.js\nimport parentModule from 'parent-module';\n\nexport default function bar() {\n\tconsole.log(parentModule());\n\t//=> '/Users/<USER>/dev/unicorn/foo.js'\n};\n```\n\n```js\n// foo.js\nimport bar from './bar.js';\n\nbar();\n```\n\n## API\n\n### parentModule(filePath?)\n\nBy default, it will return the path of the immediate parent.\n\n#### filePath\n\nType: `string`\\\nDefault: [`__filename`](https://nodejs.org/api/globals.html#globals_filename)\n\nThe file path of the module of which to get the parent path.\n\nUseful if you want it to work [multiple module levels down](fixtures/filepath).\n\n## Tip\n\nCombine it with [`read-pkg-up`](https://github.com/sindresorhus/read-pkg-up) to read the package.json of the parent module.\n\n```js\nimport path from 'node:path';\nimport {readPackageUpSync} from 'read-pkg-up';\nimport parentModule from 'parent-module';\n\nconsole.log(readPackageUpSync({cwd: path.dirname(parentModule())}).pkg);\n//=> {name: 'chalk', version: '1.0.0', …}\n```\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-parent-module?utm_source=npm-parent-module&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-09-14T06:26:34.659Z", "created": "2016-01-22T15:05:41.453Z", "0.1.0": "2016-01-22T15:05:41.453Z", "1.0.0": "2018-12-25T15:51:03.878Z", "1.0.1": "2019-03-28T11:33:52.331Z", "2.0.0": "2019-04-30T14:16:53.267Z", "3.0.0": "2021-10-04T08:13:36.738Z", "3.1.0": "2023-09-14T06:26:34.494Z"}, "homepage": "https://github.com/sindresorhus/parent-module#readme", "keywords": ["parent", "module", "package", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"nraibaud": true, "flumpus-dev": true}}