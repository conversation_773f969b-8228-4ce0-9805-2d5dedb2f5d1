{"_id": "formdata-polyfill", "_rev": "51-971e2ae361d46445d7b7f796edfb2adf", "name": "formdata-polyfill", "time": {"modified": "2022-06-18T03:06:32.006Z", "created": "2016-11-25T22:01:19.419Z", "1.0.0": "2016-11-25T22:01:19.419Z", "1.0.1": "2016-11-25T22:05:22.137Z", "1.0.2": "2016-11-25T22:06:07.344Z", "1.0.3": "2016-12-19T22:43:10.021Z", "1.0.4": "2017-02-05T13:32:54.569Z", "1.0.5": "2017-03-02T12:38:44.633Z", "1.0.6": "2017-03-03T12:24:56.593Z", "1.0.7": "2017-03-28T08:00:29.069Z", "2.0.0": "2017-06-17T14:18:54.755Z", "2.0.1": "2017-06-17T17:03:22.927Z", "2.0.2": "2017-06-17T21:47:41.273Z", "2.0.3": "2017-07-12T12:43:35.312Z", "2.0.4": "2017-08-22T06:54:45.605Z", "3.0.0-alpha.1": "2017-11-22T11:48:45.314Z", "3.0.0": "2017-11-22T11:52:11.979Z", "3.0.1": "2017-11-22T12:12:27.878Z", "3.0.2": "2017-11-28T10:10:08.196Z", "3.0.3": "2017-11-28T10:18:14.550Z", "3.0.4": "2017-11-28T10:41:14.220Z", "3.0.5": "2017-11-29T13:46:17.481Z", "3.0.6": "2017-12-03T15:42:04.645Z", "3.0.7": "2017-12-03T15:59:54.928Z", "3.0.8": "2017-12-04T13:16:27.191Z", "3.0.9": "2017-12-06T18:15:17.503Z", "3.0.10": "2018-04-16T13:00:55.799Z", "3.0.11": "2018-06-26T21:09:26.595Z", "3.0.12": "2018-09-03T14:41:37.055Z", "3.0.13": "2018-11-15T19:10:53.230Z", "3.0.14": "2019-02-09T11:52:39.989Z", "3.0.15": "2019-02-09T11:59:18.439Z", "3.0.17": "2019-03-03T09:07:22.195Z", "3.0.18": "2019-05-16T19:54:05.160Z", "3.0.19": "2019-08-06T09:05:57.645Z", "3.0.20": "2020-06-17T19:59:30.535Z", "4.0.0": "2021-05-08T19:37:24.643Z", "4.0.1": "2021-05-29T12:11:52.653Z", "4.0.2": "2021-06-06T12:35:15.567Z", "4.0.3": "2021-06-06T12:37:59.929Z", "4.0.4": "2021-06-17T17:18:42.080Z", "4.0.5": "2021-06-19T11:02:27.305Z", "4.0.6": "2021-06-21T07:04:22.915Z", "4.0.7": "2021-09-06T23:20:30.961Z", "4.0.8": "2021-09-26T13:10:14.583Z", "4.0.9": "2021-09-29T21:48:26.581Z", "4.0.10": "2021-09-30T13:07:58.251Z"}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "dist-tags": {"latest": "4.0.10"}, "description": "HTML5 `FormData` for Browsers and Node.", "readme": "### A `FormData` polyfill for the browser ...and a module for NodeJS (`New!`)\n\n```bash\nnpm install formdata-polyfill\n```\n\nThe browser polyfill will likely have done its part already, and i hope you stop supporting old browsers c\",)<br>\nBut NodeJS still laks a proper FormData<br>The good old form-data package is a very old and isn't spec compatible and dose some abnormal stuff to construct and read FormData instances that other http libraries are not happy about when it comes to follow the spec.\n\n### The NodeJS / ESM version\n- The modular (~2.3 KiB minified uncompressed) version of this package is independent of any browser stuff and don't patch anything\n- It's as pure/spec compatible as it possible gets the test are run by WPT.\n- It's compatible with [node-fetch](https://github.com/node-fetch/node-fetch).\n- It have higher platform dependencies as it uses classes, symbols, ESM & private fields\n- Only dependency it has is [fetch-blob](https://github.com/node-fetch/fetch-blob)\n\n```js\n// Node example\nimport fetch from 'node-fetch'\nimport File from 'fetch-blob/file.js'\nimport { fileFromSync } from 'fetch-blob/from.js'\nimport { FormData } from 'formdata-polyfill/esm.min.js'\n\nconst file = fileFromSync('./README.md')\nconst fd = new FormData()\n\nfd.append('file-upload', new File(['abc'], 'hello-world.txt'))\nfd.append('file-upload', file)\n\n// it's also possible to append file/blob look-a-like items\n// if you have streams coming from other destinations\nfd.append('file-upload', {\n  size: 123,\n  type: '',\n  name: 'cat-video.mp4',\n  stream() { return stream },\n  [Symbol.toStringTag]: 'File'\n})\n\nfetch('https://httpbin.org/post', { method: 'POST', body: fd })\n```\n\n----\n\nIt also comes with way to convert FormData into Blobs - it's not something that every developer should have to deal with.\nIt's mainly for [node-fetch](https://github.com/node-fetch/node-fetch) and other http library to ease the process of serializing a FormData into a blob and just wish to deal with Blobs instead (Both Deno and Undici adapted a version of this [formDataToBlob](https://github.com/jimmywarting/FormData/blob/5ddea9e0de2fc5e246ab1b2f9d404dee0c319c02/formdata-to-blob.js) to the core and passes all WPT tests run by the browser itself)\n```js\nimport { Readable } from 'node:stream'\nimport { FormData, formDataToBlob } from 'formdata-polyfill/esm.min.js'\n\nconst blob = formDataToBlob(new FormData())\nfetch('https://httpbin.org/post', { method: 'POST', body: blob })\n\n// node built in http and other similar http library have to do:\nconst stream = Readable.from(blob.stream())\nconst req = http.request('http://httpbin.org/post', {\n  method: 'post',\n  headers: {\n    'Content-Length': blob.size,\n    'Content-Type': blob.type\n  }\n})\nstream.pipe(req)\n```\n\nPS: blob & file that are appended to the FormData will not be read until any of the serialized blob read-methods gets called\n...so uploading very large files is no biggie\n\n### Browser polyfill\n\nusage:\n\n```js\nimport 'formdata-polyfill' // that's it\n```\n\nThe browser polyfill conditionally replaces the native implementation rather than fixing the missing functions,\nsince otherwise there is no way to get or delete existing values in the FormData object.\nTherefore this also patches `XMLHttpRequest.prototype.send` and `fetch` to send the `FormData` as a blob,\nand `navigator.sendBeacon` to send native `FormData`.\n\nI was unable to patch the Response/Request constructor\nso if you are constructing them with FormData then you need to call `fd._blob()` manually.\n\n```js\nnew Request(url, {\n  method: 'post',\n  body: fd._blob ? fd._blob() : fd\n})\n```\n\nDependencies\n---\n\nIf you need to support IE <= 9 then I recommend you to include eligrey's [blob.js]\n(which i hope you don't - since IE is now dead)\n\n<details>\n    <summary>Updating from 2.x to 3.x</summary>\n\nPreviously you had to import the polyfill and use that,\nsince it didn't replace the global (existing) FormData implementation.\nBut now it transparently calls `_blob()` for you when you are sending something with fetch or XHR,\nby way of monkey-patching the `XMLHttpRequest.prototype.send` and `fetch` functions.\n\nSo you maybe had something like this:\n\n```javascript\nvar FormData = require('formdata-polyfill')\nvar fd = new FormData(form)\nxhr.send(fd._blob())\n```\n\nThere is no longer anything exported from the module\n(though you of course still need to import it to install the polyfill),\nso you can now use the FormData object as normal:\n\n```javascript\nrequire('formdata-polyfill')\nvar fd = new FormData(form)\nxhr.send(fd)\n```\n\n</details>\n\n\n\nNative Browser compatibility (as of 2021-05-08)\n---\nBased on this you can decide for yourself if you need this polyfill.\n\n[![screenshot](https://user-images.githubusercontent.com/1148376/117550329-0993aa80-b040-11eb-976c-14e31f1a3ba4.png)](https://developer.mozilla.org/en-US/docs/Web/API/FormData#Browser_compatibility)\n\n\n\nThis normalizes support for the FormData API:\n\n - `append` with filename\n - `delete()`, `get()`, `getAll()`, `has()`, `set()`\n - `entries()`, `keys()`, `values()`, and support for `for...of`\n - Available in web workers (just include the polyfill)\n\n  [npm-image]: https://img.shields.io/npm/v/formdata-polyfill.svg\n  [npm-url]: https://www.npmjs.com/package/formdata-polyfill\n  [blob.js]: https://github.com/eligrey/Blob.js\n", "versions": {"1.0.1": {"name": "formdata-polyfill", "version": "1.0.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "c9dff1a702133a525e7628ab7eb0c1251f5762bc", "_id": "formdata-polyfill@1.0.1", "_shasum": "a698239a46d0e71eea09a8119373bc9d9398ff5d", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "a698239a46d0e71eea09a8119373bc9d9398ff5d", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.1.tgz", "integrity": "sha512-M7WlN1lbIrlm0gUr3sv5H656raI0XEDK5hiSieM/2kA1eUNJndH09+DUyBPYjUP8H1PvJ+dbn27ewQzA/WMt+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6Hixvr3fWZRop4TdCJ2efHRhywyUr3M89lKBG4ZCZvAiEAmOWBTmh0P2WCRg9SRtkJwzCGpJVHwGY2ZJLGicVyrHw="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.1.tgz_1480111520085_0.02396637643687427"}, "directories": {}}, "1.0.2": {"name": "formdata-polyfill", "version": "1.0.2", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "1ed0d82cd110b3ca047db5974c1d8450ff6ae2f6", "_id": "formdata-polyfill@1.0.2", "_shasum": "1f2aa19a2a593b5129b449ba7f42364b8e8e30b4", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "1f2aa19a2a593b5129b449ba7f42364b8e8e30b4", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.2.tgz", "integrity": "sha512-IyMlp4FK0mHj1/t6Ws7mmgTnN80mNgkWauLQmWxPL8YFBH2lLK0/d3IxzcoxxWGTTQu6Z3sEsCMpB+bNcrD/1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2f8/EuPEhT37jz7GM1G7DUuLEF+tC0hjY9XKTQq77DAiEAz2+fso7F8v/HVt8Ju+Hz3pcJKc0GFbBAKnl3W9nv+dg="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.2.tgz_1480111566754_0.6992857563309371"}, "directories": {}}, "1.0.3": {"name": "formdata-polyfill", "version": "1.0.3", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "59ca5ac59abb7a9a3f74dc5f84288d95d5ecdf52", "_id": "formdata-polyfill@1.0.3", "_shasum": "d89832b78b453d7caab05c1e79e47020d0262491", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "d89832b78b453d7caab05c1e79e47020d0262491", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.3.tgz", "integrity": "sha512-G6wiigZPhRnlIkrAEzndPv4gZUULXekgVvVd6M0GzJ57OeUsJo/fZfIRFo1d4Sz00Q4Fnb+xmtnZSrLtZ5S4Zg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6gLR2Z/h8HiHgBEFVy3KTqWqQAqRgp++SqjIcC13AUAiA3X36KomUjRqGnGxevRL+13ila/ZvgvuHaymITxyQ+Dw=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.3.tgz_1482187389404_0.12283244170248508"}, "directories": {}}, "1.0.4": {"name": "formdata-polyfill", "version": "1.0.4", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "b7c38dc379ced5131639f9b4a35932e3051347f4", "_id": "formdata-polyfill@1.0.4", "_shasum": "9ef5dd2d78db8d635351224c60e4a915bf1e29af", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "9ef5dd2d78db8d635351224c60e4a915bf1e29af", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.4.tgz", "integrity": "sha512-3GLBR7SRx+1KvouQMLM+t8FZmvnYWCfWzdl/OjS72rL/4feYr+N45poIivrb2p05Rr2UbGgyP/+2dzkJrzPtUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHAMWi1hKni7lyr+qhS8LWMOJKhI7bKEwetA9RXg5o0VAiA+TxqqJ+I2q1aGmwCuPZSLVMaP/aKLsgSoQzIMA2fc7g=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.4.tgz_1486301572854_0.7544996605720371"}, "directories": {}}, "1.0.5": {"name": "formdata-polyfill", "version": "1.0.5", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "77929d49347102a86fcd80639147928ae6b04e72", "_id": "formdata-polyfill@1.0.5", "_shasum": "dd024d970432ce7a46d4458f394ed9454c1eca69", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "dd024d970432ce7a46d4458f394ed9454c1eca69", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.5.tgz", "integrity": "sha512-u2O+UUBYpcjmL3KsJtrQ3qpmu3TC4eUiyk9mlhUj/KYJYC7f6YWQB4wzrZUMslh0zr1vWoELY6pQIboo00xB0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDK2Qa71mNEvjjNBYtaN0AtYgx/4RDlu90bRiQfXZCujgIgLKLdqkghZz8KrXkTRm5gFLwxoCiZimi9VRmwe688mfM="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.5.tgz_1488458323992_0.20464711193926632"}, "directories": {}}, "1.0.6": {"name": "formdata-polyfill", "version": "1.0.6", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "f85488093b330ed18107df705a5e38ca1251a13b", "_id": "formdata-polyfill@1.0.6", "_shasum": "f6fea532bde6c206cdc17e2307583e547585c146", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "f6fea532bde6c206cdc17e2307583e547585c146", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.6.tgz", "integrity": "sha512-U/hpTyQ0W9jmmYRjWuGYU0iRg2v/qAPh6rV3yP3b0OyGeHwcXjfJ1/DihwcuC2qKd+y0wwA6jg6BgKIYEkI4jQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCx90Qs3icqOGppytCJ6aqckbNEH8Xm6dVGKaHrRslnUgIhAM9QRh92FkX8oFCtpt2Y+uj+xiuznVAXkT87fygQPsoZ"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.6.tgz_1488543894812_0.38432479999028146"}, "directories": {}}, "1.0.7": {"name": "formdata-polyfill", "version": "1.0.7", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "FormData.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "83d92125c3b267a06fb9c07bf1b5ce86be823472", "_id": "formdata-polyfill@1.0.7", "_shasum": "35236fbf6d1efd096cea115004901a97762d0acf", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"shasum": "35236fbf6d1efd096cea115004901a97762d0acf", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-1.0.7.tgz", "integrity": "sha512-AiRnP2e4tSa01n7Gulb00K5PmxfP0nwPB/U19eyAe69LfcFmKe/UQPpP28ojkYAhLE3DjVIxcGiDwm7G+p69kQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwOJM5iW/nZg6KVmM3ZDZLKhxWMta1crF+LOdR1oZmowIhALR2m4L6yksZ0c2nAGeIK0mC2IpE7n9DrKy3i/CKIdwu"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/formdata-polyfill-1.0.7.tgz_1490688027036_0.5020065174903721"}, "directories": {}}, "2.0.0": {"name": "formdata-polyfill", "version": "2.0.0", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "79d7643d58925f12d2cfadc984ae450956956198", "_id": "formdata-polyfill@2.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TxL/8V5O5JgRrpujoIcZ7VqMLtLOwyh8W5Jb4+3sPdF4Bb8Oln7VQ6kGgeG96UFtWTFWlD0OvWBqSHN1kwPFMw==", "shasum": "d3f9f88f575f96d10be3e9e48adb070d3a9fefe2", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDelTwEDDzho8XnAmwuJYdPOGSmHd4EifAm1qQg1cei4wIhAKOzVthwEr65d1Gi0vIXwScGz3zWwJFFFiRoh5QqL7tb"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.0.tgz_1497709133794_0.48105699475854635"}, "directories": {}}, "2.0.1": {"name": "formdata-polyfill", "version": "2.0.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "79d7643d58925f12d2cfadc984ae450956956198", "_id": "formdata-polyfill@2.0.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4XWvky9pZFyY+68aRcEcGVM4j0QdKgDw+5lma1iw9KAFhItksNJJkclNkstfp/IxMBWrFj7yILdJAdVxVd+YGg==", "shasum": "0d584d2f101ecc277727ac79d6094b941bf8ec5e", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyqIgsEHkTHgBYIfjK33jdigdi3JYyd3IlULc16h3ckgIgFZ96yFdAlTqsjGhTXVRMs8gjylj8hFkZFTHcb0d0cCs="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.1.tgz_1497719001967_0.32420781231485307"}, "directories": {}}, "2.0.2": {"name": "formdata-polyfill", "version": "2.0.2", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "4b2f5b154068cd51a577ab20f36501698f22b9b0", "_id": "formdata-polyfill@2.0.2", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dvrrNWQE4tDUL6goS+QweI7lGi8b3WzU2aKOwpjekP91s9Mgtcfos1T7eKYr1/BvKA9kCKsoEqNqF1pBVZKxDA==", "shasum": "c4384ddd1641908cb008d92324fe113ce2a5d0ad", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG2u8XEklbhlQLiVHWH4C8EpTAw7ILw6UJ8Q+2FFPVjIAiBzpzpmnWlklmAjKE+nsm3P+mzIqrXSX1FT0G3DAIoLgQ=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.2.tgz_1497736060307_0.817123741377145"}, "directories": {}}, "2.0.3": {"name": "formdata-polyfill", "version": "2.0.3", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "c6d4e234982b2b4359ffde6cbfb317ec41a90dc9", "_id": "formdata-polyfill@2.0.3", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Jlrp2WZ4oA513iPFSXwsmSTKU3+CEbwMtPdxUuyd4WaVaxorYai3V71T9n0/TOESb3oy0F/vyvsiybDtKzYQvg==", "shasum": "d85146ec1fc37a37279221273ac5045078e79628", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-2.0.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfUIejzbxoMJ535pHl9P2eHfXQPMPj6KjIFyCKMU82PwIgMdSFtomAh3DvvrBQ2/p0e/dY4JO4ALiln9NELMyTgvw="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.3.tgz_1499863414138_0.9088069072458893"}, "directories": {}}, "2.0.4": {"name": "formdata-polyfill", "version": "2.0.4", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "f24b6e7c43813c8f1b86e112d82216f8175ec380", "_id": "formdata-polyfill@2.0.4", "_npmVersion": "5.1.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jsOLsQy0/dbO7BG3G9H1riXNuVxn5L2aBqdyzYUnxk1Jt4KztpF5N2HgHijzfs52temBpLYXz/jEwkbcQodfdQ==", "shasum": "2f5eae8f34b697cdc6b107ace7d617ed5f1fb7c4", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-2.0.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFAqGGmZrj1KbCgn5rOofWGGFotO76nm3sse4mHvywSHAiEAo6kCxakmTfU/QGEUEuRpVz+uroYA0SkWGdzZSrU4MwM="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-2.0.4.tgz_1503384884050_0.5999235606286675"}, "directories": {}}, "3.0.0-alpha.1": {"name": "formdata-polyfill", "version": "3.0.0-alpha.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "google-closure-compiler --compilation_level ADVANCED --js_output_file formdata.min.js FormData.js", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"google-closure-compiler": "^20171112.0.0"}, "gitHead": "ccd2f7ca8a3e8b95c27b0c71ced952c02ea7a7e5", "_id": "formdata-polyfill@3.0.0-alpha.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Uw3YTOszlLzumpL/ATdG6+Tj6mX9miZXOdyzTgHk9sIY1j3zfEsBTkeMgoNMTqoyYh0AoKFpMtBYSAtu4CzsRA==", "shasum": "13c16b1d6ff2c698a1863319390a6b43d3011a11", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.0-alpha.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDHU+3TXcxK/UxjH0X5/PrZqB5yTecf8efZC4WV6J8+jgIhAIBF6nMhKK+tSPkuEiFrjspt8vTRopESpllRIElqH50T"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.0-alpha.1.tgz_1511351324433_0.6919542476534843"}, "directories": {}}, "3.0.0": {"name": "formdata-polyfill", "version": "3.0.0", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "google-closure-compiler --compilation_level ADVANCED --js_output_file formdata.min.js FormData.js", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"google-closure-compiler": "^20171112.0.0"}, "gitHead": "ccd2f7ca8a3e8b95c27b0c71ced952c02ea7a7e5", "_id": "formdata-polyfill@3.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PWxesKKgQs8n4ZpDCJScvS2QDZg979eFxqLUB9BjdzRedBRVOzt6DczI438XY+QFuQW31IrWATwgVbFEeDuoig==", "shasum": "8a95a8e12caecde9217d908743b1e56fb6f68ca9", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEVWq6PLIEelWRke86uneNMat/UBCkSGFzjZP0fF9RG4AiBGIXOPRH7z4JPN0pngqWiNIkOgNMfWZWBROsoRfp/orA=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.0.tgz_1511351531046_0.49980301898904145"}, "directories": {}}, "3.0.1": {"name": "formdata-polyfill", "version": "3.0.1", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "google-closure-compiler --compilation_level ADVANCED --js_output_file formdata.min.js FormData.js", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"google-closure-compiler": "^20171112.0.0"}, "gitHead": "e44bfd6ee3b1f08a8465893a8fe8011dd96baf32", "_id": "formdata-polyfill@3.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lwPJQPW9vSnS/475ZRBdwLl9q20axFzKn+oE2PHvvTdvOJn2+jg5IRnElqqU9FzTr6hrdyEcKVXANoop7HyDpg==", "shasum": "3ba73d6ced4af6e124cb78eedcca38881b6abea1", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH7qxU15Ecfyq5S55vcxFEIsfKKQLVMwKrZs3VUq9heTAiEAqMei/YgAPvQID7Q0Cr5I9bihUqJDo9uZAfAfQPpFK8k="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.1.tgz_1511352746948_0.20994951110333204"}, "directories": {}}, "3.0.2": {"name": "formdata-polyfill", "version": "3.0.2", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "daab62a9d4ca014757e2d0beb2c34f8ffa065e46", "_id": "formdata-polyfill@3.0.2", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oUGGwATfMU6oQj5jCvXNMpjPV42LJYFeVvzJ3D0xP/36CxpnAWlZHuMGru487mKLoZ6GEZsEbHuSG41y4SZL2Q==", "shasum": "cdd6ed3579b3915f696ea830d4f56d9be0520800", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbdHZZmTI+woDSf5fd8DL8TnCWKRSE0pNsurWTxqli2AIhAIB9rc4WQ/nx+i8SjIR1Zz4KDG/fEC+QFZdWaiKZPI2F"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.2.tgz_1511863807349_0.5209966427646577"}, "directories": {}}, "3.0.3": {"name": "formdata-polyfill", "version": "3.0.3", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "echo \"Error: no test\" && exit 1"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "gitHead": "daab62a9d4ca014757e2d0beb2c34f8ffa065e46", "_id": "formdata-polyfill@3.0.3", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gEeQA4B9H2EyQCnjj8Jma3/bhrcmc61N+dTDOmXktYe06HKcp1i6BxHO3J/oQp+VQIF6jHXzeHI51wv4rjH+Wg==", "shasum": "dbca1bda194b1656ffd6aac3f4ba86d472592de2", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIArwjO3pYQ0IQfaE9jWVXIzmcQThC+oi45V36lkrqnHkAiAHGZ2ctY3epYVkg/ZSQltD26cQk7eyVeW9XBRQeexi1Q=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.3.tgz_1511864293551_0.11417500744573772"}, "directories": {}}, "3.0.4": {"name": "formdata-polyfill", "version": "3.0.4", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "328c0cdc75dfdff24b603f664a7cef21e7fc3ff4", "_id": "formdata-polyfill@3.0.4", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L/k+T0OQvAE7aN0i0Vpdlp+qXwATJcvZFUPR58ked26iPo5HS4q8wVl1JPJoufhAp96gerDE69z+zYj+g5Vkjw==", "shasum": "ae2877205ce5d4a6e413f9c19dc289654ada1a19", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiiIIhF6bH7F/n968wcy2YoxxFi57vAnwH8XxyYHxRVAIhAM/HFX1emmtxlmrAyA4nt800obHUBPmIXNT3Phd5+m0f"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.4.tgz_1511865673218_0.4973791416268796"}, "directories": {}}, "3.0.5": {"name": "formdata-polyfill", "version": "3.0.5", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "95c9a168a3111bd34a72d308e792f2e940413194", "_id": "formdata-polyfill@3.0.5", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QPaWGKQfp1FBRE+5RVfixxW94gs7QpCNivvC7U82CbZmpn5qn/S06kl4HrasUgbniJ3lt5a0Nq9AuzFOQSpczg==", "shasum": "b2800e437b9bb7b77c305886ba21b56c90319081", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDNL/r5A+nVxDu+KpYOn/uO9K1VxgiqH7nHEIB+EbnlwIgfe5CCigilER2c2R4Q4B3tCbnpBgA5bw9JswMNINtLSc="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.5.tgz_1511963176365_0.2223030086606741"}, "directories": {}}, "3.0.6": {"name": "formdata-polyfill", "version": "3.0.6", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "03526b6db506663ded4afcadb26df5d892de5745", "_id": "formdata-polyfill@3.0.6", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8teKnVhuyOUyN5QXiMe1NvN3kZetX7buS7NpA66qMq+oPyUJ0ASe7kvmBFgWx+Xtv1MCHcBfwfEjQUSJALz5CQ==", "shasum": "3a960d511294376f7e0085244b6ddb91727ca920", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.6.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDujQgB/Z1krCLbCqfokoEw2+VLZL0c/FFdLW/Wyw5lZgIgDvt5bVFtPRSX/qJGbycaGsRoiKE0J6knBXtdqaCf+dU="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.6.tgz_1512315723604_0.8781582294031978"}, "directories": {}}, "3.0.7": {"name": "formdata-polyfill", "version": "3.0.7", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "af507b2391876f0b76db25335774e3f712d39ac1", "_id": "formdata-polyfill@3.0.7", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7cnmgtyakIv0rZXKJECvFh+cTMn8P1THqIG0RE9fFVOrgG6y0bF3O4n1nilGizj4/ffiyY84kb1qF0/kef3WPw==", "shasum": "8eeec0ca62421af79164d0691725f94eca0eb081", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.7.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF1vURzR8mCYdid5LGzBm8OYXgq0kZV3BUaA0smWRYXCAiAhPycg2DMkSKGB4dOXkvWqXfG6eTQx0wDZVD6dUDOVbg=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.7.tgz_1512316794027_0.29985491721890867"}, "directories": {}}, "3.0.8": {"name": "formdata-polyfill", "version": "3.0.8", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "13284d6743303a648754be28b4caf9357158b278", "_id": "formdata-polyfill@3.0.8", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JGkQgtGIpCNldkgTbSirI9uZUzmJZWuWszFNpDMgPmdbHz3j2gGuZ6rk9A6VqeeTOZR59PmEzUS8mN4AUFAYxw==", "shasum": "eab9896f54aec79b0fc57f068e3f880bb80c6127", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.8.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEBJGzb5wJKFrT7IOqBIzaLMfZ0d8uzu1lYMpOwE5JPuAiEAlg5UXsuT/9BhLMl8ucse7OZKgGiz0de2qQand3cAkAg="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.8.tgz_1512393386264_0.2903132955543697"}, "directories": {}}, "3.0.9": {"name": "formdata-polyfill", "version": "3.0.9", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^4.0.1"}, "gitHead": "008b5c3169362f8548e0d7fcd1da1893a65bb7b1", "_id": "formdata-polyfill@3.0.9", "_npmVersion": "5.5.1", "_nodeVersion": "9.2.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-KKTifs9ipLF+y6HUdL1l7njs1he2z8QhX7te+IlX2zm7ItNlaMqnXi2GfJNq8xHFbZ1ZOHMvSBWLYs8jR3XCkA==", "shasum": "9cdcbd90e31378f5a1e65bc5f68e0db28606a5ed", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.9.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTLM9/RrThGsf6i3ZU9U9vzTie6ph3fHoEYKCPOjc+DwIgFMJWgsbXLFd24X72/XS6rb4pce1d/Cw12wVhpvqq45M="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill-3.0.9.tgz_1512584116547_0.07209669169969857"}, "directories": {}}, "3.0.10": {"name": "formdata-polyfill", "version": "3.0.10", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^2.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.1.0"}, "gitHead": "13705652b43a2d139b5f5e40f713f566ae48c766", "_id": "formdata-polyfill@3.0.10", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aAnr6/BW5pnzzeCvuYQBnyf6a2bovVeMwaFiMMkoH9RanBYAkBlTvB+RfNeNC4qLa5+m9ZtFGtrW+LzXqtXLbw==", "shasum": "4e1bfcc1e131f73b07856fc159ee103d0b37ec3c", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.10.tgz", "fileCount": 9, "unpackedSize": 27954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1J6ICRA9TVsSAnZWagAAuBYQAIIfIGbpqPLlLOIkOXLT\n8ucYwESX9OS54n0vb80/qjOzkEd+RrlimCN/hdYRb8gueyLMFmTJn7YfUygp\nPAY4JiOjBEtQuh1vyKcv4TnEnR7AWKqj2Arj/x3RbSLZUNGu9fLxE1jSkSZs\nAjZFKuPmf/qgXbpLMduOM8wcDOPYNqm6qcKB8MMCP/CMJkilJH1osANQ+XN0\nwe4BLtYyn9I+1nnhFpuIfIHcDsx21B20sz+uhUFhSmV17YsfJrZaBDQmR0NU\nAEXxpmnH7Z8PjoBicuOHA/XAKD691Q0GVNB6Lk+XixSmauDTly3SnCA/ZEbm\nb/yQQ9vUQfhjHDh+EqCnF9IjCPsyzem8VRRIYdof5Lnr/HWFvF85kVESwiNP\nKXA3LU4da1aBO7Kz2jbLNlzdTIf7SlzL94tP815Lpx6ZkJeKbze/GVK+51PE\ngwNv7Bzwq0Y8UA2O+KFmTpGIAoArMUfmKUVfcZyVEk6kmnGGdMAdookrPPQQ\nvkNIaJaK1UstCRzIepncNfcBw/xaDyIEhr6x/kTXZKWmZCUlyDJKnzAOZIys\noXvfhVErgnIxELnbNz/PkzNMK7XsyZYkYxCPUGOxJZfxxdO5L6hA8ulPM60l\nCVa7/+thgmtUW2L2rKZvtD9Xz+HIRHDmsBbYjO3H2i+0kimb275k9XrNxnEE\npK8f\r\n=qJHb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIExUEQA2ZMnACvv2pFLfpGtFKIZghupv9xTlS3czhxU8AiBt9AUOmLP96qHZujC8x/uozGIIPQL9paqaBgOeqN25/w=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.10_1523883655665_0.3556061276708973"}, "_hasShrinkwrap": false}, "3.0.11": {"name": "formdata-polyfill", "version": "3.0.11", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^2.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.1.0"}, "gitHead": "871683163d359883851b0839c6d160482dfbcec8", "_id": "formdata-polyfill@3.0.11", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lDyjdlptnGL1Fk7q+hketv31EN9rWaVC/SLz1tRaUktGrsCijyueIcjn7Tw3xKEdCjS5SeBrWp5aNLWUQq+QLg==", "shasum": "c82b4b4bea3356c0a6752219e54ce1edb2a7fb5b", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.11.tgz", "fileCount": 9, "unpackedSize": 29266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMquGCRA9TVsSAnZWagAAhosP/25ktqKGPuu1dr+0sg/p\nja9WxemI6ZKnfBY1ebjd413c5MchR4xY+eS2Kk8tSxVzOfxDPBSRiOfG3IBa\n962JbXRyav+TSI2wrcvKGxtUDNE0bYg1+CocvPGjwHlpdSPVBGTt8XwQQ/MH\nt93u2aIxlKT+LrOqQKjwgQ0l8PEmtBdRldECzzxZh7tQW6ZXjP3aOouNkZL0\nul8dpzC4V83PXKP3QVPqacecae/HlVVn7RTpGnlwJWLqNQ24rQEymFZV18tj\n0sPuiSN90cViGsy6R0sEEGkzZ75nWCvY/cYjG4uO9KNvQVHoW6zbUpKZygkZ\nR7Ismi3G1zWmtSR4SJ57qRrJwJtNxjSgJIAvZy70bKwbROUy/tGUgsA6lVVw\nnm4qjT1Eph2EnFu7blqmUNzt0wkXCD1B9lyVRIkIzHJOKR4blCCrRdvSzwHd\nHEzQu16x4mJMUh4HJAvfmqDzrUCrrYB8TRDwbW1akK3ho3a8W105/js6MxlB\n6u8wa+clTok56KaIA2RbUSQ4/QQfirStLBHXehv0bJ1kcDAwayRvDBwhOBd3\n+oN7v0oXnPCkzp0UQwC2RoCRTMSGbQmuBZ6/nnAsh5xPI/M890gVeH0QkGwx\nWsc8LQYD+ColoJXY9lNu8eOAAB3VxvVxU7eeqrAguAjlijxsmgsmMYPsEURy\nVprm\r\n=g2fO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIRJPsTPsdNZXqSjz/U2l2BhQCRzubIlmW0a4R36k2wAIgHX0lugigqqCKiTAihZUughK7grCK/qiisylmFcwXKv0="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.11_1530047366478_0.4021415435359905"}, "_hasShrinkwrap": false}, "3.0.12": {"name": "formdata-polyfill", "version": "3.0.12", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.1.0"}, "gitHead": "e84efe752a1452d8b8a69d94377d827ac173d6a5", "_id": "formdata-polyfill@3.0.12", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y5V1Y4e2VSJ29+CAHozJwN9BqvsigYicZp/MqeYP00X/UMah3cLb2ix7/58fLcaAjwr8HfztD7Ih/IQCW1BGfw==", "shasum": "43f8d9bad5408a57c4f801784526e8230b6e9f7a", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.12.tgz", "fileCount": 9, "unpackedSize": 29576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjUghCRA9TVsSAnZWagAASVQP/3b3tSu6MejYry9g6GMl\nkHPWpyxQACmm67O61WzjHPnuDZc28HXZhGadLw9Pwv++zjiDbX/OIP9nW/sw\nW1Zv6oeCQ2UZoDKfsg2rwJo7QSIbSHfjzQABUXz/Ywazp5MDwmAWtVod8nn0\nl5a2gjDDtpZOfYrZ8D3uI1xvMirFC1LN/MWR7qzH5DYtNb6me94n61VDXIb6\nhEGMQuexn8g3DUODWcvRY7BGjYgsz/8hgvqyZF7IasTDa496x1xU9dKqn6Vi\nc8IsXb3Qylrj1v5gOu+ziEGpeM8cKejTeNKC3d+Ad2mmywT5f1FWUgw/vi4o\n+yJLrfb5QdanWq7d0OstfSfsrWilBYCaxHGwRSaX+cM46RzHk1Rl1Faarylj\nDNrOt8OabIztxb4K1+1/o5UkN3T1ZbAgbxUIYYoMe2WvST4kk58vclxOz/px\naCmJDXXPZO6Nga5JCKIm1pDCJ80979knop2qddGlsWuN2OZPtbGzqcpNzH/8\nSklwBaHNSuy+eA+NN+BERlrzAZWVKFMwkYaFAny2BUVGpbkKD0ZZ3/Tg5lxU\nvr2BeHxrTfAIAbnP9aWzyi82BhNTNFrYkhZcIZCHCUgjmMGmJTcEcFg3ar1M\nWVZQpmVdDK3HZIYwqVjTh/9sGwkaTYJSgRzga+Ipy4z7PC4fYp9ytbTV5Rjw\n7WE3\r\n=CH+d\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOz90kJfLjCmLydGnc1fQtua+A8wYHqaqi4Vc81jOxvgIhAPLgC0/volUK8kcs/Htn/uBxwYwnRa06UcxRn0vd9YXx"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.12_1535985696860_0.3201304824786584"}, "_hasShrinkwrap": false}, "3.0.13": {"name": "formdata-polyfill", "version": "3.0.13", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^3.1.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0"}, "gitHead": "1695a7c732baf57260124983dd5db4f330ed7fa4", "_id": "formdata-polyfill@3.0.13", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aYrFSz9wxtBc9E1yCfD5IFhTK9FDZVRM53kUQn1W6Et5+R9c4tpa5Mg9hQIBIc2er3fLx0ElbKujipu+2551vQ==", "shasum": "991321c83b7a760aaf3788f1b7bbbf77e85b09bb", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.13.tgz", "fileCount": 9, "unpackedSize": 30117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7cS9CRA9TVsSAnZWagAAvdUP/2HrjYW17E/xNAwCAyxS\nxin+9wPAGALpYBS7CNFsWsanLQZiFKZEjajuGoVkmSFirBsuXXoc1z3VaU9M\nKBxRIU9LPwG3EGovuS+pniJQzkwwaIVNaA50m9BZBG32HZ4huB25vuyhZPZE\nOT1oeoSx6g5Ga/+GnbwXHP8oELkFHzGmwqy7XTT5Rj2jejdrrJJGl1hJ4vyO\n3EP7p9a8zNPc9+djTxXoIJ+/MMvoL96ehSLVFaJdoSClNaDyXHx/0helTDGp\n2Ir2eOGiwZNXAW4K/U/ee9nZctVGtNJJdqmG8qIfvnOBRjph6v31+8oaO+nr\n+SJuResHEig+PbKKHUYuFQ8xKXK+L/Vg+aOJ3Z/Y+G9eBjqWr48ByhNNFXyH\n2ifa3sQpei1QvFo1cMpP3Y9EZlz0wGJZ5hl7zs5TWdqzFw93bdfpwVXFoRd8\n97vgtvfOdP8XEkVoZk4MCP5bQkJykk79bCIbcL76sRzKkTtuYitgkG7+qTXU\nujOWeCVHP9snRl3UPBB7HITJt0j+Ey0eW4E6WjrhbiGtNvJQRVj8wQ5201z8\nepwAYvGFQjPFbB9plHON8/HP5Ww64m0iuyv8KzHe2934lGHvDgPVdgQRucZT\nkU1s1zqYPwkfIQyr3h73u6s7QW5+g0hCmZOvOlVnb6DJWdb+D1XFEU4iFSMm\noMUX\r\n=RSYy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBADFhva0rAJbGIZezB3dSTBK6KItUmxjTt8kApzb7J9AiBeCjT28Y1EZmzff9k7uWV4HlLv9J2TvUOUqCz/yrlnjA=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.13_1542309053065_0.25506122995683467"}, "_hasShrinkwrap": false}, "3.0.14": {"name": "formdata-polyfill", "version": "3.0.14", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0"}, "gitHead": "f6906e9a893f049f8d4573f973e4c3d75de16c5d", "_id": "formdata-polyfill@3.0.14", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nARSIzabuVb7bGoG1VusU95bQOA0QCRVGrLSaq7l6lCCEF6CAm1BrUQhXt3fCpuzfCz8aTSvx0zlHoLM0LFnMQ==", "shasum": "66b2ad64a22b732d31aba0d5537d7c34c150b2b0", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.14.tgz", "fileCount": 10, "unpackedSize": 33284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXr8ICRA9TVsSAnZWagAAFB0P/1uzaAJuHhSh4UMd8C2e\nw6Nj18BW71KVGf5D8Pi6qkZZoVTbjdx0SfOcDrXuBRz4kADeOiuEesv/9eh9\nlRiJQpmbnGcs0VZjfgG4HfXz7HkZpgGJcKRWvfCReP4Qr9O5udJz+ezNFOqT\nXpSuKU0iF45Vs+HXHwKptlrNgNpNPiXrMgwjGvJBkMmCk84ZBZyC7Qo66FMY\nTUwaRdQeKGtMhY+tHUOZmYvGEiA5SWGsGWhEeCmDjrB1v6qOruQ9SmDOneek\nJ3BShmW9jGc+4prf+kzhQ9gP96A0JWarxMitgpeFI5Q2ObZhL3aDR9OQRTV5\niB1C6UCnnNa79znnbImuE0zfI53bcYK1aJhx2JlDrag3U4cZyoURSLgL7M9p\n4cfS2VS8UuvE+Ww9yfuDlMGjp7op0FEbP5wdEMwbBHhqfrVsVFcefWRF2VdV\nT8cG0hz/4ulIdJj5vs5aOdnrB6j5CNaM0GXbPjMt4+1LcrL2E+RwmzQMfG5B\nCp8zRhI/LlfeSE7K1HqYdPBiiqNJ8oMSXzHrBOToCy63nf3aVjFGCCiueXuv\naICAklhT5Y50CpdMSuzCNeZe8/Q2hLbwkhIcHm4pQMeBrCkDfJZH7WHuNI6V\nu6y/xR5jRB5jJcJ7zK2/6ckxZDL+kZjT4WD6GyNmWVwhy2IBxAD/m4MWDTxh\n8kWL\r\n=NMUY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICTo6KK6WGeumwg42TDMHOf5Pmgnq07NENtBhLTalLAbAiEA/WjUGbUxuc8aq2QTY2xEAra3txTodAGDEYzn1HkzKIA="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.14_1549713159855_0.644691324085958"}, "_hasShrinkwrap": false}, "3.0.15": {"name": "formdata-polyfill", "version": "3.0.15", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0"}, "gitHead": "f6906e9a893f049f8d4573f973e4c3d75de16c5d", "_id": "formdata-polyfill@3.0.15", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nnsIjNta4jSkBhQpxeIL5u+4fasC091qS2k9+SQbQnITGmR1/PzbLI8gex+T/bJCqwHQTfh/gqfHsZi+L3p+UA==", "shasum": "77b38e7fff59181a81215ecc22ed0072f55b8ea6", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.15.tgz", "fileCount": 5, "unpackedSize": 22001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXsCWCRA9TVsSAnZWagAADnQP/3Jj9OlN9tvvo/PR+xzx\nolGLD1fZ29iFMTKlhPnZdBOTuxPBSyjT11izI1HxWI2E0Lysp3uvP0DaOYIC\n2iCe0xdcnD+fMHFpqvw+CyRQlgt33rYWxi/SDgKmWqC6amDJOpCLUKkqspD6\n0CuOYvffmpZzdpVSq+Pf+3dJiZIVkqfR0khKt+K795w+Vx7ZtoM8Xd2LcNX8\nSK7a3ZBV4iuQkcOSQcyTdu5ag/nV1aYTxtPGAF5PYV+TQSflkec6EylKW8dZ\nHnmNf2dpbSFLyU8BWsLweK4BMlZii/b1zBXWAv2vMZP9f3o1ialhbNkc9WDr\nww796lnOgVMBZZ3qp1DLuB1BXUYs8cF/2ocp2+cRHLC4/6GvDrayAErUot79\n29yQu4YL780/zhsHw77m4FNgrUBCRQvqmQAqa0CfJpYxUoDvTMv6V/a/0fzM\n7U38Sr+oTBeS2wm88IJfBYVGiHEpZc2KPNzlTcY5jdy32DRS0XEFy6n/P0mA\npgC9WV25hI33y0VHUAuBqi5MzMHVOvUbCelDhNhqkhwrdgFxj62QdlZz2ron\nDhcQnarLix6uFOUGBuhJ/VYjv5OUzaJshtTN7rFgWxfSU3digXNWmOpvwcRc\nNZICmWs1Uq2+iwZ3gqD2fcqtF0bTo9jBXgz3+62Rspw3uacJplUIrpb2TXk8\nfiAf\r\n=gmYd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgc63rOnT8bwVoYTmmy5u0tgro6/XNc6aQicDBjGyMyQIgck72s5lT6sEvmI9QniThbr0K5HlgYgmggrRLk8Smyo8="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.15_1549713558269_0.3837759140722732"}, "_hasShrinkwrap": false}, "3.0.17": {"name": "formdata-polyfill", "version": "3.0.17", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "standard ./FormData.js && karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.0.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.0.0", "standard": "^12.0.1"}, "gitHead": "f7b972f8a5bd83510ca2d773dea4ba0de403e583", "_id": "formdata-polyfill@3.0.17", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YQDARbu++HLwfbhY5a5Doo1Sk7Ib/UFct1AejQyjYyrYxVcus+Oiybest+xgSXaTnf99jNQti/hHngZwA494/Q==", "shasum": "b82c30ee8755653927ce2b5231e9ee8a66dd448e", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.17.tgz", "fileCount": 5, "unpackedSize": 22738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJce5lKCRA9TVsSAnZWagAAujwP/3bU3y1av0n6nWfmmG9e\n15cirEFSyw6X71jmou0HasZZKMGs7HUERIwjaZsAvn18pjgfXYXElOAtL8h2\nEx5MA5WZ/PP4fIxRPg7OYenj9B/n+yJbJ/5BGnjcFR6jagFHRcakqVTB8h1+\nND12dz+nddoggRknFI0iziUL5CLpmwTRU+KJ+6pfp/ufuxgvSNBgnP/2upQQ\nK5eEjrsUF6xngb/8REq6TaQDk0N6eNj9m/XTAfqeBRtpZqwBgecvAb6H0U1n\nP6I5xFhGyb76N1LOV7NxF0E2g5RKqnq7qRnXxpZr++BK823EXBn0Bd7GOEW8\nQvG8BIueYomTwmzT7hNmDy/xes15EeKlughrk83khFVlw1jHePJPDOWUThvg\nwxLbL15UdNTTO4KjgX/UEri4WLda0S3k6iiWgr3stTTDT6TaSphNFLI+aB8w\ne62cCHvkDF16bMBBRWW541A2sD5AggmquDac2ycZUcGhTgYuR5pYaM4amiw1\n+87arue2067vfIyZZKD4YuwpBE7k3eJ+ZvZHnCqtmR1NDw71aH6aAVUuUWys\n179aA/Dx6ivxvTCn05LbdTGfpBrz8LjUkodLWzUKQPHrN4/3FmLEzF8Tg7Vt\nn5w6KoH0TwXbABDk6Ca7Jt/GmEIigwy39JN3aGovdwJ77s3LLVGE6BKE89HG\nqdbq\r\n=rJrx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMJmIYYUvh7pd4m998OuOX1hm9HW7RbeZeuFp6vk2wPAIhAKqrTDHAhJIL7AcGYeBHTT6b6SbuNU2nOF9EF3s2qtwp"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.17_1551604042093_0.07406782022906566"}, "_hasShrinkwrap": false}, "3.0.18": {"name": "formdata-polyfill", "version": "3.0.18", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "standard ./FormData.js && karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.1.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^6.1.4", "standard": "^12.0.1"}, "gitHead": "f9d4b2028f36c9704d914480189379f37d7510b7", "_id": "formdata-polyfill@3.0.18", "_npmVersion": "6.5.0", "_nodeVersion": "11.7.0", "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qydEiPA/DWm1reRCfBJyFHs/wQhjvjoQjN8P/FatoaZJ+Efc/1kVwrHPR7Ek+BuIGINr26mHXT9KpR5WYmWZww==", "shasum": "e94916610328db1f6796860a0a78b2e7b937889e", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.18.tgz", "fileCount": 5, "unpackedSize": 23763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3b/dCRA9TVsSAnZWagAAOtoP/2DzYPC5x6GxV334qZ9s\n1ZfGcQabLY84xszor9fUCfgZCzHnzDZYTz4YFsd5lTBajNhXjVlqapZndSJZ\n4uCReCWaQvHB90vTdQEEnnqBFYKJReIxpSEe7+wPmBhiCbBbFti7oH0qMMac\nOxiO3wW5JeI2aC9uI4w6tHq13dGQoxH3mM2Y+mSMTcczzzWls/MVcOh429Ys\nTcBxAIJFprTCUzIIFgNHKvarzd9UHFOFMkou6NQuckpENzpu5NkAbA9JF3gZ\nBSCQ+yFcfZokFZUBu/3We+SMFHsVmqINuB6I49G8/Bhu/gPx+MpT94hOmVLH\nHJN7S23f3nT3rgzqGZ1kmSVoym3R4oRxUzgn00xb4dw2vWecTuRgfbaVL/ep\nixbOB2Lmyre9+QGgs6v+nFRYkBoi+zaka0pXkj1GRXtna7sEvU9+nOYi8ZEJ\nQrworG0VO5DVN7DlfgLM9VCDVSWvH+MhNRj5T9UGleP9U9JMTehujFFhoXVb\n2Muu2JW9EZl76CjVaRxbFnmjQV3/33DD3IrWa0VNcrICAzjTBPzdpBAb0rji\nwGgoaCmDT+xOZo4K2TdIPKQge7Avz/dwPDnk7eA4bPZHfFjPMeE41qI1Vz/C\n+jFT4tGeoorobrUdlrL4V3Zg/IOjS4EfkAPXWes/ENIAfLHVFK8XkVzpv1db\nCfjy\r\n=HuRn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNI/1kZrfZbK5uwObgLn598Zc76izRfCdgXbu8GXH9/QIhAIPwCDE70yWlb+S4R93cj5e4LyQ0yhJ6k2hv2779l1MH"}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.18_1558036444877_0.27173946783646397"}, "_hasShrinkwrap": false}, "3.0.19": {"name": "formdata-polyfill", "version": "3.0.19", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "standard ./FormData.js && karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^4.2.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.0.0", "karma-mocha": "^1.3.0", "mocha": "^6.2.0", "standard": "^13.1.0"}, "gitHead": "807557aa4213718305b39ecd9579ee3f7a670f22", "_id": "formdata-polyfill@3.0.19", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-nRSp8nniopIOCLZOUE2omwnUvmRH6VEdKm52rLTne8XBsW7hMMBUiOjuxUPoBsiK0CatKmxArh+Svt2s7R66JQ==", "shasum": "72f517db3a646a5dd8c31af0edf111fd8f1e4cee", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.19.tgz", "fileCount": 5, "unpackedSize": 23550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSUL2CRA9TVsSAnZWagAAOAcP/2VIPhAr0FZ6lg4KlgKw\nR0q5sNA8uKvvc0rnYdwi/b4BvClL5G/DqCKE+VqjpVv1Zv59e1TcKcXPfi5a\nB4l2IN0XkRx5nIk1G1Vw9mgwNq9kvMi8qmqhl5zIkMA0dRZTj1BM5ajD7CMB\ncEDzsmYYe8U0CHGzs58POs4eA9Z1krpM+iT7ofrwrYwIWMqVfgCSbx2Gyv5Y\nAH84zQNE7bYX/cB/m3kz5xEBQRIkhhKjGfGtEMh/xVkq1PhIHHMWxtB41htQ\nB/W4I4XdmKWgCipIgpvYJCmlScT3GEY3eEKZb4BE12bCmExLGFalVwSKOT69\nvKvuMWctaYaz+LeYrgPpW+JhmJBsIQKJrfIzrgFvSufWSvUFq//KQVXomVq2\nOkIbDBvDFFtALfJraIcZo876JOFv0uM9SrPJGXhozXyyf0ME/ofMcC4prr2s\nlWzCnQ62P9HPnvMQq+oqpQLMx7sRFFBp5xGwBzGpvtTIyuDsy8GtTX+9G7jw\nCdUfDFeNcbJvlRvA9wGiEIfzsrKPLFxx/DIqRGGJoCy9LfZkfd+sYJiEgDEp\n661PQ117ubD7uHYUZ1qAoG80VyBDw1+Zsiewv85QpSrM4xOusVD6gEegxeWz\neeZuPbl4LwMmeWI50k0x0EqaEddWKgvw/4Xho4mm40XonIE9JkjICMrE8y81\nBGN9\r\n=ZmpZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDErzIrmxSaRmsMqcBkWBsrwJgIogzaIpC4QNZX3vIAZAiAu5Th7j77nVkJq6QvlQ2olSEA9ca7XzEHCmGkVYS5Yig=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.19_1565082357545_0.8210880565852463"}, "_hasShrinkwrap": false}, "3.0.20": {"name": "formdata-polyfill", "version": "3.0.20", "description": "HTML5 `FormData` polyfill for Browsers.", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma": "^5.1.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "mocha": "^8.0.1"}, "gitHead": "18479550bebb79ae76c39cf23c2694619463170f", "_id": "formdata-polyfill@3.0.20", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-TAaxIEwTBdoH1TWndtUH1T0/GisUHwmOKcV5hjkR/iTatHBJSOHb563FP86Lra5nXo3iNdhK7HPwMl5Ihg71pg==", "shasum": "d6319db8efc5cf4bb2da27856c2b902be63be1c6", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-3.0.20.tgz", "fileCount": 5, "unpackedSize": 25320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6nYiCRA9TVsSAnZWagAA2s0QAIpY3H6ZTjkdddMwk4yh\n0wdgjgJNk/rOjW76gMKAkAl18UkqWjLRYkyJlY5sX46HqJkPiEhwz2sfuLA/\nWVsbnwcUtPW8TjgPz4eNN5sB8Vz2ZpUAhgEdGs3Dj9GP4e/xPMyfas7LDQY7\nyiLOzeF86+revl8aUQzkdkA7pTjLYUmjo5yaUcc13hZhZGLQt871NPhkb9Jp\nUiHhUlPqstrTRIDa2I8i+h8290UvhFqD4FUjQgAnuxs3BaRdX3G8VWUd7LHM\nK+ErWsxErKFm86orh7l2G4HGxhD4W5kEjJ5Z80Nsbgymth7kRK6gih/imSTF\n1Yj9JGnA/2DSgI75BfzOi7uPedkqHj+m3WwnfjJKtM6W+WemO1LhHxw4+4wB\nsHZuXYqOMQm/d9EGzuPXAhg2n+GocIuNxXyh52ONYlcqqWBXcBZqdqqqr+CO\n6zNgbCrZ04KJjJ1PUJ7VlxYHkxEGFiZ8oq3PqZCrN5jbq0Kom2kly7L2OIll\no93THDHZR0IPDpAbOh1sRYwXhbKvdUV1xvVayfIT5u6JoDCqJaPsIM9SFAPg\nANng1pwfJeVi4wTG4K35yRZp99Dkd2f7vnfED2rzAjh5LL8LIqQKkNZDUhR5\nQZmS0VQqrXoOV9aPFYf4E799kSEOk0ufqAfEhtNM2or5nn5P3/i6Gr/k4Xfg\nv2oD\r\n=FHEp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFSIbJUnayse2D6RxbyuSwpGjz46jfGz/9npIRoKpNJ3AiAho4/g6BilXNtxnTYhWGkOk6xwkeplL41R6ElTfJ9q5w=="}]}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_3.0.20_1592423970384_0.4095490439811864"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "formdata-polyfill", "version": "4.0.0", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "devDependencies": {"chai": "^4.2.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "mocha": "^8.0.1"}, "dependencies": {"fetch-blob": "*", "karma": "^6.3.2"}, "gitHead": "e6d62cb38a2d8676182bce8a0d26d2c9c4c19f0d", "_id": "formdata-polyfill@4.0.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-sBVj9czlZu7nOjbHDZa3IqNT/OCs5JR45G5FW4B7ZthDpcfqIl9CCFbLXYSEh/5YDIr0cZaFEBzHaGs1o2hCgA==", "shasum": "a4bad1033447c10b601224ccd24ab32fcb4d2464", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.0.tgz", "fileCount": 7, "unpackedSize": 30549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgluh1CRA9TVsSAnZWagAAarIQAJjC3roPdltYl1znHkix\ndd5ty/guWEMGI0n7JsDVmiR1XAREFF2IDOYzyaYfccVXzqMyU0XfRYA1wS3R\n4qRRs7svLXiNaUuHGhrC4GTi4EJrMv9H/C2YiAbPIOERhA9q0AFVHyicUTK7\n/GRuberbMgGVSNpAamHF2hsVqePT7y/OqHHuW6LL6JG9hAcfMPniD9wqi8wI\n34SUFsMOKnm1tJC6bwKMlRcGaAoVI1PSCSarXZANGvj1kpO+/NOpKiH8J7Ku\ncvCxYxb/3MdSkXsEuf6IGZ6xHeV5jzG4b+I7VQMuPwceJbQSit3M1B6kJJR1\n3Gkgf0o6XLdZNO6eM/Ft66NPhxIBnvg50El0KNT6oy9MNkzISngRBAv3YF13\nzXIUQnfQOnYHRbVUxixYEE0g6lRF/f7TcjYWZSF6EAdvXYxzXtVxaFJFpaB9\npwzDFr9C5I+eLzg055lGTbav6t6O4CQb5jQgjZEfZ146ZFhnyVqiV+zcmPbs\nKa4EVRbR6GnbBjNd22vK/KeomJaQp74HaQB+yol+7Yq7IYPar9PCQRoxHdt9\nJ/E+U8AbY5UfADQnzqaVBV32NpJVSpXEKg/jZLCelV0XsVzuNEV0DYovlYVR\noBzVw+I8wHknUyLVFRQQqdnZ+84uaiD0yiHNsfCLcY6EeRdyY8CascsogMuC\nwil6\r\n=DaY0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA5El/ZT2qW04OY419qSiMND147giqhSIVx+dHG1R6GgAiEAsLQtX2eXUF/XtKZRz63QFAh6EHJ0aq4EoGM1Pwu8v9Y="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.0_1620502644473_0.8887595405354125"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "formdata-polyfill", "version": "4.0.1", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "c3f91d86e094e7cb112233fcaa6cbe9bb0af64ee", "_id": "formdata-polyfill@4.0.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-zS1ntkqglv6ClvbiHGgLWdNHjZHyrvZzct3ufpbI5Y4S4f5/Ia11G6AWEcq+G6ANrPtOINhl1/4vfnqaqbnmdg==", "shasum": "4ac57214455b7c7e4e0849e1ff20f7c8c6315ff5", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.1.tgz", "fileCount": 7, "unpackedSize": 29509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi+ICRA9TVsSAnZWagAApGkP/RplSCm4M2SZKErZ+Bj5\nvsbyDaHzyt0ZP7YBTk55BI+9ZjnTv47KCcjRvjCUlM5Yd87nm0JLxfEeSczM\n4MIHxb0KqDQgxhoPV/Y7hG/UK1ltuXMkoxs37rxG8/ReDbuph28vCxOqbmsC\nlhK/AzUIdcrrzfhpN6zzK96m/rNrxj1YQQg0qQNLF5R0STpihhLby3hhCRE6\nTgJgTpOviA8evRn+V7Aq8uHACY6Zg19Rt+LxAgZXA5bmtlkxiqRFuGLORKGc\nPYiJSdYu0yCxazoh/1gqvn5pceU31QbABFlzOS11YlxtaYJhIEWIxQvIw8zn\n5oHSA3U4HX0t774MQUM/oU4eEUcK6NtrUv0YcalgPy2TCINYCYvzOvZXlGo1\nhB3cMO/ksJHyVbN88B4jzjl4GVoKShm+L4ox3omSWozIRwgUTCRH8oi5YkkM\nSYrqSKQUl1Ld9eTpyudUkuZw/67z6G1ylFtTqEcRVM7/mtYnvpXYYmYziczg\n2rrU62+NzgxSWY//hFvvGf5haa8Ie2eNZOJZyXFarA1nVoSNv46oAIDxO+Zj\n+bYTwYVtzVE6jT5pMi0hlG7ZRJ+Vpop9kzpFMW315bZrkLAXBWV+TEuDRiCZ\nDXezzor/rOBJuOYPxlEVS58rgoOgsweq7zM8lSNCgpxQihTQ4xOZiIj36ilY\nIkI2\r\n=s2mJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+Fd3xSq4OCk5GknGav/t5I/ZDN2bd56VZ76kDPQ/HNAiEA2znao8f/dV3DdFf25K4yRkpvVzOpcOJSrpdyVh2r6Bs="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.1_1622290312512_0.5809119995039427"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "formdata-polyfill", "version": "4.0.2", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "c3f91d86e094e7cb112233fcaa6cbe9bb0af64ee", "_id": "formdata-polyfill@4.0.2", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-ANCp/Co1e+aYNnEMfC7daDl202nG+UfRB76/mKpcJ6CPntxm01hlcJdyXes04Bi+o/B1kwCKPdjzFTBwYOS02w==", "shasum": "6b0d342a5aa2e2575692ce1d4d1325261c415679", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.2.tgz", "fileCount": 7, "unpackedSize": 29530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvMEDCRA9TVsSAnZWagAAC/oP/1VpFpl8lDOEWwthFpsg\nJzbZXztdU7YV48W5T32WFSCPJAzvWmWbioWN6DhkI27DWvVtbvVGKUWyTXS3\n4T9sI8bGRiCnxqw0k3h+4U/WVsasOlhqYu/xgjsSd6xsVvapbL20Yj/WG7xo\nq0eYjymdwV6w9NLP8T/ScCTf4Z/DQnDoAovIHnv6rxph2VjXc+pSjKIPxsPE\nrj75/jIfAKol+NCp06y4GRx0t8f9zaXF0I2QnpZ+nxPcs+3o246BULiHwM+d\nP2a+hehYbvePz6RWmnN1S5QzckcAdBS25bMx09xM4wVkCmkajgKjRjBZ3mAS\nJuqh8yJY8qUXplqRb3ZPJD3I0LEB1elHzNNCu1pknmkTcuJ9b3RmULXl+mHJ\nn78+Z3Ss7bvLGN0Az2BNIcqjp3Wy8lldNJWd/WtIu/y/l78H54ok90NDo74c\nCadzWXvz8wwj0yHpDEp+CXKCcaaX+NCl/n7zbsLzDKh2CXsxb7gh1bnHwXaQ\nSVoxKjkxp+LrJM6LJZyXyqHlJQtGLoF6wDJz3rUvkt3oD39QIQfrXsT4giGd\nHoxeAxfXW8tAQAMpLY1Mixcdtyy5q84DHMP1zcOExRGCVCtGb36p0rlDuXWO\niY7zNS/iub9npMmPY2n5NZMRFjo9/fW9E1uw1pHWCvlosNQ5zfiAZBFK9eaT\nM3kf\r\n=PpG3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD77ZShvj2WhoIbl6t9iCdG75ZSg/1uQWqdpyvcDAzLhQIhAMwfqtq7pF5NJlqu5KcrkMLAQ4HIOLUQNbNgUaOjDPAD"}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.2_1622982915373_0.902325491822993"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "formdata-polyfill", "version": "4.0.3", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "_prepare": "npm run build && npm run test", "_test": "karma start --single-run --browsers ChromeHeadless karma.conf.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "c3f91d86e094e7cb112233fcaa6cbe9bb0af64ee", "_id": "formdata-polyfill@4.0.3", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-blDq9bSQZVknQSGq2D8w49PucQ+/q1hVRtXlLfSRQZJ+istRXoISXNWHTzg0xPchhzbFe2VIUx0t3NGNWekExw==", "shasum": "ddb2df6110b698e70fac5c374b9dd0a7ade1d93a", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.3.tgz", "fileCount": 7, "unpackedSize": 29526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvMGnCRA9TVsSAnZWagAAM8AP/iuJiQV7ceBbE+JD+BPG\nmffCTiUwAuE/YNNV0qmuZaXKSVYxFjsrxK/xqUsf/ZoX7cL76XEBF4NOk4MG\n/CXFM/YXLLkL7PmQXTr4WIEVcJ0lraWZDksVgkNlKWQP0w57U+hyyp0SFW64\n8+V0qs3hcpaCDe8QU93TK2PA+I4e9xYkEnHnEyY/5xT2aV/uD45meRuqhnAI\n6RAGEIH0uugnnLvDaRRsnr9Ks+kwrWjsRROAt5kUMMBH7OCxpZo0nV4Mv272\nDC5WQfgFvZeVzyYJk9jm1NyJhxB300BQmOYfY1bPGdIQbgNC5dNujH1Hl7CZ\nUAU06QRSL5wXp7ekKIuy5fVqWr9r6PnQFrcH9H74+8XDjN9cvKON/ITBczMP\nm9dyqn0d7XuDWi4zEHueXhtH9Yd0LWLocRgOh1Sw8rM+JEj9Csjmx6jI+DrK\nxU6FvPHdCNu+4uL+z8KrURbtI2MjDulx2zQO3FT9k61vZDaFvDK85MLNm/kl\nOH2mnEz2sQ1lSPrENt8ifRfeLUZI1fIkKx7LTyHl/uKYDuP8j5V2o1/wAa9X\nA6HToQb9NYSpuzmMCqMesYRwvhH1t5VJpAWjHFu9x6d/BmtiVdSXmwoqX9n/\nuhmY9QhM9XTRae06YUMqlyu4EGd19WHvWzhCFtZjk5w4xWMRX26wi0+3zYgk\nsl7a\r\n=tl3X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6vZaE1ayZkjXg33DFAnD1+2TFzHVhFLj+qrArZ6SotQIgV6Kfy63QIN8CJNIdfHXY4m3fZuB7Tf5CuKpQu3sKEIs="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.3_1622983079719_0.18849432098643004"}, "_hasShrinkwrap": false}, "4.0.4": {"name": "formdata-polyfill", "version": "4.0.4", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "d26bf54b873d446900c8d5884faff4c4832247de", "_id": "formdata-polyfill@4.0.4", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-1ueXll39gwEaDJHGkcySG6kISzCOstgySzvMG8g5nEtDOulFO9d6l37BJlNloVqt7SiVR26EYe4z6hlQKvv5kg==", "shasum": "55f523da373c98834bc4ed4d6431c5bed1322453", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.4.tgz", "fileCount": 7, "unpackedSize": 29579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgy4PyCRA9TVsSAnZWagAAeXgP/A8Fh4QvgO4JOb6vUMHv\n6D/6L0B02tuKfN2lvhBcuRKFINqxnPu+1YA7YsE5WbLf0ANcAYgzItwErJfb\nX1f3X+ZfO1hKWMdEkIqxE4G6QfhDf6rCl/gxY2RhzYfi7dBEeVH3XumAnY4b\nNr0poak2CqorAEtj0rkE+4DAstAkJ1S2mbxhNIKAidARkWLQubIPLKuTi7aV\n+6S3iTkXB1eKjAULdkWmcK6RErfI+fg28RaAJtw5B8BO/3q4c5RIoCPHfteQ\nr20JRg+UWvJgqPXT4b/N3XD8ldv46BfISljvEKcFU47oPfEvROxuOZSHYLms\ngLjiRynuQsR7A9qMhpoWMYsEX+UAKOraWGIOiNXJtwh6wz5/s1hxAJn9petg\n/kWiwQdjzsFN2gzp4D6+4cgJNoOrTYeAfzag9R3Kh4OMQ2XYC98zY6ENiIWS\nMJj0TVeFwY2GgnHq7M2mqzMDZ1Fr6IGvtMAuqrXFKago2a02+knb5NmynBuk\neIgF3wrf7Tb/yg8NnGJ2QfaKxZcEkbuURS+ScFlszDUsdlDUIrXkYpNUlrID\ncg3bfD8Ayzzf5NyzyNPMK4blBwqiOTQxVR8rjx9ytSLntmxP4XQ7Ixtc5UrJ\nY2aL/FXkgpWuChcRBUBq45YIn0qGDVOkr+AbXRWYAa9zRXGBOphydmTQ8lEY\nEVTj\r\n=z+oC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcpA6s16K4ioUm0YV3yv4dEMtJsdQcl9+tVeIKdKAjxAIgMmGWkvRqB012Dh6VaJKXzYDSAp3LJ2iM/LYxJEkjHeg="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.4_1623950321953_0.34666127640591116"}, "_hasShrinkwrap": false}, "4.0.5": {"name": "formdata-polyfill", "version": "4.0.5", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "76df0e14cd5e829992bb9025beff3b47209f96a1", "_id": "formdata-polyfill@4.0.5", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-ww/gjDtWjnnpaX1eAC/nXCVn3oKrnobYy1VNWSiebstwwibgaZIZHYZa2fqPmK0IWpJhtcQLlhiSkJfzbalOZQ==", "shasum": "74d3acf53c3db58d3b008b96d20efa200c78365b", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.5.tgz", "fileCount": 7, "unpackedSize": 26328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzc7DCRA9TVsSAnZWagAA8y0P/A3SXs4Z8DTsYGNv2jvj\nFmYj8BqdIYjk3m9aNC4NtrVv4E1O92TXs/V39527sL6ou8VsvMf47MZO8RZh\nC8qOI3HTPpWWR3dCnFGFUFsYpZDXJwMK1NGMWwAQCfzMJaB2iy1TaX84D/VR\nTXsLGSYYhkcqRooDlKAYV9jfIQaJxTymN8OJqH/oTDfSYl1Ooxn27NFMq6w9\nQbLBsaunrW6uTFZ6O1XTdTI4ToVaU2Cra/BwpVG0LoxWe9GcCRxHPosoKIIb\nY8VCGoNpm2icxlRptBhA36a5gd+pedJEriDRSmVrUKN8wgSrvjjD5cDtoRw6\nZEZnsfCJ1y2QvOEs8dOIAgiawNTHEdbNCBeDW7dcYpFWDyf31wmRT6UxJWcY\n+skT0lBa7eYdP2fnrAAPeXTrLuzhqmDBCvW5xNxO+wqI6kWmcEsSHRCjpchb\nToOyk55R50KAuRoEpYn/NSwE73N0s8AIRqWOrgMqZgu9IUDwaFSzcotRRdrc\nsLtsYT6pzXEf7qsZ+wLckHFivzj6K5SqwPjYIJ3ytGcqI6Xkrvvc1QP5U1MD\nYawc9LSKaHVBcwYJ4mAB1DhyRKeE3jb1uko7vIE2BMHZ2OrkZn2DfV3Bj+2t\nso18EzudgRKvQSb1tRX6ySKCduo2mu21EcqWEeyRRZnSqHOumNfIxBM53zMz\ny5wZ\r\n=QjDk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAoCqucp74+SO/HYMI4fIz8wkaAQbvi8cdm9Pvp3KC2YAiEA/geRCY4HUdKxVkniquozdP9vYAhOgSqQ/6lTtzmRbS8="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.5_1624100547155_0.9545483312718841"}, "_hasShrinkwrap": false}, "4.0.6": {"name": "formdata-polyfill", "version": "4.0.6", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.0.0"}, "gitHead": "5ddea9e0de2fc5e246ab1b2f9d404dee0c319c02", "_id": "formdata-polyfill@4.0.6", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-oJeqWjhE6r9I4TiolW6sBYkLzupq3QZLNAYcR+b1tzh/9Uktuf4qRgJY8qqLo6an0uKckdHPpfsUYHqVf/tprA==", "shasum": "ba89818da1dca550b72da38c84b6bd2a9cf76130", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.6.tgz", "fileCount": 7, "unpackedSize": 30206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0Dn3CRA9TVsSAnZWagAAHBIP/0ZRlSRweHTFuuVKJ5jv\n+q6MI9wtLMnoEJY6Sv+ySwRCNwjbEXhxk9p/yEczWmpX5w9bFg6irUMj4Cg4\nlLh93hnMk6e5XIBKuZlfRR4afbSa+klsgbsO+RoRJOw7/lNkBubvuwtveznA\n5fV9itFBhovbkWBNtVH076+Ywnd5jyJg72G/cVapGAKkqBsbdoqSB2tsoYwq\na0hWtZFGjeGmm8/iq8JYUfywvFZ/+M4QpVYGx29zUdiYvijnfrcYZlfMyBUp\nUCOyPeFQlb7E00JEWzE6mnSpznf7rUUCpAI9Q9T1UPqkRrZl16RkFppPVxP0\nkn8PJxdeaiLSWCJvWnHZAEqm1t3TSvR+tGIRblvUF5TP+Ak4ViOa/8/WhqlK\ngSyzRe/IHBVgwNBvBR0La3ZtvIHzglTu/AKZrwPlg5ox/pcQe2BRKRsUm5uY\nmuUqT03oxPyK42CgZFRzDjUs1P817g8dmsPUukDMxlF0Ucq3G4d3ZzuyV0eX\njqirf+D22y9RJwI4Oo/lAULxfGujm+hOrtxFs1Uy20A2j5W7LYNDM2iBtJeo\ntmDDlpT0p9hcYdhflSMBh65zTcc3gH4BWqvE8EECT7vWk5i3bfDeSBi/J9Ks\nzXaJdG0umD38bkx83yIqbrDzpthOeZkyXS4eANR6XYZlxdZYcPM/m5yce4Kd\nIVYA\r\n=hw0A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+6mpVDi+aKU4QfV3OzYflK1GL/exIo017swlMTdHp7AiAN8MILGhKmLssBopJXmmIye5+lKHoaJHjpi9a3luoE6g=="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.6_1624259062546_0.8166713323417698"}, "_hasShrinkwrap": false}, "4.0.7": {"name": "formdata-polyfill", "version": "4.0.7", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "prepare": "npm run build && npm run test", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "978ee337826c82b3fad777810f3366a3d813cdd9", "_id": "formdata-polyfill@4.0.7", "_nodeVersion": "16.8.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-pTY5vaVDcdiEyFQfTkRoCPAtv1I9yLqsf8TICX3vqQmpywZaGfjIf9tW86yt3iTcbE5oEhhUNJQa/Nn7Uv/i2w==", "shasum": "83edbac83e66dcb4ce54be53c884999c2eefefba", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.7.tgz", "fileCount": 8, "unpackedSize": 32204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNqI+CRA9TVsSAnZWagAAFCgQAJXe8HEFYloKz75ZMnWD\ngGpZ5dFfeN+JCKw2vX8eF6HL25B8PKqoqA7/MWBOpaqHpDraKPYlUQQ92jLF\nzSTaz8eL+vl6sE8F4hoY8krIldd5jp6JwYROYx2l5XNLO6Ai5o2EGX0IU+GZ\nWp10LxK+De7cXNRl0jisfUbUVFnnYzgwStpI5YTXvQjf1LP6ovZRfyVIuB6M\nu84Gp9mevGyoB5/naOyl+7Xufzz/LqaOXo3Bu9KHLGl9IHBDSjVkZR1yEst+\ndJVibngoEguX7jnQyFPp3ZHi2g1wcXx9l2MC5jBZC8juOavP5mU53mQfDIm7\n5DAwvNFXVaS1bon6ScAXrIKXOHxG4h+4Yv+Qxg0+6S0fWBTVtnD1RIXlq06r\nf5cJ8v2qShs0M2jo2NxOxds3d13JWNUqlmATx295qCMNnh5W93vj2tUIooOU\nlw5B6oUd8TLHd1OlARiKnnsCuCkzaV+wJs24Z6GEfVh8Cd22R23VObxIVELS\nOzHxRTR9SiraHAgkJpWJ65wTVDlx/BkLQm1iQTM7KJdtVPhsw8TgxcXo1pxE\n5ElrSOxi0JTod1iC9z4IfZnA3eQP/RLsLpOAsulLYp26ManjbEfyw5h9QvuY\n76+I1W/zg6/ddiPFyeikNQdeu0dwJijarq4QwKJyy4v5pjG5fr4HWJNq523Q\nyNbI\r\n=3ML+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEzPrMroKNxTb5Z7STPxBaN5YORTHlbOh9/2Fv9o9UWfAiEA/d5tw0tx+0EGnFGk5TDbAzDZqKV3M1/ivn3DEf7brew="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.7_1630970430571_0.45618183455738515"}, "_hasShrinkwrap": false}, "4.0.8": {"name": "formdata-polyfill", "version": "4.0.8", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "04ed0f98e3228c9d51c44fb2bc7ea20d26a24e92", "_id": "formdata-polyfill@4.0.8", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-p0naEVqvCUELWr294iIyMwXH3mhlsg2AhTrFEAWbJx1i8FOrHuaoQQKEvQsK08oF+77KxFwuRVm5ltOY1Ac1rg==", "shasum": "ce9fde6430914f798ede294a037c631176160e5a", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.8.tgz", "fileCount": 8, "unpackedSize": 32209, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFjVqW9JfMa8tvhosS59Lx5dJksAyh6DycEe0ZpFd2EAiBQWkmCT+/DB27f8eHGCYO2/Zhhnmex+Qq3kpvTO2usGg=="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.8_1632661814466_0.21954346950435366"}, "_hasShrinkwrap": false}, "4.0.9": {"name": "formdata-polyfill", "version": "4.0.9", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "36e2491b3354050e8e7ba50423fae5dc46ced039", "_id": "formdata-polyfill@4.0.9", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-SuhZ/i+uopeFEiLXOuaP9f3Jdy+fxzhplh4nI5g1io9IUl6CaNl/PXXKb3gqDYvr+cxZTBzxpqc2zP7oDZ+NjA==", "shasum": "0609d8e6a5b5ba19d3eb8102c48be228aa1c8cdd", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.9.tgz", "fileCount": 8, "unpackedSize": 32216, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8/Ujg3M9EabrjvGOuAyPWtGppRha0MRsXZWG9wfunvwIhANGr5kaX9U/F50kJ1FYlhiYrBN5OCcibql7naB2hLAzH"}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.9_1632952106419_0.23415920996230066"}, "_hasShrinkwrap": false}, "4.0.10": {"name": "formdata-polyfill", "version": "4.0.10", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}, "gitHead": "fbb503396bfda85e255a2adb3711c44ff1544c7b", "_id": "formdata-polyfill@4.0.10", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.1", "dist": {"integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "shasum": "24807c31c9d402e002ab3d8c720144ceb8848423", "tarball": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "fileCount": 8, "unpackedSize": 32220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2rZQCRA9TVsSAnZWagAAsEMP/0SHmS+uIffTBG3swZKz\nTAzNFqlkPKaQKjaXMdxvqgz4wnw/NyIz1lpxdEpQGmf9W1EPlFU2UpZyquZ5\n3gSxw1+8ZnAtZqud0BErggX9HErW2r2I2SeVeJq/qnyHAxd91RAbC0YGxE0L\nqeo/h4UUliDCfZPi7YfZRdMUSHNbjh8IYPA+CGi9EgTcgG2jYP1W0VbADxII\nSSEkuHSR2avhpMCwaGGwcF/68qHtJLCHL7KRgHBJWrZ009MTvPK9E7Tqj1hj\nIViT7UC3ISe7+SqAryXTHqi4YD9QUdd2+Kg0s8BKcBa90oS00MvEvo6PqJau\nhVnYQuVNM2tVxPmWINYYYPyEyduVuuuVrEDG+2g6i7RK1DQLV2MD/gyaXUVO\nhM52gKpsZBwaEM9Jr3gCHZgdD8XFOdau3Rc59XSiFCrrQo7O7E+ICcaG8bhr\noMNVoSkCF3EECB4fNjw6gxfR3nBDXzqWFwPlIow7AGZ+pPuXe8+pvM39hxB8\nBeF1BScTNztxIPPhN4moH2VSEHYxabSF0fJMjgixz8EOhUmN/i3Mn5aQ4EL0\nHiz4fUJEBiFDIndzaq8ouvXf6uzmc2CkOmGtvIHKtONm7CmBgCCuJEV8xhQv\nvKKDStPfJhGr3j3yY4uKBqFKwdn8ZO4vH17Dqxy01W+8SK7zM/hHt8WWenUs\n7J1i\r\n=hDQV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDK0VNOPS/guY3VYhrySPz2A25Xh+s/tuBbs3BD3fcSRwIhAPO+bi/G6y8QqMd2/RJNIKznm2wxCOJQWtmrN6ueh8Vy"}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/formdata-polyfill_4.0.10_1633007278127_0.641382215737518"}, "_hasShrinkwrap": false}}, "homepage": "https://github.com/jimmywarting/FormData#readme", "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"isik": true}}