0 verbose cli C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\bin\npm-cli.js
1 info using npm@11.5.1
2 info using node@v22.17.1
3 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\npmrc
4 silly config load:file:D:\Github\Kontext\source\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm run news-v2
8 verbose argv "run" "news-v2"
9 verbose logfile logs-max:10 dir:D:\Github\Kontext\source\.cache\_logs\2025-07-30T14_23_59_505Z-
10 verbose logfile D:\Github\Kontext\source\.cache\_logs\2025-07-30T14_23_59_505Z-debug-0.log
11 silly logfile done cleaning log files
12 verbose cwd D:\Github\Kontext\source
13 verbose os Windows_NT 10.0.19045
14 verbose node v22.17.1
15 verbose npm  v11.5.1
16 verbose exit 0
17 info ok
