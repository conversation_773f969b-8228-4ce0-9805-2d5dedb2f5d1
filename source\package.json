{"name": "news", "main": "index.js", "type": "commonjs", "license": "UNLICENSED", "scripts": {"news": "tsx src/news.ts", "blog": "tsx src/blog.ts", "start": "tsx src/news.ts && tsx src/blog.ts", "clean": "npx rimraf .cache node_modules output.json"}, "devDependencies": {"@types/node": "^24.1.0", "prettier": "3.6.2", "tsx": "4.20.3", "typescript": "^5.8.3"}, "dependencies": {"@google/generative-ai": "^0.18.0", "@types/puppeteer": "5.4.7", "dotenv": "17.2.1", "node-fetch": "^3.3.2", "ora": "8.2.0", "puppeteer": "24.15.0", "rss-parser": "3.13.0"}}