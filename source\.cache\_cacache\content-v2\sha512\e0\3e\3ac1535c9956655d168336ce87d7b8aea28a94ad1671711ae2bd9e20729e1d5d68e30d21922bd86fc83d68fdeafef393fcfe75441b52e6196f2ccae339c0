{"_id": "fd-slicer", "_rev": "44-7eee9a6b111801a98e29ef69912438bb", "name": "fd-slicer", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "dist-tags": {"latest": "1.1.0"}, "versions": {"0.0.1": {"name": "fd-slicer", "version": "0.0.1", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.3", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.2"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.0.1", "dist": {"shasum": "3909d198ce9d9da5de39d986b03fa7253c976216", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.0.1.tgz", "integrity": "sha512-8Fm5ekRMt+F5flHDKGCNMP0U6Sib+OvJcAt+hyqDE5LpEKAmmT2Z9cvdYcWVrjyUA9UuMSQCtVek8BemF42GWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAL8V5w5Amwj7yZDWl8rbJri2tD06n0S1v0nyD0IuAtsAiEA/kBTTC40Tkm6T+4EbPCNv5MZ3o8bxdoL0CQGUzY2Qbc="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}]}, "0.0.2": {"name": "fd-slicer", "version": "0.0.2", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.3", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.2"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.0.2", "dist": {"shasum": "3c7dcbe0ce794a182de51cd0e88676e46e692bd6", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.0.2.tgz", "integrity": "sha512-vE8NImgWB6Kx63kRzawOFqWtUHAbLTxnevoC7olV+N9FiKBLH2JWtw+5T41Bm4HhLifBGqbcSkbwqKwbJp/LtA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQ0+ILyfkaLXXU6zN4JVaI4ju6519ycYf5YdrdKuBKHgIgefN8FQw4U5/LGprJ2Xj+ga76233hwfGtAEdWgSOnARY="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}]}, "0.1.0": {"name": "fd-slicer", "version": "0.1.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.3", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.2"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.1.0", "dist": {"shasum": "f597141dfe8a2841756fd54150a78fe0bec4bc03", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.1.0.tgz", "integrity": "sha512-KQeACM2iZaDECIKvocNr5r1UAAURMlgSJkT5noPfhmWNvlHr0DEOweJsAHX1/Sn66JKs1VhnKjiXsD2rgzWneg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBKuRHY02JB6xfIFNy9mVg44c34k5BESf6QuTn4ObNSEAiBIUUAqRHNmvHUEM0I6SyKR3LM3u3qDd6clsqaTom5atw=="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}]}, "0.2.0": {"name": "fd-slicer", "version": "0.2.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.3", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.2"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.2.0", "dist": {"shasum": "e07f141724e649c38709205dd1dc394020c75160", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.2.0.tgz", "integrity": "sha512-Hx6g6ZKN7gc7ZC2l+VLn4iiR9lcYaBZ6faAd6XN8E0BaEhhd1HSdX3F888mdvDS1SykuK20fErfW6BzoI9q8XA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtRRlOzz5dnVKHl2EtwsIHSthIkJISlKe+3fPgB7l2hgIhAN+217n/MZvWof2UqFEmNt84YhAs5ejXgj/vq4sg7pUl"}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}]}, "0.2.1": {"name": "fd-slicer", "version": "0.2.1", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.3", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.2.1", "dist": {"shasum": "4824cbd31ee247e3a2e41f76808196f2d3322018", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.2.1.tgz", "integrity": "sha512-2FHAqwR7yPdHmLYNNn5+RsyWiBud2fRcqSuZ4V0XjlGOwmt+eHjnRVEWVnBP1gKZEV6AoWm3Y4cMJ4ooEZ6rxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDIYWBuus9n71+mcLHDSfTXRM1itVzIb4CmEgjF/LvDfAiAMZ0JjaUcMqzOuv62U2jLIHUUFup52HS73wN0eOIKxGg=="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}]}, "0.3.0": {"name": "fd-slicer", "version": "0.3.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.5", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.3.0", "dist": {"shasum": "a271ae1b570365f6292522bde82081df7c4b4760", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.3.0.tgz", "integrity": "sha512-RbNaKWapGmPqbxIzEAp9mvApFGkSOd/X9BjCSXlFNMfYrcy0RjkA39t8hV7iSIGlUwf41lM9Hz7nTYYKfovBBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIByECgQOrKPKFfGcxe3Mv+jCRO3VdGZlqCPXqk3nEmtTAiBty60iPAFZ36gQWolLTDcVV4a2Mxze7b4+BKUIgiRqkg=="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}]}, "0.3.1": {"name": "fd-slicer", "version": "0.3.1", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.5", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.3.1", "dist": {"shasum": "0a142ef4e1459dfecaa1c982d738fa288b198c8a", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.3.1.tgz", "integrity": "sha512-IWqbHqfRRcovR5+XRj2CtUQGJcDg63rnvdK2u0ZWSkd/BfyBXnRN31zJ9W3kvPk4LhQWnuoc3NSSd0+ZnDQPGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDj2UI8P6ABaJtKjO2Y8bwGXSuOENupEMCGf3TD4gWYjAiEA0VGj2Bf4AcP7KjKzaRestgeUelKHorm78BRmooQI9hQ="}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}]}, "0.3.2": {"name": "fd-slicer", "version": "0.3.2", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.5", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "_id": "fd-slicer@0.3.2", "dist": {"shasum": "b8869ab02c8886af4c2bb6b15be70321186aaecd", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.3.2.tgz", "integrity": "sha512-fN/6PkmaGpXLhlQRhLsSSCZzpABAHrVMPhV5635qFuAjV5zaL+LEU7TZsRuFHUehEAFNQ9ygvHKZ0fziHTKbvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzZaDlWxfVlMTVKVzlapnl0BEJ5WAduK1HQrAqaJpv6AIhANrz/WuOqQrUjZgS6Tvp/n0B2hBEjZOAixAslJWpJrJU"}]}, "_from": ".", "_npmVersion": "1.3.10", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}]}, "0.4.0": {"name": "fd-slicer", "version": "0.4.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"mocha": "~1.21.5", "stream-equal": "~0.1.5"}, "dependencies": {"pend": "~1.1.3"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "gitHead": "446631c67dfc2452e92036c371b50d6996def80b", "_id": "fd-slicer@0.4.0", "_shasum": "4ac30025e3f6b02e046b92c2c18bfe312f1502b6", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4ac30025e3f6b02e046b92c2c18bfe312f1502b6", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-0.4.0.tgz", "integrity": "sha512-CtCbAD0eSga51Dfi92b0+mUACZFmStmYbamnElewe3TSXq8JozKGwr1Ep1k+w9iZOx0BPAJ3qDSCqBXu3nM96A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICayQEVgf98ywYyKer3r/9bqPpxdOkY0ipfBVW3YThbeAiEA/SZVc9XPPseKqjVwOhEG4sp2XjFLiDkw8KsL+tHCfWc="}]}}, "1.0.0": {"name": "fd-slicer", "version": "1.0.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec --check-leaks", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --timeout 10000 --reporter spec --check-leaks test/test.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"istanbul": "~0.3.3", "mocha": "~2.0.1", "stream-equal": "~0.1.5", "streamsink": "~1.2.0"}, "dependencies": {"pend": "~1.2.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "keywords": ["createReadStream", "createWriteStream"], "gitHead": "489539f063b169842245bcb698b3520ecf59feec", "_id": "fd-slicer@1.0.0", "_shasum": "4e067dd423fc0a014678f2b1236fd4493958cc9b", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4e067dd423fc0a014678f2b1236fd4493958cc9b", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.0.tgz", "integrity": "sha512-aQqDQUsL/dAIAwDeIrnz3E+ZOueV34ZKWnFUX7LElcq90ug8s0EZ60mHntcoPdBIJyugELA6mMqGXT3X0aMOQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7Mmrd3jsqfjgILRpl0UVa29WNc30FlSRtSH+RETyU/gIgDORD1mMZY2k0k81Ffq6XY/KP3hUX6OAphZq7UG/OHzk="}]}}, "1.0.1": {"name": "fd-slicer", "version": "1.0.1", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec --check-leaks", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --timeout 10000 --reporter spec --check-leaks test/test.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"istanbul": "~0.3.3", "mocha": "~2.0.1", "stream-equal": "~0.1.5", "streamsink": "~1.2.0"}, "dependencies": {"pend": "~1.2.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "keywords": ["createReadStream", "createWriteStream"], "gitHead": "b7a28cb5bda986748ad756c39961f46c2fd28ec6", "_id": "fd-slicer@1.0.1", "_shasum": "8b5bcbd9ec327c5041bf9ab023fd6750f1177e65", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8b5bcbd9ec327c5041bf9ab023fd6750f1177e65", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz", "integrity": "sha512-MX1ZLPIuKED51hrI4++K+1B0VX87Cs4EkybD2q12Ysuf5p4vkmHqMvQJRlDwROqFr4D2Pzyit5wGQxf30grIcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUk3u8yOWHJKIhftUSMJFXPdODl+DZ3myEt++r9GP8FQIhALUS4ewGPl/OMqndFBqnGgN9h/ieZfCyTWmHFCYPvMtm"}]}}, "1.1.0": {"name": "fd-slicer", "version": "1.1.0", "description": "safely create multiple ReadStream or WriteStream objects from the same file descriptor", "main": "index.js", "scripts": {"test": "mocha --reporter spec --check-leaks", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/test.js", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --timeout 10000 --reporter spec --check-leaks test/test.js"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"istanbul": "~0.3.3", "mocha": "~2.0.1", "stream-equal": "~0.1.5", "streamsink": "~1.2.0"}, "dependencies": {"pend": "~1.2.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "keywords": ["createReadStream", "createWriteStream"], "gitHead": "2583a9eaebe86ce5d9a70da0a02529e2f1719c47", "homepage": "https://github.com/andrewrk/node-fd-slicer#readme", "_id": "fd-slicer@1.1.0", "_shasum": "25c7c89cb1f9077f8891bbe61d8f390eae256f1e", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "8.10.0", "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "25c7c89cb1f9077f8891bbe61d8f390eae256f1e", "tarball": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz", "fileCount": 8, "unpackedSize": 29813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFHmXCRA9TVsSAnZWagAA/hEQAJ2bmvTYImK9LLqJ9M8S\nAWfPQ7ku8pZHZzgDQGdX6JthN+Xc+ewMA05BVF3SNd1iqg+YtsQvbDyCvVzL\nsVpzti9zrtrfmaANmTVbhLPYqZDKrcaMyd3ZXHIY3lFLWjGFPtJQX3xo9Elc\n5uFHu2UMyqxE8gEkIdw0adIOrMy7bJRIrOtRZXe4TvR1yT7gLCO7hljxZCTy\njnBnWwvD8FZPGDQHbruAP79aN7XyZbEFdUS8RVA2WssNrJiibgYNKzC3e4Vn\n19B8avPj30hFRWfyZfcE3gfA2Ys0iMYmaztAAhb8OcpNMZntqgU1Uacc4MZk\nTFzZ3l53LsiFuPdhkLVh0IcO8CKBnjIV1+u2PSoyl+MGkqkPBiDakVm4n16M\npaLRa4lYXYNKYIa3jPwQa39opeeXotVQbWCHb515cK1YS22kGVrNQ3IpPx/5\nKfF6Tv2YA9Lc3oApemhFO7megt1nI+M215lWWOf2bhCeFzlk0l5A7mqyQM7O\n2FemAc+t+OP3LiDwBZOTG5Zxjbd3vVRnyLsYZ9CU5hKsIQQxn4HTJLBuvYy3\nEm0yutoHhvDGKoDCDE+khjokdpbRP9FNSoaTeXO/zm5PtuGVBtywkDa6EQ2g\nZSITEJsDevpLTI33bYhkeciFqEYSLWh3tjvIEV9immHyZF0z8Ldv1SjkgzRq\no4dR\r\n=lCaP\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEs/HydM1vBon+L9Ug3SiKrg8iuhCZP0XLtLqGcgmgtQIgAWc6ICW4U5iWV9f9XZxRoml3qFbh2vy/wzk5WuS3xko="}]}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fd-slicer_1.1.0_1528068502618_0.6330915406709261"}, "_hasShrinkwrap": false}}, "readme": "# fd-slicer\n\n[![Build Status](https://travis-ci.org/andrewrk/node-fd-slicer.svg?branch=master)](https://travis-ci.org/andrewrk/node-fd-slicer)\n\nSafe `fs.ReadStream` and `fs.WriteStream` using the same fd.\n\nLet's say that you want to perform a parallel upload of a file to a remote\nserver. To do this, we want to create multiple read streams. The first thing\nyou might think of is to use the `{start: 0, end: 0}` API of\n`fs.createReadStream`. This gives you two choices:\n\n 0. Use the same file descriptor for all `fs.ReadStream` objects.\n 0. Open the file multiple times, resulting in a separate file descriptor\n    for each read stream.\n\nNeither of these are acceptable options. The first one is a severe bug,\nbecause the API docs for `fs.write` state:\n\n> Note that it is unsafe to use `fs.write` multiple times on the same file\n> without waiting for the callback. For this scenario, `fs.createWriteStream`\n> is strongly recommended.\n\n`fs.createWriteStream` will solve the problem if you only create one of them\nfor the file descriptor, but it will exhibit this unsafety if you create\nmultiple write streams per file descriptor.\n\nThe second option suffers from a race condition. For each additional time the\nfile is opened after the first, it is possible that the file is modified. So\nin our parallel uploading example, we might upload a corrupt file that never\nexisted on the client's computer.\n\nThis module solves this problem by providing `createReadStream` and\n`createWriteStream` that operate on a shared file descriptor and provides\nthe convenient stream API while still allowing slicing and dicing.\n\nThis module also gives you some additional power that the builtin\n`fs.createWriteStream` do not give you. These features are:\n\n * Emitting a 'progress' event on write.\n * Ability to set a maximum size and emit an error if this size is exceeded.\n * Ability to create an `FdSlicer` instance from a `Buffer`. This enables you\n   to provide API for handling files as well as buffers using the same API.\n\n## Usage\n\n```js\nvar fdSlicer = require('fd-slicer');\nvar fs = require('fs');\n\nfs.open(\"file.txt\", 'r', function(err, fd) {\n  if (err) throw err;\n  var slicer = fdSlicer.createFromFd(fd);\n  var firstPart = slicer.createReadStream({start: 0, end: 100});\n  var secondPart = slicer.createReadStream({start: 100});\n  var firstOut = fs.createWriteStream(\"first.txt\");\n  var secondOut = fs.createWriteStream(\"second.txt\");\n  firstPart.pipe(firstOut);\n  secondPart.pipe(secondOut);\n});\n```\n\nYou can also create from a buffer:\n\n```js\nvar fdSlicer = require('fd-slicer');\nvar slicer = FdSlicer.createFromBuffer(someBuffer);\nvar firstPart = slicer.createReadStream({start: 0, end: 100});\nvar secondPart = slicer.createReadStream({start: 100});\nvar firstOut = fs.createWriteStream(\"first.txt\");\nvar secondOut = fs.createWriteStream(\"second.txt\");\nfirstPart.pipe(firstOut);\nsecondPart.pipe(secondOut);\n```\n\n## API Documentation\n\n### fdSlicer.createFromFd(fd, [options])\n\n```js\nvar fdSlicer = require('fd-slicer');\nfs.open(\"file.txt\", 'r', function(err, fd) {\n  if (err) throw err;\n  var slicer = fdSlicer.createFromFd(fd);\n  // ...\n});\n```\n\nMake sure `fd` is a properly initialized file descriptor. If you want to\nuse `createReadStream` make sure you open it for reading and if you want\nto use `createWriteStream` make sure you open it for writing.\n\n`options` is an optional object which can contain:\n\n * `autoClose` - if set to `true`, the file descriptor will be automatically\n   closed once the last stream that references it is closed. Defaults to\n   `false`. `ref()` and `unref()` can be used to increase or decrease the\n   reference count, respectively.\n\n### fdSlicer.createFromBuffer(buffer, [options])\n\n```js\nvar fdSlicer = require('fd-slicer');\nvar slicer = fdSlicer.createFromBuffer(someBuffer);\n// ...\n```\n\n`options` is an optional object which can contain:\n\n * `maxChunkSize` - A `Number` of bytes. see `createReadStream()`.\n   If falsey, defaults to unlimited.\n\n#### Properties\n\n##### fd\n\nThe file descriptor passed in. `undefined` if created from a buffer.\n\n#### Methods\n\n##### createReadStream(options)\n\nAvailable `options`:\n\n * `start` - Number. The offset into the file to start reading from. Defaults\n   to 0.\n * `end` - Number. Exclusive upper bound offset into the file to stop reading\n   from.\n * `highWaterMark` - Number. The maximum number of bytes to store in the\n   internal buffer before ceasing to read from the underlying resource.\n   Defaults to 16 KB.\n * `encoding` - String. If specified, then buffers will be decoded to strings\n   using the specified encoding. Defaults to `null`.\n\nThe ReadableStream that this returns has these additional methods:\n\n * `destroy(err)` - stop streaming. `err` is optional and is the error that\n   will be emitted in order to cause the streaming to stop. Defaults to\n   `new Error(\"stream destroyed\")`.\n\nIf `maxChunkSize` was specified (see `createFromBuffer()`), the read stream\nwill provide chunks of at most that size. Normally, the read stream provides\nthe entire range requested in a single chunk, but this can cause performance\nproblems in some circumstances.\nSee [thejoshwolfe/yauzl#87](https://github.com/thejoshwolfe/yauzl/issues/87).\n\n##### createWriteStream(options)\n\nAvailable `options`:\n\n * `start` - Number. The offset into the file to start writing to. Defaults to\n   0.\n * `end` - Number. Exclusive upper bound offset into the file. If this offset\n   is reached, the write stream will emit an 'error' event and stop functioning.\n   In this situation, `err.code === 'ETOOBIG'`. Defaults to `Infinity`.\n * `highWaterMark` - Number. Buffer level when `write()` starts returning\n   false. Defaults to 16KB.\n * `decodeStrings` - Boolean. Whether or not to decode strings into Buffers\n   before passing them to` _write()`. Defaults to `true`.\n\nThe WritableStream that this returns has these additional methods:\n\n * `destroy()` - stop streaming\n\nAnd these additional properties:\n\n * `bytesWritten` - number of bytes written to the stream\n\nAnd these additional events:\n\n * 'progress' - emitted when `bytesWritten` changes.\n\n##### read(buffer, offset, length, position, callback)\n\nEquivalent to `fs.read`, but with concurrency protection.\n`callback` must be defined.\n\n##### write(buffer, offset, length, position, callback)\n\nEquivalent to `fs.write`, but with concurrency protection.\n`callback` must be defined.\n\n##### ref()\n\nIncrease the `autoClose` reference count by 1.\n\n##### unref()\n\nDecrease the `autoClose` reference count by 1.\n\n#### Events\n\n##### 'error'\n\nEmitted if `fs.close` returns an error when auto closing.\n\n##### 'close'\n\nEmitted when fd-slicer closes the file descriptor due to `autoClose`. Never\nemitted if created from a buffer.\n", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-07-12T19:10:34.446Z", "created": "2014-08-05T17:50:11.296Z", "0.0.1": "2014-08-05T17:50:12.276Z", "0.0.2": "2014-08-05T18:22:02.275Z", "0.1.0": "2014-08-08T21:50:53.175Z", "0.2.0": "2014-08-20T08:21:41.364Z", "0.2.1": "2014-08-22T04:27:21.489Z", "0.3.0": "2014-10-14T03:04:33.984Z", "0.3.1": "2014-10-14T03:32:23.647Z", "0.3.2": "2014-10-14T04:49:37.239Z", "0.4.0": "2014-11-03T22:40:02.637Z", "1.0.0": "2014-12-03T21:45:37.486Z", "1.0.1": "2015-01-12T16:34:28.804Z", "1.1.0": "2018-06-03T23:28:22.683Z"}, "readmeFilename": "README.md", "repository": {"type": "git", "url": "git://github.com/andrewrk/node-fd-slicer.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/andrewrk/node-fd-slicer/issues"}, "license": "MIT", "keywords": ["createReadStream", "createWriteStream"], "users": {"temasm": true, "flumpus-dev": true}, "homepage": "https://github.com/andrewrk/node-fd-slicer#readme"}