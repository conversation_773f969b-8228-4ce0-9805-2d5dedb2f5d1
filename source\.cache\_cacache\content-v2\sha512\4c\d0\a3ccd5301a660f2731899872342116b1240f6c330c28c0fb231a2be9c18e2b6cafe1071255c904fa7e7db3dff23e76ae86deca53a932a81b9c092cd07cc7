{"_id": "socks", "_rev": "80-859b43b3b6bf570f4699b643c0f1c3d7", "name": "socks", "dist-tags": {"latest": "2.8.6"}, "versions": {"0.0.1": {"name": "socks", "version": "0.0.1", "keywords": ["socks", "client"], "author": {"name": "byhgj"}, "license": "BSD", "_id": "socks@0.0.1", "maintainers": [{"name": "byhgj", "email": "<EMAIL>"}], "dist": {"shasum": "f1c0c667e1734df1642f585796152d1820f31b09", "tarball": "https://registry.npmjs.org/socks/-/socks-0.0.1.tgz", "integrity": "sha512-o2p6ZAl6AGJHwsxSNRkz5O3Yzv5UvaYXnpYSeHD8rQyeGIfVfEjmJSVf7gVeGcR0tzWs2AjZbZ/684ENTRm9KQ==", "signatures": [{"sig": "MEQCIFMkE4uTbWK4BOjqvSJAUTaLRnvRLioqIwX+HewthK74AiBJan5uz3QoMEYi1mlz7k+Epe5IpNnxqaSymaoQlkSNsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks.js", "_from": "socks", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "byhgj", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/byhgj/socks.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "socks client, support socks version 5", "directories": {}}, "1.0.0": {"name": "socks", "version": "1.0.0", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "5294bb4bb8acb10f969af6361c8afdec38a3564b", "tarball": "https://registry.npmjs.org/socks/-/socks-1.0.0.tgz", "integrity": "sha512-WKibOVb9hB+U2Ydu7/aYk0xhNLeUcoqt7bOwGgID93Eu8b5XJbyGiK3RneYU0SWNMSOr7CCL9uCEDWb/E6MhOA==", "signatures": [{"sig": "MEUCICHSnSYZ8wtXFOQwVE2kaQd1CKNwRJef/hG7wVPlb1HNAiEAunXGzbqJKSFstnDUPtmBrkjiqYcgTAvJYqZddMuDXoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5294bb4bb8acb10f969af6361c8afdec38a3564b", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "5ce5208f50971391ba10b6cdd4fd20597c0d9f48", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "dependencies": {"ip": "^0.3.2", "smart-buffer": "^1.0.1"}}, "1.1.5": {"name": "socks", "version": "1.1.5", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.1.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "90e5d70cc90763895da36870d7ff97a350636d82", "tarball": "https://registry.npmjs.org/socks/-/socks-1.1.5.tgz", "integrity": "sha512-Uh1qa2cgHPEBSONmkAaPAbIABaZrTweUc+TQZ4tlWgZmgkEBROy1VIobJ0ug+Q+1l9RgL1JlfxAevYQgD8aZfQ==", "signatures": [{"sig": "MEUCIQD8MGenBfXb2nAVvPULbZX01nHE1IWzIZhG2Do0nxZq7wIgMk9byphjF7hLMT9zqYxQcixaMyy8MVeCyGfhy+pzYB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "90e5d70cc90763895da36870d7ff97a350636d82", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "dc779765d5ae1955338776ec1331d7759f50877f", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "dependencies": {"ip": "^0.3.2", "smart-buffer": "^1.0.1"}}, "1.1.6": {"name": "socks", "version": "1.1.6", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.1.6", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "60cb6624427bb4841c970b7edb65eed50cfb7ec3", "tarball": "https://registry.npmjs.org/socks/-/socks-1.1.6.tgz", "integrity": "sha512-KFQs4fMisd52MfX37pL4tRinB2cJWaRExaOdKle0pIbZalJbuLTFpE1qoU5NYGBLuEAPAXYaMP1c5Y4iF8n1Vw==", "signatures": [{"sig": "MEYCIQD+IQcmh0AM7Q2snHCQ5VMSGGAyZVUJzx38R9GJ9nHxfwIhAKiwC4oCcqFryVSMmlDs+SmqzzhPwIdR0fZTTFxOgusZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "60cb6624427bb4841c970b7edb65eed50cfb7ec3", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "e5a6a774c60100b0e9f1fb0b43fe76b0aaf39d25", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "dependencies": {"ip": "^0.3.2", "smart-buffer": "^1.0.1"}}, "1.1.7": {"name": "socks", "version": "1.1.7", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.1.7", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "23176f05dcd2ace75485373cca71f28838b5f7d5", "tarball": "https://registry.npmjs.org/socks/-/socks-1.1.7.tgz", "integrity": "sha512-Em+90e1+enhKdBNFFvgrUiQGtUVl+t7IlO2PA6mCKax3NfEImOjPCdArVm5yAWOGIbmWsLC1MRU5DmNLFRfC5A==", "signatures": [{"sig": "MEYCIQDH/V6mbVn3p/LtAASVPfGSEav8WgLrTYg9w5YwRxD0hAIhAJEG/mDVHvUEewxo4FJq1B30cBuhLWlmn8pxSjmQePJQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "23176f05dcd2ace75485373cca71f28838b5f7d5", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "b06b7c43a3596e4715f117ae0953c2bc7b72a202", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "dependencies": {"ip": "^0.3.2", "smart-buffer": "^1.0.1"}}, "1.1.8": {"name": "socks", "version": "1.1.8", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.1.8", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "dd731a23ea237680293b09a07b085a271b558d4b", "tarball": "https://registry.npmjs.org/socks/-/socks-1.1.8.tgz", "integrity": "sha512-r68WJrUgWEmxiTCkYvHNdJUebNBs+U7Djq6kKbWcYmmT8qubSpt3/f0MVn+a1eZM+VQ6zkkUDh/Eu7PS1R2RZg==", "signatures": [{"sig": "MEYCIQCvK5d9H7Kl3Uin+88fe452FubNn0KZXgSjRpPk6WrkEAIhAPvt2g0lZI8tTd2tAirHlmuL21rPFCNi0SEb9SY6pFWz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dd731a23ea237680293b09a07b085a271b558d4b", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "c460b9a0bad9c6e6bf57b2de4df503a9cdbcec57", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "dependencies": {"ip": "^0.3.2", "smart-buffer": "^1.0.1"}}, "1.1.9": {"name": "socks", "version": "1.1.9", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.1.9", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "628d7e4d04912435445ac0b6e459376cb3e6d691", "tarball": "https://registry.npmjs.org/socks/-/socks-1.1.9.tgz", "integrity": "sha512-EgVaUkNNlJ/Fi4USs0QV8JzTxOgRcBOszWQPwderdc27LhgF1VWOiB9D1VzLtenGuezlyVe9GhscFlnicFHvsA==", "signatures": [{"sig": "MEUCIFS7lS7Fzkd+mvGx/U+u/6B1cBz7qIbsPmLkNQ1NN+8TAiEA7QIyHiqWQZnQ+m1sqESnEDkucBuTfo7UhguDrLPoJ0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "628d7e4d04912435445ac0b6e459376cb3e6d691", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "c334b976e40fd09add8d292fa4fd6c006579570f", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"ip": "^1.1.2", "smart-buffer": "^1.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/socks-1.1.9.tgz_1459998882162_0.9807194313034415", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.10": {"name": "socks", "version": "1.1.10", "keywords": ["socks", "proxy", "client", "tor", "bind", "associate", "socks 4", "socks 4a", "socks 5", "agent"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@1.1.10", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON>"}], "homepage": "https://github.com/Josh<PERSON>lazebrook/socks", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "5b8b7fc7c8f341c53ed056e929b7bf4de8ba7b5a", "tarball": "https://registry.npmjs.org/socks/-/socks-1.1.10.tgz", "integrity": "sha512-ArX4vGPULWjKDKgUnW8YzfI2uXW7kzgkJuB0GnFBA/PfT3exrrOk+7Wk2oeb894Qf20u1PWv9LEgrO0Z82qAzA==", "signatures": [{"sig": "MEUCIHHoR+PNi5e3wI8F8s5zaPtgtl3wnamAwZC1GlSQzgufAiEA/P0hxYXfDEVciMT4yOP759xA7jtW0FvciFSNoFmMjrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5b8b7fc7c8f341c53ed056e929b7bf4de8ba7b5a", "engines": {"npm": ">= 1.3.5", "node": ">= 0.10.0"}, "gitHead": "82d83923ad960693d8b774cafe17443ded7ed584", "scripts": {}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A SOCKS proxy client supporting SOCKS 4, 4a, and 5. (also supports BIND/Associate)", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"ip": "^1.1.4", "smart-buffer": "^1.0.13"}, "_npmOperationalInternal": {"tmp": "tmp/socks-1.1.10.tgz_1484366135467_0.8928562924265862", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "socks", "version": "2.0.0", "keywords": ["socks", "proxy", "tor", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "32d7e9f43bed6f38eb6ba937290e349d292a3232", "tarball": "https://registry.npmjs.org/socks/-/socks-2.0.0.tgz", "integrity": "sha512-X+rADtE/rmfFprDa3r+uMjHEiAyQC9W8lYof1s1xiwCCQ9Xo3dUYQJ/qTqOVFIvWeFpCiow6PkoMa3pqlNTbCA==", "signatures": [{"sig": "MEQCIGPRHWH16ltys1uNCx3dpecak6Eqjrla8NDCBCzorgI4AiAZ97Mhn+lGkTGvRarr7N8+4cGBu8dYUBCzSBqvDe06yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/socks.js", "engines": {"npm": ">= 3.0.0", "node": ">= 4.0.0"}, "gitHead": "98956c24adb1962c7f4a47c93759ffa6fdccf684", "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tsc -p ./", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "@types/ip": "^0.0.30", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.0.0.tgz_1513051474736_0.6828581914305687", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "socks", "version": "2.0.1", "keywords": ["socks", "proxy", "tor", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "83af4b21cb7134d3d1596349af68fd8731394fbe", "tarball": "https://registry.npmjs.org/socks/-/socks-2.0.1.tgz", "integrity": "sha512-mnva2DkBjDem9M4Ukhazto6qCm35hie4DOj2PMjFU25I+yse01f2Zmb+2440vcVFuhf11YkFWvzyPz//dvOr5A==", "signatures": [{"sig": "MEQCIDJSU+jMSlIE0owfhbpZLfw/Q5iykbw/ERUOhApJbfwGAiA/RDjY+f8s4hVlL/eu0boU9ikPVuBgjq1TDbzOW53sug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 4.0.0"}, "gitHead": "7aea005abc622a3e149ec864f745a8bf219d412e", "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tsc -p ./", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "@types/ip": "^0.0.30", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.0.1.tgz_1513051925471_0.7607622116338462", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "socks", "version": "2.0.2", "keywords": ["socks", "proxy", "tor", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.0.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "9fb6a2bc47790fb3c5542542581df828014ea332", "tarball": "https://registry.npmjs.org/socks/-/socks-2.0.2.tgz", "integrity": "sha512-MnmpJIsnlk0BcpIzDfGSH040EWl8Eu4Rzf6l52sJ8T9Q7Wb1DWpWmDfhNwqbbsYvGvnulE8HcJMNKnxceWETjw==", "signatures": [{"sig": "MEQCIENSlxwD+anBvWtKYcvEHrMNOuV+JOpaJ6xK19SiuoT2AiBXJ+gpIk/Pwy+5myzUElBbAzfE0cvzsB+TZhmIzXsLbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 4.0.0"}, "gitHead": "7aea005abc622a3e149ec864f745a8bf219d412e", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tsc -p ./", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "@types/ip": "^0.0.30", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.0.2.tgz_1513053901199_0.8383230126928538", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "socks", "version": "2.0.3", "keywords": ["socks", "proxy", "tor", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.0.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "ebf47d61a2307c6eecbe6b07752f652c9fa682e3", "tarball": "https://registry.npmjs.org/socks/-/socks-2.0.3.tgz", "integrity": "sha512-pKFYFDh9eBVl7JqcV/Q7gbojj/779Kb/BSd/+JXAYqDBSZFQJWv5NY6A7ilfVPS/KQdbFSxQeRQMr0l9x7NVOw==", "signatures": [{"sig": "MEUCIQDDx99+kPwrdfbPctEUqPvwi1u9mluJzB7+EuLjQTUTpAIgO94IvSmesOl9RlNR2WJhFg33jPwHAo7FJlJNX8u579c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 4.0.0"}, "gitHead": "7aea005abc622a3e149ec864f745a8bf219d412e", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tsc -p ./", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "@types/ip": "^0.0.30", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.0.3.tgz_1513054011960_0.9181258082389832", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "socks", "version": "2.0.4", "keywords": ["socks", "proxy", "tor", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.0.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "fe75a52d32bd7be5d331f58c3facd38c4742f981", "tarball": "https://registry.npmjs.org/socks/-/socks-2.0.4.tgz", "integrity": "sha512-xuAVMkZ36lmSfzigrq1EKdlmmb+/ZPJh/b1nJ/lsdcQ/VI9DU6ybkRfV4yTtEptuRJtmrbeB4JyFTz7SYbna2g==", "signatures": [{"sig": "MEUCIFVtYhFKMvch7bFgVyb54iHDk+8M7DmtDlLVaQfoqIHmAiEAps5zeYlk+Ab599N98RG8vcRTRzLj8Nit09U+T8zfcFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "3a6f7ab4ac71ecc5109cf2c86faa8c1206dba656", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.0.4.tgz_1514421528652_0.02005553198978305", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "socks", "version": "2.0.5", "keywords": ["socks", "proxy", "tor", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.0.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "5bbb05f0531979900b9e70a64f463f4422ce8b18", "tarball": "https://registry.npmjs.org/socks/-/socks-2.0.5.tgz", "integrity": "sha512-Jn08a9h0+WWbS5t7HkOY6AlOjbjd83bFruGhiiJ1s7vQ78wfdTZCzY77KBEcgPPC4W1ZtzhOwW3rtDkpLo7byA==", "signatures": [{"sig": "MEQCICfhpOjew5dE1QerR835tTUULykKoS41i4D8ELFRjEiwAiBEH8AD78+KaVruMoJ5GEu5DYgAfkXoe/fIJnqxG+Labw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "0bdc551c97540c315ce6de9b6324e3cee4b00313", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.0.5.tgz_1514422759394_0.9223261640872806", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "socks", "version": "2.1.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "562210bb8fc26bd3e55aed1cbb3fdbe4e3efd63a", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.0.tgz", "integrity": "sha512-yFwpFzwn1iafX5FkeI+szSyilgQyWVka9Ip4Xud4MkH4iQRGCdb1IJmyOweb5oGlZC35tlLAaREqnlDHHyjvZw==", "signatures": [{"sig": "MEYCIQCFZb/bnwk/a/X7wfqbjiTMlIWqKmowAIFFmLF1DADlIwIhAOUFk2RJdNRdPBheIwT8SRA5Wv8tySZk4Oa9ZyClkOnq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "4109669e9bdc7ff05ce6b3952113cc74bf6d5a57", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.1.0.tgz_1514424864955_0.8164735918398947", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "socks", "version": "2.1.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "0444f84533d501984366d75a3396a775e10e492c", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.1.tgz", "integrity": "sha512-oyI7fSjndFT2oE/VgyVHkXKBgM1U/XP0D6phVIkCYKZNb70Zi5+g2PFZaHiU0JbZzCDLu0OuuO4CbzJGF+/U/Q==", "signatures": [{"sig": "MEYCIQDTg56yzHXTbdCmrsmXNPlnv3QZWV0NreI6bPYdGaDllQIhAI+uxbq+9pjBHKkgWvFWniEuXP6Z7/mPG99mkLeao/lH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "e5b0c7b0554d78bc0376bcd583472a7e1b52e4d8", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.1.1.tgz_1514425461776_0.6656476031057537", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "socks", "version": "2.1.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "70e78794eb30647e6adfa0331cab6d4f01c1c91d", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.2.tgz", "integrity": "sha512-mJYuWoCLkFbeEet5SRPRguKiglDbU1qiIBgryySbQzvGnYOP02NYcHSADs2s45J70CKf7+sDDWSmegaDn7kXFQ==", "signatures": [{"sig": "MEQCIGSxjQ0dj5q/3fHb6ZPQsXNyZK+j8EgeHeTWt/i2DBMsAiAKyRAL2sERz8hNmVjAOy5z+eK5gJzRQZoxeym5OdEMCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "1e29bf592232b59a13e4aa701707cbcec246e5b8", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-2.1.2.tgz_1514607732957_0.4046901420224458", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "socks", "version": "2.1.3", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "07f59f5b95f1b15eb12d81e65829e539bb23ca1f", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.3.tgz", "fileCount": 33, "integrity": "sha512-9iR+4HpCawtkKDGdyn5boSYo1oz8QjzMPn2MUh9tEn2n0lXi2XIFoERj/ueCCyrNBEd37VH6kW39d3R9byCCNA==", "signatures": [{"sig": "MEYCIQCFeDMrEU81oNWbhvwklAP+G6WVXjzvM6HC/FB1kQzRggIhALKSZqLZgavOilxv2Vm8lM+IWKnTBTrG7Iud9wWVZnk+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200841}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "923beeca53f0c67cad008c8ac7fcc8bf8ebb11ea", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.1.3_1520575366867_0.25115159087923855", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "socks", "version": "2.1.4", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "22ea6e04d68253ec6dd42afc3404083d5b73108e", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.4.tgz", "fileCount": 33, "integrity": "sha512-C0XTdiABly820pXrf8e3k++aEeZg1vpv+BUEkfQ07hzqaWSSpACc4TQVoYBr9MgHIAd7sUQJwjIW4P0/SXstfA==", "signatures": [{"sig": "MEUCIQDZszqa7G5zW7pSe7f96Fj2RPZskNJ+R/wqKHjjbcfTBwIgEYgWl/ORONQvTeE3wojvp+AknHgu2d/th42dM6cVGuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200841}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "bf6d6c491db756988e79faa61c9506d5f08e72cb", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.1.4_1520576449456_0.1185183752665444", "host": "s3://npm-registry-packages"}}, "2.1.5": {"name": "socks", "version": "2.1.5", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "7fc286c07676773a0758a4438819be0e040dfdf4", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.5.tgz", "fileCount": 33, "integrity": "sha512-TJcgV+FBE2xBBKnJOofEgvzOefkdgR3CRY/XB1nOtxaHi9UsydzC0Rk5GOEg3Sm3AwsVrRCPauWF1w/DSS41Ew==", "signatures": [{"sig": "MEYCIQCcDTxgJANRU74jvQwNEEKsuf469PrSFuxQSZMORTCMsgIhAKn7sBkWzE9DijHdPaHTJymNWlFB7ciMiMxtHDN50ZpH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201217}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "f8ac01471a87a527a5d281d525b9d88ae5d3a779", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "If using 2.x branch, please upgrade to at least 2.1.6 to avoid a serious bug with socket data flow and an import issue introduced in 2.1.0", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.1.5_1520740189414_0.19882876594024723", "host": "s3://npm-registry-packages"}}, "2.1.6": {"name": "socks", "version": "2.1.6", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.1.6", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "684d98e137bdba484f3f4b13bacecb9fa5acc597", "tarball": "https://registry.npmjs.org/socks/-/socks-2.1.6.tgz", "fileCount": 33, "integrity": "sha512-cHaaOUfK1FIyUv5T9Tg5y7apRqluAjgCzCeOg9Eg3E4ooGJocGgQ+BEHp5o4ev2DBjkmroNjWl1njijx0epv4Q==", "signatures": [{"sig": "MEUCIQCvN5qEyNj6OLekQH/nh5YyC3KSl6Df7TRUPYTDv02JtQIge/hR9db6gUzvjCfRFkCN4LC3vb5JtAgL2kVdHuYCERI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201217}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "f8ac01471a87a527a5d281d525b9d88ae5d3a779", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.4.0", "chai": "^4.1.2", "mocha": "^4.0.1", "tslint": "^5.8.0", "ts-node": "^3.3.0", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.6.2", "@types/chai": "4.0.8", "@types/node": "8.0.57", "@types/mocha": "^2.2.44", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.1.6_1521353090885_0.9190991842554461", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "socks", "version": "2.2.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "144985b3331ced3ab5ccbee640ab7cb7d43fdd1f", "tarball": "https://registry.npmjs.org/socks/-/socks-2.2.0.tgz", "fileCount": 34, "integrity": "sha512-uRKV9uXQ9ytMbGm2+DilS1jB7N3AC0mmusmW5TVWjNuBZjxS8+lX38fasKVY9I4opv/bY/iqTbcpFFaTwpfwRg==", "signatures": [{"sig": "MEUCIQClclQAZEpnvqsgXEffO9di82J9GT/8hbgLQ3JCO8xLHQIgMLbDElcTS4WCO1blK4k3bV9sLYjpDO4AtXT0hUAJEo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301904}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "373a56884e84912c508c1b5335ed5f01da939d2a", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.2.0_1522812711021_0.7803850742664893", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "socks", "version": "2.2.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.2.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "68ad678b3642fbc5d99c64c165bc561eab0215f9", "tarball": "https://registry.npmjs.org/socks/-/socks-2.2.1.tgz", "fileCount": 33, "integrity": "sha512-0GabKw7n9mI46vcNrVfs0o6XzWzjVa3h6GaSo2UPxtWAROXUWavfJWh1M4PR5tnE0dcnQXZIDFP4yrAysLze/w==", "signatures": [{"sig": "MEYCIQDMf687s2ZggwDPZcwwDO6nQ+FWabAe8CJUNb1ntU2SKAIhAOSryXHdUI0ISxSxQz6EqLN8wB+uuUKvXXfQqJ9BZ1YT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbM/QwCRA9TVsSAnZWagAAzdkP/2GE4OhBa2b2QZa0A+cG\n1GH9aX21o6gPC/OKObvDLj/hudHwwzDlrtdykyi+Rsj4eekfReyiS/I+EDg2\n3Xpz3HB5Trt+jQ6Eg8cMp1IE40bI61Spu7HwMGVHLfyncdDmLXGZHezxb1xc\ngRkXWktcYAemIc/EiviwZPxVFLHS7/nIaF85vaWofIiTM9vjf5OJ5uAVGPqV\nGxojgwyqWUaJhs6q3liXjpofZiwyQYImOPzhlJqod/e2zvJCa9KB9s96GHsq\n1iTuFIB8spp6S9FAMmSCvwosX+DRy7xrH1G13Z8y1wHDHLeo6ACli6wdco1c\n4tJBe0/lgobOv75L1v4IHqw08rDqnuhk3ROEFO7PdhYztc0GaF6Bj5e38xG+\nqD/EN3k508Y4J60lc+fkvkDq2d6RmAdKL3sJ1PgB24zq/9MxIpw6jtGK1SOU\nhRa35d/8uHP9/QPe+ag0rnUO9h7jDpoIftaF6FD0z63JY+trk+v22j1KX49t\nZUKdEzrSOL7ZYjMPzOkQulaQFXl0Siga1Bsrn8H4GllJcPN2v9/f2ZDmEM4o\nf/CXwUKz5BOnQ/opxUTvvP20ubFSDLRhjdTisjkbsWS0m11mamsU+WWug6YR\nOhTkNWuhPtvg59CWtmROAIQEAg6KJw5pnUByy20nkEMuLNq4U2agz9EvgKri\nrrJi\r\n=XkWx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "3243255c8a21fdd01328ee2b556ffa8672f18ddf", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.2.1_1530131503987_0.7364482087051922", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "socks", "version": "2.2.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.2.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "f061219fc2d4d332afb4af93e865c84d3fa26e2b", "tarball": "https://registry.npmjs.org/socks/-/socks-2.2.2.tgz", "fileCount": 34, "integrity": "sha512-g6wjBnnMOZpE0ym6e0uHSddz9p3a+WsBaaYQaBaSCJYvrC4IXykQR9MNGjLQf38e9iIIhp3b1/Zk8YZI3KGJ0Q==", "signatures": [{"sig": "MEQCIEtDyfxximRuEfsRnp3Inq4u7b742cIf7R+cRjMNK/cvAiBiEMBrnA+Iwm183EIHsN7vccgnN+H61IOfWEqn6XJtIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4efqCRA9TVsSAnZWagAAMRsP/1GX1f7YbQZWu2F8wt9C\nXa9cXhsDHvtJMA2ab1uu6v6HuOTCY5E/Q3Wf9JomI3KnQsl/uQh35CXkQ/rt\nImngLp4AJQVVc+REVGtUJ/q1DJxfE+dY4SeaceFNDmXqfN6bl7Dfu36Fkiqo\nyHjlIac9fHhr5AVIpNKiIv4eZTuv/CNhA6/JnDuzrW4oAXKy06bfm1Tdj7Ai\nUAR/Ml3nOFgm2VFU5OT/E4/JDQ9bbNJB4t9IMFONJ5DBPoE8AnRjT5qZSZGy\n+vKKOfgwvF/65H7UgbX+ykcfaTkqP36mRQiTs4sClKyohJxJbPTljev5BC1n\ngscneHEoQMc53EKaA3fvspNfB4MeiGvqqZlLztlKT7H6JfBxHpEozYWnKfAY\nnC82ntfHhxshHuOAU5seZw3HGtNBSeO40nN89Rv9oiGVm0k76YZdIM24MOKL\nUBxwwRLJDzB9/w5q6oQH84g8nAI1RHhniR8eFhnnCbSMLnPkR0GbwyyIT/tX\njMgj8oXZN9u7pw5m4rQZCO2KhPEk4hWMqm+paug8aQGATB5d2qFeu3AxG9JM\nLrpBbtm+E7eQSF2ttF/GjHIdF/flGHRVSlaWoSJgG10Cp1O4GaDJ/g9LGy8K\nWWOpK0J9k5IXM3fhb9vKVs3GQpD8zke/58pJqzoFGENCxvC462Y/chCQJhHc\n4X9B\r\n=089q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "89eab07184d198fd3a15ca833268527ef7784a57", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.2.2_1541531625666_0.20564233306015645", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "socks", "version": "2.2.3", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.2.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "7399ce11e19b2a997153c983a9ccb6306721f2dc", "tarball": "https://registry.npmjs.org/socks/-/socks-2.2.3.tgz", "fileCount": 34, "integrity": "sha512-+2r83WaRT3PXYoO/1z+RDEBE7Z2f9YcdQnJ0K/ncXXbV5gJ6wYfNAebYFYiiUjM6E4JyXnPY8cimwyvFYHVUUA==", "signatures": [{"sig": "MEUCID3tSoIHRjbNWnbXtv7xUkKq7kGPtWoRIAmg42yrhhw1AiEAl0ZlbTtSfjOX5rxGyoNJ097Dah2dzCULhu4htPCV53I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSVz7CRA9TVsSAnZWagAAUFUP/RciPK4oDl0k6WBg+oWk\nsQq053mBRSzJjWiIxzFad0zqMHMpH9JZgclwmMamAfU5yka6rxv5cIB64CM+\nxNarEwKPLH8p01jkT9tZtYBT6I0wb4NGTd2y0bpoRc563NHK+hh9FGPKaOXK\nGwktedYFl2O0dDGc2pwi8YuHj42Rwp6ZQaLURouaxx1rOzYaWAiU6j1THtMJ\nVaEBmvQOoDeoTHwA5YqvzZMgvAFo4QlB3eppYuI7qNFpnVQwccwfpGC1vhZK\nnTEGqcGDNA9cw4l1tjOBY+THl1hW+gFMDMz6WXgeHlHX5/l1E1E04VdR5s4W\nokmQ2kbJMm1OqpHDsK7jpyZ6MYSAua9nJnvObCsoackzudISPA5e2KKS/vWG\nZ0RhHUYNivVhcE8RbkmmhvCsTnyU0YhCW9dX0kG4RamASaG+IWqENjkz0EhQ\nDR//X/QtQ0VmMmh8oDIfmSZihIV8xiN9akeu091lt7Gc4zoF0nphQP4yGEAI\nJal4O91JjZUMpOZW/OHj3gogismEUwWQUNxYB+Rq5t6ZE4jcNUUiFKZBJGeF\n/Rr4REZihLp6RfQnEm9li3r2eRDT7w1HnN1bib3zpBVNkA+5Y4zPL5+jamKM\nfa972F4ATFiHoIhJitqpmztDzBfnShGwbRPw1g8eHxECJlkieKwvjuQAn5hS\n92Js\r\n=SRfU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "d7f91b7bf58c6f80755b356e7777db6b75adff52", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.2.3_1548311802846_0.14998302811098907", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "socks", "version": "2.3.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "1bbc849dbc87601bb733b47724f432ee01282fd1", "tarball": "https://registry.npmjs.org/socks/-/socks-2.3.0.tgz", "fileCount": 34, "integrity": "sha512-GD2x4IiwWA4oQmVBlX47z7JmnpWB9HClGMXbHiEsUQGDXHvM51YylzPJpXGbbVtx5QojugVdAkkC6g548aU9/A==", "signatures": [{"sig": "MEUCIBArK1FwSRQHVT0AZbkbWIhKk/iGKBrHD1w6SinS0iToAiEA5Fkftly4DTeB0xAn2L1vliOSt3QENqtJAgY2z6zEJZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVG+nCRA9TVsSAnZWagAADvAP/3BuH7d2HMRq+aQBpDiA\nLthJbkzVfjsnVzMexOhdSvSgDHbVGG6lWP5bu6OgEgbJ8pMqyIiEetHe9jKj\n0SzPPj4pEhdMwlJvzaayBrgf3QPiZag9YbHXBwU/pvkehhFIwjjThbOdNXdw\nkhU9B5i215IV7+sqAZggKtMsX3mrSHocHi1CeWdwkSE+m2kbY84leWOnWs6c\nVr3snsVmPakbzE6xEn6xYEbe6Bcu2IJGLZlFSLZdvjVGJkz383CwnVJChReT\nyeFLEJeCd+jjGhJcoNfi5PsR511Vtq1+54kP3N26XKr5WVTT87STyrZ1tGnf\nLCH6XMfIGWL6IkBnDzrj3isqVgj4jQ+I+UUm5VnZXiGEn3lOBOCKJIqqoM7k\nC1QtK/G9rJDpJgCiscHzCtpWR1XMeu2/5PglowY/ZdbDKsCOJ1+JyF2U6zeq\ngQLWczJbsS4Wkcshb/jJS1H08p+vr2ST8hnneZlL7bcSuj6aiOmXwZxfty6n\n1A9BXcGFy1GyX+n0DlnjEo6OQMwmfcYmYIGA/p4uzeu025xfGJEWHdTAuEDb\neuGZgpYqZUgWnOplQn3WysTUONXpNtIZWbzXhrgYb1nbLGSZv8aLvEFsrhR0\nLZg3zIlsZc5BduaFSez7TSwae8fucm9/aVjQqljnACfDqT00ggcRrMQHPHZa\nFVBr\r\n=PIYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "3bb2214ed4e673df0d82daa5f9744c3538c44f5b", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.3.0_1549037478380_0.35802119208723715", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "socks", "version": "2.3.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.3.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "c321b90260addb5caf1d85d6a7f604b89ffb86ad", "tarball": "https://registry.npmjs.org/socks/-/socks-2.3.1.tgz", "fileCount": 34, "integrity": "sha512-srMrPbfQnLOVRDv/sQvBeM4rvFvKT4ErhdcXejaLHLQFPmCMt5XPF0TeE9Uv3iVa+GpXDevs1VgqZgwykr78Yw==", "signatures": [{"sig": "MEYCIQDAkvkmtQuYz1PIBSvSM/ZTMgPOr+CH8pOUKSLi7SWbjgIhAIzTEVGW31r7FyPPtOfRZaXcMEZzTiEYxGBSXhXPzaV5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVo2ICRA9TVsSAnZWagAAW8UP/A2B7ZlTpmHskh24SZbX\nBmreyV+UerHARofCzua/HG/hgctiVHAByqKtQd/5EVDt2qMZezzl1CmsG4jk\nyOVmoK4MBzN0eGsx4Y/V6BjJekucO0XSR5DsvawPYgeyBy8PH06Tvl1BWF30\nv+bi8aA0SfEk9erIRQG19FO6dNHN21/wzOJZ5TYXlOUaNkO/rsBRyN28Mty5\n5ZjKiPLCE6s7u7QBx7jiqNSpvywiT2uP3nyjoasZvBylDMQ4In6TDUe2k57l\nw1BuBJqMDJpqtmnTUsVEtYYxzJLheqn4+KLKKU0DWebAJm2N5LMTDsgp00P7\nwBg4gftRreQ+9D7IRdlRfGVwzzrxW0QKA3y6Y6b9TP1m10aosV6ICpsnXuA5\nF2FjNI8jUwG5hXeY12XGvZsFZu7aV1uzC2i2umoIpVSliC8fA2rBT4lnLr4a\nFiyaaq8U0Vtwys6UiEnychjRv2rizOlaQkp0Fna0SnfIyy0gu6bpTHXZ88pN\nfuseezAP0ZyTfLWUhw/7BVv+uNZ81m/GW2veZ+4GVn5Pwhf5GepALxlLXf8h\nPvCKUrdiSbj0oOEjfzxjb5IA5L7ZJekMGFVgy5rJ7WQE4JdwLMHLX7k3VZMl\nlqac7FUtCFtuV8N1MZUxzVyQpUuCpeyHHmUr6+0g+fbx+cJHnMSZ+S81CTfb\nO4vj\r\n=xfSy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "726e7a1bf1b2a753e25cf377de3bef879515f58a", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.3.1_1549176199602_0.8880783397389842", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "socks", "version": "2.3.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.3.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "ade388e9e6d87fdb11649c15746c578922a5883e", "tarball": "https://registry.npmjs.org/socks/-/socks-2.3.2.tgz", "fileCount": 33, "integrity": "sha512-pCpjxQgOByDHLlNqlnh/mNSAxIUkyBBuwwhTcV+enZGbDaClPvHdvm6uvOwZfFJkam7cGhBNbb4JxiP8UZkRvQ==", "signatures": [{"sig": "MEQCIHOf8GS80uET2NjsA7A+obXu23LS5igXRnkcI+Zy5BqfAiBm9mxDg9HWmPS+1RXFeJgI6cE8GhzkUfyL4XHSm9rzpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJca4NVCRA9TVsSAnZWagAA+3AP/3XYzwPpEs+1Lz99B3c0\nTN/aZnsMSSkBjynBqP0lJp8FMvxPidNHReBEIs4qpn8bhc+h3iIvSO0q5Bli\nvGR6N2zd4LLOhmJ+6cM6T/HF/oZb1Z1HnJnjXFzmFc36gg4oZACLi+uGEXVk\nAoqxc3zo2WTkjObwVpAdw0qx19r/U9SZbdY0KPUoJPfx87z4nRaTsc8MGStz\nnZDjILVGH+V7cxirLHYn9sLCPhPjvQ6ANyUfF0wX+FIlhfRtdrhUAHpPCjs6\nynBVKcQP/7ZIcLDZGVJK7I8ppOrKWthVPItTUEjF65gcjn2qyt1IbSukKrtV\nocOSi/VXdzgX5WSm2wFEo/BopbNEzENPZkXDvsSjFUELv2H70oXM5JYSRWgH\ncGduaTsRyiscn+qcA2HGwYssQPQVzatdOygzv7OHCgzawDxDX3vVoD75JIzy\n2na7H+dRpGlKoh8Qw9ODJAyNrN712s6B6T6l2diFlwcCBUGTQzK9+i1EiYlQ\nfDQqTEGa3O4jbSBwgYI4WccJ53L2+tiOqHMpZ5+3lxWM/ce4asI9WqcoRgXU\no3PUg+qnH4uFZXLzMvMY980UQsmvC1AmYNy6gaNs5EvkkkNB0YRbn2m6jhRU\ne57Qu4uSoDLn9+IXZ3PHkpCTVtMcaHhCnLcJ4lwsibk+pKh9ZvzSez2iv6UL\nN7qV\r\n=Aprg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "0416f3f316ed25c5a1d723c3a0815b727890f194", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --compilers ts:ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"ip": "^1.1.5", "smart-buffer": "4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "11.6.0", "chai": "^4.1.2", "mocha": "5.0.5", "tslint": "^5.8.0", "ts-node": "5.0.1", "prettier": "^1.9.2", "@types/ip": "^0.0.30", "coveralls": "^3.0.0", "typescript": "2.8.1", "@types/chai": "4.1.2", "@types/node": "9.6.2", "@types/mocha": "5.0.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.3.2_1550549844202_0.7117568166035253", "host": "s3://npm-registry-packages"}}, "2.3.3": {"name": "socks", "version": "2.3.3", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.3.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "01129f0a5d534d2b897712ed8aceab7ee65d78e3", "tarball": "https://registry.npmjs.org/socks/-/socks-2.3.3.tgz", "fileCount": 32, "integrity": "sha512-o5t52PCNtVdiOvzMry7wU4aOqYWL0PeCXRWBEiJow4/i/wr+wpsJQ9awEu1EonLIqsfGd5qSgDdxEOvCdmBEpA==", "signatures": [{"sig": "MEUCIE5lVQzBsb5AGCa9aItc0Ig3gIMkKOeJujYygFeRy4PiAiEAzzBrvcWxz6zh2Cm3oGdQEX8HvYOqe5wGEK5eLHDrwnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwwpUCRA9TVsSAnZWagAA1BkP/2u+s7K/PtYtsTYJOXri\n+010aoJev8oMKqk95xaeBlUGEsQ8vx/7w3OxZfjRf1j9PmdsqtqwvzywbIhX\n4fEpzxtnMxCaa8kJ+qwKWa0Zzh07C6KGNN9dz7Dl8Z8bTwbTZiklrBMWdKDb\nUmo9h7k441Sa3cm0HxH3LnQuq5gXLbnw9Vp1HfjDlMamOEVXeDEzBmYPkzcz\n6XY5K38VU+zDjNJRMEM84gfJ6Cy+SckUjjmiy4fYR++CZKGuZ2q0t/A+pUsf\nh69NngmkO88NNbIfyx7xGPRrFDd4SuZhOmVbEF3vC4S5U6ditK34HlgPMUVo\nFjPePWf+5S1TWRVYIhggkhIrAwucnjd2wx1ka8P7SxImMvKrrKLl7TM0qZwW\nFgtSLfqXoImxF2wtL56OV4N1AfhDTrC8QmMr+m19WbjCvM2anbSoNta7Qgo+\nUDkXqkelj2RVAeOeHgZlhJLwYdQUfYDI77A4gPabu99Gzp6gwD8/t5JFprG3\nyybzD/g4eEA+m/MjHC186+FgtQ0wV82eVhQwyphQxXbtMAaCwL4spY0r/hB0\nCKgwztuKCPYPweSXyA8Hpx0fsYFCcqXjjU4KNhcpsGCeZyoGYLjv9gAyrZMq\nVpfKxu0ZfU9DPj2lgdut2pxFc4lcgma5Fz/608DTu5b1ZrUlzVlBp653nBHT\n4aoV\r\n=YV4Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 6.0.0"}, "gitHead": "32f3e433affd5b9089dc1f26b0ffb4ff79d04860", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"ip": "1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "chai": "^4.1.2", "mocha": "6.2.2", "tslint": "^5.8.0", "ts-node": "8.4.1", "prettier": "^1.9.2", "@types/ip": "1.1.0", "coveralls": "^3.0.0", "typescript": "3.7.2", "@types/chai": "4.2.4", "@types/node": "12.12.6", "@types/mocha": "5.2.7", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.3.3_1573063252377_0.5162715569262386", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "socks", "version": "2.4.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.4.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "7a02589e2d38fc3169745a27e08b4ca3480c6a60", "tarball": "https://registry.npmjs.org/socks/-/socks-2.4.0.tgz", "fileCount": 32, "integrity": "sha512-PrUPKbeaYl/Y5JiugAMA3i/fduvMgOeC9ZO7XrlkXb2WftKNh1eQEDUgnf3CWUcOeAXg+cWmIzFQXnBA6oLedw==", "signatures": [{"sig": "MEUCIGUsjUQgaaDS2NhHgpFrOBSDO52x2+w7LLY9u1QjKnZNAiEA5uSGSg7pzjkifyT1LtP2BLB6eMDJf5Z2IfGPlY88lWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7974CRA9TVsSAnZWagAA2wIQAKPwSTxLzOKysUePI421\nRW7ISsCNacdE2KtAQNziK3NKlRNnkwepr7PVPab9l50NNvkEEZ13XiaK3I+1\nCfzCm5dOgtNriWXHh55RuUKZANWCG7PgaoOsvx86R23g7Ky1VPfpG5z1T0ux\nFHXVdY1jV+m1+6Zc/UHzd5iyDy0N9WfBchdADpNZ7cc2LKoFW8Wbhc6/P4Nw\n0+XtZHo3Nl0JZ88mKlBPqQE+5iM48bwjfpIWPPjxfKeMu1KawTi2yiNrGEfc\nC2i4jlcpl6hyW3Zz5Uq9SbWLTk1CR2ar7BuBfMZR720IkHdiByLX2akIYtpq\nzKjs3fMhdmtEOiow9IYpI7OVA0athOL8PlcLPsoYUfPtUFbt+KbiXaAG0433\nKZpc70MwsrHEqLfBv5xjL/JuTtZgdymdSFD36g+HDAFoGUXOyO8+ciVh6CW/\nVWoZWAaPvcJB8BvXBwUo9HpWsSvTZLQNH9rJp2pDervP+q2KveNWjTDwllH4\nwYnp3oH2voN543QShvU0b8lBnfdVGYUKkole5B47djA0jiaYjsxeYpSpXx9r\n/5Kak9/yWtkXrdDcSe27tAhKsx5gSBMeFMH76w15sacQUVWFQF3uGesAod/Y\nswxnMK3r6PAeHg1ZiIHaEKyy3PGMPHSe3CHNtbBGtYd+4qziDdEVdZvuTfDp\n8r5K\r\n=KVVl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "f1276cf96bc72f162324121abcb1ed49eb28d551", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "1.1.5", "prettier": "^2.0.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "^4.1.2", "mocha": "8.0.1", "tslint": "6.1.2", "ts-node": "8.10.2", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "3.9.5", "@types/chai": "4.2.11", "@types/node": "^14.0.13", "@types/mocha": "7.0.2", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.4.0_1592778487755_0.49173589702536735", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "socks", "version": "2.4.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.4.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "cea68a280a3bf7cb6333dbb40cfb243d10725e9d", "tarball": "https://registry.npmjs.org/socks/-/socks-2.4.1.tgz", "fileCount": 32, "integrity": "sha512-8mWHeYC1OA0500qzb+sqwm0Hzi8oBpeuI1JugoBVMEJtJvxSgco8xFSK+NRnZcHeeWjTbF82KUDo5sXH22TY5A==", "signatures": [{"sig": "MEUCIQCczZL9gRj6n1wilEq0kaqqFF6WYjp58vCeSEYQpVKduQIgMINj2wF/Rx2yo9wnxUuQgLdXzF0LMc4WTbQjvDHpxRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7+HLCRA9TVsSAnZWagAAFpAP/j0J7HAIn0W2fSaUXXrT\npZRZo4/h//vMOJouI+97qJWMRAWzENA3Lu8XOZCvNtpQr/h3CInnRHXV2Ovi\nfCd0D/+iXoLEVfyMwlwJwtKpPluKFVcj/lIuNLFLDjUQ4dhhtm+Di6Id4sm9\nY4sBHgE/OBoOI4vJvq36L8aCdne0g+b2vnXcRZYvylJrKmzWjj4BwG7lv4qE\nuRYAQqKlQrPWOXLkgCVk6Km+7OY7tSQhAIvdcK6Croy2O2BeTk9kn81T+koW\npw2nNmdhU9Lk3hBp+Lq1Ud9q09K1MsonIJwLxsSHrnp/m+ffnXaVNwYdMgDi\niVaD5x7k1LSHVaGXyNszLo/+Q/55XkiZheo5oHKZMQaO+OOCOpKGOr6TsD2G\nxmlKPurLXkTevRfxW6DCJSMITsp3LgHLQH88N/0in4nnmuWKZFuvyq84SB8v\n0iTdWIDDIeh/z2HLBLkDNR67LKJMXFUCMvf1bFYmQDatfRbbTYOA5kTkLxPd\njGS2kzWvAZVyZClqoOczMXIkrP+4VhoG9PifWfczuCp6mwnSLa90ApQmBJPB\nwjEQlfjvX1TCIfVHrmYCtJ0qHFnliQLdn751Up3pMlbFq3Qv+cFXLCOpmUi+\n1HoutaCsyzAXV8NbJ5BtK7bFJ4/wc3mUobQlMilzy3P1Q3cF6Wybq5nC+sPF\nHT+Q\r\n=vsxX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "0a4ead6d5a5240d77f23cf779d43a8ff287401d2", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "^4.1.2", "mocha": "8.0.1", "tslint": "6.1.2", "ts-node": "8.10.2", "prettier": "^2.0.5", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "3.9.5", "@types/chai": "4.2.11", "@types/node": "^14.0.13", "@types/mocha": "7.0.2", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.4.1_1592779211423_0.785510955815504", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "socks", "version": "2.4.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.4.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "f19431c8e4faa06f245dbeed0a7b5d25bef37a1c", "tarball": "https://registry.npmjs.org/socks/-/socks-2.4.2.tgz", "fileCount": 32, "integrity": "sha512-cE6P9R+/CET9XhIMvqhRgaNT1GXKIahioMjwwhgYOd+8UQWP50rrFm+faqG/4xkZsjTdVeBYr0Tb246BSC9/fg==", "signatures": [{"sig": "MEQCICDY6WEnxZDBIFDelqlBTqztg8bHY3VGVDw54T4AbzX4AiADtZjIiZ3++Kc21xb6GllwxPgVLTOt+y1GcTSVJ50lOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSePACRA9TVsSAnZWagAAI8gQAIKJSSqMUEWYYKb1ehQL\nvvACSDuzvuP/d4GQVcM/m9C4Po0pD7sDBjYQpdjNzsLCkBDTXmYa/7setR8k\nEXf5vkdh/+sq1ikrAXMUjyMxrZnNRkgA5GwTzg5UAjXjWC25drkP9HzXmU9d\npr14ORMBoxqvGQX5vKoHtBnrTELrCI64Q448Z60qoysF2peYi3nIfcqhb7Ib\nK7BLtOa3qnYwKJcVt5nwJb+sCynIfdHkmSWLbjqU6LEJbwKg2wNCK9KanEjS\nzayUNBjuSeRGS4faQd+p4Mr0lkJZmGjz1Scbif67XvYzqWjjV3MwZAQ9mbkT\nafRUUwYv04S0npZv/gLwmneOnwrvdsOb2HS8HhOehZzyCtQOZ23GLDo9CFBY\nin0ys1SqSvmwHZdADqqI9IInJ748+9nUryGesBWHHZNN3oZjJ82EAYzGjInL\n6ZCOmpbhbWoAH9dH+/LXj3RXA5B+WvjFUFcDK6xRqFq6yTlUOUM2C5mYISji\nEKIavEDSMN6R5uV/SztXnNj1XO4e6eyk8vxDkNtoN8fb2r7fcm4qLY0LW3KE\niSZH0IefnrB3ilc+Zfs2MTuSDLmtEq+/KuXNyGK0T+2HOrzUUGs6j86iHIDJ\nKQ09/chz4+oVa/ERQKwJwajUkghQ4kHKtb6GKZjKjRkaSfF+ukqd2netvNSf\nJO0X\r\n=IL9n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "5eee806825d72c35d53cbae1b8835d1b9c0ac19d", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "^4.1.2", "mocha": "^8.1.3", "tslint": "^6.1.3", "ts-node": "^9.0.0", "prettier": "^2.1.1", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.0.2", "@types/chai": "^4.2.12", "@types/node": "^14.6.2", "@types/mocha": "^8.0.3", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.4.2_1598677951535_0.16260755267290006", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "socks", "version": "2.4.3", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.4.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "dc346507c84f5c366677b5dd7abe7fc44abad398", "tarball": "https://registry.npmjs.org/socks/-/socks-2.4.3.tgz", "fileCount": 32, "integrity": "sha512-IZI8/+sULG9TJtDUq6ayXKdlZOHTGlmqzXwUP/eUJC+sPuw5zaPA3W4zbJRRmAktvkfwmc9Bed5EwowdGaUm9g==", "signatures": [{"sig": "MEQCICY8R8AoM3Ui+XKj7J2+VChHfzzLe0uDMTHPl6XASZqBAiAjaxu0KMf072fdBhAh8FM/IMllo1a1lPv7/s1KDSJ/Hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWBCOCRA9TVsSAnZWagAA7CEP/17SpYmdrfpyy1q1GWIO\ne2V592g8XxYjY7/D4iV4pw0OYeKlqXRyyrSoN0hXfquu/FVYeDY4KQ/58SlF\n2j5HMh0ZVmK9nDoECO/2GcQkbI7ZOh++l4h24c8+/YmLGO2WxLHlt5/Ab3SB\nbd5ykK60PzYOumcZGK7L8LCr8v/uDas62Zd4lBwCVPcBIGX7choTiAlZXat1\n5mPqU29+iiy/S+g8HmvGRaaxmzuYWwody73mY9vtIakFuwVYpagraCVfWEFz\nJqFHuPTgBaz8ihFBqTUmM8zDt2qWybQVUjiYXbmVxgEs2lEFmAChO8BggsPZ\nhAzxPTE+e+cp7jVvCrGYlMNIem3ZJfi3kT9d3w42bRT6msuK5ASjRp5MRGZC\n7zaeK8/jjwiCmHISLpyGqvWJTtNmh+vuFksEXmRD1ZoAgChDX7FsgSKJ6GEh\nsEoAw0sbY0hgR0D/RmuN4lCpTm0SaLltslkhQAc2VCKKmTsGkQ+Sp1xAYrdL\nGZEqON8+P7IXIlhfoRK8SFCn4nF5EEHwSLjR9gUg5VDz62Kxx8nt9nygmAzP\npsw1pLkOgd+Ni9VEW0432fgvLpTfZFQvDJBACkiXOUAw9k2Byp8ZHQj1Qzpm\np+N1VfuZjrF7EYREJkIJR/Y5oRBJ0ZRSh/Tc1pa2zHMUPtWqq/bYgdubm0wU\n01bG\r\n=33ae\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "6fa885089d8354802445c7d88e4ba3d131dc889b", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "^4.1.2", "mocha": "^8.1.3", "tslint": "^6.1.3", "ts-node": "^9.0.0", "prettier": "^2.1.1", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.0.2", "@types/chai": "^4.2.12", "@types/node": "^14.6.2", "@types/mocha": "^8.0.3", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.4.3_1599606926362_0.6500096614001964", "host": "s3://npm-registry-packages"}}, "2.4.4": {"name": "socks", "version": "2.4.4", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.4.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "f1a3382e7814ae28c97bb82a38bc1ac24b21cca2", "tarball": "https://registry.npmjs.org/socks/-/socks-2.4.4.tgz", "fileCount": 32, "integrity": "sha512-7LmHN4IHj1Vpd/k8D872VGCHJ6yIVyeFkfIBExRmGPYQ/kdUkpdg9eKh9oOzYYYKQhuxavayJHTnmBG+EzluUA==", "signatures": [{"sig": "MEQCIE4qyjwVoAmFoDCbwOCSZkHuwJhK+/HdMQImutIvL55SAiArBE5JSX0CMP9N+/6H+HRdsf3m+5fAV/08+0fBceAOXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWBGeCRA9TVsSAnZWagAA1woP/jWBKipKRFFFpnuTqXCR\nQDK4bxSAmbjJGkLHBZs8ULnEgsQRRmLw4H5p1TmTi9RUBxhaE7S2suuNJuSH\n6XSJReVCcOQkL/lxnKo5sGfbJkS6QWlPUUva2InbwMofXsA+LlHIeFiLbRna\notCxEpYUkDELRhDisLBN/y1Ihm1h1Gs+I3oqztzdaXDsfwNkjfK2o0V1FLRg\n2MaKRSMhT6A62/pEg1Wgx8wnVnnhO/QdMvhbxdHPlGWVMuTiGJp6bAl++2sC\nNKUDx6lidW3DgHm8cIzoKYKopDB54pNMxcc1j564iGLITqDVLmFgL2HpQei4\nEKRyTtOTU0gbV+49lDtzDIkYhxcnuioOo4YeR/gd7PjjoROGLggUBp7Je1T3\naCUAq5p2cwwR2u1TsVBqiTf7zuaQqJpdGMBkPT7WYdwJkcJbRjBlgX0M5KaM\n1Tdr7xFM2iK9x1KRZ1UUkwCIuF9Hl/LDiupb1w7ftUCo44ppM4nfmy206PmA\nzM6w4XM7oSShk+/ILom02YnKv7ymXjCVxbz5BHiZcmV9VTR8/EXjBmxK82kA\n9+o9bCY+dzzE1aeGK+fu/Ansbkp3cT1o2c+Z/Yy1x+GHYEHPLmQdff7nfpvh\n8v8+WjMTTmAh5KKoOh7h4jMJpJRH+zFaBJXWy8Wbf3TQiwNSV8zyN1jXT9QK\nPwfR\r\n=LtDm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "0745f63d6885635b108c5dc143600ffce38ab04f", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "chai": "^4.1.2", "mocha": "^8.1.3", "tslint": "^6.1.3", "ts-node": "^9.0.0", "prettier": "^2.1.1", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.0.2", "@types/chai": "^4.2.12", "@types/node": "^14.6.2", "@types/mocha": "^8.0.3", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.4.4_1599607197791_0.47554152500771685", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "socks", "version": "2.5.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.5.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "3a7c286db114f67864a4bd8b4207a91d1db3d6db", "tarball": "https://registry.npmjs.org/socks/-/socks-2.5.0.tgz", "fileCount": 32, "integrity": "sha512-00OqQHp5SCbwm9ecOMJj9aQtMSjwi1uVuGQoxnpKCS50VKZcOZ8z11CTKypmR8sEy7nZimy/qXY7rYJYbRlXmA==", "signatures": [{"sig": "MEUCIQDeqVHVxvLSykst26dVty/bqNdRntn5+S1kS39Vk06fxgIgVFiOPak4nZVsvJKnl3uK66eUOWyDuYxb1GwCb/qxDj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfllBkCRA9TVsSAnZWagAA2AIP+QAqHRNOzET6ynoVI0C2\naCmoZAwLHYNc4o+QFetdDbMZtfUTk2Pg9Oo6wQoduPoXgXC39FJSHSoRv0bu\nA38I9MfsBTSBsJWRIoDWAxeA1Fay0KAAdIFudtVYNi7tlgVSY5zFGEF0+jjh\nRTFC2bSxQOVpb/KsQQ5wDx5+zUeSLmUeIdN5XLu0OClvnCd+F+FD+GUQ93aS\nTajyWu9/qC7ZsGxNnUd3p4hv7mUM+3tt3+0E01nDQhr8+i4hz3mpvl9PwdcL\n9bg89/HAeNvxqSGtnNykXusNFlR1Zv5eYGK9HqrG1oSt9Vo2+fBVrAXwr0rA\nmtfY/gRI4bvkoMAtlnESlWF6RGDcglh4Y8cs83tuVnJSGTYfPLP56BGt1wJe\nEBrs61FIelMIVq6exJoW5sbl1lInH/NwwfnGN7339EkzxYakCad5F1mbnc67\nmgZ+NK9xs1yKLk+N2ZJ7MN2x3anzHg1K/KHybbg14rG7QM+K/lrzTKro4quW\nMNRzk+9JpRxMjIHMGvu9QYbdzzHB63oJFHtE8hRapJX3PXbAvOjk8BjtHfw2\nalWc0Smx/Ul/o46Wyf8VGkOGPEjhTbNaCVhIzd4Zkv22mJ32b3lfWiBer1aI\nxWoXknu8vYc1/+j52kpDliVChUcDI7lvl/udcZ6x1jiCaeIn24D7OtnVcoU/\nfw/+\r\n=FuEJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "6c221089c0744ff99d9685e22eec99ea7855b9b0", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "^8.2.0", "tslint": "^6.1.3", "ts-node": "^9.0.0", "prettier": "^2.1.2", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.0.3", "@types/node": "^14.14.3", "@types/mocha": "^8.0.3", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.5.0_1603686500166_0.3434437914928514", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "socks", "version": "2.5.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.5.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "7720640b6b5ec9a07d556419203baa3f0596df5f", "tarball": "https://registry.npmjs.org/socks/-/socks-2.5.1.tgz", "fileCount": 32, "integrity": "sha512-oZCsJJxapULAYJaEYBSzMcz8m3jqgGrHaGhkmU/o/PQfFWYWxkAaA0UMGImb6s6tEXfKi959X6VJjMMQ3P6TTQ==", "signatures": [{"sig": "MEYCIQC0Yhbw0BuqyJf7ZTurl2PVrPsEUnDwa46zgAYdQJJ3rwIhAN3k3H3o2MUG8TELm5aaJkivKq9Y9YRmh4dg1PuQ8stj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfycB9CRA9TVsSAnZWagAADWsP/jru0udIetnumUaL+fp3\nVYkTk+61d8wcJ6BgfO3ielmqw1JPu9zKiUzEAqG5ThW76geA3EoRrpg/pKHc\n/usxTdp9zj5xe7WxLDlGIBbJDloZUcpYNk6DmSQ4MUEldGsNz3jfWhVCLkHc\n7nxoCB46z02iuGyDAdTLoexroq3kBDYv38bW3JlRd4joV6uOWCD0ChHxT7Bt\nX9gQKYkZE73A5ozWTFaaW+nLmkHX8FuEAe8261t1Ai1G5iOZIYnaGGpSfIiS\nEIodr26eL0Fs0g4bwRUZLvfK//Pj+AEvVmg18CzHNCfw5weZXubHxA4LfRYu\nBQISQPr8rkPKIf9uTDsfAYTR2vLVh7qRhTMAyj/W1zf0eRHjESaD1IkGubz/\n4m70KJrgYfXYPCsCnjxcO8gg3fP3bCzF7fW9EuffYsxcCbarzuy1IsTbgKS+\nNg7DRKy1RTd7R+JCTHVhGitpDing0uvJi6JZEwlDv6o/R2JncfQ9rmCgJLMN\nwv4pLxwpEiMAQjY/qdM6U21Q/71CKD/nj8MOj5yqgxAiRsrD6yeHgrwOVIsc\nD55SYz3MgY7kAvafJA3CtOXFR1pWmAGNFfs6SgOL6l7p11em6vAh5epZu4vB\nw8YiQfHEGIyJpDCrp+ofkb9oOV+dEkM9MEp6kRDD2L8bxtEL0esVUESEPJ+0\nzoBy\r\n=Rnz1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "4e390c8938aa475f9e5edbfe87da852c7724a5b6", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && tslint --project tsconfig.json && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "^8.2.0", "tslint": "^6.1.3", "ts-node": "^9.0.0", "prettier": "^2.1.2", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.0.3", "@types/node": "^14.14.3", "@types/mocha": "^8.0.3", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.5.1_1607057532505_0.11617236902415873", "host": "s3://npm-registry-packages"}}, "2.6.0-beta.1": {"name": "socks", "version": "2.6.0-beta.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.6.0-beta.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "028a44aca066524cbc13d2560fa50653853a687f", "tarball": "https://registry.npmjs.org/socks/-/socks-2.6.0-beta.1.tgz", "fileCount": 32, "integrity": "sha512-SdyLPF3o/Woysfoe0PazxChunIoJLOSNhbix2+W3rV2o7lCYzZrXO4lOjiBAeIpZpHDDUvLTp+3l0YBVu9gUDQ==", "signatures": [{"sig": "MEQCIBkNehIBqTlgtwXoHCh3qsafIjUTkVjhJWprEuodaEzAAiAOexXp7W7SrStloc9d+CQNNZJW8+ezSVtxKFcc241R+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUB0UCRA9TVsSAnZWagAAPekP/iIdVElnqaKkz64pTlLO\nnQNtbsK7mqJqiXmBoNJMweD4dnz7VRLhMmUIlVhg0owPHG9rV31+4ccweXmc\njo5CMg2g9r5d4Ax/GqbUTBMkcGPQv+pq3LiKXpaJ2WZL8rPRZAEOMapti4Uy\n7nrt+sHBYR15rZVRctUOG1U0N/hH/slhha8PRLH2N4KZrg08JHqzXx8wO4sk\nsQLBPacnykvLrw8DWZ3riIO3Qqj052rG/Q61snjxnmmDXNMJ/hVbnmq0EzWH\nQCNTaI3ZgHkN0WVSdOZzpmJApoaMvYOA/twLL4lJAkuLIzqtasfvkV52jma5\nsxuVZtB8wIiGwcf+OVYXf6TANtFuYjTutbahhjZzRf7KPsIXT2QJYb03AXWp\nNkePOIsAp/tut4PuM4PL02tZbyJbQdTllYEVcJRQEE0+cyDMZmunmSLVSIkp\nNYXAgzQlD3HyX8gTyt7LfvDgYqTHDrjawxCH1Q54hS+oq769C5un1O5Gdeou\nnMvyrG7FPqWSA7e7Up6iPZS1dNHhA7Woux3d0MiY8EPArrocoSKh6czLzsCm\nntIDYopLLPiMPxcumcbHZI1UWNsSggZcgx4LMt7NOBvKEv3X4vAGz9KQjyhY\nMBNy06zqFV26bmGfEh56Ubnr1jrUiZh3umeKqC2qBKg6pO3CS0Ak5n469F83\nu5nk\r\n=e5cc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "23af9c411fffb5f186aec65db9f88591f47c8b4b", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "15.1.0", "mocha": "^8.3.2", "tslint": "^6.1.3", "ts-node": "^9.1.1", "prettier": "^2.2.1", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.2.3", "@types/node": "^14.14.35", "@types/mocha": "^8.2.1", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.6.0-beta.1_1615863060248_0.5414969963662386", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "socks", "version": "2.6.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.6.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "6b984928461d39871b3666754b9000ecf39dfac2", "tarball": "https://registry.npmjs.org/socks/-/socks-2.6.0.tgz", "fileCount": 32, "integrity": "sha512-mNmr9owlinMplev0Wd7UHFlqI4ofnBnNzFuzrm63PPaHgbkqCFe4T5LzwKmtQ/f2tX0NTpcdVLyD/FHxFBstYw==", "signatures": [{"sig": "MEQCIDIX4+I//YD3dEX/E9crPK/Ad62Nq0bQ4V7fo4SSOAB8AiAwJOI6Tfk+J4C7uqNv4vXR/oEpgSbFK1RwaT2KsfS8Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUWFcCRA9TVsSAnZWagAArWcP/2i7cFd8Ss64muaSvLV1\noFfHjvMYBag52UECfQNNX0S6girgSR/9kFR+ld441gYZ8HH3hf1Ato3y26Sd\niyag3F5bk+BD4KNFT6yh3kE+YphIIOjWtEF/hDie1r+lA5FkZhswjwx99VMe\nSS8JXLWB38JjtD9/uhv935gCfNN+lCsnIFooqCxCppim/u4WklO9vUkMqXCN\n2ND01RsmxpJMsr+AgnGl1Wuds4a7biZg1pYwSEy47ZULhfoVlZTc5SxhuhAl\nLGcLPIckF8DFWQd3GDVZRDGE1aWLxEkp+jJx9510CJ5rQw3UQj2JLtOd/oO2\neDjMiL8oS5qRXMTv97nR3Egn64pMKTyTlzzbaytsAQrbbbgPiYcX1+Dxnjq2\n8yWT7QwttRmbf3TvTp9uJq6G3eHS84fZ+JfqjbrUrAHh79Sm5YbSby+dbski\np7HMe8so/PrsPhuzWkVZg/usAPuxTGUGKsOjiRVi6lIBxG/XMBwq/lW4s3Ig\neeVOyXGKecQg/UEXLd8Ae67n++YAQyeoOit5soq6F4S7VMtkwJhXged3eeSK\nU59ie54GNKlAkmSkfimbOgFjcfEdVnCAfnSZcAZ/8cDwwHbdq2nwZIa/bER8\nEVaCmihe2JW3fspXfUM+uRPoVPZ4iXde56OEZpGkElu40SSDy9kRxtPfgkef\nNFLB\r\n=qNVo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "0bfb6be17eba87d593003236d8ab34ffb4ce5e55", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "^8.3.2", "tslint": "^6.1.3", "ts-node": "^9.1.1", "prettier": "^2.2.1", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.2.3", "@types/node": "^14.14.35", "@types/mocha": "^8.2.1", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.6.0_1615946075469_0.9510182095278978", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "socks", "version": "2.6.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.6.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "989e6534a07cf337deb1b1c94aaa44296520d30e", "tarball": "https://registry.npmjs.org/socks/-/socks-2.6.1.tgz", "fileCount": 32, "integrity": "sha512-kLQ9N5ucj8uIcxrDwjm0Jsqk06xdpBjGNQtpXy4Q8/QY2k+fY7nZH8CARy+hkbG+SGAovmzzuauCpBlb8FrnBA==", "signatures": [{"sig": "MEUCIQDLQoscSQAjFFvWTQX1QA1Crd4Mi5wbFJEm1v9N8yD0kAIgaNAu9naznrM9/CXt9YumBFezgYAQ+UMRGomJOVT0Wg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge7FiCRA9TVsSAnZWagAAZdEP/AkbzeCZuDzjdZZhGqPz\n5HUHj1OJ8AJ0Whpr1qsfVlsPozt67S4dNJtBkw382ba/B44F2dqafrJaccGg\nL6QosnSzNpaTrw8qJ/wK8ThCW01AkXxAJIRI+6/hp9bK6mIcgTSbCysGqhy3\n/07tPeXX0g0OlhCcQwQzzJOnKZ+IC0W/aF9Yix3LyZ9IqTtv5ccS/mqfjIIf\nwlEYy5sThRyOxTID1dY7ZaeA6d0KvgY4UOMF2XP2LyQ53HzRwDp/xsOK9+l0\nM8TInC5N2kS6pKsqjEYJ7JKeE1baFlhhmZV6KuSOsif1DDO+W9pc9zH7UPLN\nw82BqmfxhCI0ccK2/pde1Vdya1/pTZkZldn1hQyxMeXG3JEKqAYnPoktvVed\ntQmXhpGxMnP7ta3lA+eGc5/dOJUVNeZ455aENrrOzKOXsIhMjaksXzJ9KWC3\nK4L4xLH2zTMudr2UbfSPO9PacXCd8CRBZWTGSnQXqQtBfP5S6tG5GFZnymg6\nAnE64u/SyHmQQzOlKQqH+np8m9vFPZPbMxbFTxyHI6jOrKUtNEWtn6WXtk1l\ns/VWELv3DwPZeMMQaKyZsIDqOoTTfavZTR5lNHQfJPBlRgG0oNzgsLVmOpir\nO66Z7tsOC2a5ketahLp5kVV1TDnSNhnaaaqfbOsLSwMbB/Qy0kRDMSWOa+Je\nD5Dk\r\n=JYPn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "6c777c229cf37aaf81ca82c9e06c0de2faead332", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "^8.3.2", "tslint": "^6.1.3", "ts-node": "^9.1.1", "prettier": "^2.2.1", "@types/ip": "1.1.0", "coveralls": "3.1.0", "typescript": "^4.2.4", "@types/node": "^14.14.41", "@types/mocha": "^8.2.2", "socks5-server": "^0.1.1", "tslint-config-airbnb": "^5.11.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.6.1_1618719073931_0.5886135018408913", "host": "s3://npm-registry-packages"}}, "2.6.2": {"name": "socks", "version": "2.6.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.6.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "nyc": {"all": true, "exclude": ["**.*.d.ts", "node_modules", "typings"], "include": ["src/*.ts", "src/**/*.ts"], "require": ["ts-node/register"], "reporter": ["json", "html"], "extension": [".ts", ".tsx"]}, "dist": {"shasum": "ec042d7960073d40d94268ff3bb727dc685f111a", "tarball": "https://registry.npmjs.org/socks/-/socks-2.6.2.tgz", "fileCount": 32, "integrity": "sha512-zDZhHhZRY9PxRruRMR7kMhnf3I8hDs4S3f9RecfnGxvcBHQcKcIH/oUcEWffsfl1XxdYlA7nnlGbbTvPz9D8gA==", "signatures": [{"sig": "MEYCIQDaf1fbalqTtCT1LVxMJGpCvEU0pyw9USo+EgplXewtQQIhAMPRXkPT0Hbd7/baBd+Et2jl9vjoTzldnZ9/o27HN1H+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/v+xCRA9TVsSAnZWagAARn4P/A3u7l2mQNvZsxKPhluN\n4JUFnM+FydLHy8LB8FJ4SsBor0mRdkPZO2WJOlkmbu9ooKqqrG/VyFeJieCp\nfCdQgRWbw0uT4YcuYJ5OAuUdJYDFAkDv+U4J0D1AGYEhrDXsEEGO05VL6WPq\nfhG58bYPuoofk4P72aRNVvlxOWhi8Q6/+4XpO2CeSSkooTRCLebAErmxWBdv\nDnOVcUBHmEbhtyD+eDrx5zdBw/Nnwu/C7Y5x3bmCubg6i6c+qg1pyQA4z/XH\nxt5dIkCp0s2teMhK/awX421jcPckL2IQvWRsXEgWCOSVki7rGa4mx85j81fg\nBfH+10i1UdnW+EB3jyqv3piygVE/EnA9xQwOskedRPv2BNNGaCXLnHeFrmog\nU6kDBVwVP58WzVmocB3vbfC6nZNZt0A2noT7UTAahHCla/BBm/7EG3qv0QFU\n1s4VaoPnl0JQ5kICtFHI9Y3c5Qs1v1fUQa4+Sxy+tUrxROwp9nOQcC9RXnKB\n5H694rtlEq56EmU2j367QcxzK8xSm2XR5qdGjqYywsx2rloWm+5c3l82UU8l\nlUIOzUEL6F5hPP6VtSwh3VoR+VUIPsREnrPDEmVZizJBELDIZRRuVEdGUsBY\ndQlQM/CvFmLjxjoT+/eQ8TOa+0DQLyVuOXf1N0yTKE8P9gPExOTdR8oDDM9A\n/6SL\r\n=+QY8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "beeac09c05983dfdd532278503a0f50ee1c4d9f5", "private": false, "scripts": {"lint": "tslint --project tsconfig.json 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "coverage": "NODE_ENV=test nyc npm test", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "coveralls": "NODE_ENV=test nyc npm test && nyc report --reporter=text-lcov | coveralls", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"ip": "^1.1.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "mocha": "^9.2.0", "ts-node": "^10.4.0", "prettier": "^2.5.1", "@types/ip": "1.1.0", "coveralls": "^3.1.1", "typescript": "^4.5.5", "@types/node": "^17.0.15", "@types/mocha": "^9.1.0", "socks5-server": "^0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.6.2_1644101553235_0.13651270208472188", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "socks", "version": "2.7.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.7.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "f9225acdb841e874dca25f870e9130990f3913d0", "tarball": "https://registry.npmjs.org/socks/-/socks-2.7.0.tgz", "fileCount": 32, "integrity": "sha512-scnOe9y4VuiNUULJN72GrM26BNOjVsfPXI+j+98PkyEfsIXroa5ofyjT+FzGvn/xHs73U2JtoBYAVx9Hl4quSA==", "signatures": [{"sig": "MEQCIE96zPoRxv+jpH2oqHq0wW65ojSH5QXsD4ocWSb9zcqjAiAFFJlvbO3II6auM8XrZa3RdCTNupPxEone14vAWI8c7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1JPDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYrw//V2xAPWJwvKT2+SX9S2+TI8f+QHUTTAo+LLzbK4jMXPf5G3N8\r\nx97iNw2NIjSNJkCpVB5LxQok0DSiQaUwDRPLjGYgXsTUQr+nlBupD0SjxIRA\r\nCAqzN+dgwY3WnFmcOXS4QYXyhqmXTt+JQX6m/fPdQ1zR+Ftahc6zdSI39pHm\r\n9OP/9SVCrHg0iy3vbsMEx7yhg0aHRvUuI+afcf65ZCNQvsxUx2X9IBbUeNWw\r\n81ZhudhyzrUAn0vUYICvrxzfXDKYRMICqStRcH/bWH+/Ok1y+8MNHLgAJi7u\r\nwELRXV4K7/Dlp85W8mCnzQp+cAWtPFkeTaPR8CzvxESVdSSEBx1oI23oqKn3\r\n2J18H6K8ezVL6J+xi3b5iKQKYiBQHtmomYsvGfceyQoXTBihc6CN4mpjC155\r\nvUcdlL9vCcUNZwXbWGnINXcwVfsmmih6sGEaN1hdpHLpoNRCgURI47kLn9Lh\r\nAf0BHsEBOz8wX0K1Iiq2X6qKniYqPXgrdy/rVrxDnGBDD4yjvHxCZu3qLHRd\r\n0Gb7SxIeIQMwiPOVhYNxS6GE+8A71m12oQpjV5s4bWxXE8N7jVjWd+CDYm0x\r\n1nqAxe562yLzn/3Hhq/ZYiA7VU2RFP8m8hUtzlTRdEUI+sr3vuBekVC6VWgB\r\n53i2jcPyDY6PYiohlPtEKCEBdN1TWmiN9MI=\r\n=Yr1w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "6ad8587e1443799da84a7b2e0829e4bf70b34ea7", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^2.7.1", "@types/ip": "1.1.0", "typescript": "^4.7.4", "@types/node": "^18.0.6", "@types/mocha": "^9.1.1", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.7.0_1658098627776_0.002541619000081541", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "socks", "version": "2.7.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.7.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "d8e651247178fde79c0663043e07240196857d55", "tarball": "https://registry.npmjs.org/socks/-/socks-2.7.1.tgz", "fileCount": 32, "integrity": "sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==", "signatures": [{"sig": "MEUCIQChuEybvQyHYKrnSi2etgjVo2YQYhPqHIucDnfaM8mVTgIgcrR4rMP7vJq+w9PGVYWb6h7C3eeJLaRhauYF1UmTQDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 152060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjOP7LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9iw//Vp4a3VG8dCUo44A4aTHqlvHXIax8nTr2QL7dnE0LgyLmSypF\r\n4NI31drArZ3U6DBC/WPCvfxXQjAGcz74ZF2abzq84vfyqwa1BeFLd40cVm3m\r\nv5C6M87ljzs8VxFX9gVqNnbRWKF3HXNGKkrCF5uLM7o9zJEY6EfPYB8gFCJJ\r\nbGGmPYLjryzyyufS3hnjG8YH7TSnymtL3iKZG455Pe/8DOFulTezOWbyFjIj\r\nt6WOMdWJdMug3G3azv4V5F8vH/xE0ghV6p4vjGFZcPC71JgbF8dNe2spzh9Z\r\nrn+X+dmRogxcwACxEhGoARI6BEemx/na20EZ+xHDCiyhJfRA0+D3tV8n4zvS\r\n8Z3AOGE18ZabTbXIVSDbuqRaZ6lwdWVwDC/AGEuflrNejTMznctu6xxNNgWM\r\n2PtXXH+vBFgKx6RUwGow1eFdcCo0KSPe/9rd+ZSwXbbWmfncszVQwkW/YYku\r\nM22eGUkJATXQ8W9py5t1JUXL7ngPpmATL99L1b3vroXNoDIomMVr7AHq5UTX\r\nTPH078q8JLvwGZpTKAOWsIl8LB+NuNceTCWhCrAWxxDRsrN2LsTn5XY5n74o\r\ncGP6rX8jf/3vClQVklqfo9jRoCf8XEw/WDs6BneyhGOHkwHE9eb0AmpezeQE\r\nBwlJ5HGjZOaB6M/vrbEmrp4z5gz1M8+iL4I=\r\n=jCmn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.13.0"}, "gitHead": "76d013e4c9a2d956f07868477d8f12ec0b96edfc", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "14.19.1", "dependencies": {"ip": "^2.0.0", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^2.7.1", "@types/ip": "1.1.0", "typescript": "^4.7.4", "@types/node": "^18.0.6", "@types/mocha": "^9.1.1", "@typescript-eslint/parser": "^5.30.6", "@typescript-eslint/eslint-plugin": "^5.30.6"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.7.1_1664679627354_0.91848606138677", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "socks", "version": "2.8.0", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.8.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "c9b0b741bbedc356d19ec5ba968b1b23a24ed415", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.0.tgz", "fileCount": 32, "integrity": "sha512-AvXLNBlmf/AN7g6ZuCRNtwbLFacfNBYvy7pchLnpJ1aqCw7FPOK0HEC/LxOZxWiJpqwnjYPxxxNxXYOgX8+3fw==", "signatures": [{"sig": "MEQCIH7wFeaWoemEqsUoYju/HEKHg0VNsj/KtadvoVLKrXqpAiAtfxYYtYF+6H60Vk+eOpJEZARv32PzxhXYtG2aOWyA7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155977}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 16.0.0"}, "gitHead": "209c398d86f40dd2398f70ae1f7a832e0a1a9b09", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "please use 2.8.1 to fix package-lock issue", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.8.0_1707701249289_0.22353324627485582", "host": "s3://npm-registry-packages"}}, "2.7.2": {"name": "socks", "version": "2.7.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.7.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "1aa014890a2884ca217de51eca78b6a28efdb1b4", "tarball": "https://registry.npmjs.org/socks/-/socks-2.7.2.tgz", "fileCount": 32, "integrity": "sha512-qxHq8dn3pxMQNgVpWnkpz+x82MztVliGBm5an8Z8ILpHnsK9rzrZ11hYczQskHkq+4NqQw/RgQVkTS508RReHQ==", "signatures": [{"sig": "MEUCIQD7cjyUZgKbOCx/sFlFUs877AOkQ3j+4yVrM+S1c+qkSwIgdJa+WWhqNx9g22ptX8aWtz8wT15l+FHUZXHnSTTkPC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156030}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "b428d7c46bc1bca9b6293c228e8977053551a86d", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "please use 2.7.4 or 2.8.1 to fix package-lock issue", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.7.2_1707709149194_0.9688333973334666", "host": "s3://npm-registry-packages"}}, "2.7.3": {"name": "socks", "version": "2.7.3", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.7.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "7d8a75d7ce845c0a96f710917174dba0d543a785", "tarball": "https://registry.npmjs.org/socks/-/socks-2.7.3.tgz", "fileCount": 32, "integrity": "sha512-vfuYK48HXCTFD03G/1/zkIls3Ebr2YNa4qU9gHDZdblHLiqhJrJGkY3+0Nx0JpN9qBhJbVObc1CNciT1bIZJxw==", "signatures": [{"sig": "MEYCIQCkwmm5ZM2I4QK1CPMsuKLlQ+VzxYjCsRpEaCkDlXQaYAIhAPl2Y88TAdzBDu7lOi9eACxcDo/L0mNW15ae62Mtitpo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156030}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "66b7f73023697f6ffb9751b5749b1a8f9b8d5066", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "please use 2.7.4 or 2.8.1 to fix package-lock issue", "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.7.3_1707710443629_0.6370766747550198", "host": "s3://npm-registry-packages"}}, "2.7.4": {"name": "socks", "version": "2.7.4", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.7.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "ebe77775b6dfe8def79ed2c646421c4d8856b15a", "tarball": "https://registry.npmjs.org/socks/-/socks-2.7.4.tgz", "fileCount": 32, "integrity": "sha512-eQPLsN0lC4k5WxwKwETwVUP5bv2S2cgEgXC0s0h9yJpuCGiQwwNAqi0KjJaJMlPGhFD90KW4YVjAgn6W8/iG0Q==", "signatures": [{"sig": "MEQCIDufdWomYimicd0MjaHcTNYM8SHUL96KjrMtaxiPkLXXAiAnP5c9FhqsdsJkICUwDALqnhT3pwfQdwyKdD+BNLgRcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156030}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "89d8c07fbccd973db9ac7ed16f034fa5b2c6488c", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.7.4_1708661999311_0.6487974714441029", "host": "s3://npm-registry-packages"}}, "2.8.1": {"name": "socks", "version": "2.8.1", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.8.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "22c7d9dd7882649043cba0eafb49ae144e3457af", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.1.tgz", "fileCount": 32, "integrity": "sha512-B6w7tkwNid7ToxjZ08rQMT8M9BJAf8DKx8Ft4NivzH0zBUfd6jldGcisJn/RLgxcX3FPNDdNQCUEMMT79b+oCQ==", "signatures": [{"sig": "MEQCIGD836zp24LgZ2GVNltyZaNBiSI6L4R/Gho50CM/LFgQAiArZF9CP/tcKeqjJKNA3dzAZG1WlCWf4GDQY06YjnGqVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156030}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "99633ae95b2e5b63370d50b93c8e174ca80b9907", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.8.1_1708662265448_0.7378055746532552", "host": "s3://npm-registry-packages"}}, "2.8.2": {"name": "socks", "version": "2.8.2", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.8.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "568443367cd28d87caa19ae86c7c9d9337f6e6c7", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.2.tgz", "fileCount": 32, "integrity": "sha512-5Hvyu6Md91ooZzdrN/bSn/zlulFaRHrk2/6IY6qQNa3oVHTiG+CKxBV5PynKCGf31ar+3/hyPvlHFAYaBEOa3A==", "signatures": [{"sig": "MEQCIH45WTMxJWEbdiIcde59w3q0AaWjaUwhbFlqywRPqi4PAiBqqsVWN8kwLQxJai3FQcYgttXXjJIEbEwvFPzRkCGeXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156030}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "d3c2adbd72bf5afabc1543158f68e038acc32193", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.8.2_1712684369819_0.17242920184794364", "host": "s3://npm-registry-packages"}}, "2.8.3": {"name": "socks", "version": "2.8.3", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.8.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "1ebd0f09c52ba95a09750afe3f3f9f724a800cb5", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.3.tgz", "fileCount": 32, "integrity": "sha512-l5x7VUUWbjVFbafGLxPWkYsHIhEvmF85tbIeFZWc8ZPtoMyybuEhL7Jye/ooC4/d48FgOjSJXgsF/AJPYCW8Zw==", "signatures": [{"sig": "MEUCIQCK1DTr6IDjVG3NrjjnBKEwJ3UIUhA0yGDE6+Troh5+ugIgV5jooHtwcnN5GVpk4HeGWlTt3VbJghKRRQe0ztJjc9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156302}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "a2a06d9967edfaa317e6d20d33963b95a3e654e3", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.8.3_1712714721411_0.16414561909301684", "host": "s3://npm-registry-packages"}}, "2.8.4": {"name": "socks", "version": "2.8.4", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.8.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "07109755cdd4da03269bda4725baa061ab56d5cc", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.4.tgz", "fileCount": 32, "integrity": "sha512-D3YaD0aRxR3mEcqnidIs7ReYJFVzWdd6fXJYUM8ixcQcJRGTka/b3saV0KflYhyVJXKhb947GndU35SxYNResQ==", "signatures": [{"sig": "MEYCIQCQwFX2UTS0bZhs95yCk/pfUkrm5V1eUOgPZrTqf1mWlQIhAM/MpbXsPSX7kyaqDgTwAViUdDZzvhvxGWb25lk/Sglg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 156296}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "f047ee4a55c427d925849e355c8fbd0820fd855f", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.8.4_1739142444846_0.8038804332671294", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.5": {"name": "socks", "version": "2.8.5", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "socks@2.8.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "castorw"}], "homepage": "https://github.com/JoshGlazebrook/socks/", "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "dist": {"shasum": "bfe18f5ead1efc93f5ec90c79fa8bdccbcee2e64", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.5.tgz", "fileCount": 32, "integrity": "sha512-iF+tNDQla22geJdTyJB1wM/qrX9DMRwWrciEPwWLPRWAUEM8sQiyxgckLxWT1f7+9VabJS0jTGGr4QgBuvi6Ww==", "signatures": [{"sig": "MEYCIQD6lzYBRLTwhY4gFA8N8jlq0QJraIKtV2tHWL8bG3JwpQIhAOlkrqFD1D5W31jLS/ogf///LzLfQIqhPaxRlYm2N3GF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 156312}, "main": "build/index.js", "engines": {"npm": ">= 3.0.0", "node": ">= 10.0.0"}, "gitHead": "93818fc6475d31cc680da519effeb612c5f9e5d5", "private": false, "scripts": {"lint": "eslint 'src/**/*.ts'", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "build-raw": "rm -rf build typings && tsc -p .", "prepublish": "npm install -g typescript && npm run build"}, "typings": "typings/index.d.ts", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/JoshGlazebrook/socks.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "eslint": "^8.20.0", "ts-node": "^10.9.1", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/node": "^20.11.17", "@types/mocha": "^10.0.6", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/eslint-plugin": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks_2.8.5_1749523146371_0.8826315967879845", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.6": {"name": "socks", "private": false, "version": "2.8.6", "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "main": "build/index.js", "typings": "typings/index.d.ts", "homepage": "https://github.com/JoshGlazebrook/socks/", "repository": {"type": "git", "url": "git+https://github.com/JoshGlazebrook/socks.git"}, "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}, "author": {"name": "<PERSON>"}, "contributors": [{"name": "castorw"}], "license": "MIT", "devDependencies": {"@types/mocha": "^10.0.6", "@types/node": "^20.11.17", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.20.0", "mocha": "^10.0.0", "prettier": "^3.2.5", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "scripts": {"prepublish": "npm install -g typescript && npm run build", "test": "NODE_ENV=test mocha --recursive --require ts-node/register test/**/*.ts", "prettier": "prettier --write ./src/**/*.ts --config .prettierrc.yaml", "lint": "eslint 'src/**/*.ts'", "build": "rm -rf build typings && prettier --write ./src/**/*.ts --config .prettierrc.yaml && tsc -p .", "build-raw": "rm -rf build typings && tsc -p ."}, "_id": "socks@2.8.6", "gitHead": "80cbeb900478309de6c58392ccc6711d0804fadc", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-pe4Y2yzru68lXCb38aAqRf5gvN8YdjP1lok5o0J7BOHljkyCGKVz7H3vpVIXKD27rj2giOJ7DwVyk/GWrPHDWA==", "shasum": "e335486a2552f34f932f0c27d8dbb93f2be867aa", "tarball": "https://registry.npmjs.org/socks/-/socks-2.8.6.tgz", "fileCount": 32, "unpackedSize": 156420, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCOaHvA/SQwIVZXnhCmm3Ui6PPIaOR9HTg1rdNsiJVXHgIgWmOfEp+fTWP+QYZ60RZegM6kMggoHpVmsQagm0XWBD8="}]}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/socks_2.8.6_1752210041565_0.8517677883064507"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-02-15T12:58:54.652Z", "modified": "2025-07-11T05:00:41.957Z", "0.0.1": "2013-02-15T12:59:01.831Z", "1.0.0": "2015-02-26T22:05:11.951Z", "1.1.5": "2015-02-26T22:40:50.190Z", "1.1.6": "2015-02-26T23:18:28.742Z", "1.1.7": "2015-03-02T01:39:15.091Z", "1.1.8": "2015-06-30T02:50:07.573Z", "1.1.9": "2016-04-07T03:14:44.536Z", "1.1.10": "2017-01-14T03:55:36.177Z", "2.0.0": "2017-12-12T04:04:35.874Z", "2.0.1": "2017-12-12T04:12:06.504Z", "2.0.2": "2017-12-12T04:45:01.939Z", "2.0.3": "2017-12-12T04:46:52.990Z", "2.0.4": "2017-12-28T00:38:49.233Z", "2.0.5": "2017-12-28T00:59:19.494Z", "2.1.0": "2017-12-28T01:34:26.148Z", "2.1.1": "2017-12-28T01:44:23.011Z", "2.1.2": "2017-12-30T04:22:14.177Z", "2.1.3": "2018-03-09T06:02:46.967Z", "2.1.4": "2018-03-09T06:20:49.529Z", "2.1.5": "2018-03-11T03:49:49.484Z", "2.1.6": "2018-03-18T06:04:51.026Z", "2.2.0": "2018-04-04T03:31:51.143Z", "2.2.1": "2018-06-27T20:31:44.116Z", "2.2.2": "2018-11-06T19:13:45.856Z", "2.2.3": "2019-01-24T06:36:43.043Z", "2.3.0": "2019-02-01T16:11:18.635Z", "2.3.1": "2019-02-03T06:43:19.731Z", "2.3.2": "2019-02-19T04:17:24.379Z", "2.3.3": "2019-11-06T18:00:52.554Z", "2.4.0": "2020-06-21T22:28:07.875Z", "2.4.1": "2020-06-21T22:40:11.577Z", "2.4.2": "2020-08-29T05:12:31.695Z", "2.4.3": "2020-09-08T23:15:26.482Z", "2.4.4": "2020-09-08T23:19:57.959Z", "2.5.0": "2020-10-26T04:28:20.339Z", "2.5.1": "2020-12-04T04:52:12.716Z", "2.6.0-beta.1": "2021-03-16T02:51:00.434Z", "2.6.0": "2021-03-17T01:54:35.666Z", "2.6.1": "2021-04-18T04:11:14.072Z", "2.6.2": "2022-02-05T22:52:33.381Z", "2.7.0": "2022-07-17T22:57:07.934Z", "2.7.1": "2022-10-02T03:00:27.528Z", "2.8.0": "2024-02-12T01:27:29.515Z", "2.7.2": "2024-02-12T03:39:09.348Z", "2.7.3": "2024-02-12T04:00:43.811Z", "2.7.4": "2024-02-23T04:19:59.703Z", "2.8.1": "2024-02-23T04:24:25.622Z", "2.8.2": "2024-04-09T17:39:29.988Z", "2.8.3": "2024-04-10T02:05:21.594Z", "2.8.4": "2025-02-09T23:07:25.079Z", "2.8.5": "2025-06-10T02:39:06.624Z", "2.8.6": "2025-07-11T05:00:41.778Z"}, "bugs": {"url": "https://github.com/Josh<PERSON><PERSON>zebrook/socks/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/JoshGlazebrook/socks/", "keywords": ["socks", "proxy", "tor", "socks 4", "socks 5", "socks4", "socks5"], "repository": {"type": "git", "url": "git+https://github.com/JoshGlazebrook/socks.git"}, "description": "Fully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.", "contributors": [{"name": "castorw"}], "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# socks  [![Build Status](https://travis-ci.org/<PERSON>Gla<PERSON><PERSON>/socks.svg?branch=master)](https://travis-ci.org/JoshGlazebrook/socks)  [![Coverage Status](https://coveralls.io/repos/github/<PERSON>Gla<PERSON><PERSON>/socks/badge.svg?branch=master)](https://coveralls.io/github/<PERSON>Gla<PERSON><PERSON>/socks?branch=v2)\n\nFully featured SOCKS proxy client supporting SOCKSv4, SOCKSv4a, and SOCKSv5. Includes Bind and Associate functionality.\n\n> Looking for Node.js agent? Check [node-socks-proxy-agent](https://github.com/TooTallNate/node-socks-proxy-agent).\n\n### Features\n\n* Supports SOCKS v4, v4a, v5, and v5h protocols.\n* Supports the CONNECT, BIND, and ASSOCIATE commands.\n* Supports callbacks, promises, and events for proxy connection creation async flow control.\n* Supports proxy chaining (CONNECT only).\n* Supports user/password authentication.\n* Supports custom authentication.\n* Built in UDP frame creation & parse functions.\n* Created with TypeScript, type definitions are provided.\n\n### Requirements\n\n* Node.js v10.0+  (Please use [v1](https://github.com/JoshGla<PERSON>brook/socks/tree/82d83923ad960693d8b774cafe17443ded7ed584) for older versions of Node.js)\n\n### Looking for v1?\n* Docs for v1 are available [here](https://github.com/JoshGlazebrook/socks/tree/82d83923ad960693d8b774cafe17443ded7ed584)\n\n## Installation\n\n`yarn add socks`\n\nor\n\n`npm install --save socks`\n\n## Usage\n\n```typescript\n// TypeScript\nimport { SocksClient, SocksClientOptions, SocksClientChainOptions } from 'socks';\n\n// ES6 JavaScript\nimport { SocksClient } from 'socks';\n\n// Legacy JavaScript\nconst SocksClient = require('socks').SocksClient;\n```\n\n## Quick Start Example\n\nConnect to github.com (**************) on port 80, using a SOCKS proxy.\n\n```javascript\nconst options = {\n  proxy: {\n    host: '**************', // ipv4 or ipv6 or hostname\n    port: 1080,\n    type: 5 // Proxy version (4 or 5)\n  },\n\n  command: 'connect', // SOCKS command (createConnection factory function only supports the connect command)\n\n  destination: {\n    host: '**************', // github.com (hostname lookups are supported with SOCKS v4a and 5)\n    port: 80\n  }\n};\n\n// Async/Await\ntry {\n  const info = await SocksClient.createConnection(options);\n\n  console.log(info.socket);\n  // <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy server)\n} catch (err) {\n  // Handle errors\n}\n\n// Promises\nSocksClient.createConnection(options)\n.then(info => {\n  console.log(info.socket);\n  // <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy server)\n})\n.catch(err => {\n  // Handle errors\n});\n\n// Callbacks\nSocksClient.createConnection(options, (err, info) => {\n  if (!err) {\n    console.log(info.socket);\n    // <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy server)\n  } else {\n    // Handle errors\n  }\n});\n```\n\n## Chaining Proxies\n\n**Note:** Chaining is only supported when using the SOCKS connect command, and chaining can only be done through the special factory chaining function.\n\nThis example makes a proxy chain through two SOCKS proxies to ip-api.com. Once the connection to the destination is established it sends an HTTP request to get a JSON response that returns ip info for the requesting ip.\n\n```javascript\nconst options = {\n  destination: {\n    host: 'ip-api.com', // host names are supported with SOCKS v4a and SOCKS v5.\n    port: 80\n  },\n  command: 'connect', // Only the connect command is supported when chaining proxies.\n  proxies: [ // The chain order is the order in the proxies array, meaning the last proxy will establish a connection to the destination.\n    {\n      host: '**************', // ipv4, ipv6, or hostname\n      port: 1081,\n      type: 5\n    },\n    {\n      host: '***************', // ipv4, ipv6, or hostname\n      port: 1081,\n      type: 5\n    }\n  ]\n}\n\n// Async/Await\ntry {\n  const info = await SocksClient.createConnectionChain(options);\n\n  console.log(info.socket);\n  // <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy servers)\n\n  console.log(info.socket.remoteAddress) // The remote address of the returned socket is the first proxy in the chain.\n  // **************\n\n  info.socket.write('GET /json HTTP/1.1\\nHost: ip-api.com\\n\\n');\n  info.socket.on('data', (data) => {\n    console.log(data.toString()); // ip-api.com sees that the last proxy in the chain (***************) is connected to it.\n    /*\n      HTTP/1.1 200 OK\n      Access-Control-Allow-Origin: *\n      Content-Type: application/json; charset=utf-8\n      Date: Sun, 24 Dec 2017 03:47:51 GMT\n      Content-Length: 300\n\n      {\n        \"as\":\"AS14061 Digital Ocean, Inc.\",\n        \"city\":\"Clifton\",\n        \"country\":\"United States\",\n        \"countryCode\":\"US\",\n        \"isp\":\"Digital Ocean\",\n        \"lat\":40.8326,\n        \"lon\":-74.1307,\n        \"org\":\"Digital Ocean\",\n        \"query\":\"***************\",\n        \"region\":\"NJ\",\n        \"regionName\":\"New Jersey\",\n        \"status\":\"success\",\n        \"timezone\":\"America/New_York\",\n        \"zip\":\"07014\"\n      }\n    */\n  });\n} catch (err) {\n  // Handle errors\n}\n\n// Promises\nSocksClient.createConnectionChain(options)\n.then(info => {\n  console.log(info.socket);\n  // <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy server)\n\n  console.log(info.socket.remoteAddress) // The remote address of the returned socket is the first proxy in the chain.\n  // **************\n\n  info.socket.write('GET /json HTTP/1.1\\nHost: ip-api.com\\n\\n');\n  info.socket.on('data', (data) => {\n    console.log(data.toString()); // ip-api.com sees that the last proxy in the chain (***************) is connected to it.\n    /*\n      HTTP/1.1 200 OK\n      Access-Control-Allow-Origin: *\n      Content-Type: application/json; charset=utf-8\n      Date: Sun, 24 Dec 2017 03:47:51 GMT\n      Content-Length: 300\n\n      {\n        \"as\":\"AS14061 Digital Ocean, Inc.\",\n        \"city\":\"Clifton\",\n        \"country\":\"United States\",\n        \"countryCode\":\"US\",\n        \"isp\":\"Digital Ocean\",\n        \"lat\":40.8326,\n        \"lon\":-74.1307,\n        \"org\":\"Digital Ocean\",\n        \"query\":\"***************\",\n        \"region\":\"NJ\",\n        \"regionName\":\"New Jersey\",\n        \"status\":\"success\",\n        \"timezone\":\"America/New_York\",\n        \"zip\":\"07014\"\n      }\n    */\n  });\n})\n.catch(err => {\n  // Handle errors\n});\n\n// Callbacks\nSocksClient.createConnectionChain(options, (err, info) => {\n  if (!err) {\n    console.log(info.socket);\n    // <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy server)\n\n    console.log(info.socket.remoteAddress) // The remote address of the returned socket is the first proxy in the chain.\n  // **************\n\n  info.socket.write('GET /json HTTP/1.1\\nHost: ip-api.com\\n\\n');\n  info.socket.on('data', (data) => {\n    console.log(data.toString()); // ip-api.com sees that the last proxy in the chain (***************) is connected to it.\n    /*\n      HTTP/1.1 200 OK\n      Access-Control-Allow-Origin: *\n      Content-Type: application/json; charset=utf-8\n      Date: Sun, 24 Dec 2017 03:47:51 GMT\n      Content-Length: 300\n\n      {\n        \"as\":\"AS14061 Digital Ocean, Inc.\",\n        \"city\":\"Clifton\",\n        \"country\":\"United States\",\n        \"countryCode\":\"US\",\n        \"isp\":\"Digital Ocean\",\n        \"lat\":40.8326,\n        \"lon\":-74.1307,\n        \"org\":\"Digital Ocean\",\n        \"query\":\"***************\",\n        \"region\":\"NJ\",\n        \"regionName\":\"New Jersey\",\n        \"status\":\"success\",\n        \"timezone\":\"America/New_York\",\n        \"zip\":\"07014\"\n      }\n    */\n  });\n  } else {\n    // Handle errors\n  }\n});\n```\n\n## Bind Example (TCP Relay)\n\nWhen the bind command is sent to a SOCKS v4/v5 proxy server, the proxy server starts listening on a new TCP port and the proxy relays then remote host information back to the client. When another remote client connects to the proxy server on this port the SOCKS proxy sends a notification that an incoming connection has been accepted to the initial client and a full duplex stream is now established to the initial client and the client that connected to that special port.\n\n```javascript\nconst options = {\n  proxy: {\n    host: '**************', // ipv4, ipv6, or hostname\n    port: 1081,\n    type: 5\n  },\n\n  command: 'bind',\n\n  // When using BIND, the destination should be the remote client that is expected to connect to the SOCKS proxy. Using 0.0.0.0 makes the Proxy accept any incoming connection on that port.\n  destination: {\n    host: '0.0.0.0',\n    port: 0\n  }\n};\n\n// Creates a new SocksClient instance.\nconst client = new SocksClient(options);\n\n// When the SOCKS proxy has bound a new port and started listening, this event is fired.\nclient.on('bound', info => {\n  console.log(info.remoteHost);\n  /*\n  {\n    host: \"**************\",\n    port: 57362\n  }\n  */\n});\n\n// When a client connects to the newly bound port on the SOCKS proxy, this event is fired.\nclient.on('established', info => {\n  // info.remoteHost is the remote address of the client that connected to the SOCKS proxy.\n  console.log(info.remoteHost);\n  /*\n    host: ************,\n    port: 49823\n  */\n\n  console.log(info.socket);\n  // <Socket ...>  (This is a raw net.Socket that is a connection between the initial client and the remote client that connected to the proxy)\n\n  // Handle received data...\n  info.socket.on('data', data => {\n    console.log('recv', data);\n  });\n});\n\n// An error occurred trying to establish this SOCKS connection.\nclient.on('error', err => {\n  console.error(err);\n});\n\n// Start connection to proxy\nclient.connect();\n```\n\n## Associate Example (UDP Relay)\n\nWhen the associate command is sent to a SOCKS v5 proxy server, it sets up a UDP relay that allows the client to send UDP packets to a remote host through the proxy server, and also receive UDP packet responses back through the proxy server.\n\n```javascript\nconst options = {\n  proxy: {\n    host: '**************', // ipv4, ipv6, or hostname\n    port: 1081,\n    type: 5\n  },\n\n  command: 'associate',\n\n  // When using associate, the destination should be the remote client that is expected to send UDP packets to the proxy server to be forwarded. This should be your local ip, or optionally the wildcard address (0.0.0.0)  UDP Client <-> Proxy <-> UDP Client\n  destination: {\n    host: '0.0.0.0',\n    port: 0\n  }\n};\n\n// Create a local UDP socket for sending packets to the proxy.\nconst udpSocket = dgram.createSocket('udp4');\nudpSocket.bind();\n\n// Listen for incoming UDP packets from the proxy server.\nudpSocket.on('message', (message, rinfo) => {\n  console.log(SocksClient.parseUDPFrame(message));\n  /*\n  { frameNumber: 0,\n    remoteHost: { host: '***************', port: 4444 }, // The remote host that replied with a UDP packet\n    data: <Buffer 74 65 73 74 0a> // The data\n  }\n  */\n});\n\nlet client = new SocksClient(options);\n\n// When the UDP relay is established, this event is fired and includes the UDP relay port to send data to on the proxy server.\nclient.on('established', info => {\n  console.log(info.remoteHost);\n  /*\n    {\n      host: '**************',\n      port: 44711\n    }\n  */\n\n  // Send 'hello' to ***************:4444\n  const packet = SocksClient.createUDPFrame({\n    remoteHost: { host: '***************', port: 4444 },\n    data: Buffer.from('hello')\n  });\n  udpSocket.send(packet, info.remoteHost.port, info.remoteHost.host);\n});\n\n// Start connection\nclient.connect();\n```\n\n**Note:** The associate TCP connection to the proxy must remain open for the UDP relay to work.\n\n## Additional Examples\n\n[Documentation](docs/index.md)\n\n\n## Migrating from v1\n\nLooking for a guide to migrate from v1? Look [here](docs/migratingFromV1.md)\n\n## Api Reference:\n\n**Note:** socks includes full TypeScript definitions. These can even be used without using TypeScript as most IDEs (such as VS Code) will use these type definition files for auto completion intellisense even in JavaScript files.\n\n* Class: SocksClient\n  * [new SocksClient(options[, callback])](#new-socksclientoptions)\n  * [Class Method: SocksClient.createConnection(options[, callback])](#class-method-socksclientcreateconnectionoptions-callback)\n  * [Class Method: SocksClient.createConnectionChain(options[, callback])](#class-method-socksclientcreateconnectionchainoptions-callback)\n  * [Class Method: SocksClient.createUDPFrame(options)](#class-method-socksclientcreateudpframedetails)\n  * [Class Method: SocksClient.parseUDPFrame(data)](#class-method-socksclientparseudpframedata)\n  * [Event: 'error'](#event-error)\n  * [Event: 'bound'](#event-bound)\n  * [Event: 'established'](#event-established)\n  * [client.connect()](#clientconnect)\n  * [client.socksClientOptions](#clientconnect)\n\n### SocksClient\n\nSocksClient establishes SOCKS proxy connections to remote destination hosts. These proxy connections are fully transparent to the server and once established act as full duplex streams. SOCKS v4, v4a, v5, and v5h are supported, as well as the connect, bind, and associate commands.\n\nSocksClient supports creating connections using callbacks, promises, and async/await flow control using two static factory functions createConnection and createConnectionChain. It also internally extends EventEmitter which results in allowing event handling based async flow control.\n\n**SOCKS Compatibility Table**\n\nNote: When using 4a please specify type: 4, and when using 5h please specify type 5.\n\n| Socks Version | TCP | UDP | IPv4 | IPv6 | Hostname |\n| --- | :---: | :---: | :---: | :---: | :---: |\n| SOCKS v4 | ✅ | ❌ | ✅ | ❌ | ❌ |\n| SOCKS v4a | ✅ | ❌ | ✅ | ❌ | ✅ |\n| SOCKS v5 (includes v5h) | ✅ | ✅ | ✅ | ✅ | ✅ |\n\n### new SocksClient(options)\n\n* ```options``` {SocksClientOptions} - An object describing the SOCKS proxy to use, the command to send and establish, and the destination host to connect to.\n\n### SocksClientOptions\n\n```typescript\n{\n  proxy: {\n    host: '**************', // ipv4, ipv6, or hostname\n    port: 1080,\n    type: 5, // Proxy version (4 or 5). For v4a use 4, for v5h use 5.\n\n    // Optional fields\n    userId: 'some username', // Used for SOCKS4 userId auth, and SOCKS5 user/pass auth in conjunction with password.\n    password: 'some password', // Used in conjunction with userId for user/pass auth for SOCKS5 proxies.\n    custom_auth_method: 0x80,  // If using a custom auth method, specify the type here. If this is set, ALL other custom_auth_*** options must be set as well.\n    custom_auth_request_handler: async () =>. {\n      // This will be called when it's time to send the custom auth handshake. You must return a Buffer containing the data to send as your authentication.\n      return Buffer.from([0x01,0x02,0x03]);\n    },\n    // This is the expected size (bytes) of the custom auth response from the proxy server.\n    custom_auth_response_size: 2,\n    // This is called when the auth response is received. The received packet is passed in as a Buffer, and you must return a boolean indicating the response from the server said your custom auth was successful or failed.\n    custom_auth_response_handler: async (data) => {\n      return data[1] === 0x00;\n    }\n  },\n\n  command: 'connect', // connect, bind, associate\n\n  destination: {\n    host: '**************', // ipv4, ipv6, hostname. Hostnames work with v4a and v5.\n    port: 80\n  },\n\n  // Optional fields\n  timeout: 30000, // How long to wait to establish a proxy connection. (defaults to 30 seconds)\n\n  set_tcp_nodelay: true // If true, will turn on the underlying sockets TCP_NODELAY option.\n}\n```\n\n### Class Method: SocksClient.createConnection(options[, callback])\n* ```options``` { SocksClientOptions } - An object describing the SOCKS proxy to use, the command to send and establish, and the destination host to connect to.\n* ```callback``` { Function } - Optional callback function that is called when the proxy connection is established, or an error occurs.\n* ```returns``` { Promise } - A Promise is returned that is resolved when the proxy connection is established, or rejected when an error occurs.\n\nCreates a new proxy connection through the given proxy to the given destination host. This factory function supports callbacks and promises for async flow control.\n\n**Note:** If a callback function is provided, the promise will always resolve regardless of an error occurring. Please be sure to exclusively use either promises or callbacks when using this factory function.\n\n```typescript\nconst options = {\n  proxy: {\n    host: '**************', // ipv4, ipv6, or hostname\n    port: 1080,\n    type: 5 // Proxy version (4 or 5)\n  },\n\n  command: 'connect', // connect, bind, associate\n\n  destination: {\n    host: '**************', // ipv4, ipv6, or hostname\n    port: 80\n  }\n}\n\n// Await/Async (uses a Promise)\ntry {\n  const info = await SocksClient.createConnection(options);\n  console.log(info);\n  /*\n  {\n    socket: <Socket ...>,  // Raw net.Socket\n  }\n  */\n  / <Socket ...>  (this is a raw net.Socket that is established to the destination host through the given proxy server)\n\n} catch (err) {\n  // Handle error...\n}\n\n// Promise\nSocksClient.createConnection(options)\n.then(info => {\n  console.log(info);\n  /*\n  {\n    socket: <Socket ...>,  // Raw net.Socket\n  }\n  */\n})\n.catch(err => {\n  // Handle error...\n});\n\n// Callback\nSocksClient.createConnection(options, (err, info) => {\n  if (!err) {\n    console.log(info);\n  /*\n  {\n    socket: <Socket ...>,  // Raw net.Socket\n  }\n  */\n  } else {\n    // Handle error...\n  }\n});\n```\n\n### Class Method: SocksClient.createConnectionChain(options[, callback])\n* ```options``` { SocksClientChainOptions } - An object describing a list of SOCKS proxies to use, the command to send and establish, and the destination host to connect to.\n* ```callback``` { Function } - Optional callback function that is called when the proxy connection chain is established, or an error occurs.\n* ```returns``` { Promise } - A Promise is returned that is resolved when the proxy connection chain is established, or rejected when an error occurs.\n\nCreates a new proxy connection chain through a list of at least two SOCKS proxies to the given destination host. This factory method supports callbacks and promises for async flow control.\n\n**Note:** If a callback function is provided, the promise will always resolve regardless of an error occurring. Please be sure to exclusively use either promises or callbacks when using this factory function.\n\n**Note:** At least two proxies must be provided for the chain to be established.\n\n```typescript\nconst options = {\n  proxies: [ // The chain order is the order in the proxies array, meaning the last proxy will establish a connection to the destination.\n    {\n      host: '**************', // ipv4, ipv6, or hostname\n      port: 1081,\n      type: 5\n    },\n    {\n      host: '***************', // ipv4, ipv6, or hostname\n      port: 1081,\n      type: 5\n    }\n  ]\n\n  command: 'connect', // Only connect is supported in chaining mode.\n\n  destination: {\n    host: '**************', // ipv4, ipv6, hostname\n    port: 80\n  }\n}\n```\n\n### Class Method: SocksClient.createUDPFrame(details)\n* ```details``` { SocksUDPFrameDetails } - An object containing the remote host, frame number, and frame data to use when creating a SOCKS UDP frame packet.\n* ```returns``` { Buffer } - A Buffer containing all of the UDP frame data.\n\nCreates a SOCKS UDP frame relay packet that is sent and received via a SOCKS proxy when using the associate command for UDP packet forwarding.\n\n**SocksUDPFrameDetails**\n\n```typescript\n{\n  frameNumber: 0, // The frame number (used for breaking up larger packets)\n\n  remoteHost: { // The remote host to have the proxy send data to, or the remote host that send this data.\n    host: '*******',\n    port: 1234\n  },\n\n  data: <Buffer 01 02 03 04...> // A Buffer instance of data to include in the packet (actual data sent to the remote host)\n}\ninterface SocksUDPFrameDetails {\n  // The frame number of the packet.\n  frameNumber?: number;\n\n  // The remote host.\n  remoteHost: SocksRemoteHost;\n\n  // The packet data.\n  data: Buffer;\n}\n```\n\n### Class Method: SocksClient.parseUDPFrame(data)\n* ```data``` { Buffer } - A Buffer instance containing SOCKS UDP frame data to parse.\n* ```returns``` { SocksUDPFrameDetails } - An object containing the remote host, frame number, and frame data of the SOCKS UDP frame.\n\n```typescript\nconst frame = SocksClient.parseUDPFrame(data);\nconsole.log(frame);\n/*\n{\n  frameNumber: 0,\n  remoteHost: {\n    host: '*******',\n    port: 1234\n  },\n  data: <Buffer 01 02 03 04 ...>\n}\n*/\n```\n\nParses a Buffer instance and returns the parsed SocksUDPFrameDetails object.\n\n## Event: 'error'\n* ```err``` { SocksClientError } - An Error object containing an error message and the original SocksClientOptions.\n\nThis event is emitted if an error occurs when trying to establish the proxy connection.\n\n## Event: 'bound'\n* ```info``` { SocksClientBoundEvent } An object containing a Socket and SocksRemoteHost info.\n\nThis event is emitted when using the BIND command on a remote SOCKS proxy server. This event indicates the proxy server is now listening for incoming connections on a specified port.\n\n**SocksClientBoundEvent**\n```typescript\n{\n  socket: net.Socket, // The underlying raw Socket\n  remoteHost: {\n    host: '*******', // The remote host that is listening (usually the proxy itself)\n    port: 4444 // The remote port the proxy is listening on for incoming connections (when using BIND).\n  }\n}\n```\n\n## Event: 'established'\n* ```info``` { SocksClientEstablishedEvent } An object containing a Socket and SocksRemoteHost info.\n\nThis event is emitted when the following conditions are met:\n1. When using the CONNECT command, and a proxy connection has been established to the remote host.\n2. When using the BIND command, and an incoming connection has been accepted by the proxy and a TCP relay has been established.\n3. When using the ASSOCIATE command, and a UDP relay has been established.\n\nWhen using BIND, 'bound' is first emitted to indicate the SOCKS server is waiting for an incoming connection, and provides the remote port the SOCKS server is listening on.\n\nWhen using ASSOCIATE, 'established' is emitted with the remote UDP port the SOCKS server is accepting UDP frame packets on.\n\n**SocksClientEstablishedEvent**\n```typescript\n{\n  socket: net.Socket, // The underlying raw Socket\n  remoteHost: {\n    host: '*******', // The remote host that is listening (usually the proxy itself)\n    port: 52738 // The remote port the proxy is listening on for incoming connections (when using BIND).\n  }\n}\n```\n\n## client.connect()\n\nStarts connecting to the remote SOCKS proxy server to establish a proxy connection to the destination host.\n\n## client.socksClientOptions\n* ```returns``` { SocksClientOptions } The options that were passed to the SocksClient.\n\nGets the options that were passed to the SocksClient when it was created.\n\n\n**SocksClientError**\n```typescript\n{ // Subclassed from Error.\n  message: 'An error has occurred',\n  options: {\n    // SocksClientOptions\n  }\n}\n```\n\n# Further Reading:\n\nPlease read the SOCKS 5 specifications for more information on how to use BIND and Associate.\nhttp://www.ietf.org/rfc/rfc1928.txt\n\n# License\n\nThis work is licensed under the [MIT license](http://en.wikipedia.org/wiki/MIT_License).\n", "readmeFilename": "README.md", "users": {"knoja4": true, "tailot": true, "raelgor": true, "zuojiang": true, "nomodeset666": true, "joshglazebrook": true}}