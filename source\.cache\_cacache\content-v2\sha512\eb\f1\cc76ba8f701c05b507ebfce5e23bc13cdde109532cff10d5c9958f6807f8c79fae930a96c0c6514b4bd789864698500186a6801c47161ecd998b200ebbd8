{"_id": "yauzl", "_rev": "77-7a7fa399ead7c476cf17c04e01084e77", "name": "yauzl", "dist-tags": {"latest": "3.2.0"}, "versions": {"0.0.0": {"name": "yauzl", "version": "0.0.0", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@0.0.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "3fc19e3c5809c11a619ee3a3fa507a5b65002a3e", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-0.0.0.tgz", "integrity": "sha512-Vspk8XLHLfDyCxDnPYu6Co6WNiQ87rFlWaRRZEWysyqGeTWeNjCZexPzhzOblzpxU1yX/3/1HKW8CHCXbAkr4w==", "signatures": [{"sig": "MEYCIQCOJ6mwFG8POMpmyiLkdn1q3kk1NxJ2bxA24S23dNsinwIhAJfih53EwBnShgrMJqh0yBXB7sYlRINKw7QobD0/dM1p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3fc19e3c5809c11a619ee3a3fa507a5b65002a3e", "gitHead": "39b00e0bbc22d3d1134b3ce51b8a62234c70aef2", "scripts": {}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"fd-slicer": "~0.2.1"}}, "1.0.0": {"name": "yauzl", "version": "1.0.0", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@1.0.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "a9a73fafc2e47a5aa5ceb4cb3f52f81eca0ab4ab", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-1.0.0.tgz", "integrity": "sha512-nH/YB5mMoks8tcW5rcYNOgvjPDAbi+DO3iuydQxIP9g72A9tzoKo35c61nKhHBKH8RzbCDD6w8eI9kwQ3nUaIw==", "signatures": [{"sig": "MEUCIFSyTdlWce31pjBnas6UbxxHWk21TdOdV7H6S7Jcd6TNAiEA0p3AycoFzspbTSG1qc/OM1SsaqZbKoxnX517Hy0Wuzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a9a73fafc2e47a5aa5ceb4cb3f52f81eca0ab4ab", "gitHead": "7aeb10f68df659543cdf02ba4a94d93f371e4918", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "1.1.0": {"name": "yauzl", "version": "1.1.0", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@1.1.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "19630199ede8597b84d5337c633d3f42965c2061", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-1.1.0.tgz", "integrity": "sha512-oqmGhNaK44UTaOGmGlii+t+K96Gys1QmKga7BXKdUr8UEFKryEZNt2MZSGoagYaZ54uRcbvxkzSQCLxZVkLFGQ==", "signatures": [{"sig": "MEYCIQD/uEurY1MV1TPUOvyJZO7IFxNr/jS4iSKqJ5cEAcduzwIhAKbGqXT6n2eX8UwGjGpNJGnGr1PTc8OZjmFg9JAo/Hf/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "19630199ede8597b84d5337c633d3f42965c2061", "gitHead": "f71f0a1a4624602a8a435fe9e76d6f1f397560b9", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "1.1.1": {"name": "yauzl", "version": "1.1.1", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@1.1.1", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "17a02b9dc489543cab17c744b9c8c31b5aaa04e5", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-1.1.1.tgz", "integrity": "sha512-XXZ1mnZyHRGjpe/ev/M5L7POtLPnit3IlJfJMNPBV0xiu8GUxMs43ekxbVoryWfB/uRfSJAfkSu6xdy0JjyrQw==", "signatures": [{"sig": "MEQCIGMJmsxbeXoMGQWroROAH5sTz7y6G98/5iYpPjbkeMB3AiBrsntbh7nMwMdHhvDngr+6uK0O08bUl20CMgDxJy/EuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "17a02b9dc489543cab17c744b9c8c31b5aaa04e5", "gitHead": "bb3a129b0f80cc99a6d9bf875c0bbf07620aa05a", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "2.0.0": {"name": "yauzl", "version": "2.0.0", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.0.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "3e9eaf431c13d89b207a7d06d7ff8d307db0b2ff", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.0.0.tgz", "integrity": "sha512-IQCgvSlT8DPs20PIvBrQdvRqVsblE5Vut8J+3pN9OwLpyPTx+aYsRK2OO+SHGH9MvLr3F6LGbACj4RhPSxUEqg==", "signatures": [{"sig": "MEUCIQDpFTRqzpTFsMS+ei3o4kTSVoNqxrN0tIkZA8OEvlBecwIgEIuwgaN/1S8DwtgsXFP/EbfbXuCkZIApOUf1GK2dE0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3e9eaf431c13d89b207a7d06d7ff8d307db0b2ff", "gitHead": "2f6277a8f57267e4149a31d7ed60bcdd0f88a88c", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "2.0.1": {"name": "yauzl", "version": "2.0.1", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.0.1", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "ac0d1c8600609179697e57b596007d570d115fe8", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.0.1.tgz", "integrity": "sha512-xJ2xwVP/+n5vGrYbLI5YwTYX8SU8tZqT3a0Op2zzH2A1FKFBblvS+XDgdeRb+LnX47C+g2prTNMNQc3TKYf5ig==", "signatures": [{"sig": "MEUCIQC+VkCTGFyvV7rcnn6Zs7S8DIgFkYoYzlqNQDDUEDkrhQIgCauKLE4aIAE5opXFt7PVm/F1IBCIGfylX9Kfy5uPFc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.3.10", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "2.0.2": {"name": "yauzl", "version": "2.0.2", "keywords": ["unzip"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.0.2", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "6bbf06ad056de81d741ba99aed061036e8a509a6", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.0.2.tgz", "integrity": "sha512-NSxHOuC1VbZ5+dZbHw32Ni12xtrP7sKGqSIlJjrQSK1BKsVYML2NvDSEv93OrMpUShdQMOM6xL3OSLotmWaxEw==", "signatures": [{"sig": "MEUCIBc+nc+Z/Sik7Bd4fjB0zSWMuAJzCz6aC18EJZ1jkBijAiEA9kCbe4UOGKgAL+iMbTY3kTiIO/N0Ezn83FHdcJrmkDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6bbf06ad056de81d741ba99aed061036e8a509a6", "gitHead": "bbad333a8bbb892cbf2b4ae94e69c40210015c9d", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "2.0.3": {"name": "yauzl", "version": "2.0.3", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.0.3", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "099d0b480b3836ab8e8505f9723d87a72cd1678a", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.0.3.tgz", "integrity": "sha512-pj+lvhs8EW0D5O+kH9p7mx56K4YBOEJjD1XJ9GUHXV7iPdEQwkOQZiAywKAflzikSUv27FHWa92CLGFsksst3A==", "signatures": [{"sig": "MEUCIH4Dbvqu0sg8gx2Ab6BazIwZ+fYelfqeC4rrzU8oWR04AiEA476gLm/1EHd2AaOgKmZojJtTscx38hBrjQ5iBEwUIj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "099d0b480b3836ab8e8505f9723d87a72cd1678a", "gitHead": "997de8421b3ccdbbc3fa6a2cb1136db60ad6b450", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "iconv": "~2.1.4", "fd-slicer": "~0.2.1"}}, "2.1.0": {"name": "yauzl", "version": "2.1.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.1.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "4597cec4308d8040e93d500a4c78599dc40d874c", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.1.0.tgz", "integrity": "sha512-jMSlfEp628q/Q6QhMJu1quP00ZfmzANfCgq5T/CoouYHj2RCaipZDZfYyHrJNMivJ+GiGcm0Hdk1d6xxvzI6KQ==", "signatures": [{"sig": "MEQCIEUntBDuEBFKtwjE0jFHQxtNhMPBOwzGgCowWmWDm1qgAiA+BPXlTyi974qweGnncEd3NLvgz8WxgekxdLtAsYC6YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4597cec4308d8040e93d500a4c78599dc40d874c", "gitHead": "457392ff3f6cfa0c2d64ab84d4cc2da06bb05511", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.1.3", "fd-slicer": "~0.2.1"}}, "2.2.0": {"name": "yauzl", "version": "2.2.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.2.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "860af8c8593ea75da5d6da5a7a00db8a5996baa1", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.2.0.tgz", "integrity": "sha512-EwYSrTPYjM0zTN9yE4xHiV0+wuiFxeCJ8kC//vYNrfpfS6aXnv7JO+fQ+6RDwVkOfsaiwLQSzBrVY1tFOum7Tw==", "signatures": [{"sig": "MEYCIQDfAEdyvZ2Ne6ZVApH2A94B8LrAum/ik2zx1LXh5kXULwIhAInjXRk3fkD2+o/DFbzSTR5cdiyv01xJG9eUlhQqQAqY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "860af8c8593ea75da5d6da5a7a00db8a5996baa1", "gitHead": "7013c5e2bbac6a5ae3788cb531723156b2090172", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.2.0", "fd-slicer": "~1.0.0"}, "devDependencies": {"istanbul": "~0.3.4"}}, "2.2.1": {"name": "yauzl", "version": "2.2.1", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.2.1", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "ff5a30c3b30801139c6675e39ea52eea7b7a2354", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.2.1.tgz", "integrity": "sha512-atJR9acNmC0N7IKUf2LVRg0LkGnqMGe1vFz+OL5ptb1pKqtGnsmc5YWjRlyvEgfNNluPsMfQI+c28cEhrtoPWg==", "signatures": [{"sig": "MEUCIQDitToLprSOYXQiFFZt9GqRD/isvt8AppHD/3LZvfsSwAIgPa8QKF037j6HQu0UobgkNFsy6JoQHvDZMFi0YRuJku8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ff5a30c3b30801139c6675e39ea52eea7b7a2354", "gitHead": "1201e26aae88a869d9a5dd2b99779331b8f2f467", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "superjoe", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.2.0", "fd-slicer": "~1.0.1"}, "devDependencies": {"istanbul": "~0.3.4"}}, "2.3.0": {"name": "yauzl", "version": "2.3.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.3.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "0e4c6861239419fa449a5f90e96911c2c8e2f66c", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.3.0.tgz", "integrity": "sha512-tUddg3UIpGe/c8VYpnWYR7oznCLw6J5bjImsMTNnj/ztBUK2nTBCL9CbM393lmx3Y0iVq5XxW3hL2OLUxfBxUA==", "signatures": [{"sig": "MEQCIDguntFn5WA2ZLUux43bwrZbtRD4MNGxA8dfivVm1JH5AiB71thcohIYRudTY7OWVn2pXUOAFiM/F6RzAAAKLlR5/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0e4c6861239419fa449a5f90e96911c2c8e2f66c", "gitHead": "1ff4bfcd1e270a3807fbcf0630cb2eaf207a5247", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.2.0", "fd-slicer": "~1.0.1"}, "devDependencies": {"istanbul": "~0.3.4"}}, "2.3.1": {"name": "yauzl", "version": "2.3.1", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.3.1", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "6707fe2b6a4dac9445cc429bf04a11c7dedfa36a", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.3.1.tgz", "integrity": "sha512-Ub15RmGPuDvneRqS0hAAeYDePa6Q0wgEXNfC4r06nCQgDm7kHbRpg+sotUvWBd1etFnj9yt7ffmXT1v1pxugkQ==", "signatures": [{"sig": "MEUCIHA20JTlv8lKtKzGBBNEPYQs9ZI8SQczG1Z4dIyeYF3YAiEA39hhslA3vNH4P0b8E3gKlXNl+xqWKb/bpUTeEIWicwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6707fe2b6a4dac9445cc429bf04a11c7dedfa36a", "gitHead": "4c86fdb8a3467d68adfa1223fa1d656fe4125b40", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "yet another unzip library for node", "directories": {}, "dependencies": {"pend": "~1.2.0", "fd-slicer": "~1.0.1"}, "devDependencies": {"istanbul": "~0.3.4"}}, "2.4.0": {"name": "yauzl", "version": "2.4.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.4.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "2bae234a1816e133a1ed7efe21b2e0442ead02ad", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.0.tgz", "integrity": "sha512-+wp1yUDt6v1jXBGAAa9jNvUibZsR8dZlHsSvkBkLJmB2aO/ktzShtvRzL57UoYrvDPww5C4RLjMlWos7zQCoHQ==", "signatures": [{"sig": "MEQCIFlEcqaIjkiylJJylhXcOl8pgbOpv6JyhOnHQHnooB8BAiBZHE7WkuBqeFZf3PiVAk8jycm1h9MuqaYENZ7gUOhQ6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2bae234a1816e133a1ed7efe21b2e0442ead02ad", "gitHead": "579ed80e3fdfbf4d6426bf17676f642130394278", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"fd-slicer": "~1.0.1"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}}, "2.4.1": {"name": "yauzl", "version": "2.4.1", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.4.1", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "9528f442dab1b2284e58b4379bb194e22e0c4005", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz", "integrity": "sha512-TXNR2Feu/p/8k5YRy4z45wCUhoncIrZywmRd+xW0IvB3lWTAM7F6wVbeJvRjO0dplQ8oqmJEj/TpJuULBV/hbw==", "signatures": [{"sig": "MEUCIQD5yNlOVQeS1WnyZeN/3jbwCQ0Xk07mxCqnez3hZsdBqQIgMDrpTrj4vlicgruySHSFVUEJ6jYru/xJG+K5ReXbvJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9528f442dab1b2284e58b4379bb194e22e0c4005", "gitHead": "d528c3ad49efd27986ee661159fade32617cedb6", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"fd-slicer": "~1.0.1"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}}, "2.4.2": {"name": "yauzl", "version": "2.4.2", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.4.2", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "e0a1e49e88bfb1ae57c8aa3a9d27e31dcca0b0a0", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.2.tgz", "integrity": "sha512-6wdV/BqTpHzVy8cK/Jvb6tIeVdNQxWR9lSzhl7TMwthNyRZcz0cjV4NT2qkLHPyjtD8LGwAXyIbpJUpaOwhMtQ==", "signatures": [{"sig": "MEQCIH1A01nxdjARXUEttYwAwRJ1xEkME9/imW3HsNu1QNLpAiBRycm1YVhtJfOssO1iYwyCwhb8H9eavCn//ic1bBrVhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "e0a1e49e88bfb1ae57c8aa3a9d27e31dcca0b0a0", "gitHead": "690f5742f1c94b875c60744f46656eec4a1afccb", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"fd-slicer": "~1.0.1"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.4.2.tgz_1462819198837_0.8542271046899259", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.3": {"name": "yauzl", "version": "2.4.3", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.4.3", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "5d9b6c0fc3be961ea0412122843016fdcf7c35bc", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.3.tgz", "integrity": "sha512-IsI7F1VxvOR79a60c9Dn5qj5HSxWyb/KAFuw7QJ+ctDElyuQEKkauVEhvjC/JwOKGNX6zVTNvn3VBoVlqAArtA==", "signatures": [{"sig": "MEUCIQC2dmHP0Rup5svLBwDeSNzXBBt0KwLlMwgqPumwM87KfgIgWqZZqvKPx8ZO68UceM1katdrxiHcvdwXD8E1lKEL3is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "5d9b6c0fc3be961ea0412122843016fdcf7c35bc", "gitHead": "a525eed80a3eaf22108edd2462dc00f7ae13b113", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"fd-slicer": "~1.0.1"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.4.3.tgz_1465055041942_0.27617780142463744", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.0": {"name": "yauzl", "version": "2.5.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.5.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "17236c4819e8ecc4f0fe4e6c4945675b6f591883", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.5.0.tgz", "integrity": "sha512-IbxCRMFF8bGVH6Et2hssDWQdcGbGuP4+bPhbIWvQn/1rB8gLXi1oaJeLwJMJ+wN/gYQthP5ke4sPuMrSy6Pcvw==", "signatures": [{"sig": "MEQCIGyISwzHZB3ppnYn7qFazMvlIvxeQiuVQ8SEW2UA0uY6AiApg9+UewCwt9TmQnVQRvmrHuSvuGP5h1ij8R7NG4xyTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "17236c4819e8ecc4f0fe4e6c4945675b6f591883", "gitHead": "285584687cbc322777bd9e9ce1f5f0a5ef2dbe60", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"fd-slicer": "~1.0.1"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.5.0.tgz_1465366103059_0.3114269031211734", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.0": {"name": "yauzl", "version": "2.6.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.6.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "40894d4587bb125500d05df45471cd5e114a76f9", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.6.0.tgz", "integrity": "sha512-DM1HqrX3aGjanLPxPFznACCYYwKFZwsMBlHhPzRZo9Kzphwa5AMegSRFdSt8UdYZ9xrXYm1pgIKSuQtoK6ZsLA==", "signatures": [{"sig": "MEQCIFtcct9n9oyiqWXnr9gGyyR9vVzoeV4e8ta6hFvpaW4vAiAX6vYEIush/YLaKhnqge0YO65kT/Oy974G+37/NTAA6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "40894d4587bb125500d05df45471cd5e114a76f9", "gitHead": "1efb566ea8407ef48b72aa83b9aa586ffb69802a", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"fd-slicer": "~1.0.1", "buffer-crc32": "~0.2.3"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.6.0.tgz_1466241272314_0.022158635081723332", "host": "packages-16-east.internal.npmjs.com"}}, "2.7.0": {"name": "yauzl", "version": "2.7.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.7.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "e21d847868b496fc29eaec23ee87fdd33e9b2bce", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.7.0.tgz", "integrity": "sha512-Va3zHtr8LlgGA793wwelHBRqUy8EFStjxv80VpBRuvgK6twAn4L7aPs/M7S0tVFbR3LXsIqAPZRbCDbKDZlGhg==", "signatures": [{"sig": "MEUCIE1gP1SHzgC2+LZQgmtJeuPy829rWdmjXKbTQhFBcp7UAiEAkKwx29VmbKuPc0YHI5TpyHaN0SCO4gRb5EMJrAwUb0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "e21d847868b496fc29eaec23ee87fdd33e9b2bce", "gitHead": "1f5cb3dc6f4e9d4df06e96c665af52e576616cc3", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "4.2.6", "dependencies": {"fd-slicer": "~1.0.1", "buffer-crc32": "~0.2.3"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.7.0.tgz_1477366320782_0.2824456379748881", "host": "packages-12-west.internal.npmjs.com"}}, "2.8.0": {"name": "yauzl", "version": "2.8.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.8.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "79450aff22b2a9c5a41ef54e02db907ccfbf9ee2", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.8.0.tgz", "integrity": "sha512-p4Iu7j4w12LC7/wcpTZ9IXuTfUyoBwt4BV/OWEhgr9GzG7BgmY9Ddw0XL/z9uiG6u3kzjINAXV4trxRviKGRoQ==", "signatures": [{"sig": "MEUCIQDsVYW81an5fb+D42hZvzRXLnwLXL4cjJp2zi3hNTBRhgIgEKxz1KX/P5qAIGzVmOkHf/wyyI3HUZiIj5vSw8JMwZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "79450aff22b2a9c5a41ef54e02db907ccfbf9ee2", "gitHead": "1107f1a42f39ff73c2f2b95c2824d1eba3a2955e", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"fd-slicer": "~1.0.1", "buffer-crc32": "~0.2.3"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.8.0.tgz_1492887021651_0.6359197890851647", "host": "packages-18-east.internal.npmjs.com"}}, "2.9.0": {"name": "yauzl", "version": "2.9.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.9.0", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "927aa84392eed053124496b6ea8fc1f52dae5b52", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.9.0.tgz", "integrity": "sha512-U5y8099OaeklIbkqAHjhEqSADwQUzL3xlcf+F5R6Ep9X50d802/42uMrjBZ0DEe6i4dPhdLKZlDLlzZjxvQaRA==", "signatures": [{"sig": "MEYCIQDooqzbzVrbB8joWqs5R/wRjQQli5vd5rlelFStDTN5SQIhAOruWtmvWoOi/dd7zjMUjXptefBgGkaKsWH14OsnrwJ7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "927aa84392eed053124496b6ea8fc1f52dae5b52", "gitHead": "3c40b76fa0483bef734436d8f7739521cdb95a86", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {"fd-slicer": "~1.0.1", "buffer-crc32": "~0.2.3"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.9.0.tgz_1509224055243_0.8155409498140216", "host": "s3://npm-registry-packages"}}, "2.9.1": {"name": "yauzl", "version": "2.9.1", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.9.1", "maintainers": [{"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, {"name": "superjoe", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "a81981ea70a57946133883f029c5821a89359a7f", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.9.1.tgz", "integrity": "sha512-tOFjaiYI4cNrDuqujDv5G1KdCmGtuIULZqLv263CCADNQlNInl8sJPD+Gf3neEVecFQ0sw6D4oJTI/dqlunkSw==", "signatures": [{"sig": "MEQCIADpvDVsH9XRhc+dlgi1PyG/U6fZ0ufeJ2sWCxn+eRiBAiAnLi+4Wn9m2RXvDJLkT8kxI7VC51Pbdo4yFIdRWVysDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "a81981ea70a57946133883f029c5821a89359a7f", "gitHead": "7d619bf4e498d0bc9164f8644cb8df18727ce98b", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "7.10.1", "dependencies": {"fd-slicer": "~1.0.1", "buffer-crc32": "~0.2.3"}, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl-2.9.1.tgz_1509364572952_0.6192978695034981", "host": "s3://npm-registry-packages"}}, "2.9.2": {"name": "yauzl", "version": "2.9.2", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.9.2", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "4fb1bc7ae1fc2f57037b54af6acc8fe1031c5b77", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.9.2.tgz", "fileCount": 4, "integrity": "sha512-yyeepe1mQuFKOWHNhvnLzoPPyjjunG8XY/BtRIFAcyyYe4w24diqUHnR4T+ZzfhH/Vas/wk3uClYNgu+GKLYhg==", "signatures": [{"sig": "MEUCIAf6LNgiTMMFvXiUwirWLbDrKvn4wTuaRE4HR0lUGlC7AiEAmWoG9hXjKAcQozsOIjUz7HUGLr2jrY1Q02xIiwCumgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFHn2CRA9TVsSAnZWagAAQWUP/3JDEpwpvu4FG2qSvW8J\nvDDvKJuw1ZyqHGkYByDmu/hoe11Yx5mBIyA5OxGBJxHx71yFJsmPYW9/omdV\nZyv5643pUQR+rJpMslRfqQ7necr5zrUsWagH3pPkRi5glWfn7fHWSztk9HRy\nXXmd8v9brGNnSx1SMIH0jqSw/l3/s9xpRm1CDYsFx2B05r+tqB/C5LlqDTVA\nbUd5RUBvBrxiLwOiW8B1gwS7HI9JxmVbvlXIxMUgFgQdj6QAbLOEEY4Kcmhy\nJY9Lo8Ce7aMBf2KRb9UoFRnycm9dHBJfspPR4X8ib8L1pB1icyJyveaVzfhu\n0rxN2HginHJUmHlcIR+OtTGRkQLUYlfBn0od++uTmouNj3f5gCoxBrj+AF3/\n2qbYse9g+KpAOp3coB22bsaSu1zqwBEjE2kCp0zPvVdM1I4y+U+xaF6imcZ1\nrLRGuHAXcvLDeXt8bIY9ZyVEW8YWTWvyXOanB0PASMNf5rE6U6Pmk4zz9QGf\nnlmxopbN4Ddevvmum5bZ+tZFP2QWMePFS391bUQ3GWIKJ6EqvBSrCnNr8KFx\nH0m2wbP2BVfL75B20XH7G6xShhqtmJf9LGFo1jNtb6R6AVRnDp9S9lnBoR40\nzY8rvtLDGAX5ubPEnqthK5NA4pQABFju5gR32PLoVBIGHlyY2lMQvsTBS/J9\nnwv/\r\n=AaIu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "4fb1bc7ae1fc2f57037b54af6acc8fe1031c5b77", "gitHead": "3e0fa59ab8a3e85149f6e9efae587580f4af1e2b", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"fd-slicer": "~1.1.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.9.2_1528068596852_0.6286468921668902", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "yauzl", "version": "2.10.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@2.10.0", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz", "fileCount": 4, "integrity": "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==", "signatures": [{"sig": "MEQCIEUZ5+w7fNwaAEsEJNd+8V+QaBdI3+wLofpLbc+EbP/LAiB4H1uLiLMnNz7T5FQZiw96sdvebEPlec0e9IX3XvrE7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66204}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9", "gitHead": "51010ce4e8c7e6345efe195e1b4150518f37b393", "scripts": {"test": "node test/test.js", "test-cov": "istanbul cover test/test.js", "test-travis": "istanbul cover --report lcovonly test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"fd-slicer": "~1.1.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "~1.0.0", "pend": "~1.2.0", "istanbul": "~0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.10.0_1530590433265_0.04141917659922112", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "yauzl", "version": "3.0.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@3.0.0", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "5c317a3a26d243bd755dc089a3a4070dc809effa", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-6LiN2QaAWal/+uTf0N5nt4oAsSgddNO69kkLF0lXFY8J8Zt9WrcJq9saIIsg9vyOAnQ9m/dxoyPRMzo3G5BZHg==", "signatures": [{"sig": "MEYCIQCYZ0tn8spuPHnMESEbo3iL1lKCGerYGdQI8pl5nyR4EQIhAOMeZMMIIWeSWlpuQjXJKvtsCdwvv6MSy2FU8bFnmEb7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76351}, "main": "index.js", "gitHead": "2d7c244cba8156e0015538972272f941bd543925", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"pend": "~1.2.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^6.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_3.0.0_1707987322362_0.32814521826369525", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "yauzl", "version": "3.1.0", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@3.1.0", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "6ebf3c3bb11372dd45e458e78aa5571a9a422223", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-zbff6SaAPyewVextulqeBjJm+1ZhS69vSN7cRpqVD7jMNSE9oXEdQ1SGF+ydfB+gKE2a3GiWfXf/pnwVZ1/tOA==", "signatures": [{"sig": "MEUCIE9MtyAHvd5MlKbePGVFzv+EkVxYyYnhcvVrNWmS1d2aAiEAoPZgmTOez0Cg/tFQzVNGCABDAtz1fFfWqW9GHEGqwns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86838}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "256f5b73a2d89fececd96622faebbbd9a7f3a801", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"pend": "~1.2.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^6.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_3.1.0_1708305232747_0.8142025128934722", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "yauzl", "version": "3.1.1", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@3.1.1", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "d85503cc34933c0bcb3646ee2b97afedbebe32e7", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-MPxA7oN5cvGV0wzfkeHKF2/+Q4TkMpHSWGRy/96I4Cozljmx0ph91+Muxh6HegEtDC4GftJ8qYDE51vghFiEYA==", "signatures": [{"sig": "MEUCIBn1o5+dDmQc1LlvJwYrfmao+cxX3cgUN93pT5jp2bJXAiEA9CqNSxhxppQSRuC9tZ/hYJqf68cgMS4uyEkB0D+R9Zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88599}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "d4476cca7a490ba735f696fc742b10d83f04c0ac", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"pend": "~1.2.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^6.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_3.1.1_1709043952710_0.8224384116454182", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "yauzl", "version": "3.1.2", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@3.1.2", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "f3f3d3bdb8b98fbd367e37e1596ad45210da1533", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-621iCPgEG1wXViDZS/L3h9F8TgrdQV1eayJlJ8j5A2SZg8OdY/1DLf+VxNeD+q5QbMFEAbjjR8nITj7g4nKa0Q==", "signatures": [{"sig": "MEUCIBZ7wi+ybqzzNoqgZDjaWeRWx0tabvr4QbhWBSEB/5pfAiEA+WLoogw54DWmLGYyV/R22fmIq3jbMb1Z0QktZkt41VQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88482}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "e0da07592c61216cb325b301c3d3eaa9a5425760", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"pend": "~1.2.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^6.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_3.1.2_1709641102823_0.6019493191906029", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "yauzl", "version": "3.1.3", "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "yauzl@3.1.3", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/thejoshwolfe/yauzl", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "dist": {"shasum": "f61c17ad1a09403bc7adb01dfb302a9e74bf4a50", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-3.1.3.tgz", "fileCount": 5, "integrity": "sha512-JCCdmlJJWv7L0q/KylOekyRaUrdEoUxWkWVcgorosTROCFWiS9p2NNPE9Yb91ak7b1N5SxAZEliWpspbZccivw==", "signatures": [{"sig": "MEYCIQCwNQ5arrlArxhFkomcdUBtG7s8wA+I2jEaHm3+BoCj5AIhAKXZQGBvOSVKyf0t3Q7uXc2ZtG/kYZrpbuy16ubX3c+q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90125}, "main": "index.js", "engines": {"node": ">=12"}, "gitHead": "b15f865c219536693108f13266bbd9d658fd6f66", "scripts": {"test": "node test/test.js"}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/thejoshwolfe/yauzl.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "yet another unzip library for node", "directories": {}, "_nodeVersion": "18.19.1", "dependencies": {"pend": "~1.2.0", "buffer-crc32": "~0.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"bl": "^6.0.11"}, "_npmOperationalInternal": {"tmp": "tmp/yauzl_3.1.3_1713525212330_0.1064063609456194", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "yauzl", "version": "3.2.0", "description": "yet another unzip library for node", "engines": {"node": ">=12"}, "main": "index.js", "scripts": {"test": "node test/test.js"}, "repository": {"type": "git", "url": "git+https://github.com/thejoshwolfe/yauzl.git"}, "keywords": ["unzip", "zip", "stream", "archive", "file"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "homepage": "https://github.com/thejoshwolfe/yauzl", "dependencies": {"buffer-crc32": "~0.2.3", "pend": "~1.2.0"}, "devDependencies": {"bl": "^6.0.11"}, "_id": "yauzl@3.2.0", "gitHead": "6eb82421a7e0ac74825bfb80ee473b8e0ae64276", "_nodeVersion": "20.15.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-Ow9nuGZE+qp1u4JIPvg+uCiUr7xGQWdff7JQSk5VGYTAZMDe2q8lxJ10ygv10qmSj031Ty/6FNJpLO4o1Sgc+w==", "shasum": "7b6cb548f09a48a6177ea0be8ece48deb7da45c0", "tarball": "https://registry.npmjs.org/yauzl/-/yauzl-3.2.0.tgz", "fileCount": 5, "unpackedSize": 96267, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHkCaWhuAK6N47SPbuB+pezuW8vaUTfWTdjmQwCHsOSWAiEAs6+lizZH6jOVxxDjSEh9Fn/4f45xtd0QuaGOum4at1Y="}]}, "_npmUser": {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yauzl_3.2.0_1730598473247_0.680506164724545"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-08-22T05:43:04.937Z", "modified": "2024-11-03T01:47:53.632Z", "0.0.0": "2014-08-22T05:43:04.937Z", "1.0.0": "2014-08-27T08:55:05.424Z", "1.1.0": "2014-08-27T09:44:14.077Z", "1.1.1": "2014-09-03T03:17:05.930Z", "2.0.0": "2014-09-20T08:04:05.875Z", "2.0.1": "2014-09-29T19:26:01.654Z", "2.0.2": "2014-10-13T04:57:22.130Z", "2.0.3": "2014-10-16T00:12:41.642Z", "2.1.0": "2014-10-16T09:51:30.706Z", "2.2.0": "2014-12-03T23:41:36.343Z", "2.2.1": "2015-01-12T16:35:37.427Z", "2.3.0": "2015-04-29T23:30:17.362Z", "2.3.1": "2015-05-15T22:56:34.901Z", "2.4.0": "2015-12-31T19:04:12.173Z", "2.4.1": "2015-12-31T20:01:42.865Z", "2.4.2": "2016-05-09T18:40:01.386Z", "2.4.3": "2016-06-04T15:44:04.466Z", "2.5.0": "2016-06-08T06:08:25.517Z", "2.6.0": "2016-06-18T09:14:34.970Z", "2.7.0": "2016-10-25T03:32:01.720Z", "2.8.0": "2017-04-22T18:50:23.563Z", "2.9.0": "2017-10-28T20:54:15.543Z", "2.9.1": "2017-10-30T11:56:13.119Z", "2.9.2": "2018-06-03T23:29:57.055Z", "2.10.0": "2018-07-03T04:00:33.361Z", "3.0.0": "2024-02-15T08:55:22.525Z", "3.1.0": "2024-02-19T01:13:52.944Z", "3.1.1": "2024-02-27T14:25:52.883Z", "3.1.2": "2024-03-05T12:18:23.033Z", "3.1.3": "2024-04-19T11:13:32.510Z", "3.2.0": "2024-11-03T01:47:53.478Z"}, "bugs": {"url": "https://github.com/thejoshwolfe/yauzl/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/thejoshwolfe/yauzl", "keywords": ["unzip", "zip", "stream", "archive", "file"], "repository": {"type": "git", "url": "git+https://github.com/thejoshwolfe/yauzl.git"}, "description": "yet another unzip library for node", "maintainers": [{"name": "superjoe", "email": "<EMAIL>"}, {"name": "<PERSON>joshwolf<PERSON>", "email": "<EMAIL>"}], "readme": "# yauzl\n\nyet another unzip library for node. For zipping, see\n[yazl](https://github.com/thejoshwolfe/yazl).\n\nDesign principles:\n\n * Follow the spec.\n   Don't scan for local file headers.\n   Read the central directory for file metadata.\n   (see [No Streaming Unzip API](#no-streaming-unzip-api)).\n * Don't block the JavaScript thread.\n   Use and provide async APIs.\n * Keep memory usage under control.\n   Don't attempt to buffer entire files in RAM at once.\n * Never crash (if used properly).\n   Don't let malformed zip files bring down client applications who are trying to catch errors.\n * Catch unsafe file names.\n   See `validateFileName()`.\n\n## Usage\n\n```js\nvar yauzl = require(\"yauzl\");\n\nyauzl.open(\"path/to/file.zip\", {lazyEntries: true}, function(err, zipfile) {\n  if (err) throw err;\n  zipfile.readEntry();\n  zipfile.on(\"entry\", function(entry) {\n    if (/\\/$/.test(entry.fileName)) {\n      // Directory file names end with '/'.\n      // Note that entries for directories themselves are optional.\n      // An entry's fileName implicitly requires its parent directories to exist.\n      zipfile.readEntry();\n    } else {\n      // file entry\n      zipfile.openReadStream(entry, function(err, readStream) {\n        if (err) throw err;\n        readStream.on(\"end\", function() {\n          zipfile.readEntry();\n        });\n        readStream.pipe(somewhere);\n      });\n    }\n  });\n});\n```\n\nSee also `examples/` for more usage examples.\n\n## API\n\nThe default for every optional `callback` parameter is:\n\n```js\nfunction defaultCallback(err) {\n  if (err) throw err;\n}\n```\n\n### open(path, [options], [callback])\n\nCalls `fs.open(path, \"r\")` and reads the `fd` effectively the same as `fromFd()` would.\n\n`options` may be omitted or `null`. The defaults are `{autoClose: true, lazyEntries: false, decodeStrings: true, validateEntrySizes: true, strictFileNames: false}`.\n\n`autoClose` is effectively equivalent to:\n\n```js\nzipfile.once(\"end\", function() {\n  zipfile.close();\n});\n```\n\n`lazyEntries` indicates that entries should be read only when `readEntry()` is called.\nIf `lazyEntries` is `false`, `entry` events will be emitted as fast as possible to allow `pipe()`ing\nfile data from all entries in parallel.\nThis is not recommended, as it can lead to out of control memory usage for zip files with many entries.\nSee [issue #22](https://github.com/thejoshwolfe/yauzl/issues/22).\nIf `lazyEntries` is `true`, an `entry` or `end` event will be emitted in response to each call to `readEntry()`.\nThis allows processing of one entry at a time, and will keep memory usage under control for zip files with many entries.\n\n`decodeStrings` is the default and causes yauzl to decode strings with `CP437` or `UTF-8` as required by the spec.\nThe exact effects of turning this option off are:\n\n* `zipfile.comment`, `entry.fileName`, and `entry.fileComment` will be `Buffer` objects instead of `String`s.\n* Any Info-ZIP Unicode Path Extra Field will be ignored. See `extraFields`.\n* Automatic file name validation will not be performed. See `validateFileName()`.\n\n`validateEntrySizes` is the default and ensures that an entry's reported uncompressed size matches its actual uncompressed size.\nThis check happens as early as possible, which is either before emitting each `\"entry\"` event (for entries with no compression),\nor during the `readStream` piping after calling `openReadStream()`.\nSee `openReadStream()` for more information on defending against zip bomb attacks.\n\nWhen `strictFileNames` is `false` (the default) and `decodeStrings` is `true`,\nall backslash (`\\`) characters in each `entry.fileName` are replaced with forward slashes (`/`).\nThe spec forbids file names with backslashes,\nbut Microsoft's `System.IO.Compression.ZipFile` class in .NET versions 4.5.0 until 4.6.1\ncreates non-conformant zipfiles with backslashes in file names.\n`strictFileNames` is `false` by default so that clients can read these\nnon-conformant zipfiles without knowing about this Microsoft-specific bug.\nWhen `strictFileNames` is `true` and `decodeStrings` is `true`,\nentries with backslashes in their file names will result in an error. See `validateFileName()`.\nWhen `decodeStrings` is `false`, `strictFileNames` has no effect.\n\nThe `callback` is given the arguments `(err, zipfile)`.\nAn `err` is provided if the End of Central Directory Record cannot be found, or if its metadata appears malformed.\nThis kind of error usually indicates that this is not a zip file.\nOtherwise, `zipfile` is an instance of `ZipFile`.\n\n### fromFd(fd, [options], [callback])\n\nReads from the fd, which is presumed to be an open .zip file.\nNote that random access is required by the zip file specification,\nso the fd cannot be an open socket or any other fd that does not support random access.\n\n`options` may be omitted or `null`. The defaults are `{autoClose: false, lazyEntries: false, decodeStrings: true, validateEntrySizes: true, strictFileNames: false}`.\n\nSee `open()` for the meaning of the options and callback.\n\n### fromBuffer(buffer, [options], [callback])\n\nLike `fromFd()`, but reads from a RAM buffer instead of an open file.\n`buffer` is a `Buffer`.\n\nIf a `ZipFile` is acquired from this method,\nit will never emit the `close` event,\nand calling `close()` is not necessary.\n\n`options` may be omitted or `null`. The defaults are `{lazyEntries: false, decodeStrings: true, validateEntrySizes: true, strictFileNames: false}`.\n\nSee `open()` for the meaning of the options and callback.\nThe `autoClose` option is ignored for this method.\n\n### fromRandomAccessReader(reader, totalSize, [options], [callback])\n\nThis method of reading a zip file allows clients to implement their own back-end file system.\nFor example, a client might translate read calls into network requests.\n\nThe `reader` parameter must be of a type that is a subclass of\n[RandomAccessReader](#class-randomaccessreader) that implements the required methods.\nThe `totalSize` is a Number and indicates the total file size of the zip file.\n\n`options` may be omitted or `null`. The defaults are `{autoClose: true, lazyEntries: false, decodeStrings: true, validateEntrySizes: true, strictFileNames: false}`.\n\nSee `open()` for the meaning of the options and callback.\n\n### dosDateTimeToDate(date, time)\n\n*Deprecated*. Since yauzl 3.2.0, it is highly recommended to call [`entry.getLastModDate()`](#getlastmoddateoptions)\ninstead of this function due to enhanced support for reading third-party extra fields.\nIf you ever have a use case for calling this function directly please\n[open an issue against yauzl](https://github.com/thejoshwolfe/yauzl/issues/new)\nrequesting that this function be properly supported again.\n\nThis function only remains exported in order to maintain compatibility with older version of yauzl.\nIt will be removed in yauzl 4.0.0 unless someone asks for it to remain supported.\n\n### getFileNameLowLevel(generalPurposeBitFlag, fileNameBuffer, extraFields, strictFileNames)\n\nIf you are setting `decodeStrings` to `false`, then this function can be used to decode the file name yourself.\nThis function is effectively used internally by yauzl to populate the `entry.fileName` field when `decodeStrings` is `true`.\n\nWARNING: This method of getting the file name bypasses the security checks in [`validateFileName()`](#validatefilename-filename).\nYou should call that function yourself to be sure to guard against malicious file paths.\n\n`generalPurposeBitFlag` can be found on an [`Entry`](#class-entry) or [`LocalFileHeader`](#class-localfileheader).\nOnly General Purpose Bit 11 is used, and only when an Info-ZIP Unicode Path Extra Field cannot be found in `extraFields`.\n\n`fileNameBuffer` is a `Buffer` representing the file name field of the entry.\nThis is `entry.fileNameRaw` or `localFileHeader.fileName`.\n\n`extraFields` is the parsed extra fields array from `entry.extraFields` or `parseExtraFields()`.\n\n`strictFileNames` is a boolean, the same as the option of the same name in `open()`.\nWhen `false`, backslash characters (`\\`) will be replaced with forward slash characters (`/`).\n\nThis function always returns a string, although it may not be a valid file name.\nSee `validateFileName()`.\n\n### validateFileName(fileName)\n\nReturns `null` or a `String` error message depending on the validity of `fileName`.\nIf `fileName` starts with `\"/\"` or `/[A-Za-z]:\\//` or if it contains `\"..\"` path segments or `\"\\\\\"`,\nthis function returns an error message appropriate for use like this:\n\n```js\nvar errorMessage = yauzl.validateFileName(fileName);\nif (errorMessage != null) throw new Error(errorMessage);\n```\n\nThis function is automatically run for each entry, as long as `decodeStrings` is `true`.\nSee `open()`, `strictFileNames`, and `Event: \"entry\"` for more information.\n\n### parseExtraFields(extraFieldBuffer)\n\nThis function is used internally by yauzl to compute [`entry.extraFields`](#extrafields).\nIt is exported in case you want to call it on [`localFileHeader.extraField`](#class-localfileheader).\n\n`extraFieldBuffer` is a `Buffer`, such as `localFileHeader.extraField`.\nReturns an `Array` with each item in the form `{id: id, data: data}`,\nwhere `id` is a `Number` and `data` is a `Buffer`.\nThrows an `Error` if the data encodes an item with a size that exceeds the bounds of the buffer.\n\nYou may want to surround calls to this function with `try { ... } catch (err) { ... }` to handle the error.\n\n### Class: ZipFile\n\nThe constructor for the class is not part of the public API.\nUse `open()`, `fromFd()`, `fromBuffer()`, or `fromRandomAccessReader()` instead.\n\n#### Event: \"entry\"\n\nCallback gets `(entry)`, which is an `Entry`.\nSee `open()` and `readEntry()` for when this event is emitted.\n\nIf `decodeStrings` is `true`, entries emitted via this event have already passed file name validation.\nSee `validateFileName()` and `open()` for more information.\n\nIf `validateEntrySizes` is `true` and this entry's `compressionMethod` is `0` (stored without compression),\nthis entry has already passed entry size validation.\nSee `open()` for more information.\n\n#### Event: \"end\"\n\nEmitted after the last `entry` event has been emitted.\nSee `open()` and `readEntry()` for more info on when this event is emitted.\n\n#### Event: \"close\"\n\nEmitted after the fd is actually closed.\nThis is after calling `close()` (or after the `end` event when `autoClose` is `true`),\nand after all stream pipelines created from `openReadStream()` have finished reading data from the fd.\n\nIf this `ZipFile` was acquired from `fromRandomAccessReader()`,\nthe \"fd\" in the previous paragraph refers to the `RandomAccessReader` implemented by the client.\n\nIf this `ZipFile` was acquired from `fromBuffer()`, this event is never emitted.\n\n#### Event: \"error\"\n\nEmitted in the case of errors with reading the zip file.\n(Note that other errors can be emitted from the streams created from `openReadStream()` as well.)\nAfter this event has been emitted, no further `entry`, `end`, or `error` events will be emitted,\nbut the `close` event may still be emitted.\n\n#### readEntry()\n\nCauses this `ZipFile` to emit an `entry` or `end` event (or an `error` event).\nThis method must only be called when this `ZipFile` was created with the `lazyEntries` option set to `true` (see `open()`).\nWhen this `ZipFile` was created with the `lazyEntries` option set to `true`,\n`entry` and `end` events are only ever emitted in response to this method call.\n\nThe event that is emitted in response to this method will not be emitted until after this method has returned,\nso it is safe to call this method before attaching event listeners.\n\nAfter calling this method, calling this method again before the response event has been emitted will cause undefined behavior.\nCalling this method after the `end` event has been emitted will cause undefined behavior.\nCalling this method after calling `close()` will cause undefined behavior.\n\n#### openReadStream(entry, [options], callback)\n\n`entry` must be an `Entry` object from this `ZipFile`.\n`callback` gets `(err, readStream)`, where `readStream` is a `Readable Stream` that provides the file data for this entry.\nIf this zipfile is already closed (see `close()`), the `callback` will receive an `err`.\n\n`options` may be omitted or `null`, and has the following defaults:\n\n```js\n{\n  decompress: entry.isCompressed() ? true : null,\n  decrypt: null,\n  start: 0,                  // actually the default is null, see below\n  end: entry.compressedSize, // actually the default is null, see below\n}\n```\n\nIf the entry is compressed (with a supported compression method),\nand the `decompress` option is `true` (or omitted),\nthe read stream provides the decompressed data.\nOmitting the `decompress` option is what most clients should do.\n\nThe `decompress` option must be `null` (or omitted) when the entry is not compressed (see `isCompressed()`),\nand either `true` (or omitted) or `false` when the entry is compressed.\nSpecifying `decompress: false` for a compressed entry causes the read stream\nto provide the raw compressed file data without going through a zlib inflate transform.\n\nIf the entry is encrypted (see `isEncrypted()`), clients may want to avoid calling `openReadStream()` on the entry entirely.\nAlternatively, clients may call `openReadStream()` for encrypted entries and specify `decrypt: false`.\nIf the entry is also compressed, clients must *also* specify `decompress: false`.\nSpecifying `decrypt: false` for an encrypted entry causes the read stream to provide the raw, still-encrypted file data.\n(This data includes the 12-byte header described in the spec.)\n\nThe `decrypt` option must be `null` (or omitted) for non-encrypted entries, and `false` for encrypted entries.\nOmitting the `decrypt` option (or specifying it as `null`) for an encrypted entry\nwill result in the `callback` receiving an `err`.\nThis default behavior is so that clients not accounting for encrypted files aren't surprised by bogus file data.\n\nThe `start` (inclusive) and `end` (exclusive) options are byte offsets into this entry's file data,\nand can be used to obtain part of an entry's file data rather than the whole thing.\nIf either of these options are specified and non-`null`,\nthen the above options must be used to obain the file's raw data.\nSpecifying `{start: 0, end: entry.compressedSize}` will result in the complete file,\nwhich is effectively the default values for these options,\nbut note that unlike omitting the options, when you specify `start` or `end` as any non-`null` value,\nthe above requirement is still enforced that you must also pass the appropriate options to get the file's raw data.\n\nIt's possible for the `readStream` provided to the `callback` to emit errors for several reasons.\nFor example, if zlib cannot decompress the data, the zlib error will be emitted from the `readStream`.\nTwo more error cases (when `validateEntrySizes` is `true`) are if the decompressed data has too many\nor too few actual bytes compared to the reported byte count from the entry's `uncompressedSize` field.\nyauzl notices this false information and emits an error from the `readStream`\nafter some number of bytes have already been piped through the stream.\n\nThis check allows clients to trust the `uncompressedSize` field in `Entry` objects.\nGuarding against [zip bomb](http://en.wikipedia.org/wiki/Zip_bomb) attacks can be accomplished by\ndoing some heuristic checks on the size metadata and then watching out for the above errors.\nSuch heuristics are outside the scope of this library,\nbut enforcing the `uncompressedSize` is implemented here as a security feature.\n\nIt is possible to destroy the `readStream` before it has piped all of its data.\nTo do this, call `readStream.destroy()`.\nYou must `unpipe()` the `readStream` from any destination before calling `readStream.destroy()`.\nIf this zipfile was created using `fromRandomAccessReader()`, the `RandomAccessReader` implementation\nmust provide readable streams that implement a `._destroy()` method according to\nhttps://nodejs.org/api/stream.html#writable_destroyerr-callback (see `randomAccessReader._readStreamForRange()`)\nin order for calls to `readStream.destroy()` to work in this context.\n\n#### readLocalFileHeader(entry, [options], callback)\n\nThis is a low-level function you probably don't need to call.\nThe intended use case is either preparing to call `openReadStreamLowLevel()`\nor simply examining the content of the local file header out of curiosity or for debugging zip file structure issues.\n\n`entry` is an entry obtained from `Event: \"entry\"`.\nAn `entry` in this library is a file's metadata from a Central Directory Header,\nand this function gives the corresponding redundant data in a Local File Header.\n\n`options` may be omitted or `null`, and has the following defaults:\n\n```js\n{\n  minimal: false,\n}\n```\n\nIf `minimal` is `false` (or omitted or `null`), the callback receives a full `LocalFileHeader`.\nIf `minimal` is `true`, the callback receives an object with a single property and no prototype `{fileDataStart: fileDataStart}`.\nFor typical zipfile reading usecases, this field is the only one you need,\nand yauzl internally effectively uses the `{minimal: true}` option as part of `openReadStream()`.\n\nThe `callback` receives `(err, localFileHeaderOrAnObjectWithJustOneFieldDependingOnTheMinimalOption)`,\nwhere the type of the second parameter is described in the above discussion of the `minimal` option.\n\n#### openReadStreamLowLevel(fileDataStart, compressedSize, relativeStart, relativeEnd, decompress, uncompressedSize, callback)\n\nThis is a low-level function available for advanced use cases. You probably want `openReadStream()` instead.\n\nThe intended use case for this function is calling `readEntry()` and `readLocalFileHeader()` with `{minimal: true}` first,\nand then opening the read stream at a later time, possibly after closing and reopening the entire zipfile,\npossibly even in a different process.\nThe parameters are all integers and booleans, which are friendly to serialization.\n\n* `fileDataStart` - from `localFileHeader.fileDataStart`\n* `compressedSize` - from `entry.compressedSize`\n* `relativeStart` - the resolved value of `options.start` from `openReadStream()`. Must be a non-negative integer, not `null`. Typically `0` to start at the beginning of the data.\n* `relativeEnd` - the resolved value of `options.end` from `openReadStream()`. Must be a non-negative integer, not `null`. Typically `entry.compressedSize` to include all the data.\n* `decompress` - boolean indicating whether the data should be piped through a zlib inflate stream.\n* `uncompressedSize` - from `entry.uncompressedSize`. Only used when `validateEntrySizes` is `true`. If `validateEntrySizes` is `false`, this value is ignored, but must still be present, not omitted, in the arguments; you have to give it some value, even if it's `null`.\n* `callback` - receives `(err, readStream)`, the same as for `openReadStream()`\n\nThis low-level function does not read any metadata from the underlying storage before opening the read stream.\nThis is both a performance feature and a safety hazard.\nNone of the integer parameters are bounds checked.\nNone of the validation from `openReadStream()` with respect to compression and encryption is done here either.\nOnly the bounds checks from `validateEntrySizes` are done, because that is part of processing the stream data.\n\n#### close()\n\nCauses all future calls to `openReadStream()` to fail,\nand closes the fd, if any, after all streams created by `openReadStream()` have emitted their `end` events.\n\nIf the `autoClose` option is set to `true` (see `open()`),\nthis function will be called automatically effectively in response to this object's `end` event.\n\nIf the `lazyEntries` option is set to `false` (see `open()`) and this object's `end` event has not been emitted yet,\nthis function causes undefined behavior.\nIf the `lazyEntries` option is set to `true`,\nyou can call this function instead of calling `readEntry()` to abort reading the entries of a zipfile.\n\nIt is safe to call this function multiple times; after the first call, successive calls have no effect.\nThis includes situations where the `autoClose` option effectively calls this function for you.\n\nIf `close()` is never called, then the zipfile is \"kept open\".\nFor zipfiles created with `fromFd()`, this will leave the `fd` open, which may be desirable.\nFor zipfiles created with `open()`, this will leave the underlying `fd` open, thereby \"leaking\" it, which is probably undesirable.\nFor zipfiles created with `fromRandomAccessReader()`, the reader's `close()` method will never be called.\nFor zipfiles created with `fromBuffer()`, the `close()` function has no effect whether called or not.\n\nRegardless of how this `ZipFile` was created, there are no resources other than those listed above that require cleanup from this function.\nThis means it may be desirable to never call `close()` in some usecases.\n\n#### isOpen\n\n`Boolean`. `true` until `close()` is called; then it's `false`.\n\n#### entryCount\n\n`Number`. Total number of central directory records.\n\n#### comment\n\n`String`. Always decoded with `CP437` per the spec.\n\nIf `decodeStrings` is `false` (see `open()`), this field is the undecoded `Buffer` instead of a decoded `String`.\n\n### Class: Entry\n\nObjects of this class represent Central Directory Records.\nRefer to the zipfile specification for more details about these fields.\n\nThese fields are of type `Number`:\n\n * `versionMadeBy`\n * `versionNeededToExtract`\n * `generalPurposeBitFlag`\n * `compressionMethod`\n * `lastModFileTime` (MS-DOS format, see [`getLastModDate()`](#getlastmoddateoptions))\n * `lastModFileDate` (MS-DOS format, see [`getLastModDate()`](#getlastmoddateoptions))\n * `crc32`\n * `compressedSize`\n * `uncompressedSize`\n * `fileNameLength` (in bytes)\n * `extraFieldLength` (in bytes)\n * `fileCommentLength` (in bytes)\n * `internalFileAttributes`\n * `externalFileAttributes`\n * `relativeOffsetOfLocalHeader`\n\nThese fields are of type `Buffer`, and represent variable-length bytes before being processed:\n * `fileNameRaw`\n * `extraFieldRaw`\n * `fileCommentRaw`\n\nThere are additional fields described below: `fileName`, `extraFields`, `fileComment`.\nThese are the processed versions of the `*Raw` fields listed above. See their own sections below.\n(Note the inconsistency in pluralization of \"field\" vs \"fields\" in `extraField`, `extraFields`, and `extraFieldRaw`.\nSorry about that.)\n\nThe `new Entry()` constructor is available for clients to call, but it's usually not useful.\nThe constructor takes no parameters and does nothing; no fields will exist.\n\n#### fileName\n\n`String`.\nFollowing the spec, the bytes for the file name are decoded with\n`UTF-8` if `generalPurposeBitFlag & 0x800`, otherwise with `CP437`.\nAlternatively, this field may be populated from the Info-ZIP Unicode Path Extra Field\n(see `extraFields`).\n\nThis field is automatically validated by `validateFileName()` before yauzl emits an \"entry\" event.\nIf this field would contain unsafe characters, yauzl emits an error instead of an entry.\n\nIf `decodeStrings` is `false` (see `open()`), this field is the undecoded `Buffer` instead of a decoded `String`.\nTherefore, `generalPurposeBitFlag` and any Info-ZIP Unicode Path Extra Field are ignored.\nFurthermore, no automatic file name validation is performed for this file name.\n\n#### extraFields\n\n`Array` with each item in the form `{id: id, data: data}`,\nwhere `id` is a `Number` and `data` is a `Buffer`.\n\nThis library looks for and reads the ZIP64 Extended Information Extra Field (0x0001)\nin order to support ZIP64 format zip files.\n\nThis library also looks for and reads the Info-ZIP Unicode Path Extra Field (0x7075)\nin order to support some zipfiles that use it instead of General Purpose Bit 11\nto convey `UTF-8` file names.\nWhen the field is identified and verified to be reliable (see the zipfile spec),\nthe file name in this field is stored in the `fileName` property,\nand the file name in the central directory record for this entry is ignored.\nNote that when `decodeStrings` is false, all Info-ZIP Unicode Path Extra Fields are ignored.\n\nNone of the other fields are considered significant by this library.\nFields that this library reads are left unaltered in the `extraFields` array.\n\n#### fileComment\n\n`String` decoded with the charset indicated by `generalPurposeBitFlag & 0x800` as with the `fileName`.\n(The Info-ZIP Unicode Path Extra Field has no effect on the charset used for this field.)\n\nIf `decodeStrings` is `false` (see `open()`), this field is the undecoded `Buffer` instead of a decoded `String`.\n\nPrior to yauzl version 2.7.0, this field was erroneously documented as `comment` instead of `fileComment`.\nFor compatibility with any code that uses the field name `comment`,\nyauzl creates an alias field named `comment` which is identical to `fileComment`.\n\n#### getLastModDate([options])\n\nReturns the modification time of the file as a JavaScript `Date` object.\nThe timezone situation is a mess; read on to learn more.\n\nDue to the zip file specification having lackluster support for specifying timestamps natively,\nthere are several third-party extensions that add better support.\nyauzl supports these encodings:\n\n1. InfoZIP \"universal timestamp\" extended field (`0x5455` aka `\"UT\"`): signed 32-bit seconds since `1970-01-01 00:00:00Z`, which supports the years 1901-2038 (partially inclusive) with 1-second precision. The value is timezone agnostic, i.e. always UTC.\n2. NTFS extended field (`0x000a`): 64-bit signed 100-nanoseconds since `1601-01-01 00:00:00Z`, which supports the approximate years 20,000BCE-20,000CE with precision rounded to 1-millisecond (due to the JavaScript `Date` type). The value is timezone agnostic, i.e. always UTC.\n3. DOS `lastModFileDate` and `lastModFileTime`: supports the years 1980-2108 (inclusive) with 2-second precision. Timezone is interpreted either as the local timezone or UTC depending on the `timezone` option documented below.\n\nIf both the InfoZIP \"universal timestamp\" and NTFS extended fields are found, yauzl uses one of them, but which one is unspecified.\nIf neither are found, yauzl falls back to the built-in DOS `lastModFileDate` and `lastModFileTime`.\nEvery possible bit pattern of every encoding can be represented by a JavaScript `Date` object,\nmeaning this function cannot fail (barring parameter validation), and will never return an `Invalid Date` object.\n\n`options` may be omitted or `null`, and has the following defaults:\n\n```js\n{\n  timezone: \"local\", // or \"UTC\"\n  forceDosFormat: false,\n}\n```\n\nSet `forceDosFormat` to `true` (and do not set `timezone`) to enable pre-yauzl 3.2.0 behavior\nwhere the InfoZIP \"universal timestamp\" and NTFS extended fields are ignored.\n\nThe `timezone` option is only used in the DOS fallback.\nIf `timezone` is omitted, `null` or `\"local\"`, the `lastModFileDate` and `lastModFileTime` are interpreted in the system's current timezone (using `new Date(year, ...)`).\nIf `timezone` is `\"UTC\"`, the interpretation is in UTC+00:00 (using `new Date(Date.UTC(year, ...))`).\n\nThe JavaScript `Date` object, has several inherent limitations surrounding timezones.\nThere is an ECMAScript proposal to add better timezone support to JavaScript called the `Temporal` API.\nLast I checked, it was at stage 3. https://github.com/tc39/proposal-temporal\nOnce that new API is available and stable, better timezone handling should be possible here somehow.\nIf you notice that the new API has become widely available, please open a feature request against this library to add support for it.\n\n#### isEncrypted()\n\nReturns is this entry encrypted with \"Traditional Encryption\".\nEffectively implemented as:\n\n```js\nreturn (this.generalPurposeBitFlag & 0x1) !== 0;\n```\n\nSee `openReadStream()` for the implications of this value.\n\nNote that \"Strong Encryption\" is not supported, and will result in an `\"error\"` event emitted from the `ZipFile`.\n\n#### isCompressed()\n\nEffectively implemented as:\n\n```js\nreturn this.compressionMethod === 8;\n```\n\nSee `openReadStream()` for the implications of this value.\n\n### Class: LocalFileHeader\n\nThis is a trivial class that has no methods and only the following properties.\nThe constructor is available to call, but it doesn't do anything.\nSee `readLocalFileHeader()`.\n\nSee the zipfile spec for what these fields mean.\n\n * `fileDataStart` - `Number`: inferred from `fileNameLength`, `extraFieldLength`, and this struct's position in the zipfile.\n * `versionNeededToExtract` - `Number`\n * `generalPurposeBitFlag` - `Number`\n * `compressionMethod` - `Number`\n * `lastModFileTime` - `Number`\n * `lastModFileDate` - `Number`\n * `crc32` - `Number`\n * `compressedSize` - `Number`\n * `uncompressedSize` - `Number`\n * `fileNameLength` - `Number`\n * `extraFieldLength` - `Number`\n * `fileName` - `Buffer`\n * `extraField` - `Buffer`\n\nNote that unlike `Class: Entry`, the `fileName` and `extraField` are completely unprocessed.\nThis notably lacks Unicode and ZIP64 handling as well as any kind of safety validation on the file name.\nSee also [`parseExtraFields()`](#parseextrafields-extrafieldbuffer).\n\nAlso note that if your object is missing some of these fields,\nmake sure to read the docs on the `minimal` option in `readLocalFileHeader()`.\n\n### Class: RandomAccessReader\n\nThis class is meant to be subclassed by clients and instantiated for the `fromRandomAccessReader()` function.\n\nAn example implementation can be found in `test/test.js`.\n\n#### randomAccessReader._readStreamForRange(start, end)\n\nSubclasses *must* implement this method.\n\n`start` and `end` are Numbers and indicate byte offsets from the start of the file.\n`end` is exclusive, so `_readStreamForRange(0x1000, 0x2000)` would indicate to read `0x1000` bytes.\n`end - start` will always be at least `1`.\n\nThis method should return a readable stream which will be `pipe()`ed into another stream.\nIt is expected that the readable stream will provide data in several chunks if necessary.\nIf the readable stream provides too many or too few bytes, an error will be emitted.\n(Note that `validateEntrySizes` has no effect on this check,\nbecause this is a low-level API that should behave correctly regardless of the contents of the file.)\nAny errors emitted on the readable stream will be handled and re-emitted on the client-visible stream\n(returned from `zipfile.openReadStream()`) or provided as the `err` argument to the appropriate callback\n(for example, for `fromRandomAccessReader()`).\n\nIf you call `readStream.destroy()` on streams you get from `openReadStream()`,\nthe returned stream *must* implement a method `._destroy()` according to https://nodejs.org/api/stream.html#writable_destroyerr-callback .\nIf you never call `readStream.destroy()`, then streams returned from this method do not need to implement a method `._destroy()`.\n`._destroy()` should abort any streaming that is in progress and clean up any associated resources.\n`._destroy()` will only be called after the stream has been `unpipe()`d from its destination.\n\nNote that the stream returned from this method might not be the same object that is provided by `openReadStream()`.\nThe stream returned from this method might be `pipe()`d through one or more filter streams (for example, a zlib inflate stream).\n\n#### randomAccessReader.read(buffer, offset, length, position, callback)\n\nSubclasses may implement this method.\nThe default implementation uses `createReadStream()` to fill the `buffer`.\n\nThis method should behave like `fs.read()`.\n\n#### randomAccessReader.close(callback)\n\nSubclasses may implement this method.\nThe default implementation is effectively `setImmediate(callback);`.\n\n`callback` takes parameters `(err)`.\n\nThis method is called once the all streams returned from `_readStreamForRange()` have ended,\nand no more `_readStreamForRange()` or `read()` requests will be issued to this object.\n\n## How to Avoid Crashing\n\nWhen a malformed zipfile is encountered, the default behavior is to crash (throw an exception).\nIf you want to handle errors more gracefully than this,\nbe sure to do the following:\n\n * Provide `callback` parameters where they are allowed, and check the `err` parameter.\n * Attach a listener for the `error` event on any `ZipFile` object you get from `open()`, `fromFd()`, `fromBuffer()`, or `fromRandomAccessReader()`.\n * Attach a listener for the `error` event on any stream you get from `openReadStream()`.\n\nMinor version updates to yauzl will not add any additional requirements to this list.\n\n## Limitations\n\nThe automated tests for this project run on node versions 12 and up. Older versions of node are not supported.\n\n### Files corrupted by the Mac Archive Utility are not my problem\n\nFor a lengthy discussion, see [issue #69](https://github.com/thejoshwolfe/yauzl/issues/69).\nIn summary, the Mac Archive Utility is buggy when creating large zip files,\nand this library does not make any effort to work around the bugs.\nThis library will attempt to interpret the zip file data at face value,\nwhich may result in errors, or even silently incomplete data.\nIf this bothers you, that's good! Please complain to Apple. :)\nI have accepted that this library will simply not support that nonsense.\n\n### No Streaming Unzip API\n\nDue to the design of the .zip file format, it's impossible to interpret a .zip file from start to finish\n(such as from a readable stream) without sacrificing correctness.\nThe Central Directory, which is the authority on the contents of the .zip file, is at the end of a .zip file, not the beginning.\nA streaming API would need to either buffer the entire .zip file to get to the Central Directory before interpreting anything\n(defeating the purpose of a streaming interface), or rely on the Local File Headers which are interspersed through the .zip file.\nHowever, the Local File Headers are explicitly denounced in the spec as being unreliable copies of the Central Directory,\nso trusting them would be a violation of the spec.\n\nAny library that offers a streaming unzip API must make one of the above two compromises,\nwhich makes the library either dishonest or nonconformant (usually the latter).\nThis library insists on correctness and adherence to the spec, and so does not offer a streaming API.\n\nHere is a way to create a spec-conformant .zip file using the `zip` command line program (Info-ZIP)\navailable in most unix-like environments, that is (nearly) impossible to parse correctly with a streaming parser:\n\n```\n$ echo -ne '\\x50\\x4b\\x07\\x08\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00' > file.txt\n$ zip -q0 - file.txt | cat > out.zip\n```\n\nThis .zip file contains a single file entry that uses General Purpose Bit 3,\nwhich means the Local File Header doesn't know the size of the file.\nAny streaming parser that encounters this situation will either immediately fail,\nor attempt to search for the Data Descriptor after the file's contents.\nThe file's contents is a sequence of 16-bytes crafted to exactly mimic a valid Data Descriptor for an empty file,\nwhich will fool any parser that gets this far into thinking that the file is empty rather than containing 16-bytes.\nWhat follows the file's real contents is the file's real Data Descriptor,\nwhich will likely cause some kind of signature mismatch error for a streaming parser (if one hasn't occurred already).\n\nBy using General Purpose Bit 3 (and compression method 0),\nit's possible to create arbitrarily ambiguous .zip files that\ndistract parsers with file contents that contain apparently valid .zip file metadata.\n\n### Limited ZIP64 Support\n\nFor ZIP64, only zip files smaller than `8PiB` are supported,\nnot the full `16EiB` range that a 64-bit integer should be able to index.\nThis is due to the JavaScript Number type being an IEEE 754 double precision float.\n\nThe Node.js `fs` module probably has this same limitation.\n\n### ZIP64 Extensible Data Sector Is Ignored\n\nThe spec does not allow zip file creators to put arbitrary data here,\nbut rather reserves its use for PKWARE and mentions something about Z390.\nThis doesn't seem useful to expose in this library, so it is ignored.\n\n### No Multi-Disk Archive Support\n\nThis library does not support multi-disk zip files.\nThe multi-disk fields in the zipfile spec were intended for a zip file to span multiple floppy disks,\nwhich probably never happens now.\nIf the \"number of this disk\" field in the End of Central Directory Record is not `0`,\nthe `open()`, `fromFd()`, `fromBuffer()`, or `fromRandomAccessReader()` `callback` will receive an `err`.\nBy extension the following zip file fields are ignored by this library and not provided to clients:\n\n * Disk where central directory starts\n * Number of central directory records on this disk\n * Disk number where file starts\n\n### Limited Encryption Handling\n\nYou can detect when a file entry is encrypted with \"Traditional Encryption\" via `isEncrypted()`,\nbut yauzl will not help you decrypt it.\nSee `openReadStream()`.\n\nIf a zip file contains file entries encrypted with \"Strong Encryption\", yauzl emits an error.\n\nIf the central directory is encrypted or compressed, yauzl emits an error.\n\n### Local File Headers Are Ignored\n\nMany unzip libraries mistakenly read the Local File Header data in zip files.\nThis data is officially defined to be redundant with the Central Directory information,\nand is not to be trusted.\nAside from checking the signature, yauzl ignores the content of the Local File Header.\n\n### No CRC-32 Checking\n\nThis library provides the `crc32` field of `Entry` objects read from the Central Directory.\nHowever, this field is not used for anything in this library.\n\n### versionNeededToExtract Is Ignored\n\nThe field `versionNeededToExtract` is ignored,\nbecause this library doesn't support the complete zip file spec at any version,\n\n### No Support For Obscure Compression Methods\n\nRegarding the `compressionMethod` field of `Entry` objects,\nonly method `0` (stored with no compression)\nand method `8` (deflated) are supported.\nAny of the other 15 official methods will cause the `openReadStream()` `callback` to receive an `err`.\n\n### Data Descriptors Are Ignored\n\nThere may or may not be Data Descriptor sections in a zip file.\nThis library provides no support for finding or interpreting them.\n\n### Archive Extra Data Record Is Ignored\n\nThere may or may not be an Archive Extra Data Record section in a zip file.\nThis library provides no support for finding or interpreting it.\n\n### No Language Encoding Flag Support\n\nZip files officially support charset encodings other than CP437 and UTF-8,\nbut the zip file spec does not specify how it works.\nThis library makes no attempt to interpret the Language Encoding Flag.\n\n### How Ambiguities Are Handled\n\nThe zip file specification has several ambiguities inherent in its design. Yikes!\n\n* The `.ZIP file comment` must not contain the `end of central dir signature` bytes `50 4b 05 06`. This corresponds to the text `\"PK☺☻\"` in CP437. While this is allowed by the specification, yauzl will hopefully reject this situation with an \"Invalid comment length\" error. However, in some situations unpredictable incorrect behavior will ensue, which will probably manifest in either an invalid signature error or some kind of bounds check error, such as \"Unexpected EOF\".\n* In non-ZIP64 files, the last central directory header must not have the bytes `50 4b 06 07` (`\"PK♠•\"` in CP437) exactly 20 bytes from its end, which might be in the `file name`, the `extra field`, or the `file comment`. The presence of these bytes indicates that this is a ZIP64 file.\n\n## Change History\n\n * 3.2.0\n   * Added support for reading third-party extensions for timestamps: InfoZIP \"universal timestamp\" extra field and NTFS extra field. [pull #160](https://github.com/thejoshwolfe/yauzl/pull/160)\n   * `entry.getLastModDate()` takes options `forceDosFormat` to revert the above change, and `timezone` to allow UTC interpretation of DOS timestamps.\n   * Documented `dosDateTimeToDate()` as now deprecated.\n * 3.1.3\n   * Fixed a crash when using `fromBuffer()` to read corrupt zip files that specify out of bounds file offsets. [issue #156](https://github.com/thejoshwolfe/yauzl/pull/156)\n   * Enahnced the test suite to run the error tests through `fromBuffer()` and `fromRandomAccessReader()` in addition to `open()`, which would have caught the above.\n * 3.1.2\n   * Fixed handling non-64 bit entries (similar to the version 3.1.1 fix) that actually have exactly 0xffffffff values in the fields. This fixes erroneous \"expected zip64 extended information extra field\" errors. [issue #109](https://github.com/thejoshwolfe/yauzl/pull/109)\n * 3.1.1\n   * Fixed handling non-64 bit files that actually have exactly 0xffff or 0xffffffff values in End of Central Directory Record. This fixes erroneous \"invalid zip64 end of central directory locator signature\" errors. [issue #108](https://github.com/thejoshwolfe/yauzl/pull/108)\n   * Fixed handling of 64-bit zip files that put 0xffff or 0xffffffff in every field overridden in the Zip64 end of central directory record even if the value would have fit without overflow. In particular, this fixes an incorrect \"multi-disk zip files are not supported\" error. [pull #118](https://github.com/thejoshwolfe/yauzl/pull/118)\n * 3.1.0\n   * Added `readLocalFileHeader()` and `Class: LocalFileHeader`.\n   * Added `openReadStreamLowLevel()`.\n   * Added `getFileNameLowLevel()` and `parseExtraFields()`.\n     Added fields to `Class: Entry`: `fileNameRaw`, `extraFieldRaw`, `fileCommentRaw`.\n   * Added `examples/compareCentralAndLocalHeaders.js` that demonstrate many of these low level APIs.\n   * Noted dropped support of node versions before 12 in the `\"engines\"` field of `package.json`.\n   * Fixed a crash when calling `openReadStream()` with an explicitly `null` options parameter (as opposed to omitted).\n * 3.0.0\n   * BREAKING CHANGE: implementations of [RandomAccessReader](#class-randomaccessreader) that implement a `destroy` method must instead implement `_destroy` in accordance with the node standard https://nodejs.org/api/stream.html#writable_destroyerr-callback (note the error and callback parameters). If you continue to override `destory` instead, some error handling may be subtly broken. Additionally, this is required for async iterators to work correctly in some versions of node. [issue #110](https://github.com/thejoshwolfe/yauzl/issues/110)\n   * BREAKING CHANGE: Drop support for node versions older than 12.\n   * Maintenance: Fix buffer deprecation warning by bundling `fd-slicer` with a 1-line change, rather than depending on it. [issue #114](https://github.com/thejoshwolfe/yauzl/issues/114)\n   * Maintenance: Upgrade `bl` dependency; add `package-lock.json`; drop deprecated istanbul dependency. This resolves all security warnings for this project. [pull #125](https://github.com/thejoshwolfe/yauzl/pull/125)\n   * Maintenance: Replace broken Travis CI with GitHub Actions. [pull #148](https://github.com/thejoshwolfe/yauzl/pull/148)\n   * Maintenance: Fixed a long-standing issue in the test suite where a premature exit would incorrectly signal success.\n   * Officially gave up on supporting Mac Archive Utility corruption in order to rescue my motivation for this project. [issue #69](https://github.com/thejoshwolfe/yauzl/issues/69)\n\n * 2.10.0\n   * Added support for non-conformant zipfiles created by Microsoft, and added option `strictFileNames` to disable the workaround. [issue #66](https://github.com/thejoshwolfe/yauzl/issues/66), [issue #88](https://github.com/thejoshwolfe/yauzl/issues/88)\n * 2.9.2\n   * Removed `tools/hexdump-zip.js` and `tools/hex2bin.js`. Those tools are now located here: [thejoshwolfe/hexdump-zip](https://github.com/thejoshwolfe/hexdump-zip) and [thejoshwolfe/hex2bin](https://github.com/thejoshwolfe/hex2bin)\n   * Worked around performance problem with zlib when using `fromBuffer()` and `readStream.destroy()` for large compressed files. [issue #87](https://github.com/thejoshwolfe/yauzl/issues/87)\n * 2.9.1\n   * Removed `console.log()` accidentally introduced in 2.9.0. [issue #64](https://github.com/thejoshwolfe/yauzl/issues/64)\n * 2.9.0\n   * Throw an exception if `readEntry()` is called without `lazyEntries:true`. Previously this caused undefined behavior. [issue #63](https://github.com/thejoshwolfe/yauzl/issues/63)\n * 2.8.0\n   * Added option `validateEntrySizes`. [issue #53](https://github.com/thejoshwolfe/yauzl/issues/53)\n   * Added `examples/promises.js`\n   * Added ability to read raw file data via `decompress` and `decrypt` options. [issue #11](https://github.com/thejoshwolfe/yauzl/issues/11), [issue #38](https://github.com/thejoshwolfe/yauzl/issues/38), [pull #39](https://github.com/thejoshwolfe/yauzl/pull/39)\n   * Added `start` and `end` options to `openReadStream()`. [issue #38](https://github.com/thejoshwolfe/yauzl/issues/38)\n * 2.7.0\n   * Added option `decodeStrings`. [issue #42](https://github.com/thejoshwolfe/yauzl/issues/42)\n   * Fixed documentation for `entry.fileComment` and added compatibility alias. [issue #47](https://github.com/thejoshwolfe/yauzl/issues/47)\n * 2.6.0\n   * Support Info-ZIP Unicode Path Extra Field, used by WinRAR for Chinese file names. [issue #33](https://github.com/thejoshwolfe/yauzl/issues/33)\n * 2.5.0\n   * Ignore malformed Extra Field that is common in Android .apk files. [issue #31](https://github.com/thejoshwolfe/yauzl/issues/31)\n * 2.4.3\n   * Fix crash when parsing malformed Extra Field buffers. [issue #31](https://github.com/thejoshwolfe/yauzl/issues/31)\n * 2.4.2\n   * Remove .npmignore and .travis.yml from npm package.\n * 2.4.1\n   * Fix error handling.\n * 2.4.0\n   * Add ZIP64 support. [issue #6](https://github.com/thejoshwolfe/yauzl/issues/6)\n   * Add `lazyEntries` option. [issue #22](https://github.com/thejoshwolfe/yauzl/issues/22)\n   * Add `readStream.destroy()` method. [issue #26](https://github.com/thejoshwolfe/yauzl/issues/26)\n   * Add `fromRandomAccessReader()`. [issue #14](https://github.com/thejoshwolfe/yauzl/issues/14)\n   * Add `examples/unzip.js`.\n * 2.3.1\n   * Documentation updates.\n * 2.3.0\n   * Check that `uncompressedSize` is correct, or else emit an error. [issue #13](https://github.com/thejoshwolfe/yauzl/issues/13)\n * 2.2.1\n   * Update dependencies.\n * 2.2.0\n   * Update dependencies.\n * 2.1.0\n   * Remove dependency on `iconv`.\n * 2.0.3\n   * Fix crash when trying to read a 0-byte file.\n * 2.0.2\n   * Fix event behavior after errors.\n * 2.0.1\n   * Fix bug with using `iconv`.\n * 2.0.0\n   * Initial release.\n\n## Development\n\nOne of the trickiest things in development is crafting test cases located in `test/{success,failure}/`.\nThese are zip files that have been specifically generated or design to test certain conditions in this library.\nI recommend using [hexdump-zip](https://github.com/thejoshwolfe/hexdump-zip) to examine the structure of a zipfile.\n\nFor making new error cases, I typically start by copying `test/success/linux-info-zip.zip`, and then editing a few bytes with a hex editor.\n", "readmeFilename": "README.md", "users": {"306766053": true, "bret": true, "mnew": true, "anchnk": true, "d-band": true, "elisee": true, "chriszs": true, "morewry": true, "erikvold": true, "maxogden": true, "ramyabot": true, "shiva127": true, "strawhat": true, "superjoe": true, "surfdude": true, "timdream": true, "adobnikar": true, "matthiasg": true, "nickeljew": true, "nycdotnet": true, "arichiardi": true, "jswartwood": true, "nate-river": true, "raycharles": true, "shuoshubao": true, "wenhsiaoyi": true, "fishrock123": true, "flumpus-dev": true, "alex_giannis": true}}