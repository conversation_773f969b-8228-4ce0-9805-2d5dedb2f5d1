{"_id": "tar-fs", "_rev": "122-41d043a7d1c609c22373aa26e8fbbfc0", "name": "tar-fs", "dist-tags": {"tmp": "1.16.5", "latest": "3.1.0"}, "versions": {"0.1.0": {"name": "tar-fs", "version": "0.1.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "73af0c637429e251bb02d9a299a0e0c23fa1493a", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.0.tgz", "integrity": "sha512-mY4rMt6DJ1LNBChe4vCJnIsjzQ8nyBxYWTSCcoZAaVq1DA7OmeT9Fj6nbNDS+jhobX47DZVDocaIZuegKPW1sw==", "signatures": [{"sig": "MEYCIQDNFKfBpZ9XzN1dT88najb7HkojAu/ibYm1g4Pfaz3N4gIhAKXb5DFfXMi0WGNGIcimHSiM6knsEH31N6nDLWMM0cpY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.1"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.1": {"name": "tar-fs", "version": "0.1.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "1b881c3f0c4b21e783595566324f9287ae6da668", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.1.tgz", "integrity": "sha512-dR9pVBTggBLsRQrjQnf0uFgfAhld/dZAjKdjSuk4GvMWanLOIvXesIusqvhefwN/n6sbu0Jo19T8/yRt03vypQ==", "signatures": [{"sig": "MEUCICF8Cs1FkEV9m2p8yCEB9fS3K7YvxovFGCQ697v3TsREAiEAh2PvwCa2YlZftcxF+nwWaGqNCbUQHA8Pk4Mxcd7DmAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.1"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.2": {"name": "tar-fs", "version": "0.1.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "0f198bfae09c8b7060fd8b4033d886a6fdd2236b", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.2.tgz", "integrity": "sha512-+BC+NYONy0AH7d6h5MlXbVBeOgvxQfm0GQTrj3pUJbWUaBIJC/oOwbR5Uj5Fk84mNOXu/P+YEyorSryUwNrDXw==", "signatures": [{"sig": "MEUCID4tMmkOmH9JMB3BmZqGDr+B1ZN1QO5ZXvzKIEw/u736AiEA6oReCfz6exaXQRwxSFH6C00dbWpvVJuDWVPQDk2EKLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.2"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.3": {"name": "tar-fs", "version": "0.1.3", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "0824e2f3631598630e78b5e007bb3b09eb14e4b6", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.3.tgz", "integrity": "sha512-crn1BKce8hzvMsj3sWP3xjNN2kOf1DJU+OV1J5cHkJatBwj468tmbbSGZjpB3XIeS0CeZMpx20LKPeoiOLag5Q==", "signatures": [{"sig": "MEYCIQCrIpqvOjXal7Wb2bgHDaTuTUHBrCbTLI2cATlcC7y9UwIhAMsSo5WTv745Udj2kfkkZo4OSdmfXC4lH8WIbnNeBhYO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.2"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.4": {"name": "tar-fs", "version": "0.1.4", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "856c9177688f650cf2f69cbc7ab6b020dab55e1d", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.4.tgz", "integrity": "sha512-Oy0GDszIGGOfcJdoaDqvkoPg5nTID0gXAAgOriUGyz+F8Mzk42uJ2MsWoiAuEBTAutihJV3BYBlo7T2hT93QwQ==", "signatures": [{"sig": "MEUCIB0JmyxqLbQ7hVcOKSYkfWPNFZFe6LKuKuXavcdWvpgoAiEA0WMXS4EaHaSQDR2hgrtXDnBF+JH+Pd1JwQg4NJniiY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.2"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.5": {"name": "tar-fs", "version": "0.1.5", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "2533e2faa20e6b9e98eb3b8c62dadbdc89e0a881", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.5.tgz", "integrity": "sha512-JJq3hmSQLtePeCT2RrzGB63fbr7L5C1BldfClpoaGsfMaOUQrQ8fayURohtwTIryuYSRN5rVVKSZpkgDfSkV6A==", "signatures": [{"sig": "MEUCIBDBqoqw4IiQl3db377X8OjwPu2BgqY9dqjjy+4xsirfAiEA01zztzVgEHpHhf8VLtapOCtXidj84OyEeIDqaLXr5KA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.2"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.6": {"name": "tar-fs", "version": "0.1.6", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.6", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "174a6f901e52d702dad198e5827be0964352f45e", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.6.tgz", "integrity": "sha512-WzG8HZWeYaXIQOWQx3sbbxzgePcCQsP89nJEu4vKivOM5pUcAhVWnpJa7t6iOD93FojrPsDTge5ETrTIuatGvw==", "signatures": [{"sig": "MEUCIFo3lFXvgYO1XUAs8GeUgtrUfMAi7RGyUs7MewpXrbq0AiEAk71ZI4/YXgN3B9srB7AY46wE1h5cTNdkgZj7DGfH9Kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.2"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.7": {"name": "tar-fs", "version": "0.1.7", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.7", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "603c9b5c9a27e4f58678ed12a3cf75ae6c845206", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.7.tgz", "integrity": "sha512-hpZHwsLZCoK4wgzu04400NT7/zSWTM0rgYy+8roR7DAnROyZKDC9ab27GSMOSYoHuUn1OMGXH1+YhZ9ozFzoGg==", "signatures": [{"sig": "MEYCIQCjBRg2yVdNuatPE7bnXGYALRU6sEvUCVp59ojNE/9h6QIhAN4CF+a7NQvcSHTMQr59kDUH97Cg6ciEoqzBJT0hgjwu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.5"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.1.8": {"name": "tar-fs", "version": "0.1.8", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.1.8", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "dd7bdb531d2e8cbe712d878c18554bd1cd503ab5", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.1.8.tgz", "integrity": "sha512-awDKklnt11211NQq+TlcMSzKL3i0cN8G2GcriTZtYoIOfPpRtR4Bb2FAuT4P8C54N47c6TITdp2d36XIlUncRQ==", "signatures": [{"sig": "MEUCIQDp6UhSISFZasRjjTal5Ht3Z2gEDuwrdqRvyRDY/nCGugIgUidhD3qAXH464Nw+7OjOx4stq392e9StQmwpjop9rX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.5"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.2.0": {"name": "tar-fs", "version": "0.2.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "dc4f58d95c54a50d030b2132e6329d5dd9137ac4", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.2.0.tgz", "integrity": "sha512-6CbyoWlJxo/05lwo2EyRwWmRz6ZvxFt0rki1UIuJQe9j9P+DxnOL28lZhXXfCUOby6vnRIqB9FQgBbUEc8/C0Q==", "signatures": [{"sig": "MEUCIDKqaIvrwRCrVdS/3jrwOgHFKebxCA9hwbzI+zPMH0kWAiEAnS68Ijx7ImDuyWpERXrLv/UDNXfTdDs9+y3+n1aMf5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.5"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.2.1": {"name": "tar-fs", "version": "0.2.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "3068146357ec92978812170933f7e6f50c5d5150", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.2.1.tgz", "integrity": "sha512-ADJoc0Nn+wl715dyke9Q/7UUosEzc0SaZ4tASTyyJngqhTGROcHnzkkeGHgKC2qA+mtdcaHZFSJqiY8iq+9GUg==", "signatures": [{"sig": "MEQCIG7vUM8c5HPygGQRCZSJ4ORE4wXPa+uBxjpDXTuUYupaAiBa1phFEqgmkBVhvpulUpWl8YYU2wwVyyLffp5qf959YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.2.2", "mkdirp": "~0.3.5", "tar-stream": "~0.2.5"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.2.2": {"name": "tar-fs", "version": "0.2.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.2.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "1af3f4d2a9029a68b4d9b59700e2e0d47bbef4ae", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.2.2.tgz", "integrity": "sha512-OfFcrdxwiOBSKGL5myQwah+/a/TQAyF3NfpG/OeFJTfiDvjK1VZ5e0l0u+C8KMZjgfF1ztkliwZUBeAp8nstag==", "signatures": [{"sig": "MEUCIAlHPp5AAmG97D0ZbxmAH8z96wd8V48drfdx4GsRcnKCAiEAyu6DGIpSGsrL9NptjnlluKISVfcHXxw4RGmF61AGxcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.2.5"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.3.0": {"name": "tar-fs", "version": "0.3.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.3.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "11c90eddb174d0c0d9994099c0e23ac9a0e850ad", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.3.0.tgz", "integrity": "sha512-UNIMnikRQu5QNBAtcb5UwBCIWy4OPXk8LuODsimhD8MicGD9tD3gtG0pL0Fc0xSmtANZN/rW9/1aGsy+Ic084g==", "signatures": [{"sig": "MEQCIA09uwtzXu2Mg/ucsDNshb74VHxvFTrFrfi67W/CsIUJAiBF6oQEqZRH1wXG6BhS5NRzucnxVIAU299DbjKRo1VDyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.2.5"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.3.1": {"name": "tar-fs", "version": "0.3.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.3.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "eb161ec9dd77ee3e3852d72d12bdd78d4ade7035", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.3.1.tgz", "integrity": "sha512-1P4Ij46WHHfAHak897cbJcbV0rLEmMKPP1LwDbddiYsALWW/lkqyyJ6gstd21oA+Q0Nof9hJiwnIEFmmWkWUVQ==", "signatures": [{"sig": "MEYCIQC4gbRInR7oktLogFk171Y+bgyoPmfgVsQsACdG39qwygIhAPFje4HhceYgi3QURJBfyeyJq7t8RKw0uvAQ4vR5HCfy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.3.2"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.3.2": {"name": "tar-fs", "version": "0.3.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.3.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "4cbf7099ae0871d76cccdfb7ac3c77326cd01896", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.3.2.tgz", "integrity": "sha512-JtqEl4cbO2zHVHGYQI242y+xQ3NtY3foT6B0RdTHrwX+wnjWqT7kA8WYBMIldf/YM1ETOmsOaoWp4uPVjW2nVg==", "signatures": [{"sig": "MEQCIGRbDSYnTWvWw2nJtMhSWAjb2Dh3/6gMnWxCGImt2BlFAiA38IReHSb211IsDGBxGyAmtxtZT+gxrV+NXJDIY/WHzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.3.3"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.3.3": {"name": "tar-fs", "version": "0.3.3", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.3.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "c50bc3becefa63955ab25874728150d605718b4b", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.3.3.tgz", "integrity": "sha512-qxrs6tMbRU5SD8SBXulOCEe9hmWXoiewJWNYkWuMpOAvOZkzvtOUohDzfs926SHMydUUSf4RHMxLOyZE3s9MgQ==", "signatures": [{"sig": "MEQCIEpPi/bxdXFA1zpBUlevRxD4lw5BCeAo++I3vUV9jmzUAiAGjMP1IYf5lrd9TxpYiOS3UlN49SFaNCEzXMsNRveVTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "c50bc3becefa63955ab25874728150d605718b4b", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.4.1"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.4.0": {"name": "tar-fs", "version": "0.4.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.4.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "6b182d50edad1631a4327f96a396c691fd9c9002", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.4.0.tgz", "integrity": "sha512-ylknD13BZ/q+V2t7aaS0eP3tRwAJ2r7m8xpvTmqPswQqmuh+hfvEReOeWPoT5njoDYkWYmfJRn4VqXx5FbAoEw==", "signatures": [{"sig": "MEYCIQDp5Dt7DPLQgt+XGeZfccSLO1i5MQHxqjtTlrHQiKdchAIhAJUBGsGxi/oQ73sSzDk0VkVQ9wnmQmnchdRcbIWbrT3H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6b182d50edad1631a4327f96a396c691fd9c9002", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.4.1"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.4.1": {"name": "tar-fs", "version": "0.4.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.4.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "f045b072f90ba02984d1c8cf5de9c7f3d0bc9cfe", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.4.1.tgz", "integrity": "sha512-VDQL0LLF1xoxClr6ztEfStldOwuv2gNx44eNF22ZNpA67SUZ4Ii4xKds+JWyHh0xCgETPLN7snnYROTVPCJjGA==", "signatures": [{"sig": "MEYCIQD4jtR0DPOGgJ4P7ptF3eoWUzDUPwrDgvJCLT5aoxk1HwIhAP/m0baOxtaO+dqoB0N66fG9f9831s9jXKwtoM5k8AeW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f045b072f90ba02984d1c8cf5de9c7f3d0bc9cfe", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.4.1"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.5.0": {"name": "tar-fs", "version": "0.5.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "_id": "tar-fs@0.5.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "b361fff8eac2d58fc31d9c88d37368244031146b", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.5.0.tgz", "integrity": "sha512-cP7BpgNNZaPSncqEbF7NC2QadZ/ChffymL7mE2lbPDc5oVr3Ip4XY5Fe5QRVQTAufjXUCcr6Kn0uBV1Ur2/C1Q==", "signatures": [{"sig": "MEUCIAerkTt13eC8aVzIqPA5ob97Gkk1WbfxPEzHmIeN3QbFAiEAqabN+mwmnw45bPf0GOtgaDTO0oHGdQPyV2uePrUIcBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b361fff8eac2d58fc31d9c88d37368244031146b", "gitHead": "6597956b0b94c809c56e4928c4be86d326add2f5", "scripts": {"test": "tap test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "filesystem bindings for tar-stream", "directories": {}, "dependencies": {"pump": "~0.3.0", "mkdirp": "~0.3.5", "tar-stream": "~0.4.1"}, "devDependencies": {"tap": "~0.4.6", "rimraf": "~2.2.5"}}, "0.5.1": {"name": "tar-fs", "version": "0.5.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@0.5.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "479b3165a6cbc9cd502aa33fc93a16f61510d24d", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.5.1.tgz", "integrity": "sha512-BPQXmbOTpjTRO0gt0Rwe0rBjqqRdyxaOzi/EHMNP+thElF3usANFOwK5i06mtUaHmGG2ieb+3JN6E3475E8VNg==", "signatures": [{"sig": "MEUCIQDi8nQpC1rEcokDgZp4iYPYe62LyKOofZgdnnPcSe7JBQIgacH1ZBJa/T6fKlTTGjzglP9qlsRnvsNxW50V9UYFnfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "479b3165a6cbc9cd502aa33fc93a16f61510d24d", "gitHead": "2e9c5ce620863f79fd6eae336adbd21c1d279527", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "dependencies": {"pump": "^0.3.5", "mkdirp": "^0.5.0", "tar-stream": "^0.4.6"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "0.5.2": {"name": "tar-fs", "version": "0.5.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@0.5.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "0f59424be7eeee45232316e302f66d3f6ea6db3e", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-0.5.2.tgz", "integrity": "sha512-MqmfOi0c4YcprB8WfyE2nKZPE//2253LQueyliGfZ6Hy9L6N4QM9zJNXXSSmp+PfdljyAkHB30Vapm0aFa6G2g==", "signatures": [{"sig": "MEUCIQDPuXK5BpBb3dHWezBoVeIr9oI8PPtBV0Ux6nxHiW1tDAIgTGCe/LZdYIVcbA1PW+wSJvTVNWzH2xMP5SevcAjhy6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0f59424be7eeee45232316e302f66d3f6ea6db3e", "gitHead": "908604dfc8b21fd4df5db8c6dddc46bd5b771e33", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "dependencies": {"pump": "^0.3.5", "mkdirp": "^0.5.0", "tar-stream": "^0.4.6"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.0.0": {"name": "tar-fs", "version": "1.0.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "6aa1821437a89970623745dc5d98ad8d23ba2b7e", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.0.0.tgz", "integrity": "sha512-GALDvyDwz8u4eqC+evgMlrWwMexzXXQWFZ7jYpjnc9QGj+TUwQVBEC/MWxjMOwph2AJHMJQbrMCSYGkg3HSwcA==", "signatures": [{"sig": "MEUCIQC7cChuVSKltNm9s7dg3zCG+5qoH/GiAiM8ISCAsfqRdAIgKdkJCK+nuFBDqNzuv/T5eDFSvOizvOEJk9PSxt8B96I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6aa1821437a89970623745dc5d98ad8d23ba2b7e", "gitHead": "75f49ca9bd7db26f0d4a1325ba82dcbf159009c2", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.0.0"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.1.0": {"name": "tar-fs", "version": "1.1.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "14d9e03a487533dc24220635ee1966545c3ef4dc", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.1.0.tgz", "integrity": "sha512-xX7UVQ0bdH6HshdK/k+XCbiDlTVyCcuDCc3a0MCKIm28HqEwgL1LjWD/yhFLlgG8Kci/bKkyPmcQ6iHBWgWwtg==", "signatures": [{"sig": "MEQCID/mxwDHAFpN4FTGxwN9fpFySZuRlaJbfvD90ttN1cWFAiAqudCyUl+hbbFFAh4yk3HUWtEYCt5mQCY5E9wsyLNAEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "14d9e03a487533dc24220635ee1966545c3ef4dc", "gitHead": "5e034f17f32cde3b843ff45081f329f049d699b5", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.0.0"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.2.0": {"name": "tar-fs", "version": "1.2.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "91130374dc6737f053926596ee5068063edccad3", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.2.0.tgz", "integrity": "sha512-k+yiQLHo53qMYSiow3ER/tGwRPqYFd7P/kiu07K3SgzcfJ8Vz0yom7UAPdpMZcJiCL36UXskisXcaP2FfhnBwA==", "signatures": [{"sig": "MEUCIQC8+fp6lXoc/Ij2W1DjelVUagMIHJnykPON2/Cd8vKg6gIgbFlO6EnAqIZkLtqiB69y4DGhO4+i1eO/Oi4scXAf+SE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "91130374dc6737f053926596ee5068063edccad3", "gitHead": "661d77a8faa786fe8e815abcf95491e507427f03", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.1.3", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.10.32", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.0.0"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.3.0": {"name": "tar-fs", "version": "1.3.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.3.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "0869c2042be881a99fafcdebb7fb400c1ecdfdb0", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.3.0.tgz", "integrity": "sha512-00bmu99yNCqFbgmEJK+NOKj8tmo4Pf1oF2xapiYbkOssho+ktPHr1xWkg/4l7pau2IkAbd2xl2yznJItwNVrjQ==", "signatures": [{"sig": "MEUCIHu/h+4nxPzySb/ThFlPy81etxqgiWpdAMyuVom8XfObAiEAoA5pgcn4OPf1mfKxBsx4e1/U94uqh2XNcOk7IXBNqew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0869c2042be881a99fafcdebb7fb400c1ecdfdb0", "gitHead": "df7c5c364b0f534da80a97fbb66f593e31c3765e", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.10.33", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.0.0"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.4.0": {"name": "tar-fs", "version": "1.4.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.4.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "de29320e06b9b4d645f93764d048a1083a581da0", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.4.0.tgz", "integrity": "sha512-zVkQvMiTeVN70HgE4ZOuupEErGeskokFSs9fzQCDmIYh9y5UdUc+metYvSw2Q9RqGp9297bHlkQWUNRDRdrEXw==", "signatures": [{"sig": "MEUCIQCHN0eBu2emOOFpKC5adEfbsN64Z5RKhC7F/6UBYUcefgIgFIhTKgUGKbZ3eapmdCPv9t/GZ6pzLS4AYw8hKo74nm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "de29320e06b9b4d645f93764d048a1083a581da0", "gitHead": "65c79a7022a5347e10420883444ff814ca6f62ea", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.10.33", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.0.0"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.4.1": {"name": "tar-fs", "version": "1.4.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.4.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "8629106756347c25a0c607d8368ba6d527b86d06", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.4.1.tgz", "integrity": "sha512-kevrwmu2zRXOcTYtoCmu+Ht5IwAHHZNcM4vzx0mcyTWeWw3nxhqaHV9BKz+oOrGlbirEko1zrx0KGWqZtaL5jw==", "signatures": [{"sig": "MEUCIAPrtFq/HY3vgv8WxIOtMEcJMuaXwtaLNURz0jvAlEGbAiEA71LK+DnvB8Az876NUQ661IwZajIoMSlusdiuU9uUrfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8629106756347c25a0c607d8368ba6d527b86d06", "gitHead": "9d5048cf6b2953827775a07ef3c5fae093fd9309", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.1.8", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.10.33", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.0.0"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.4.2": {"name": "tar-fs", "version": "1.4.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.4.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "08a1998fd5017371dc5963023fda004bd5b6e984", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.4.2.tgz", "integrity": "sha512-LHhQpd9rM12Eg90O+fcTbvsTDKzQXRAN+j0ZEk6pf+bINCzpsvH/mJj3FnOGGXM/usE5xdTWRaf/Gq1p+rm8hQ==", "signatures": [{"sig": "MEUCIFlQ25nnkoCO3TUSvuthwute88Ui+XMPxYYLJ4nN3iF3AiEAyrzLuApixhCtDXcsQIRRnjxd788K+mzcCbX9aSG/u2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "08a1998fd5017371dc5963023fda004bd5b6e984", "gitHead": "55ed348ef49367f481807629c8abc8aee97c903e", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.1.17", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.10.35", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.5.0": {"name": "tar-fs", "version": "1.5.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.5.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "461da12f3a756adcfe781afbc0c13694d9c5d613", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.5.0.tgz", "integrity": "sha512-<PERSON><PERSON>llikyMXgDSPSZlCL7Kc5hgWLhfzA8xzkrJzhWYy8V+rH4tBqhAlEN7Fdtj5wdfoOb2OD+vDH7VxrtLvzJinw==", "signatures": [{"sig": "MEUCIEs3bSNJ2PFTTOJVr3+AUorxNiybsNS2PMMH7E5sfOeeAiEAweNNey0DozEIrQE64nuEhcIEfj/JN53r6N3yu+yCE4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "461da12f3a756adcfe781afbc0c13694d9c5d613", "gitHead": "bd0028b8e6543c0f85595bfa85d302cfb0e501fe", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com:mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "1.3.0", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.5.1": {"name": "tar-fs", "version": "1.5.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.5.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "5fc6744cf79c1331330112cbdc290aa6ebf0eda8", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.5.1.tgz", "integrity": "sha512-zamYLU8nlPBHnQQ8paq4QNcqH8H8XA46YRom/Re4WsJysDTJKW7308KW+CUhjLwbx5bvhSEzb63h/vT1qX32vw==", "signatures": [{"sig": "MEQCIAQFWgE5qrESgE9Vbg+7CjS8hYTzX1zNaUYCjbumbkHDAiAINl1YHbULcdb8CL5MCs+0b+/MM19Y2eXCP0AmpCPHOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5fc6744cf79c1331330112cbdc290aa6ebf0eda8", "gitHead": "75c8b684d2ea73647115c27514c474bfdd0b488f", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "maxogden", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "2.0.0", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.6.0": {"name": "tar-fs", "version": "1.6.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.6.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "f18c84421be0ebfef6b61b3b3bbbf7b276ff45bd", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.6.0.tgz", "integrity": "sha512-DnianXAewCSUh4Q/L1Y+rbz+OVfISdjgMM19LgOCsTFWRSZVNvwKN5lgfHA9ZQi9EHxp4vfN4WftoQQ+BKvAwA==", "signatures": [{"sig": "MEYCIQDFLKrH7oDL+Fq/onv+JsdZwWS0oCNNPPYJjdu63HXSZAIhAL+3pwi7IBSKPEvG7nZSd8NkyXkieRga2Qx3D8M7zhYM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f18c84421be0ebfef6b61b3b3bbbf7b276ff45bd", "gitHead": "fd1ddd1926e443e8cdadacff972ef7dcfc5a0203", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.12.4", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.7.0": {"name": "tar-fs", "version": "1.7.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.7.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "3fbc987242234603c9a07811e19f01fd0d774b47", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.7.0.tgz", "integrity": "sha512-PElIjd4b/H1AnQROyxCMwKTJdDjhdWh3eD6JsVjbxhqgin+zo2iOdCn6l/0iD7PB7LFyhetZu4I4MaOyNjfeiQ==", "signatures": [{"sig": "MEYCIQCFx7Lzn5oQ9aRb+APNudBkYZJwHn1W9DnhcRdj9j8k6wIhANrYhSD48DRuhTxPqGpRWb2Iq0Jh5BnFIeQ3tQib0YUl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3fbc987242234603c9a07811e19f01fd0d774b47", "gitHead": "794de32d086c933f8e5d567b03881127236f375e", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "0.12.4", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.8.0": {"name": "tar-fs", "version": "1.8.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.8.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "f7fd5591c5946b0f22f73df4d97839f9d3bba5a7", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.8.0.tgz", "integrity": "sha512-X4DC+c3YHULz1xh60dk0lfGYwdKrvKHBdnTGl2+mPfvGr/DAZ6ZPDsEs3GLK40ouWEvsUZ/+Xw3fKKJxT7YbLg==", "signatures": [{"sig": "MEQCIAWXW5vScY4mdzZOAxKelewlNPfGAiLD/Y7DcqdVLzpdAiB55Ft/+bqpTvy5YpaJ0TqX0GTPQI29jW3At4jUIpsAVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f7fd5591c5946b0f22f73df4d97839f9d3bba5a7", "gitHead": "4d8d8966d7f127006fe7ce93b138a28053d2da0d", "scripts": {"test": "tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.13.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "2.4.0", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8"}}, "1.8.1": {"name": "tar-fs", "version": "1.8.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.8.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "3ee6cfa351633775eaa75e3af6ec307e9eaa403c", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.8.1.tgz", "integrity": "sha512-xMwvVcc3XQ0uldqPC9OwkZ4+480xUnf9smOhSepySOoGVMJ5ZkrYO9c3+bU8ueex/vQITj55ETEkL55bwIbfkg==", "signatures": [{"sig": "MEUCIGWwy8HHQYJHdTG+vipF3GhnB0sM0di8BwZe69Q/bO/HAiEA9AyVGf9MG01aYKHPQ8UZDvJx2RIceplZoNDK/Czlxmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3ee6cfa351633775eaa75e3af6ec307e9eaa403c", "gitHead": "e6088739467d34beaa74f0d6aca4c5b3692c9c52", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.13.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "2.4.0", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}}, "1.9.0": {"name": "tar-fs", "version": "1.9.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.9.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "8bd0ae0daf33769dc411d47ce850ea3be1802e22", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.9.0.tgz", "integrity": "sha512-ijwvzQU4zxmKU/skUcsLzz84h18mHH+REEmWX8nIWdE22gNL7wmj1B+1mLV4ug4aQKCIs2QdnwOoPq2mkubsSA==", "signatures": [{"sig": "MEUCIGS3o+luwfGNrzXOpIIP2stusfKCpRFiWW4ZjjXpn+k3AiEAipdZsIOxTOW9XEJNNxIvnoWkAMcqTOGg6ad3uAYlQLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8bd0ae0daf33769dc411d47ce850ea3be1802e22", "gitHead": "bf70e8fc775fcd6a45c984681793e553e100490a", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.2.3", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}}, "1.10.0": {"name": "tar-fs", "version": "1.10.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.10.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "7df377d7d5de6fe0777be839a7983862698da4c7", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.10.0.tgz", "integrity": "sha512-pHyaZbJreVlNBhJlF+15lswz7P/1nLQCSLDnbtBPf5Y4Bso7KPE9S8/8zS+IbSXVcYlbgHd0spE8VIz0w41PKQ==", "signatures": [{"sig": "MEYCIQDFEN8MElhULmwuV1j16wNa9egzlLboxuI4gCq1fJrkswIhAPJVeGMIIrE6V9hoyVdGTMGLLiu87TTQ2dueo9l//D3G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7df377d7d5de6fe0777be839a7983862698da4c7", "gitHead": "1bb6875a1dc9ff462b4660a1f0914ac553ac0275", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.2.2", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.10.0.tgz_1455010829228_0.7107159374281764", "host": "packages-9-west.internal.npmjs.com"}}, "1.11.0": {"name": "tar-fs", "version": "1.11.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.11.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "d7f9fce2423065a98b844ec26b7c96b5af393d66", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.11.0.tgz", "integrity": "sha512-s573wZ2pXrGutN21tA2b4QRzkcrY/jDQ4gYWURRIrJQ2PFL5LdS6uvuxyC6tp63P40I7KPfbp8+Gwd8qq+KMmA==", "signatures": [{"sig": "MEQCIG8Fb1CBv2oU0A5M1WLFC2jKe3XDWoY0WbIemrZqQAtiAiBZlN3yLI9z8R7hYGNiEpxKkJrZzAFiExRCbxhcvRSZNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d7f9fce2423065a98b844ec26b7c96b5af393d66", "gitHead": "f173c0fe47e92834764e5fbc077462a7e576959c", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.2.3", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.11.0.tgz_1456596490308_0.3059691891539842", "host": "packages-5-east.internal.npmjs.com"}}, "1.11.1": {"name": "tar-fs", "version": "1.11.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.11.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "a2237c84cc6eb8ed535d7fa269557b44b64d41d5", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.11.1.tgz", "integrity": "sha512-sDYaE8qxtCwbEk7XZ7sjmfKtQoPdGtdRJYv7Gx0XqPLuS9O/7eKfDOfre/oHJ0AqgcPgVjH/PGHVv8QCk+ZZ1Q==", "signatures": [{"sig": "MEUCICCBdmJ/6lNzo02cShMacBNZcjtwFHoSdFO2TUivIXlRAiEAtm4ea5IUkqhEvOlugLcO5ipVL3JUZirMDmdu4kVgH8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a2237c84cc6eb8ed535d7fa269557b44b64d41d5", "gitHead": "66ef5e30ad7f8bccb18de7ceef80c58e17e17186", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.2.3", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.11.1.tgz_1456685011080_0.9387126569636166", "host": "packages-5-east.internal.npmjs.com"}}, "1.12.0": {"name": "tar-fs", "version": "1.12.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.12.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "a6a80553d8a54c73de1d0ae0e79de77035605e1d", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.12.0.tgz", "integrity": "sha512-1VwExkPM4YM8Z1uv8GntYiqEmHeRuIaHF+QTEAxyVoiQ180EFajU1NXKPGlapewWujhCIaY5eni+EhEHzvIZ/g==", "signatures": [{"sig": "MEYCIQC8E6Og5nOJdy7xgNgC/dr9YhJ6qkiDVIbKblfnVVnqpwIhANQW1hVJuZ7aXybFUCD5GOpSB03o0EvjdZRzef7Lr0OR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a6a80553d8a54c73de1d0ae0e79de77035605e1d", "gitHead": "ebc956e5778f5023fe3170399b216a7ea6efac26", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.14.20", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.4.1", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.12.0.tgz_1459404055903_0.36411944683641195", "host": "packages-12-west.internal.npmjs.com"}}, "1.13.0": {"name": "tar-fs", "version": "1.13.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.13.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "4ac62c0de490dbba9e307d0a0312641091d5c45e", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.13.0.tgz", "integrity": "sha512-fOeMQLcmW1kYqA3XcexarLyq3t9w2AwIu4SpFcRjp/sgwdZ5/DM52L2oAH2uXUAxcj0kuLSe7G9xqfeDkGT8kA==", "signatures": [{"sig": "MEUCIQDxdn29Zw8oUmvp7zFXrucjhTQdVKDA98qnrVDpKwTBWAIgcOrUvDJlK1pUXRZJfiHlvFvThQUBxqkqOr8pPpyt4rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4ac62c0de490dbba9e307d0a0312641091d5c45e", "gitHead": "90b650e5b8f700e025b97364c736af3dcae588bb", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.15.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.4.3", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.13.0.tgz_1463955162613_0.06584193650633097", "host": "packages-12-west.internal.npmjs.com"}}, "1.13.1": {"name": "tar-fs", "version": "1.13.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.13.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "15ad6ea7544a13e6e2804b84df854061af1bff90", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.13.1.tgz", "integrity": "sha512-nDXqKXyDeyal0CrD8H2n9O/H5xoDouvg+iOk4Epwla7W1atZSU4KEOorTm27Q28pZnq6e7kMm/hY7PWOl/l9Bg==", "signatures": [{"sig": "MEUCIDz9+eBq7boWrYRbRfheGr91DclwuDI+GaxGWwnP5kcmAiEA60vNGfsL9tRArp2G/4bSHf43hCJJV9SWYOLuofe4yo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "15ad6ea7544a13e6e2804b84df854061af1bff90", "gitHead": "861969b3d4dab9f0a315cda320fe2d49793312d2", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.4.3", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.13.1.tgz_1471562275105_0.03221063385717571", "host": "packages-12-west.internal.npmjs.com"}}, "1.13.2": {"name": "tar-fs", "version": "1.13.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.13.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "433e879f9538d7e1e883ba914904f339c6414835", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.13.2.tgz", "integrity": "sha512-2Aa6KmxE0o1aqYC/kfIEWsaAYkZUlm8S9kcwD3y5gG4v+mw8XtrhbgRwFPDYZSg+nvk1QsBUmKKOuNzhNSyb3w==", "signatures": [{"sig": "MEUCIAoJvFqeDx8pBxEmO//UwTfd0wMivijr5I4iOgP3qc7dAiEAz3eBsyrwThp/EZaQjMsTJvFnWvtVcs/fkgtaRzyWGCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "433e879f9538d7e1e883ba914904f339c6414835", "gitHead": "e99a5f0ea262f2a6c7406e77dedddc655cef3fbb", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.15.9", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.4.3", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.13.2.tgz_1471813137691_0.8330481017474085", "host": "packages-12-west.internal.npmjs.com"}}, "1.14.0": {"name": "tar-fs", "version": "1.14.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.14.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "f99cc074bf33bed21cd921a21720797bb18e6c96", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.14.0.tgz", "integrity": "sha512-OcAbwLlUDfGujdELS5vrfnla8YR2wwgUWyJoVlymkPTXFPdiGaOrUBp2s2xoDKfIz6tbTbG5hnCXPrbC2XNTMA==", "signatures": [{"sig": "MEUCIQCeKG5jJYpTgATqzP4ctIUsJ25Ypxz+w7itk52YRUg0MQIgUxdgf4fLHRt4ibx/rM2AQUukT8TAmSF+fYQmz8x2j0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f99cc074bf33bed21cd921a21720797bb18e6c96", "gitHead": "a5e4f5e847fd6ed64195ab83ccc1637a986e28f1", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.2.6", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.14.0.tgz_1476305810597_0.8256293274462223", "host": "packages-16-east.internal.npmjs.com"}}, "1.15.0": {"name": "tar-fs", "version": "1.15.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.15.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "74c97dc773737c2aeacbfff246c654d6528a5315", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.15.0.tgz", "integrity": "sha512-rrE4m/bjb2ksYmS7UGdgR7bWT3QUY1PdROwSYcRWu5vWk6o1l2w8nJWXcQnVyZC5L7WFQsV7KpE+H7IBpVB/1A==", "signatures": [{"sig": "MEUCIQCKAEa7uIwWIpfXiiAYpAy7wDyRWqCuM51+WASY+e8TWQIgcCp8E2cYgd1uufyp/d1wTHR09UV7Cw8fpxkqLUh+u+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "74c97dc773737c2aeacbfff246c654d6528a5315", "gitHead": "bc28be17fa82d7c10c07f566970a34626f254da5", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "4.6.2", "dependencies": {"pump": "^1.0.0", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.15.0.tgz_1481918698874_0.2050120229832828", "host": "packages-18-east.internal.npmjs.com"}}, "1.15.1": {"name": "tar-fs", "version": "1.15.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.15.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "f4622f5d5e250742b3679a9a8463acfc12cdefd1", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.15.1.tgz", "integrity": "sha512-51m6+WGzQrcnf19nR5yZs3vdUfpq0o2d2UXA2ZpjwuuGRzFNePSSedXewwjmpiqCGM9Flq5Q33yTI3Be3ef0vQ==", "signatures": [{"sig": "MEUCIB2asihopDOsud8lzXZoS3LfiKDSO+71Aao/23YMRRIfAiEA0Sqlh621LRRtBT6qq+EVw+8RqHOMLVKqBKf1jl69/qQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f4622f5d5e250742b3679a9a8463acfc12cdefd1", "gitHead": "e59deed830fded0e4e5beb016d2df9c7054bb544", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "6.9.5", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.0", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.15.1.tgz_1487735847534_0.5334909483790398", "host": "packages-18-east.internal.npmjs.com"}}, "1.15.2": {"name": "tar-fs", "version": "1.15.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.15.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "761f5b32932c7b39461a60d537faea0d8084830c", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.15.2.tgz", "integrity": "sha512-qjHTD961nW+Kxm24FlluKORft8CNC0z4s2zqhpnkShd4nm55VDd38Q6XuWCUR9AuOscIDh+jGyW9DIvR3N+gLQ==", "signatures": [{"sig": "MEUCIDzI3YoUNayaLHfrhMecpHKqV39adD2dnPBaD+WSMFQtAiEA4Fl4cFWj13nCp/+/wVwq0lKQ/cG8AhM7SAL8+sSFAGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "761f5b32932c7b39461a60d537faea0d8084830c", "gitHead": "b79d82a79c5e21f6187462d7daaba1fc03cdd1de", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "6.9.5", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.15.2.tgz_1490092359091_0.23782586585730314", "host": "packages-18-east.internal.npmjs.com"}}, "1.15.3": {"name": "tar-fs", "version": "1.15.3", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.15.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "eccf935e941493d8151028e636e51ce4c3ca7f20", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.15.3.tgz", "integrity": "sha512-6AUctOWs1qStZLSujdZBqsW3+wwJH96D65u8dzlspqDfT5tVJBHTviVdo0aABP9ObYI0sCTqm2jEof0WasaK8Q==", "signatures": [{"sig": "MEQCIBctVHYS/DrK2FXAQPbqXnzugRm/Tx4Jrgizrus2DzZRAiAbNVAcWZx4vu0PiPj9bHZeYEiQh2Q3PIz/hkJbZbugOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "eccf935e941493d8151028e636e51ce4c3ca7f20", "gitHead": "4c89247d42c44dbd043ff71ec9c956dc07a249ef", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "6.10.3", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.15.3.tgz_1496735945450_0.5075000307988375", "host": "s3://npm-registry-packages"}}, "1.16.0": {"name": "tar-fs", "version": "1.16.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.16.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "e877a25acbcc51d8c790da1c57c9cf439817b896", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.0.tgz", "integrity": "sha512-I9rb6v7mjWLtOfCau9eH5L7sLJyU2BnxtEZRQ5Mt+eRKmf1F0ohXmT/Jc3fr52kDvjJ/HV5MH3soQfPL5bQ0Yg==", "signatures": [{"sig": "MEQCIE5W7Dis8uonUJ+B9Nzp4JaGFKc4Ov+b/ozHwL5/UiVeAiAGfMuD52asCBuNCnb25mxqrSPMxYSWl8KaXtJSvk62dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "e074d9cddea661df01ec6693a05b5b2d4cf573d0", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "8.6.0", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs-1.16.0.tgz_1507209045562_0.02224875008687377", "host": "s3://npm-registry-packages"}}, "1.16.1": {"name": "tar-fs", "version": "1.16.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.16.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "a8382bf5d5263d50c4902732b5ebfb31007fa819", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.1.tgz", "fileCount": 16, "integrity": "sha512-Cw1FWzBUmiQGldYaFbxiYDWkHLwL3FXQfryWbYkPxrlahkaC9Nr+29gPRz7WIwwzO0FVSb7MZKb8vAI1uSpdZQ==", "signatures": [{"sig": "MEQCIDFIbRijCwyymlBYWXxTyU5M5lLNI2KON7Zv4qjJJ80/AiAsLXaBaE481mYeacq8yFpc+AV9eDuanMqvOEFVFrErUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5ds3CRA9TVsSAnZWagAA/CAP/22cCMU8/PBK9Ykpbb1o\nY+PSTeJHQkRaKkiA71eRUd60FoJ0fNcF8G42eoKCr6TC+25LAKEoyiw4tmFf\n35eeAlH/XC/ShaDGtm7bK5xCUbnvVeCkQMcPLJdNsmKdV5yp6lNrXMN+B4wc\nDS9M2qBDasauU64/6nZ3RnQK7H6tqOosUJivxq1MXqfulKl07QwezGXfK/wZ\noRMhivs7g0ikiMLauuHpDjYGfw9hXOzftNhfEuvre3ns0pNNgwEhVsy7xASx\nyn/2w6uMENjnhrSZPAjts/Nks+R4CZPwV6tYc7uj3PIENYmGYme77DgHax+B\nD0O3ih9W6b/EBleBYYYueAF6aE3aKvqEHKDkTvffkjQ6eVdI912A5nvfRxCA\nW+yXSXKrutWofId30Knp5CfSbj5TcMS3wWWJDtgA7AJMWJLjdLoWnkk6rAg5\nYLu+NnP31NrCRxlKglr3jiQFtiQh7hNtfKBEjYV2SONSK+0gnlUrNH2joHrA\n9b0QWv591JlyhLU6YGmMaZuhLcktRYFtSoXd/WmEVJUIqP3pnxAHmXt1bdBg\nSWADcMqfvNLxjfuiwHf2cdWzAHkGQCEEteBmhhLRmtxu4OrOWsO7F77atJcs\nXXtQk9FyogzYYcS6PZl5X7Zd1iqYA6KhOl1Qx9KJmJVjYNrzkPPfjDKvLNLE\nb2nD\r\n=gk1q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d590fc7148ef1812a5669c947078de220c028526", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "9.11.1", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_1.16.1_1525013302054_0.35464590458608947", "host": "s3://npm-registry-packages"}}, "1.16.2": {"name": "tar-fs", "version": "1.16.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.16.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "17e5239747e399f7e77344f5f53365f04af53577", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.2.tgz", "fileCount": 16, "integrity": "sha512-LdknWjPEiZC1nOBwhv0JBzfJBGPJar08dZg2rwZe0ZTLQoRGEzgrl7vF3qUEkCHpI/wN9e7RyCuDhMsJUCLPPQ==", "signatures": [{"sig": "MEUCIQC83heiWwjqD6UKf6RVrdlWhnVwk501XtSNJGzM+u0sfgIgA7zXlquUFhr+RuAMTNrjkiKfFAeNw+UEJG/svi2Fb+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5w9NCRA9TVsSAnZWagAAKfUP/1FR+4lZeOWWkgfSgt1J\nb4/37rtp2dvnOdeIYjrMJE/Goi4HkDCGE3xn8YPsdQ1X9PGj+1LvuXb7VJQO\nUuRtD1uDdCmQv7AoFHJwEmTg6IIl60SiCE0N+5OJXlzPs0IbUiaMc5o0abOH\nUjIQkklVrnyDuR+rD+FD9DrwWWyauzrA4UXScdeadRRCMUVTXYXzlLhPErZ3\nwg5a9UVjMDbTYEYNUmpB2z4zBz6XWirL4G9UkvsmHbuyam49ayHx+fmW7wGI\ny9Z71PNjG32b0/Gzx5ho3wr0lC7WQ5uy5Mlc25K/gbdbhz9b+0Kb96QFN0f1\np1apuhMNl2AoVFjxn0dl8qxepnJtinyehn96KWjWsqeJUlVtvmCcCROioWnA\nqJeDY619qlwSXxJVKlmA7d0Dqw8Zf1B6qZuez6haaq8ANILJLV2CtLrXGX1g\nHUPRHJJGqnJ5eAWcGjF4FXjVuf0lntJqhU+IDBGz0WxKR9sI7ZfEQyD5a4x0\nd8cLvhl5maOEph+VYqSuQXjeWNTKu1WhL3qz2tsKzNijzMqeQ70+PMAOhF1y\nfITQK6Dzs9/fREaH7bZTckQJfUTlvJQXI6NlLZBthFfWN/VfYOnko3S73jIo\nSdBESE/CaJw/A1+M5hd/JROeQxrocckk2vPEVcbFNPJFaAB7RrnXc85KwsrG\neyYd\r\n=NGt3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a35ce2fd1e91f11cf2eaf78f284c35ad214dbe04", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "9.11.1", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_1.16.2_1525092172923_0.03814186974545253", "host": "s3://npm-registry-packages"}}, "1.16.3": {"name": "tar-fs", "version": "1.16.3", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.16.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "966a628841da2c4010406a82167cbd5e0c72d509", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.3.tgz", "fileCount": 16, "integrity": "sha512-NvCeXpYx7OsmOh8zIOP/ebG55zZmxLE0etfWRbWok+q2Qo8x/vOR/IJT1taADXPe+jsiu9axDb3X4B+iIgNlKw==", "signatures": [{"sig": "MEYCIQDWl3Mq5mXWCGYeCBcgDoGRwBkYlI0t1NrqvKMzjyoongIhAIq7tHeWihKkC0umqQs7i1RcBV51+ali0JiKxApygUUw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKg5YCRA9TVsSAnZWagAAxLcP/3ciyULwU9bea6dkO8g+\nlfnHf8GQGSuEVTXRCVNFN9oF9WxX793561Kr+blU2dRdn+B10PBeuUpdgFKd\nJn6DE2Tq77GMRKwUQbcNl/G4RMFPIKOd51yDn5mZjZVFYEF2TjOHYN6bJaWk\nbDch+WDzVuoTSQWOpz27dF9vLUFXMNTaJi2rb1WtXR4TzgVftfKhZtuFYYFv\nIN1Cs2jlI3L5aoUfJwNSBswpyJ2fZE65/IC/Drxa+GflFJO0KZ1GjdEhZx9v\n4pug+ASPqvA+Vx+EyZrv3wO6rpSz7+15jpFMtUeaArxp4QnNMDs+ogKBfQGp\n/j0wYxDFkajUyejxzBC5+XJr0Hoi8gmlQYCczD8O2fFvH5EeqNbmRLszpbri\nT2BFTRDFdEEQRIYvMzXlGxqE7KY+BPttONx6Iy0YpJMfKt0Jp4tmdPaE2kQO\nzvb5qtSf+7CzSzVRE9pXsksXYSDls1RA1w3NMCWtDQQE6zt/JtSogUsmWiAO\nlvT2JaGiphzh5aB6hnopfvY8CNJmz5BLODtdknw/z9Ab7Q2BL+7HkL2cQ/HX\n2yk41jmxswcN1ats6MapfprYYnlLmlK6oD5zIpk0ucbsXjVRndi7qvpScBvs\nk8wF7C08Kbhxnyj1t70ny8Pb5VetG/qno+wxJcFE5RpUMGUojrjP5cppXUmJ\nBhTI\r\n=Uk1O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "873cda1e15503b1686aa544b4420f42bd6aa55a5", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "10.3.0", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_1.16.3_1529482839287_0.9628256030497233", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "tar-fs", "version": "2.0.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@2.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "677700fc0c8b337a78bee3623fdc235f21d7afad", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.0.0.tgz", "fileCount": 16, "integrity": "sha512-vaY0obB6Om/fso8a8vakQBzwholQ7v5+uy+tF3Ozvxv1KNezmVQAiWtcNmMHFSFPqL3dJA8ha6gdtFbfX9mcxA==", "signatures": [{"sig": "MEUCIQDaoVyQ0UWhKO6aOjjdEsgjYwUGkwPtbeWX6YRIy/xSHAIgERMiwvk/Lc1E7wiz+Ys50MH2byVPiIQXV+3jJ0WaJvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWA3KCRA9TVsSAnZWagAAgBYP/2NCAKV0XURM2q4H6qER\nHsOK98IRMRfFjn6hchrEIXS3oG5AADK10S+314eEogSwNeynTSW9HDSiSFEP\nQNx2jpi1rgrL3A2QO8tctCXWqMmQXvlWef6AXdi8DPAPBE1yukKo8gkUUYx7\n89e1l6y8u83QmEqXnYThVh1lmP9VXHQznrZMfsRed1Fu9nwW/DIkULvJ25/J\nF58AUkyGOgK1e89gxMZVaqjUO8XU1H3y0dFFGBSHt/B0VWt9aIU/e6V7PCXF\nYGVlU0jiS22wS3yfROBdR0KXdewS4raM+K5n9hze0JLR77SybXlVM6s9ccJu\nRsf1lwvu9Hlnljn55XeasB82UxSVIaFFdVEnm46AmjW3Qy12WqCTrcwoVg2u\nH0yzcOQx/z8xD1aCqaCs2bVuz7Xxi0kCdMoDsvYMTKEf0SPvKxZp+epQLtJ4\nsp4pWdIfABxIp+Bp6lsAOiunxzvL+W5WAGignjCQ4apWLqexMOlgeBDFvCf0\n+49PllFd2TM68WgldCmMC0jDQvpdrzyDxJQXJ52F63GHllkzo3ajlcoV8/bS\nZ3O8xthTQ4oGWQrK5mRRIpUfMzer+tIqJWB15oRVW6mMxRBEH/WHK5Wcma2Q\nSXdYKFAH59SIDL2Dmnm8vhOydsNq8rYvPTKxMevrhJneBDbp5JUkUK3RIRdR\nn0IO\r\n=kxCL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0f54a78bcc8735c4257177fd004c2f9e55c588bb", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "10.15.1", "dependencies": {"pump": "^3.0.0", "chownr": "^1.1.1", "mkdirp": "^0.5.1", "tar-stream": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.2", "rimraf": "^2.6.3", "standard": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_2.0.0_1549274570344_0.9889846152819155", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "tar-fs", "version": "2.0.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@2.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "e44086c1c60d31a4f0cf893b1c4e155dabfae9e2", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.0.1.tgz", "fileCount": 16, "integrity": "sha512-6tzWDMeroL87uF/+lin46k+Q+46rAJ0SyPGz7OW7wTgblI273hsBqk2C1j0/xNadNLKDTUL9BukSjB7cwgmlPA==", "signatures": [{"sig": "MEQCID1fglg84OEV1TkeoYQsIhY5bH9GFUKJdvnRFjbYVxIoAiA7Cp0kA5xb0zsdGOj2JSuO/NCfAgFgaYuHT1WaSqesWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeenNBCRA9TVsSAnZWagAAIXAP/2OP8SLWBRst9YMdFrF+\nyKhMcqw3QXJ20vL7v/6VFeTTCI4o3tATlEnP0lW/NBPDUO+T300NHw3+QLac\nG39iXf7U6n4iA/aznY6zM0uBrtU9bsiBDHpW9sA4uFz1ZB9lJ4wRQxUGxWCb\nI+TXtnMgN77upeGePhjVAzdT+TsT6K6aVjs77SBlAoqdp+bvx4FPpRC7QhK1\nFKQfayoJwpAMkW6AXu9veJTt2OmxnfxjmQ7dbVi+uJTV3TeOhKfQsbI73IRi\nOydRAEvog7kIII+wX9PL7zGRO71bPaJCQ8SYrae/kRjnvF9eG9g1k9kdloQC\n6QS3PAftD2sKLGj8hZtxwuYCB7R8VY67Mk9KdE4kP4rIgMB3NKAYVj1Fsz5C\nr8Ukr01jaKkxsGwjwPIBVmBZ/dTM+QXUKx1KUcjG9pNalX2c8QDNNOHy3Qik\nPh/+XtfkgJUXIUreMw5aSLvL5nd+/N5eCakpu7XmoErOS4Wk6XhOboKQyTqE\nXakwEmdtOsLavciLuLrxQgySEok8GQ0l2dXKoQbJEmRb5UTzJ+30KqtOG0q2\nqPgvOtCQiCAhosKG6leQO0R3eHNgAeqq8Kpi1Q3VzyYW1CoKNvjdvfMzP5Jp\nfsAx4ckW+OX8ak+THQ0rC2UpyGeu3OcJZd1bT3DysS9MDWUEHf52WsS4yBZZ\nJ9gI\r\n=yhEy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "6e1068520eb2702500e858888ff33a474d80420c", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "13.9.0", "dependencies": {"pump": "^3.0.0", "chownr": "^1.1.1", "tar-stream": "^2.0.0", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.2", "rimraf": "^2.6.3", "standard": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_2.0.1_1585083201065_0.27426726467201257", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "tar-fs", "version": "2.1.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@2.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "d1cdd121ab465ee0eb9ccde2d35049d3f3daf0d5", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.0.tgz", "fileCount": 16, "integrity": "sha512-9uW5iDvrIMCVpvasdFHW0wJPez0K4JnMZtsuIeDI7HyMGJNxmDZDOCQROr7lXyS+iL/QMpj07qcjGYTSdRFXUg==", "signatures": [{"sig": "MEUCIQDTdnyr1zIl90+ul06sKMNy4ZjVPFxp6GrXr9YT6IO5xAIgcXsjNdBgv4Nt8AzRGGeg9EuZj8U70JkzwwsG2+SfWCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27799, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeua+wCRA9TVsSAnZWagAAny8QAIYxWq8kFKvKywnqH7Ay\n1S7XhqGB1TIwCwAscgaB0RiSydpa8QtCRfXQoIAbDtMhSyi59yWd7soRZSkc\n5EGGAvHNi3tPsGlj2r67cDmEDBBf2s49DP4ou9nic70kZu0cf353PRAFUcOm\njviQjFnsRbXxx/87IBV8w8z0Q98sKU3AHGVGPSSBgvNmU52bN68PP3LGlbzN\nKhSB23HRwhp2q7jx5BplM7UDyMEEXt8c4wvrifW4cn95WorS08WjslJfojOj\nTqEv+j/UQgCRhUVH41lJ2loqYMK65CC0+RZ/Co1IVs7T0zE7M4Z9j9tZ2dUB\nuF1m6AWy7vrksrlorgQELH3wsolU2ZjXn5S+i0jieWSkM6ZLU44KSZm9ymho\nkRb34F1zzgwdTfh3itFT8Bni8F4V2f3L4DUuN2BcvwYWj06uUrIWspE0Fsep\nF9uMe2HzOHHLm7fJu5ilMuZqX/2+GALOZ70l40KoDXKS2IJWvD9TMYctwhsB\n+p4V17PrFCl/cwjPpiaZg2i07FK/vwMXxO+139nm6tHlcLdTfw7lItbvCa8K\nMtDRY4EGXPdlncPIGJ1FYHT8Z8zAqCxlVwLxxvTqZ9gC33AAG6N5lFFuEVY9\n7hpzrcLrf1b4htT1b4tNF3I4Gkyjto0sl76ee8Ns2OwWXuZqV3DptCf3fava\nUD2k\r\n=wUx8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "33971c272f06958473b52841294fe1528363bc12", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "12.16.3", "dependencies": {"pump": "^3.0.0", "chownr": "^1.1.1", "tar-stream": "^2.0.0", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.2", "rimraf": "^2.6.3", "standard": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_2.1.0_1589227439758_0.9789647586680021", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "tar-fs", "version": "2.1.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@2.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "489a15ab85f1f0befabb370b7de4f9eb5cbe8784", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.1.tgz", "fileCount": 16, "integrity": "sha512-V0r2Y9scmbDRLCNex/+hYzvp/zyYjvFbHPNgVTKfQvVrb6guiE/fxP+XblDNR011utopbkex2nM4dHNV6GDsng==", "signatures": [{"sig": "MEYCIQD9iN9wpHqV6i0zKHbHDLrsMRVWo+a31WDdS+MpxFn2rQIhANydN+tdwCMlCfPLrkV9kYl1vQBvufXfzD5y+zndHiED", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpZlhCRA9TVsSAnZWagAA1hMP/AgdCqh62oLUr8ABRNHi\n9kEu45aD0Daax9lT5gk29VhXWNPs5hQ71ik5204r83rLdMzJL/am3dwnZ6D9\nR0sHJJ6PyRn+2IIIgo5N0WmLaVPbkIJ+2Md9ItMhbBq5kuAweSL2Ss0irwrb\n0Ph8IwPsurUBxcfUjIjO0QVo5GGUbmciZ6+FqiOFBmSYS51mNawi+Ov1vNDS\n7cRT15EBWX8SlF8Lj4bP1Fl1TYCbhVNiaeekOSGKFoHsLOwNUGw0ShRL5r9z\nE3bX23GVdpYabZgMH6aOR6c9JfSYy9PfbeHyNNooJpnSvNsymEKdFd7uegoa\na7f3kqpcMqH5UACSEn32QXwhv2WxwGF21YPPS1SAZoeN/5m/0QWwNtO94GUX\n7hDKH85YZDnPARexRmf4E8cv7UrJey6H8/AanPMyn1LTYJXwaSUshROxZgHz\nQXVkUHuoRT0x3nrjvuODoFRFbE9114bok3NLBPP0uQSdvgw4DtUhMD+j2WxT\nsRPY/FL6+4pJPih6VjJWsHzbVJztG+x8nylT3WlaL9oF9AR7Orcy6Hy67IdI\nG4nJoH1dX+dlPNhMKK5D1U0vNAdSmxpUZJhXeH6a9kh09v3Jw0WVktei+eL0\nvE1SMpEhT8NByDC+m7aU1A6qx41amW8rYw/sLEAsZrZUVKPteaa5mkq84TcX\nCfde\r\n=mNfI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "7ec11cb27f93948193770f32b4d820e2e7195715", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "14.15.0", "dependencies": {"pump": "^3.0.0", "chownr": "^1.1.1", "tar-stream": "^2.1.4", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.2", "rimraf": "^2.6.3", "standard": "^13.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_2.1.1_1604688225202_0.3751737256096017", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "tar-fs", "version": "3.0.0", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "08e0dd41625842a3a682c23f430e410da30af51b", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-ugBSDFgExxmDSZgvqs3dcnldzH05Za9Sntv921Bh7v8o8qwyC8EM3Py57NR6p4Ub34zNUj1BGFGM9ZqGHvRmxA==", "signatures": [{"sig": "MEQCIHKv4taBtWuZhM4Mr0dT2dDNFJLm5zgOvlSv+XuEAmaLAiAkHrfhZAaGYRQB9Z+tkvF50Ud5P0AoVnPY63QIeDhvrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17065}, "main": "index.js", "gitHead": "6d66143dc5e40480dd6135a4453f6da26a5602e0", "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "18.16.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.0", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.0_1687032365537_0.9285284602939807", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "tar-fs", "version": "3.0.1", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "c07fb2c447a5d8cc63e8a7e5c51a83703a8a33e3", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-MnbwT/swgoiYQzynx0RCUsgvUGhLhF1uxuc64dOmVG+kVGWFzn24oaM11LgZWZXhpEJcKNTSmrfPZd4A5rktoQ==", "signatures": [{"sig": "MEYCIQDf3NyPTqdaHM1GNAXKvP89QtiKGCDLdUpqLGEyiQKL9gIhAKRXU8hu1E9TPF1kJFM6VDiH4qVi8D7gcyxvSazUVCGc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16595}, "main": "index.js", "gitHead": "9076ed087e0e7e23155439cad60cfbeec9a45d9a", "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "18.16.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.0", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.1_1687032708697_0.8138284959714894", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "tar-fs", "version": "3.0.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "41bed7bafd700f61e8c13a7efbf84f7a1fc81969", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.2.tgz", "fileCount": 4, "integrity": "sha512-mLQ5iTTCv2tt3a4BwvD8QX1YFVBL/94/Nd+U2il38wt2+zaJSusp1VwJSNkBmB48FeTdOqptf1DAUIosXQBRrQ==", "signatures": [{"sig": "MEQCIDZJ6tG5Qm9CTL2s4nXPaCv2aaAC7wv/vvgSx1/Z02eVAiBNeBq2q2HfKsALvrlk6x64YvD15qsvLIjH2ebPy7893A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16591}, "main": "index.js", "gitHead": "c87e65b6c1d9dfa6b107889aad3558dcdd919a3d", "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "18.16.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.0", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.2_1687035562353_0.6999237852248368", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "tar-fs", "version": "3.0.3", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "6593e7df92f337e74d755be183a192213d923050", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.3.tgz", "fileCount": 4, "integrity": "sha512-ZK36riGYnFI6LujIBfBRoDfeaaWUkStIFKwtPjnDWCKnsDE9kuQthG09aQjLjpzoRtVElEMZ/AIAURNb7N9mkA==", "signatures": [{"sig": "MEQCIGsJDcvEF8h91ojq6affBnwmrT9Ce83aQI96PUPedYOmAiASd43+oZFj1QC0HWOEcW9vq3oQf4Obak4UtFTTGg/z3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16644}, "main": "index.js", "gitHead": "7e10a5b416f2b65152a102db16a50f492234b406", "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "18.16.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.0", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.3_1687259038595_0.4870329611718869", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "tar-fs", "version": "3.0.4", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "a21dc60a2d5d9f55e0089ccd78124f1d3771dbbf", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.4.tgz", "fileCount": 4, "integrity": "sha512-5AFQU8b9qLfZCX9zp2duONhPmZv0hGYiBPJsyUdqMjzq/mqVpy/rEUSeHk1+YitmxugaptgBh5oDGU3VsAJq4w==", "signatures": [{"sig": "MEYCIQD8AuD9Y926gPq1UXSuKW+zljnQ8wN2yPxE0Zjfy0dyKQIhAKjVqh2xdiopODm9SWzOSUvBuLrCzKi8OZdAt9UufagO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16644}, "main": "index.js", "gitHead": "30ce1ce5659d5820f7a07bef910cf094edf345fc", "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "18.16.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.4_1688310803028_0.5877459283692321", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "tar-fs", "version": "3.0.5", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "f954d77767e4e6edf973384e1eb95f8f81d64ed9", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.5.tgz", "fileCount": 4, "integrity": "sha512-JOgGAmZyMgbqpLwct7ZV8VzkEB6pxXFBVErLtb+XCOqzc6w1xiWKI9GVd6bwk68EX7eJ4DWmfXVmq8K2ziZTGg==", "signatures": [{"sig": "MEQCID6rWQ+Sux3gCUH9g//vI6+b8u3sqOE/v5fDP8h4bHZFAiA5LW54qv+WleMvbDU/yRzpGeKmmlENwF7swwl/e1G/dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16846}, "main": "index.js", "gitHead": "8d561e6b5827a308dcebbeb77525ed10ffed9bea", "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "20.10.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "optionalDependencies": {"bare-fs": "^2.1.1", "bare-path": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.5_1707473006655_0.36098253805915226", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "tar-fs", "version": "3.0.6", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.6", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "eaccd3a67d5672f09ca8e8f9c3d2b89fa173f217", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.6.tgz", "fileCount": 4, "integrity": "sha512-iokBDQQkUyeXhgPYaZxmczGPhnhXZ0CmrqI+MOb/WFGS9DW5wnfrLgtjUJBvz50vQ3qfRwJ62QVoCFu8mPVu5w==", "signatures": [{"sig": "MEUCIQDS652RWTHDX1wMNESgHaZalkLdX57K1qUPbhXCZ04bbwIgLQqE4YedFrWzVmNcH8L3FC3tkNEH9sT7oa1KFt5jYKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16877}, "main": "index.js", "gitHead": "3082593d8c5a6422176beb86510cd191398b3bfb", "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "20.11.1", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "optionalDependencies": {"bare-fs": "^2.1.1", "bare-path": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.6_1714141525946_0.7493708081219166", "host": "s3://npm-registry-packages"}}, "3.0.7": {"name": "tar-fs", "version": "3.0.7", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.7", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "b8ecd22f9452e5116b93273a754a1f835edb5319", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.7.tgz", "fileCount": 4, "integrity": "sha512-2sAfoF/zw/2n8goUGnGRZTWTD4INtnScPZvyYBI6BDlJ3wNR5o1dw03EfBvuhG6GBLvC4J+C7j7W+64aZ0ogQA==", "signatures": [{"sig": "MEYCIQDAoTvC0d+NOz5nZ3PynZgQ9fuLD0zlNZHIKd0+KBGLhAIhAL7NEsYZZb1IgWx1JOKTFv4V5Lt+8gFdv9sW/bdVdXbV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17112}, "main": "index.js", "gitHead": "1e4cc042b7a767a69c9728412d39d588519094f6", "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "optionalDependencies": {"bare-fs": "^2.1.1", "bare-path": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.7_1736679216710_0.21503928011073237", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.2": {"name": "tar-fs", "version": "2.1.2", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@2.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "425f154f3404cb16cb8ff6e671d45ab2ed9596c5", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.2.tgz", "fileCount": 16, "integrity": "sha512-EsaAXwxmx8UB7FRKqeozqEPop69DXcmYwTQwXvyAPF352HJsPdkVhvTaDPYqfNgruveJIJy3TA2l+2zj8LJIJA==", "signatures": [{"sig": "MEQCIB0rPHSZp7aqyIrggtLnGW30tFi+nSCpdYsEwIHYc6FLAiBwA5x/jCW7DxvgDMX4HVJ0a8W1bErEDS90IL37BsL6+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28201}, "main": "index.js", "gitHead": "d97731b0e1b8a244ab859784b514cfcf5585ad3d", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^3.0.0", "chownr": "^1.1.1", "tar-stream": "^2.1.4", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.9.2", "rimraf": "^2.6.3", "standard": "^13.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_2.1.2_1736679472801_0.19131496140799853", "host": "s3://npm-registry-packages-npm-production"}}, "1.16.4": {"name": "tar-fs", "version": "1.16.4", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.16.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "d3f0e1edf164b821f086640b1f0596f004021dc8", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.4.tgz", "fileCount": 16, "integrity": "sha512-u3XczWoYAIVXe5GOKK6+VeWaHjtc47W7hyuTo3+4cNakcCcuDmlkYiiHEsECwTkcI3h1VUgtwBQ54+RvY6cM4w==", "signatures": [{"sig": "MEUCIFN55YRBlW+Rv23Z3SHhM3jrV2XWXzkTXx0yTPGFpEWpAiEA5eq+Xf2AOkP5comkcN0xV8YeFXVr0qB+B+0MhTzTKEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27777}, "main": "index.js", "gitHead": "ff6510d7ba63b6ee200442b80f4326f369e7d4bd", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_1.16.4_1736680451667_0.31519313818241534", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.8": {"name": "tar-fs", "version": "3.0.8", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.8", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "8f62012537d5ff89252d01e48690dc4ebed33ab7", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.8.tgz", "fileCount": 4, "integrity": "sha512-ZoROL70jptorGAlgAYiLoBLItEKw/fUxg9BSYK/dF/GAGYFJOJJJMvjPAKDJraCXFwadD456FCuvLWgfhMsPwg==", "signatures": [{"sig": "MEQCIAwDKxFnj0snsEdqjZcRCJBKo8aRv7V7QusWDoT+c1DjAiAuvIzq0s9XauxdnDHGe0KV52sGDqZmLF/rKu6JEHlmxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17112}, "main": "index.js", "gitHead": "e4a7a401e80267247b8e9e39d8e5ba82c4fe2f7b", "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.8_1736885148453_0.8714757273823386", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.9": {"name": "tar-fs", "version": "3.0.9", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.9", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "d570793c6370d7078926c41fa422891566a0b617", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.9.tgz", "fileCount": 4, "integrity": "sha512-XF4w9Xp+ZQgifKakjZYmFdkLoSWd34VGKcsTCwlNWM7QG3ZbaxnTsaBwnjFZqHRf/rROxaR8rXnbtwdvaDI+lA==", "signatures": [{"sig": "MEQCIG+VzHKe9WR45S+qk9+UiPBZy7PYeTO72xAXDOamTdM3AiAm1JbvuBS/0x4w0Q5OIKMxHNrgKC+RTVOTJ3Xr5yIxYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17381}, "main": "index.js", "gitHead": "2ceedf4cf807e89a071ebd585291aa785c980829", "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.9_1747939644758_0.3328780980087154", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.3": {"name": "tar-fs", "version": "2.1.3", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@2.1.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "fb3b8843a26b6f13a08e606f7922875eb1fbbf92", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.3.tgz", "fileCount": 16, "integrity": "sha512-090nwYJDmlhwFwEW3QQl+vaNnxsO2yVsd45eTKRBzSzu+hlb1w2K9inVq5b0ngXuLVqQ4ApvsUHHnu/zQNkWAg==", "signatures": [{"sig": "MEUCIQDgykJVm317nMaF1zV21cDW6Zz645lFlR8QvbjoHHuoDwIgOP78XBdL1mMKY8VBctacY/MLAjxI9BE4A6WU9Xuc86E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28390}, "main": "index.js", "gitHead": "4b7e8688a54268b7c3268848504167635050aa10", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^3.0.0", "chownr": "^1.1.1", "tar-stream": "^2.1.4", "mkdirp-classic": "^0.5.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^4.9.2", "rimraf": "^2.6.3", "standard": "^13.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_2.1.3_1747941779374_0.9800206318410574", "host": "s3://npm-registry-packages-npm-production"}}, "1.16.5": {"name": "tar-fs", "version": "1.16.5", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@1.16.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "716a323609c11182d1d3d7b5bf277d15dc128665", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-1.16.5.tgz", "fileCount": 16, "integrity": "sha512-1ergVCCysmwHQNrOS+Pjm4DQ4nrGp43+Xnu4MRGjCnQu/m3hEgLNS78d5z+B8OJ1hN5EejJdCSFZE1oM6AQXAQ==", "signatures": [{"sig": "MEYCIQCIG0edqdK0fRT/XHB6eKFER/oOuwQkBNoclziU4A80DAIhANL3HUFtYlucfE4i5gUiKH0h0YncMIBBf7pzBkFy6tbh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27966}, "main": "index.js", "gitHead": "3c6eddb6008d14f4ca83a439dd432263d29ecb0f", "scripts": {"test": "standard && tape test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.12.0", "dependencies": {"pump": "^1.0.0", "chownr": "^1.0.1", "mkdirp": "^0.5.1", "tar-stream": "^1.1.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^3.0.0", "rimraf": "^2.2.8", "standard": "^4.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_1.16.5_1747941937008_0.24277553533949514", "host": "s3://npm-registry-packages-npm-production"}}, "3.0.10": {"name": "tar-fs", "version": "3.0.10", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "tar-fs@3.0.10", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/tar-fs", "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "dist": {"shasum": "60f8ccd60fe30164bdd3d6606619650236ed38f7", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.0.10.tgz", "fileCount": 4, "integrity": "sha512-C1SwlQGNLe/jPNqapK8epDsXME7CAJR5RL3GcE6KWx1d9OUByzoHVcbu1VPI8tevg9H8Alae0AApHHFGzrD5zA==", "signatures": [{"sig": "MEQCIF8oOMSFEHNfqJbfIxJYmzAxKlrMzHI2Doc/rJOMnlXQAiA0wMw2GWlHQdtwrrLWJgh4/nZLZeKYf8KMMBimqjVGig==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17430}, "main": "index.js", "gitHead": "5bfe6dfb9d26436829ec6a6400eca3a030d4757a", "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "scripts": {"test": "standard && brittle test/index.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "standard": {"ignore": ["test/fixtures/**"]}, "repository": {"url": "git+https://github.com/mafintosh/tar-fs.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "filesystem bindings for tar-stream", "directories": {"test": "test"}, "_nodeVersion": "22.16.0", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"rimraf": "^2.6.3", "brittle": "^3.1.3", "standard": "^17.0.1"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/tar-fs_3.0.10_1750076885783_0.473080841385741", "host": "s3://npm-registry-packages-npm-production"}}, "3.1.0": {"name": "tar-fs", "version": "3.1.0", "description": "filesystem bindings for tar-stream", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}, "imports": {"fs": {"bare": "bare-fs", "default": "fs"}, "path": {"bare": "bare-path", "default": "path"}}, "standard": {"ignore": ["test/fixtures/**"]}, "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "devDependencies": {"brittle": "^3.1.3", "rimraf": "^2.6.3", "standard": "^17.0.1"}, "scripts": {"test": "standard && brittle test/index.js"}, "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "homepage": "https://github.com/mafintosh/tar-fs", "main": "index.js", "directories": {"test": "test"}, "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-fs.git"}, "_id": "tar-fs@3.1.0", "gitHead": "cb1c571fba8ec6dd56340f55dcd5d284372a8249", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-5Mty5y/sOF1YWj1J6GiBodjlDc05CUR8PKXrsnFAiSG0xA+GHeWLovaZPYUDXkH/1iKRf2+M5+OrRgzC7O9b7w==", "shasum": "4675e2254d81410e609d91581a762608de999d25", "tarball": "https://registry.npmjs.org/tar-fs/-/tar-fs-3.1.0.tgz", "fileCount": 4, "unpackedSize": 17508, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDu1OIcw0XIDAtk/RPxs8oeKG9stBF+9G7PH4FpScVbuQIhAODvJ9R2vybuTOwAIPHijsbq/rEAL9G1y2Qw6GDAEpJv"}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/tar-fs_3.1.0_1751270037729_0.9790210112984037"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-12-21T04:05:14.603Z", "modified": "2025-06-30T07:53:58.072Z", "0.1.0": "2013-12-21T04:05:14.603Z", "0.1.1": "2013-12-21T22:00:59.028Z", "0.1.2": "2013-12-23T00:45:01.209Z", "0.1.3": "2013-12-23T00:52:29.390Z", "0.1.4": "2013-12-23T00:56:06.498Z", "0.1.5": "2013-12-23T13:27:34.211Z", "0.1.6": "2013-12-25T20:32:35.619Z", "0.1.7": "2014-01-13T20:39:43.316Z", "0.1.8": "2014-01-28T09:54:31.663Z", "0.2.0": "2014-02-12T19:37:28.498Z", "0.2.1": "2014-02-12T19:44:42.487Z", "0.2.2": "2014-02-12T20:02:31.634Z", "0.3.0": "2014-03-31T06:45:35.392Z", "0.3.1": "2014-04-03T00:45:47.129Z", "0.3.2": "2014-04-11T12:32:38.637Z", "0.3.3": "2014-05-30T12:23:27.264Z", "0.4.0": "2014-07-10T14:37:43.801Z", "0.4.1": "2014-07-10T15:10:06.509Z", "0.5.0": "2014-08-20T19:41:55.348Z", "0.5.1": "2014-09-23T14:07:56.743Z", "0.5.2": "2014-09-23T14:10:42.888Z", "1.0.0": "2014-09-24T04:17:41.713Z", "1.1.0": "2014-10-10T07:30:01.463Z", "1.2.0": "2014-10-16T07:10:01.296Z", "1.3.0": "2014-11-25T10:03:14.368Z", "1.4.0": "2014-11-27T09:00:31.355Z", "1.4.1": "2014-12-04T08:20:38.473Z", "1.4.2": "2015-01-07T09:21:28.722Z", "1.5.0": "2015-03-02T12:49:55.980Z", "1.5.1": "2015-05-14T23:56:37.777Z", "1.6.0": "2015-06-22T15:18:27.264Z", "1.7.0": "2015-06-30T12:26:57.030Z", "1.8.0": "2015-07-28T22:41:18.290Z", "1.8.1": "2015-07-30T10:25:46.765Z", "1.9.0": "2015-12-16T11:14:51.128Z", "1.10.0": "2016-02-09T09:40:31.968Z", "1.11.0": "2016-02-27T18:08:12.700Z", "1.11.1": "2016-02-28T18:43:33.872Z", "1.12.0": "2016-03-31T06:00:58.299Z", "1.13.0": "2016-05-22T22:12:44.988Z", "1.13.1": "2016-08-18T23:17:56.871Z", "1.13.2": "2016-08-21T20:58:59.443Z", "1.14.0": "2016-10-12T20:56:51.474Z", "1.15.0": "2016-12-16T20:04:59.558Z", "1.15.1": "2017-02-22T03:57:28.159Z", "1.15.2": "2017-03-21T10:32:39.700Z", "1.15.3": "2017-06-06T07:59:06.297Z", "1.16.0": "2017-10-05T13:10:46.628Z", "1.16.1": "2018-04-29T14:48:22.153Z", "1.16.2": "2018-04-30T12:42:52.997Z", "1.16.3": "2018-06-20T08:20:39.349Z", "2.0.0": "2019-02-04T10:02:50.464Z", "2.0.1": "2020-03-24T20:53:21.172Z", "2.1.0": "2020-05-11T20:03:59.894Z", "2.1.1": "2020-11-06T18:43:45.307Z", "3.0.0": "2023-06-17T20:06:05.793Z", "3.0.1": "2023-06-17T20:11:48.844Z", "3.0.2": "2023-06-17T20:59:22.476Z", "3.0.3": "2023-06-20T11:03:58.759Z", "3.0.4": "2023-07-02T15:13:23.233Z", "3.0.5": "2024-02-09T10:03:26.794Z", "3.0.6": "2024-04-26T14:25:26.132Z", "3.0.7": "2025-01-12T10:53:36.879Z", "2.1.2": "2025-01-12T10:57:52.985Z", "1.16.4": "2025-01-12T11:14:11.909Z", "3.0.8": "2025-01-14T20:05:48.631Z", "3.0.9": "2025-05-22T18:47:24.949Z", "2.1.3": "2025-05-22T19:22:59.592Z", "1.16.5": "2025-05-22T19:25:37.182Z", "3.0.10": "2025-06-16T12:28:05.955Z", "3.1.0": "2025-06-30T07:53:57.882Z"}, "bugs": {"url": "https://github.com/mafintosh/tar-fs/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/mafintosh/tar-fs", "keywords": ["tar", "fs", "file", "tarball", "directory", "stream"], "repository": {"type": "git", "url": "git+https://github.com/mafintosh/tar-fs.git"}, "description": "filesystem bindings for tar-stream", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "maxogden", "email": "<EMAIL>"}], "readme": "# tar-fs\n\nFilesystem bindings for [tar-stream](https://github.com/mafintosh/tar-stream).\n\n```\nnpm install tar-fs\n```\n\n## Usage\n\ntar-fs allows you to pack directories into tarballs and extract tarballs into directories.\n\nIt doesn't gunzip for you, so if you want to extract a `.tar.gz` with this you'll need to use something like [gunzip-maybe](https://github.com/mafintosh/gunzip-maybe) in addition to this.\n\n``` js\nconst tar = require('tar-fs')\nconst fs = require('fs')\n\n// packing a directory\ntar.pack('./my-directory').pipe(fs.createWriteStream('my-tarball.tar'))\n\n// extracting a directory\nfs.createReadStream('my-other-tarball.tar').pipe(tar.extract('./my-other-directory'))\n```\n\nTo ignore various files when packing or extracting add a ignore function to the options. `ignore`\nis also an alias for `filter`. Additionally you get `header` if you use ignore while extracting.\nThat way you could also filter by metadata.\n\n``` js\nconst pack = tar.pack('./my-directory', {\n  ignore (name) {\n    return path.extname(name) === '.bin' // ignore .bin files when packing\n  }\n})\n\nconst extract = tar.extract('./my-other-directory', {\n  ignore (name) {\n    return path.extname(name) === '.bin' // ignore .bin files inside the tarball when extracing\n  }\n})\n\nconst extractFilesDirs = tar.extract('./my-other-other-directory', {\n  ignore (_, header) {\n    // pass files & directories, ignore e.g. symlinks\n    return header.type !== 'file' && header.type !== 'directory'\n  }\n})\n```\n\nYou can also specify which entries to pack using the `entries` option\n\n```js\nconst pack = tar.pack('./my-directory', {\n  entries: ['file1', 'subdir/file2'] // only the specific entries will be packed\n})\n```\n\nIf you want to modify the headers when packing/extracting add a map function to the options\n\n``` js\nconst pack = tar.pack('./my-directory', {\n  map (header) {\n    header.name = 'prefixed/'+header.name\n    return header\n  }\n})\n\nconst extract = tar.extract('./my-directory', {\n  map (header) {\n    header.name = 'another-prefix/'+header.name\n    return header\n  }\n})\n```\n\nSimilarly you can use `mapStream` incase you wanna modify the input/output file streams\n\n``` js\nconst pack = tar.pack('./my-directory', {\n  mapStream (fileStream, header) {\n    // NOTE: the returned stream HAS to have the same length as the input stream.\n    // If not make sure to update the size in the header passed in here.\n    if (path.extname(header.name) === '.js') {\n      return fileStream.pipe(someTransform)\n    }\n    return fileStream\n  }\n})\n\nconst extract = tar.extract('./my-directory', {\n  mapStream (fileStream, header) {\n    if (path.extname(header.name) === '.js') {\n      return fileStream.pipe(someTransform)\n    }\n    return fileStream\n  }\n})\n```\n\nSet `options.fmode` and `options.dmode` to ensure that files/directories extracted have the corresponding modes\n\n``` js\nconst extract = tar.extract('./my-directory', {\n  dmode: parseInt(555, 8), // all dirs should be readable\n  fmode: parseInt(444, 8) // all files should be readable\n})\n```\n\nIt can be useful to use `dmode` and `fmode` if you are packing/unpacking tarballs between *nix/windows to ensure that all files/directories unpacked are readable.\n\nAlternatively you can set `options.readable` and/or `options.writable` to set the dmode and fmode to readable/writable.\n\n``` js\nvar extract = tar.extract('./my-directory', {\n  readable: true, // all dirs and files should be readable\n  writable: true, // all dirs and files should be writable\n})\n```\n\nSet `options.strict` to `false` if you want to ignore errors due to unsupported entry types (like device files)\n\nTo dereference symlinks (pack the contents of the symlink instead of the link itself) set `options.dereference` to `true`.\n\n## Copy a directory\n\nCopying a directory with permissions and mtime intact is as simple as\n\n``` js\ntar.pack('source-directory').pipe(tar.extract('dest-directory'))\n```\n\n## Interaction with [`tar-stream`](https://github.com/mafintosh/tar-stream)\n\nUse `finalize: false` and the `finish` hook to\nleave the pack stream open for further entries (see\n[`tar-stream#pack`](https://github.com/mafintosh/tar-stream#packing)),\nand use `pack` to pass an existing pack stream.\n\n``` js\nconst mypack = tar.pack('./my-directory', {\n  finalize: false,\n  finish (sameAsMypack) {\n    mypack.entry({name: 'generated-file.txt'}, \"hello\")\n    tar.pack('./other-directory', {\n      pack: sameAsMypack\n    })\n  }\n})\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "users": {"d0x7": true, "panlw": true, "ahme-t": true, "gliviu": true, "itonyyo": true, "julien-f": true, "maxogden": true, "maxorelus": true, "coderaiser": true, "alxe.master": true, "flumpus-dev": true, "brianneisler": true, "shanewholloway": true}}