{"_id": "@types/yauzl", "_rev": "508-aae699473729c1b67e22d932b32e9014", "name": "@types/yauzl", "dist-tags": {"ts2.0": "2.9.1", "ts2.1": "2.9.1", "ts2.2": "2.9.1", "ts2.3": "2.9.1", "ts2.4": "2.9.1", "ts2.5": "2.9.1", "ts2.6": "2.9.1", "ts2.7": "2.9.1", "ts2.8": "2.9.1", "ts2.9": "2.9.1", "ts3.0": "2.9.1", "ts3.1": "2.9.1", "ts3.2": "2.9.1", "ts3.3": "2.9.1", "ts3.4": "2.9.1", "ts3.5": "2.9.1", "ts3.6": "2.9.2", "ts3.7": "2.9.2", "ts3.8": "2.9.2", "ts3.9": "2.10.0", "ts4.0": "2.10.0", "ts4.1": "2.10.0", "ts4.2": "2.10.0", "ts4.3": "2.10.0", "ts4.4": "2.10.0", "ts5.8": "2.10.3", "ts5.7": "2.10.3", "latest": "2.10.3", "ts4.5": "2.10.3", "ts4.6": "2.10.3", "ts4.7": "2.10.3", "ts4.8": "2.10.3", "ts4.9": "2.10.3", "ts5.0": "2.10.3", "ts5.1": "2.10.3", "ts5.2": "2.10.3", "ts5.3": "2.10.3", "ts5.4": "2.10.3", "ts5.5": "2.10.3", "ts5.6": "2.10.3", "ts5.9": "2.10.3"}, "versions": {"2.9.0": {"name": "@types/yauzl", "version": "2.9.0", "license": "MIT", "_id": "@types/yauzl@2.9.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "dist": {"shasum": "b8427df4659c77a9f5f1bf684a1f54a5159032f6", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.9.0.tgz", "fileCount": 4, "integrity": "sha512-KVQbjKvieCq6d5LqZ8KIzzwygF88fWC+l7wvPbRPM3OI3f9ZAlhaKUlk3kjiyvOMqopSTM7enjduXXl5B+msXw==", "signatures": [{"sig": "MEUCIA4NOfgaO4ZF3089Kh14hCg1/Qsu2OwvL6vQt2+lzxqjAiEA6gRqzmH+knRv0Fb7caAG2sGroSWYsW7b95Im4rWL2vM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5977}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.9.0_1519685566987_0.8814565883716894", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ec5e2d67caf9069fa31dd349d4cb0f54f152493a58053d270318967ba80ed46d"}, "2.9.1": {"name": "@types/yauzl", "version": "2.9.1", "license": "MIT", "_id": "@types/yauzl@2.9.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "dist": {"shasum": "d10f69f9f522eef3cf98e30afb684a1e1ec923af", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.9.1.tgz", "fileCount": 4, "integrity": "sha512-A1b8SU4D10uoPjwb0lnHmmu8wZhR9d+9o2PKBQT2jU5YPTKsxac6M2qGAdY7VcL+dHHhARVUDmeg0rOrcd9EjA==", "signatures": [{"sig": "MEUCIQDum7JFHdBCF+le/LhZQAir4slRqScBa30R2m/EsKlC6AIgX6n0mi9SsNlFz5/nD6l5RGXNxJnilO2Dskw/pvMCBDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLR9SCRA9TVsSAnZWagAAJgYP/10k39F1bm2zIB3EfPBh\nbU1/T2vpQiI1vSHz8Pkx1O9ZZAiAU533Vph8fW/sgXNQN56MZ8FHr/6nZotU\nCgieQD+leRsb725Uq0P2+c+SaP8/LotS1hvdIselBJhFgvUeBMDe2vqmlSz/\nf3v8TWjrqxMe8WgOGi2HQ1NcDr6IrUaJRrF3ZGPjTY2DmNh9L9RTv7oQk0Vb\nb7zTC5uqn8TWi9u1LYHyUgitO80Du9PUl4Cmklhy3iQPx/FNUb4oStVh1QoV\nATA8xFQD5CqAYCgprU23F3etrJ5sV2/+S5Ctiux9e/p2DD38c6r2bLWstEGG\nTDXJV82VPEEyzjz2gdYWPiSJCEd3JCxBAi2rPzYERci3ItC4l4W7/YtqJsUM\nZWu4o5pKFkEqZ6aEPGarT5THXSZLHV6S9CYxGWc/Nez7ooN8RxqRv5CzO0Gq\njB2ycYAMpPj9CrgHI/Me3kW5JgnGK78IUE4TjIDRuOsBrzeKt0acDy6iThV/\njhwqA6dNmOJ4EBDmDCTH8VXsDpWpZ/qUaFELTn1vDHt79MGaVD2okqFzyLZR\ncIVMHLo0o92N2luVJml0UrxqDcaxDnAEmgIjnkykQj0KZc+OQQgvQkcd9Sk+\nEXEUkKfmw6jGqIk1N4UUBOrGyGPIGBOBECx5jRAJaoAmfc4kdovnd1c5hf0J\nXmwQ\r\n=hT68\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.9.1_1546461010135_0.6728434289588223", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "78f765e4caa71766b61010d584b87ea4cf34e0bac10cac6b16d722d8a8456073"}, "2.9.2": {"name": "@types/yauzl", "version": "2.9.2", "license": "MIT", "_id": "@types/yauzl@2.9.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "dist": {"shasum": "c48e5d56aff1444409e39fa164b0b4d4552a7b7a", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.9.2.tgz", "fileCount": 4, "integrity": "sha512-8uALY5LTvSuHgloDVUvWP3pIauILm+8/0pDMokuDYIoNsOkSwd5AiHBTSEJjKTDcZr5z8UpgOWZkxBF4iJftoA==", "signatures": [{"sig": "MEUCIQDX7rIQpLozQroYaPXuIFSb57pF4C8kKCAlX4ghg8BAOQIgMuR8Ruq2RiYLWCCaXw7XJ+un76lkD7ajZuQ8aUUlilE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30CTCRA9TVsSAnZWagAAFVoQAJ67OccsMjBADodgHLm0\nRRgXEXPTFfrvGcOat12nss9U5xMeAuoex1pVBSEn3KpprNsN6d8qCgxdrvjL\nxbQhzCekioGQ0mp22HD2GDH9ynBa5klXxSjxLccHOuZu+A5Cv+5IxUiV339O\nxZnnpK2iVUsr9FBkibdtUu4s6AlkuZWWIUXOPvtkudpcS9OqTddoXOuNjn2E\nmI00syndhMOjm5nUNkOidfCBByJKz4Ec7m2Q/4zXBe7oOnRn7+0sMv/iUEJ7\n3YL0fPPUaNSMhDOP7njE/vWOj6WZs7/vZuXncU2BH4PpyTcElZp6aJ7bmK8w\n0Jl9N1ci23oq8Gx5loGNu77c0qxNpZoKRfgnQV6NMCOMvWv3GPl2o/WUt6aB\nwNj6VAt6srzPZ+qt1lPcggB7e3n/HeaX1sLFfCwOxumzcgHNOuHQ4bcIm/uC\n1tI5c9FB9BBE17vC93A3BLpKwkUO5yToUdy0ixuuy8yja0wh1jOfKYq2+T9/\n8i9/8q7LTQXoA5Q6XyqVVFobOZEpJ0dyc9Ec3LdZ+teoCUgKWJi1QPaSEI15\nSW/d1ZuSHtOCLiUYMhu0qitl5tT+pxkE46dEAOPDQGeWr2wv63Qo/+F0Ifxm\n2X0SD2NyrTtmVj0U9KouM5fO9H61i8nathyZ6H059DhrwBG14xNgsjXErJF3\nHSwA\r\n=amdb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yauzl"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.9.2_1625243795259_0.5756993964257473", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "66e9dbfe16bce3e314bf2f77c0f250a6ef6a082062b3dc253fbaa9451e1de452"}, "2.10.0": {"name": "@types/yauzl", "version": "2.10.0", "license": "MIT", "_id": "@types/yauzl@2.10.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "dist": {"shasum": "b3248295276cf8c6f153ebe6a9aba0c988cb2599", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.0.tgz", "fileCount": 4, "integrity": "sha512-Cn6WYCm0tXv8p6k+A8PvbDG763EDpBoTzHdA+Q/MF6H3sapGjCm9NzoaJncJS9tUKSuCoDs9XHxYYsQDgxR6kw==", "signatures": [{"sig": "MEYCIQDYb6GCtQ6+FQCKBCjgZqaGt/LE+/nSbOuzGnY38JySBQIhAMlNxQcFSgvPBDPWGukA8VKh8dH5YNoXZUsEN1e8O1+T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiU4v0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolLg//Qpy5HfUvvM3K45dJV6j86I55NeLCdvlIh4+lM0ezcxAlVlcz\r\nGNo+lki7VawPCqvApJgjOeqHYMdqgbJSwHXWS6R2v8+IqS1tu5qVXAQ7sO36\r\nTdhPoNOglRbOpkgfUDrT1WldGRiIZFk+CE6SFBGbxcM2kvjTSObdz3ZHhh8z\r\n8PFkvfHLG7q3Vg/0yJXtWrGR2kUoJlT22Dwq6gpv6xx1GctozSAK3YYzng9R\r\nb8/hbRUkMWG3yn8Hy9JkHVJjm6uRBQpiHZYMOnx7HX0csdm70BIJa1MNNuD8\r\nT3SPOvNBBRkuFK+owyflL+8qRNXrSCdwp/3MTGrS/Nl8BUg9v/5o9ANP1Q3F\r\nukNLH0A90YCTOvw2ZEJ4l03PzzV+KdkljzTjlyPX9Oq77Y9hbAjI8gijZS7d\r\nfS+sHcUZ0ZLbPZYI+NrlFhCOl2ACAOu3h7+XcpzBXsuhgwpUjLV+V0EpNE0p\r\nzV6ePyHqBG/+QrPTnZBbmiKDwOwJMSj7IlIsLGnJfgaFdiYxGXhnVLRDVL4G\r\nqLbiFBPiJKfNAS1QLQmOvpw+z+AHXgafFhQrKY7Tu/+tsPNGc/TQlcb7YLRD\r\nrr3WlWuFaOltx71aIQT2OS9T9UYJkJVGQRd1H9EIUC9TOPQqQ8Ed7JrXXrqY\r\n3DQMcPaHrjK2fo5RqkM6HmbX3jmeTPcOGcQ=\r\n=ikqq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yauzl"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.10.0_1649642484474_0.1352100228701989", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "183e6a31c00c4e59b6e6ffe0cf97b06d4b843b2d1208d8917820cbb70b57eae9"}, "2.10.1": {"name": "@types/yauzl", "version": "2.10.1", "license": "MIT", "_id": "@types/yauzl@2.10.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "dist": {"shasum": "4e8f299f0934d60f36c74f59cb5a8483fd786691", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.1.tgz", "fileCount": 5, "integrity": "sha512-CHzgNU3qYBnp/O4S3yv2tXPlvMTq0YWSTVg2/JYLqWZGHwwgJGAwd00poay/11asPq8wLFwHzubyInqHIFmmiw==", "signatures": [{"sig": "MEUCIBHSXI8G6n/yXKuyQNBU459Yloq8/zU1/d/nHPqHRdFVAiEAh6gLOeFfmZbaBs7UlkPdNG6ggIjf8ILYseBtsXrrWOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6348}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yauzl"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.10.1_1695620305040_0.8188263842661687", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "08ffd2f30e335dcf74af9f666b149d789c1468db6fe64e6de58de58def227c08"}, "2.10.2": {"name": "@types/yauzl", "version": "2.10.2", "license": "MIT", "_id": "@types/yauzl@2.10.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "dist": {"shasum": "dab926ef9b41a898bc943f11bca6b0bad6d4b729", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.2.tgz", "fileCount": 5, "integrity": "sha512-Km7XAtUIduROw7QPgvcft0lIupeG8a8rdKL8RiSyKvlE7dYY31fEn41HVuQsRFDuROA8tA4K2UVL+WdfFmErBA==", "signatures": [{"sig": "MEUCIQC6MBhk/jszdshRQa5PZQu1OFN0HMBL9dFJouVzYJtINAIgP8r7Hbx9bkU6Mgqu0QJQ7kaZ/pirIdftrlDXd5n+NJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6106}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yauzl"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.10.2_1697657120066_0.10611847424699472", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "815e80acff3066be82b92225f6945f37fe2a6aaf8a9a3f3975e8c09db9973f64"}, "2.10.3": {"name": "@types/yauzl", "version": "2.10.3", "license": "MIT", "_id": "@types/yauzl@2.10.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "dist": {"shasum": "e9b2808b4f109504a03cda958259876f61017999", "tarball": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz", "fileCount": 5, "integrity": "sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q==", "signatures": [{"sig": "MEUCIQC9ON0PZ/mUovi6yDhjohwP2WUta6xxurcPd48b6SUefgIgNMKZVGUmeOx2zT0gLRys/MviRgCEtJ5xr4GJ+Fu47M8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6106}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yauzl"}, "description": "TypeScript definitions for yauzl", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yauzl_2.10.3_1699385947761_0.13744417322316793", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4fb24c28ac8c0fdb7539555e955c273a2a4a433e99938ed73d9e7df3a9e1e2a7"}}, "time": {"created": "2018-02-26T22:52:46.941Z", "modified": "2025-02-23T08:05:59.428Z", "2.9.0": "2018-02-26T22:52:47.030Z", "2.9.1": "2019-01-02T20:30:10.290Z", "2.9.2": "2021-07-02T16:36:35.351Z", "2.10.0": "2022-04-11T02:01:24.611Z", "2.10.1": "2023-09-25T05:38:25.191Z", "2.10.2": "2023-10-18T19:25:20.338Z", "2.10.3": "2023-11-07T19:39:07.953Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yauzl", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yauzl"}, "description": "TypeScript definitions for yauzl", "contributors": [{"url": "https://github.com/ffflorian", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "f<PERSON>lorian"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"flumpus-dev": true}}