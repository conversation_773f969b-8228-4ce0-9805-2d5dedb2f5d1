{"_id": "cosmiconfig", "_rev": "90-b57e196260ae7fac34a279fbeea7cbb2", "name": "cosmiconfig", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "dist-tags": {"latest": "9.0.0", "next": "9.0.0-alpha.3"}, "versions": {"0.1.0": {"name": "cosmiconfig", "version": "0.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "scripts": {"lint": "eslint .", "test": "npm run lint && tape test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "files": ["index.js"], "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "lodash": "^3.10.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^1.0.0", "require-from-string": "^1.1.0", "resolve-from": "^1.0.1"}, "devDependencies": {"eslint": "1.9.0", "sinon": "1.17.2", "tape": "4.2.2"}, "gitHead": "ea874b90ee989ab9855badc29d572d2a55944b7c", "_id": "cosmiconfig@0.1.0", "_shasum": "bdfbb256f714ffb2b310d35623f133139a1b6dae", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bdfbb256f714ffb2b310d35623f133139a1b6dae", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-0.1.0.tgz", "integrity": "sha512-cMLbXr3sXlXtFwtkXq1wtf3q31Rvz6kcEM4enAl5pPedojqNAZIHII72PSqj3UhmxH+uqy2+ujwupt+Q2AZx3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICqJ5JmTmpxllf/ZRm98xqBLcOD/2JZ5EV7aEBx1QQN1AiEAg2XYnqHX5E5JN68Xk/UPlvGQtZ0hTcOU3n6tvW3mD3Q="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "cosmiconfig", "version": "0.2.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "scripts": {"lint": "eslint .", "test": "npm run lint && tape test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "files": ["index.js"], "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "lodash": "^3.10.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^1.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "1.9.0", "sinon": "1.17.2", "tape": "4.2.2"}, "gitHead": "a087b9f0cb4973de60de85bdf19ed98cf62cfe04", "_id": "cosmiconfig@0.2.0", "_shasum": "2fcad200e61230077fd1e3a6495d0da95edeef1a", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2fcad200e61230077fd1e3a6495d0da95edeef1a", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-0.2.0.tgz", "integrity": "sha512-t1S/IHEvLabi4TeONlnkEdWV5SdjJGX3PIMeg6+DWWKSr9XFkt48BV6qjb/c74VGrhb7l6AAo9DL1oItckSXTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC42QFJEDPRgdz+u1wqHoq4HFWeoUeT0lpkinV1uQ54NgIhANN9YfsnS+fWQLBcTF3liJETMYeiAopG2TBiklIpMYuT"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "cosmiconfig", "version": "0.3.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "scripts": {"lint": "eslint .", "test": "npm run lint && tape test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "lodash": "^3.10.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^1.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "1.9.0", "sinon": "1.17.2", "tape": "4.2.2"}, "gitHead": "eb93fd3e83965bd9670cefd966a3b8b9d350fb4b", "_id": "cosmiconfig@0.3.0", "_shasum": "2c9dd2730b045817919c096071b2b61baf0d6b9e", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2c9dd2730b045817919c096071b2b61baf0d6b9e", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-0.3.0.tgz", "integrity": "sha512-8hKxq/Wl3lfz4SCchCMtZBBTgRxNTgzDQNblnRHldYJw5JOwU/WP3L571s6vTiUV+dWTuGp2gOxNWCy/+lUqiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDefmjHGotSt2iKHL6viW2StBGGcbsgx3SMWEtquQgolAIgMgz42zIo5VJWR9iqzgCMPBxzC2kKCjJC1mNx/9thHSM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "cosmiconfig", "version": "0.4.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "scripts": {"lint": "eslint .", "test": "npm run lint && tape test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "lodash": "^3.10.1", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^1.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "1.9.0", "sinon": "1.17.2", "tape": "4.2.2"}, "gitHead": "67e4e69a2b9634057c0d9f7e2c8e301454418eeb", "_id": "cosmiconfig@0.4.0", "_shasum": "386c7c6691df62315b843aeb0a9c83fca0accca6", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "386c7c6691df62315b843aeb0a9c83fca0accca6", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-0.4.0.tgz", "integrity": "sha512-dMe6L1AzDSsEeRyxqPeoS+eFMf2p4jubkIkAHJu0hr3tVKTGrcgWdL83WRksUalvKzKx20oyqrXdshUnkpiMlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGD4yvJ1tXF2WKJxGuAyeZUzIISbLZ+/PfVaMDcyN148AiBqXXei9f+xbkWl++Q+V1CAIbI6rOJ/E/fjsnCrgPDwsw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.1": {"name": "cosmiconfig", "version": "0.4.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "scripts": {"lint": "eslint .", "test": "npm run lint && tape test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^1.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "1.9.0", "sinon": "1.17.2", "tape": "4.2.2"}, "gitHead": "c837ff3e040a069e646ce03fe9fe6c8443d4ad3d", "_id": "cosmiconfig@0.4.1", "_shasum": "1a78efb42e8c4e9a1b4fa09ffaab717e6cef626a", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "1a78efb42e8c4e9a1b4fa09ffaab717e6cef626a", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-0.4.1.tgz", "integrity": "sha512-FFbBMo5XNwi3SEhT5EVbpReKsn3sH5yAmgtkNPq/1ezoNjiXRb7fmXNmq3YL1vPvgnIMIojfoGBUNfnBmzLuKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcnltNOLCX8YcmJLL7oMdjt0mBF9yheUPU5sAk0JMAkAiEA6PA37aYHWlz4hF3UPKTKITLtm0ShdlDf0F0DoXjGwwo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.5.0": {"name": "cosmiconfig", "version": "0.5.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "scripts": {"lint": "eslint .", "test": "npm run lint && tape test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"defaults": "^1.0.3", "graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^2.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "1.9.0", "sinon": "1.17.2", "tape": "4.2.2"}, "gitHead": "1374fba1ce19967b06fc314b6a92ab4e2f1aa67a", "_id": "cosmiconfig@0.5.0", "_shasum": "9a6e8006c034e83c1b995846dcd12405c8ea6c07", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9a6e8006c034e83c1b995846dcd12405c8ea6c07", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-0.5.0.tgz", "integrity": "sha512-eVIDLyWVHIc8VkNdR6rj5JBSi1/uJ/232hbcPd9LKP5TtZBDf7XBUba2UXTbTxaoiXkCQ62KAbGXmEabFRqxwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChZ/P0+K41EKGOYEmxyubCIQJJH9c7kejvCbl46mrpBgIgJIrm0PbktvjABiDBxLT/yxYrNMweU02So7OpSWf79Fo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "cosmiconfig", "version": "1.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "ava", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"bluebird": "^3.0.5", "graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.0.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "^0.5.0", "eslint": "1.9.0", "sinon": "1.17.2"}, "gitHead": "edcd9fde279416790a3dede34c65d420cf28fbb0", "_id": "cosmiconfig@1.0.0", "_shasum": "132ff8b46e2bb1df735a8c3e9db1ff6ca90643a2", "_from": ".", "_npmVersion": "3.3.10", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "132ff8b46e2bb1df735a8c3e9db1ff6ca90643a2", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-1.0.0.tgz", "integrity": "sha512-GAoL2uheXFbbSV4JX78IdwJ1k6fJ2aboh6CLbPahvab0NKYIuIzsbgDMmGTFP61em412o75GNIFnkVN+BxwI7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICaMwuHJP3GNOq84vFUiZ2Zk9wT71rcNG65LGSbPVdZfAiEAsQZjyOE5xtC2v2JbAzd8ZgDx9h+F23iGWuBz61bLpd4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "cosmiconfig", "version": "1.0.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "ava", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.0.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^2.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "^0.5.0", "eslint": "1.9.0", "sinon": "1.17.2"}, "gitHead": "38ca03025b7ee82bc4479fcffbac88027e999b4c", "_id": "cosmiconfig@1.0.1", "_shasum": "27995f978268fe941d8dae8924fc0c9a6b55c74f", "_from": ".", "_npmVersion": "3.4.1", "_nodeVersion": "5.1.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "27995f978268fe941d8dae8924fc0c9a6b55c74f", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-1.0.1.tgz", "integrity": "sha512-kJZu0z9sMLOFCBesTCg9k4npX9bnqkXdcq21mmxEzMK/16MUrY0GcX3ZR7yugb5eLa8BneS9fkJIAmy4BJfI2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGrKB+MVfemvRGZE1v9I2eyh/whpHyb3OwPZdZ/tx92AAiAWTfQH69uPlFIvPgaw07OEkXvxwx33t1eptVxcIf4wwQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "cosmiconfig", "version": "1.0.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "ava", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.0.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^2.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.9.1", "eslint": "1.10.3", "sinon": "1.17.2"}, "gitHead": "1135486e4483208dcd0047cf6588581be9a892a4", "_id": "cosmiconfig@1.0.2", "_shasum": "84225dd0ae301a5ac83a4f4d6636104f7065c826", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "84225dd0ae301a5ac83a4f4d6636104f7065c826", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-1.0.2.tgz", "integrity": "sha512-zCEq5cvVFVnWAE4Pv3FwgwlULKiKunlBVquVv7hEJHW6yl+Wdu68Fti3J9VwvjmSYtzS9ZokadI3rU2xDgWVJw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG010fsUrq5pNhgMgOlaWPVO9QfnRV9WyajhQnPsR6wVAiEA0uHHtBeXxI2qP+siWxLVLMEDhpxAfXcmZyFR31uB9V4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "cosmiconfig", "version": "1.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "ava", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.0.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "pinkie-promise": "^2.0.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.9.1", "eslint": "1.10.3", "lodash": "4.0.0", "sinon": "1.17.2"}, "gitHead": "3bf5bef54fc18e8193a8e0b34f3cd17eb1033dea", "_id": "cosmiconfig@1.1.0", "_shasum": "0dea0f9804efdfb929fbb1b188e25553ea053d37", "_from": ".", "_npmVersion": "3.5.3", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0dea0f9804efdfb929fbb1b188e25553ea053d37", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-1.1.0.tgz", "integrity": "sha512-lD04n/6cUdP+MyTyvg34qO5+XzUxS0qIhNVnZON0Ba/LAHN7o/BPIPoXBGq1hNvhWe2slvbsXGhY/bMQw1MsyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDbdsWo6e3lIO1iuGJhwp60LgQDS4HYyGspNIPvnyrIJAiEA5jU3YnOqhlvinK/BF2MCPgXewvWLoyWdLXK6XFlhAfs="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "cosmiconfig", "version": "2.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "ava": "ava test/*.test.js", "coverage": "nyc npm run ava && nyc report --reporter=html && open coverage/index.html", "test": "npm run ava && npm run lint", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.5.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^2.0.0", "expect": "^1.20.2", "lodash": "4.16.1", "nyc": "^8.3.0", "sinon": "1.17.6"}, "engines": {"node": "4"}, "gitHead": "31fec2c5179bc4d5494789be2b046c738c4f4fca", "_id": "cosmiconfig@2.0.0", "_shasum": "0a4a456b8e62de679370d86de5e17b12bf08fafb", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "0a4a456b8e62de679370d86de5e17b12bf08fafb", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.0.0.tgz", "integrity": "sha512-ozpj2haajG1/rmQxoM34sgph24maKip/PWVXdQrBCUAbBhihp2hukzztNEcLg3gOJwFyRZIE7chaLgZwM2exRA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB+wsA0d3KJuYbba+mkUeBYBfHrrjMcW9+kkrCuO1I1aAiEA+PKy0IN5fMuTzx6DUjWVHGgDWoPgR+yPbTWwcfvIpgA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.0.0.tgz_1475937068818_0.0514873587526381"}, "directories": {}}, "2.0.1": {"name": "cosmiconfig", "version": "2.0.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "ava": "ava test/*.test.js", "coverage": "nyc npm run ava && nyc report --reporter=html && open coverage/index.html", "test": "npm run ava && npm run lint", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.5.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^2.0.0", "expect": "^1.20.2", "lodash": "4.16.1", "nyc": "^8.3.0", "sinon": "1.17.6"}, "engines": {"node": "4"}, "gitHead": "529cb128016f3f09bc876100b3eba2d89934da6c", "_id": "cosmiconfig@2.0.1", "_shasum": "7bd3cb61a9f5a4a807c0a04b2905a99a6a868f23", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7bd3cb61a9f5a4a807c0a04b2905a99a6a868f23", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.0.1.tgz", "integrity": "sha512-jifqXf+aijc20cz13JP9k9ofoesugzvcgyiKqNjDlCIoYJ9XQyClkl3z581bqfIh1YGW5g5BlFa5LrtHia6jBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHtmlr+PyWpFcbr4dJSJ+i2z/ijWyob1FeYDQ73AmjBWAiBeIlV+oxaeEZ1xt055/wFK5P3BOtnPcfHqeYSdxpmVXQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.0.1.tgz_1476198181717_0.8488575008232147"}, "directories": {}}, "2.0.2": {"name": "cosmiconfig", "version": "2.0.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "eslint .", "ava": "ava test/*.test.js", "coverage": "nyc npm run ava && nyc report --reporter=html && open coverage/index.html", "test": "npm run ava && npm run lint", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.5.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^2.0.0", "expect": "^1.20.2", "lodash": "4.16.1", "nyc": "^8.3.0", "sinon": "1.17.6"}, "engines": {"node": ">=4"}, "gitHead": "2c5ace504ebab0d67f1481dda443189d4c9506ae", "_id": "cosmiconfig@2.0.2", "_shasum": "555501957f17b849d44488d55dd2275a6452fff1", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "555501957f17b849d44488d55dd2275a6452fff1", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.0.2.tgz", "integrity": "sha512-gXFln93sc2h3kcQLqU0LafPXopWLtURHZYhR80LIeOFkX0P/Q757jOT4OKPQpVGk5HAFh/07dGwhaJLnz/aUgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzbzhIem5NQ0JhqyZZqLLbRFNlcag6zyV6o9YH1sh7nAiEA2r6/lzaQrPVoXZgSbJsM5pv8dY/QTeP5ueFGoCkNVxU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.0.2.tgz_1476209549923_0.25399640412069857"}, "directories": {}}, "2.1.0": {"name": "cosmiconfig", "version": "2.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "ava": "ava test/*.test.js", "coverage": "nyc npm run ava && nyc report --reporter=html && open coverage/index.html", "test": "npm run ava && npm run lint", "prepublish": "npm test"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"graceful-fs": "^4.1.2", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.5.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^2.0.0", "expect": "^1.20.2", "lodash": "4.16.1", "node-version-check": "^2.1.1", "nyc": "^8.3.0", "sinon": "1.17.6"}, "engines": {"node": ">=0.12"}, "gitHead": "141c4adb5244b6abbb98a941f662a07ed70c8783", "_id": "cosmiconfig@2.1.0", "_shasum": "26e384a2055ea4e087050e5e08d53eb4eac8f86e", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "26e384a2055ea4e087050e5e08d53eb4eac8f86e", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.1.0.tgz", "integrity": "sha512-T61lKwZfdUilqYDNEQCByehzp1wDSNBP7fn+c867+Fd/v6PO+rkUQJpBPjKtuGQFuL/pVJK4rWYX7Ye7wR9KWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIER5T4Dj+BiFdI3W4Bj9JmnLAny72JEGHqKm5uOc2yDDAiAqd9dzSX8g3LYVuIRh01ce8fPMa1glfFWRV5Wy3q8q+A=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.1.0.tgz_1476412220096_0.5366442145314068"}, "directories": {}}, "2.1.1": {"name": "cosmiconfig", "version": "2.1.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "ava": "ava test/*.test.js", "coverage": "nyc npm run ava && nyc report --reporter=html && open coverage/index.html", "test": "npm run ava && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"ava": "0.16.0", "eslint": "3.5.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^2.0.0", "expect": "^1.20.2", "lodash": "4.16.1", "node-version-check": "^2.1.1", "nyc": "^8.3.0", "sinon": "1.17.6"}, "engines": {"node": ">=0.12"}, "gitHead": "833385b0897c7d698e346442cc260a40f0e8eb9d", "_id": "cosmiconfig@2.1.1", "_shasum": "817f2c2039347a1e9bf7d090c0923e53f749ca82", "_from": ".", "_npmVersion": "4.0.2", "_nodeVersion": "4.6.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "817f2c2039347a1e9bf7d090c0923e53f749ca82", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.1.1.tgz", "integrity": "sha512-SAPjCRS0nEW7SmELOgF+kILTx1+a7jhe6peq9eMRXBz5Oyz0sxDq3MfTwbPFBV4fJUC0vFciHbwlIrhX2oeTMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHNpksEEP16tjXPrpqG5AuYx33NkRZ943zydgJfcJgWCAiBpoUoHVDqN+y9Dsyvn7l76QgqJmJYM1JV0ryOIr1EP9A=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.1.1.tgz_1480883998374_0.7163176657631993"}, "directories": {}}, "2.1.2": {"name": "cosmiconfig", "version": "2.1.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "tape": "tape test/*.test.js | tap-spec", "coverage": "nyc npm run tape && nyc report --reporter=html && open coverage/index.html", "test": "npm run tape && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.4.3", "json-parse-helpfulerror": "^1.0.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "^3.13.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^3.0.5", "expect": "^1.20.2", "lodash": "^4.17.4", "node-version-check": "^2.1.1", "nyc": "^10.0.0", "sinon": "^1.17.7", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "engines": {"node": ">=0.12"}, "gitHead": "324ba6e6899822218027c145220faad3da52a231", "_id": "cosmiconfig@2.1.2", "_shasum": "c43ae86d238f08f1728a345ed60ceb0aef63c060", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "6.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c43ae86d238f08f1728a345ed60ceb0aef63c060", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.1.2.tgz", "integrity": "sha512-tUGmAOPHB0XlyfdayT2klTfp9mwJvF+F5rrMRobWt4lB0iHHDkx1HggLfGyiJJp7YV6SJyosg1YaXLm7v8079w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFdDE71zgUBQWUEEKAjrZglxxaPnpJU3BvZAMs73LyEmAiBUdtf19T4XwZM0YlNf2Qu/C84CKbgBQLeBUY4Kt//vhQ=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.1.2.tgz_1493170533469_0.09523397800512612"}, "directories": {}}, "2.1.3": {"name": "cosmiconfig", "version": "2.1.3", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "tape": "tape test/*.test.js | tap-spec", "coverage": "nyc npm run tape && nyc report --reporter=html && open coverage/index.html", "test": "npm run tape && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "^3.13.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^3.0.5", "expect": "^1.20.2", "lodash": "^4.17.4", "node-version-check": "^2.1.1", "nyc": "^10.0.0", "sinon": "^1.17.7", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "engines": {"node": ">=0.12"}, "gitHead": "334065d9f7b557170a38b3f920bf5e90cecfab72", "_id": "cosmiconfig@2.1.3", "_shasum": "952771eb0dddc1cb3fa2f6fbe51a522e93b3ee0a", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "6.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "952771eb0dddc1cb3fa2f6fbe51a522e93b3ee0a", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.1.3.tgz", "integrity": "sha512-PBJCNPm5hheT6PiD/AeMVlJYIfA0E7PED7lJvq7O/G0SN6wh2MDwargN4kRidPWlV/6TiO2kZHNcG+rhPUfeYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKzO33MMZFoPyDSZpiwZl9+xcemt7p7Q9boWLAMWuEgQIhAO3qyqeT/3O36wqE65vU5Kwdtu80RiRpC0Eb8k9X4eom"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/cosmiconfig-2.1.3.tgz_1493484678373_0.50360560673289"}, "directories": {}}, "2.2.0": {"name": "cosmiconfig", "version": "2.2.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "tape": "tape test/*.test.js | tap-spec", "coverage": "nyc npm run tape && nyc report --reporter=html && open coverage/index.html", "test": "npm run tape && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"is-directory": "^0.3.1", "is-promise": "^2.1.0", "js-yaml": "^3.9.0", "minimist": "^1.2.0", "object-assign": "^4.1.1", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "^4.2.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^3.0.5", "expect": "^1.20.2", "lodash": "^4.17.4", "node-version-check": "^2.2.0", "nyc": "^11.0.3", "sinon": "^2.3.8", "tap-spec": "^4.1.1", "tape": "^4.7.0"}, "engines": {"node": ">=0.12"}, "gitHead": "bfa7721399d8a094796725456ddf0a143379f0a2", "_id": "cosmiconfig@2.2.0", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L5gAiD3YAzzD2o+XAL8HD8wCprGC+2gGudfdckAIpV3zAEgYMHnolmK3iRnMsYiS0W4e/ey6fDBy6O27bQYdpg==", "shasum": "a14ef6f81bf4cc8b7c708730a4010c41b4ab35b8", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGatPohRLm6BZE1FfPEdZOC93m6ikWPE3ip/tPe4t1xgIgMjn2XGYx8N+0KXs3prylLqMWTH7p7dp8ZqG1j0oU3XI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-2.2.0.tgz_1500645658522_0.9643848484847695"}, "directories": {}}, "2.2.1": {"name": "cosmiconfig", "version": "2.2.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "tape": "tape test/*.test.js | tap-spec", "coverage": "nyc npm run tape && nyc report --reporter=html && open coverage/index.html", "test": "npm run tape && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "^3.13.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^3.0.5", "expect": "^1.20.2", "lodash": "^4.17.4", "node-version-check": "^2.1.1", "nyc": "^10.0.0", "sinon": "^1.17.7", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "engines": {"node": ">=0.12"}, "gitHead": "a004ec9d36adf0b4d47a527f2bb8cff12aca0405", "_id": "cosmiconfig@2.2.1", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-17m9pl5cD9jhPUHqaxSA4fyoiAQJUG7V3CQDxCF7gWzGYeUY0YEnLQdQyOEKjEPVv0yGbdCfdfJMq6SphRiRjw==", "shasum": "7fbdc6fb47597d5f88175de1df696b66d36e5944", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDCgsv6HVkWfBl9B0PZzDeWxvm54sjFLvM6U4gYt+ApAiA8Ox6iYbnKuFP9GtYvU09XlHeyjNMuh3six9uh19WXvQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sudo-suhas"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-2.2.1.tgz_1500650393526_0.31028774357400835"}, "directories": {}}, "2.2.2": {"name": "cosmiconfig", "version": "2.2.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "index.js", "files": ["index.js", "lib"], "scripts": {"lint": "node-version-gte-4 && eslint . || echo \"ESLint not supported\"", "tape": "tape test/*.test.js | tap-spec", "coverage": "nyc npm run tape && nyc report --reporter=html && open coverage/index.html", "test": "npm run tape && npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.4.3", "minimist": "^1.2.0", "object-assign": "^4.1.0", "os-homedir": "^1.0.1", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"eslint": "^3.13.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-plugin-node": "^3.0.5", "expect": "^1.20.2", "lodash": "^4.17.4", "node-version-check": "^2.1.1", "nyc": "^10.0.0", "sinon": "^1.17.7", "tap-spec": "^4.1.1", "tape": "^4.6.3"}, "engines": {"node": ">=0.12"}, "gitHead": "6ba8acded691b08e550436de3bbad7d25598ad75", "_id": "cosmiconfig@2.2.2", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GiNXLwAFPYHy25XmTPpafYvn3CLAkJ8FLsscq78MQd1Kh0OU6Yzhn4eV2MVF4G9WEQZoWEGltatdR+ntGPMl5A==", "shasum": "6173cebd56fac042c1f4390edf7af6c07c7cb892", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-2.2.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHTp45tIoE+T2IirP7S4uTX7SOCntlZ/SyV4jRED2X6oAiEA1+Nx+xqj5CovbR4swvFIaMxa2X3IG+6q2PPflQI7+Oo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sudo-suhas"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-2.2.2.tgz_1500820216479_0.9056716256309301"}, "directories": {}}, "3.0.0": {"name": "cosmiconfig", "version": "3.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["index.js", "dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint": "eslint .", "lint:fix": "eslint . --fix", "pretest": "npm run lint && flow check", "test": "jest", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"]}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^2.2.0", "require-from-string": "^1.1.0"}, "devDependencies": {"babel-eslint": "^7.2.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "eslint": "^4.5.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-config-prettier": "^2.3.0", "eslint-plugin-flowtype": "^2.35.1", "eslint-plugin-node": "^3.0.5", "flow-bin": "^0.54.1", "flow-remove-types": "^1.2.1", "husky": "^0.14.3", "jest": "^20.0.4", "lint-staged": "^4.0.4", "prettier": "^1.6.1"}, "engines": {"node": ">=4"}, "gitHead": "e9226f1310f36dadd0323d68ab0256e976be8645", "_id": "cosmiconfig@3.0.0", "_npmVersion": "5.4.2", "_nodeVersion": "6.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BkkaWKg1oZ1qhaKTbRPAYNshAstpR5xiS6s3yL/o5H8rW/FvoOyZuPiqrpS9tBIJZIRKxGNdKZv3pREazD/FyQ==", "shasum": "9c465e02f5a7cc6e8f7e5975fce4a1b712d7d936", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFTevJ3mIzYdz2T02bF0R6YBBCNXa6TO7pUq8aBz4UfmAiArPxCYRUBxB/n594gZSbrX903wHaRdoVP0a6IBnz/7uw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sudo-suhas"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-3.0.0.tgz_1505525636771_0.17221695114858449"}, "directories": {}}, "3.0.1": {"name": "cosmiconfig", "version": "3.0.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint": "eslint .", "lint:fix": "eslint . --fix", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^3.0.0", "require-from-string": "^2.0.1"}, "devDependencies": {"babel-eslint": "^8.0.0", "babel-plugin-transform-flow-strip-types": "^6.22.0", "eslint": "^4.7.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-config-prettier": "^2.5.0", "eslint-plugin-flowtype": "^2.35.1", "eslint-plugin-node": "^3.0.5", "flow-bin": "^0.54.1", "flow-remove-types": "^1.2.1", "husky": "^0.14.3", "jest": "^21.1.0", "lint-staged": "^4.2.1", "prettier": "^1.7.0"}, "engines": {"node": ">=4"}, "gitHead": "2dac2f9165c01a571f5c61648ff3c0079d4a4bee", "_id": "cosmiconfig@3.0.1", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "sudo-suhas", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ybh58PgNtXMlTN+xpsHNPyhzdSg4PCS9QBVxXMLi5sJGfudUqdtu2XTZd3vRys7iqgMVuWinMSKRMNHbfJH9SA==", "shasum": "d290e2b657a7f3a335257a0d306587836650fdc0", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-3.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCe2rVHArCx1suP/KEtk3VDDjoNftJ4gmjwvNVRU2pwCQIgEYzXrg+hrl+VXSwY0irBhbLke5CMsNhGXXEtAkFo3zU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sudo-suhas"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-3.0.1.tgz_1505660827478_0.797999853733927"}, "directories": {}}, "3.1.0": {"name": "cosmiconfig", "version": "3.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint": "eslint .", "lint:fix": "eslint . --fix", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^3.0.0", "require-from-string": "^2.0.1"}, "devDependencies": {"babel-eslint": "^8.0.0", "babel-plugin-transform-flow-strip-types": "^6.22.0", "eslint": "^4.7.0", "eslint-config-davidtheclark-node": "^0.2.0", "eslint-config-prettier": "^2.5.0", "eslint-plugin-flowtype": "^2.35.1", "eslint-plugin-node": "^3.0.5", "flow-bin": "^0.54.1", "flow-remove-types": "^1.2.1", "husky": "^0.14.3", "jest": "^21.1.0", "lint-staged": "^4.2.1", "prettier": "^1.7.0"}, "engines": {"node": ">=4"}, "gitHead": "930926e7ca4e4f4c6cd57c371748a6c9d45083d8", "_id": "cosmiconfig@3.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "sudo-suhas", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zedsBhLSbPBms+kE7AH4vHg6JsKDz6epSv2/+5XHs8ILHlgDciSJfSWf8sX9aQ52Jb7KI7VswUTsLpR/G0cr2Q==", "shasum": "640a94bf9847f321800403cd273af60665c73397", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-3.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPGlm98cBuYCMyRi3Ipx6bryf/FoWV7ILuGO1eAhFFWAiEAgbuxxzkQgEKBuhtgHTYM1r9Eq9kL+Tm+qfcfJRhJ86s="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sudo-suhas"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-3.1.0.tgz_1506906985338_0.6278869041707367"}, "directories": {}}, "4.0.0": {"name": "cosmiconfig", "version": "4.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0", "require-from-string": "^2.0.1"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.54.1", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "prettier": "^1.8.2"}, "engines": {"node": ">=4"}, "gitHead": "e520cde1cbbbd528e8b0533c3f02bcb35e8a359e", "_id": "cosmiconfig@4.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.3", "_npmUser": {"name": "sudo-suhas", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6e5vDdrXZD+t5v0L8CrurPeybg4Fmf+FCSYxXKYVAqLUtyCSbuyqE059d0kDthTNRzKVjL7QMgNpEUlsoYH3iQ==", "shasum": "760391549580bbd2df1e562bc177b13c290972dc", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCT6qlG5a+b/SwrP652lPndT6kdWoTHu7CG+1rBsTLJfgIhAP4tBqe8cdveo3VhecGZJOK2VJHNF4m5iz8W+5u/j4My"}]}, "maintainers": [{"email": "<EMAIL>", "name": "sudo-suhas"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig-4.0.0.tgz_1516072984111_0.9652654137462378"}, "directories": {}}, "5.0.1": {"name": "cosmiconfig", "version": "5.0.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "493c62aca93b9b79ea75b0768f7b425e685e2d57", "_id": "cosmiconfig@5.0.1", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Kb8por4Jn2HbgGMmQoS2glS7FipYnNxIDccjLr1y9jRjonlSeHrciUfa2y5D6ucahMnKj5oUAToLU6cRXUfOpg==", "shasum": "1467d4b6010116a3adcc1d8cbcc126d993a5afa9", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.1.tgz", "fileCount": 10, "unpackedSize": 40134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7eNYCRA9TVsSAnZWagAA+MEP/1SBECl2u0Ek7Q8pyJN3\nWQnSOw7CUCphyXqMEINQY1a6E/u2r94P6G2quNp6DeFADbzEDWjMxGwTH3lC\n33JCWghnNJn1ZAXXXVmvU6RFM/hxZ1APNMhzpD4nsF1UWFhy+6K0tCLTMvfQ\n3AQOJjJStdPZERH2QJ3FTNPFrJajCN5TYWy+gO8j7nNRRunlw+a4bDtDuytV\n/kqeOoCKIk5GPxk+Fl+Nz8huhbr/er4nfUINDZSktFchWtlo2g0A33uuL2Op\nrlwTmuBxbjt88I7nV4wctljQ9ztXRvoNAk2+8PsUQxXePjWKDlyXLjufDGBa\nZTDWLrHLltf9tKk6LKhXUoCDO5U1VDAhO2dDoVkYBOukBghm27vxIP4nDkYT\nlSYHCyUh51OX5beew4my7mWS+sQ16V/jQy1fKfLXltD9fsodP/5rrJlk8zOZ\nTNgiYB+j+DqkuVQ4glA5hMLwGwRSuz3OrBjVBvIGvoz37495owtKlssxwL9b\npWmF8Oke2rzjxKD1ZmPBnaM8WCaQaGu5QzFOgHPlhL8C7N71J/FUuJ4Spw8x\n/LGU+pthXiLCKi4PxxUkwutAEC4FhObxMrXaMTmBY0WQRd6XiN1VaNZ9S2Ct\n8jFcxZh/yiYXcBVid4b1XDN/G8AVvy5yi5ErFZ86TVNhsp0snRLMJkPK37iM\nV5ky\r\n=eSLI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF/+DQ+sgY8IqQIteprvXMWegN8K2dzWKcbV8iLzuN00AiBViIbv89IsWo59qTmf3HjLuER0XrqF0VgqTSKtRG1csw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.1_1525539670694_0.4144289824136158"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "cosmiconfig", "version": "5.0.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "e454aba02a6acfa45a3f9eb7d0784fb03b266acf", "_id": "cosmiconfig@5.0.2", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zJV4MeVVN4DN/RdvqJURLFBnIfdwJDSHJ9IoxxqwnW48ZNKG9rbOEZb5tuWpnjLkqKCYvDKqGFt7egAUFHdakQ==", "shasum": "03f8965ae4675317a0015b4a5a48a470d9baeada", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.2.tgz", "fileCount": 10, "unpackedSize": 39929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8M8UCRA9TVsSAnZWagAApRAP/jq9oyQFNrvSupvMd1/Z\nr1jgZatpM9jeMKDVKI7fG8RtyBXYvct/7gePp2aOc6Xi5hYK5WAy1tDB+ze2\nvJsY5z47b5St0l40ZC74JpaXpptJZ/GdibGyQHYaXm+BnXFukf5E7Z8ACZ/s\nxNHfHIpp5nb14p4R/c5sIlRoCuFi+3Nz/W9NmuaX5/J43xptqwiGiVyGyNP9\nMvwUXlVq4IdbQsmgrLqi+HernqlbBj/ugBceGbn5IWn8m7qJVlsVNvTdapAs\nhGXYZQRCoGhdKxUNv0m2wary2shozjjHYpfu6YYQPZciZxH7OxDbpM6wJhiy\nSN2YHYDhtnP+YYrOUt0GM+mZcokCA4AOJRpR9+SQwM9g10TOlzqowpCUkN1U\nDLj/JSzynELIjTLCpBDir7yWF1R7vHkUv1NWixVKUIL31DEk9cEF8GrkYm3R\nYdHJxLIY0y6706sLJV/v3uL8654loGAe/0aMHtG8Gpup1RHDckmx19kQbTuv\nV+kuaoYTtvdtpt39uBmJSz3kcvmmydIipuETLPweEbX2VntKN5I4xl4Nmyhf\nx5PDKL/CIZaIjuiwfo+y3vJm48QiKSfyetkdTccwfgKy5CV1UUVvOc4tEQPU\n4rabjQ6GCvcPuelaNl+i8ld90P3jSHO3pH3vaeZ0S70Xne9LX0e6X4skxZXa\nEWmy\r\n=b71b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTpLta+nylGyH/rjvJrbqhEgZZj1sZE4zsGTTVDYZjeAIhAOCTioCibPstXh55W1kQd+XiAwTgJg/46HwhCkMA5w9d"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.2_1525731090723_0.5044492278052646"}, "_hasShrinkwrap": false}, "5.0.3": {"name": "cosmiconfig", "version": "5.0.3", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "fa5f52ef43e9eea14105fc13c6c6f49556c99c77", "_id": "cosmiconfig@5.0.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "sudo-suhas", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-x7vMpNH5favpvFjxwSzfQkB5ozdxikcmWzxah9aOh8BCOKeR+j6TM6PxQ2zyMm3+EDZcSajQrzzPKrsVqbsUDA==", "shasum": "299c08ebce9d2df8a2c9ec3f11e55b1c2e897bf3", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.3.tgz", "fileCount": 10, "unpackedSize": 40036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+lWhCRA9TVsSAnZWagAAf80P/jy1zWeZ+4N29Qtv1nGS\nrnjlaTryt8JkD+emUsO5Z0975EBGVkp/Y6D3Dg3w+kElmtK234Xa8zXHHdkj\nfDzXUYo/36EXpiHr7VKA0AX/JnFgbxEnIiF1MHjgOiXa/KMosYJgMacaQeeK\nJfAGvYM7RoH8pO0EAD+eWYLlkN65KPml1QP9QiWrFU9Ru8Ju2sKflPjNT+Gm\n0XTHKAHAZZ6g8AdanXmRznBLQ61Mkgj8LOAWiQtagwT1B/AiGHA3cgrvpdhf\ntMhBmTK+6r2Fxs7LOdLlBMbP27/VlXgoaRvo7ZlFqzjrnf0GIg8SYZlAwltQ\nFgRXCRSBwzP0R7tQpQZ5jmOfhRL0fl6dFd+2sgdEqTzknFfs7dt/sptm/vTd\nhNU1THaLkCxqgkRppqSgBvnF3ABKCy/3rmUorRkmRhbi/N+mxNXlPZ119QgS\nwVUXLXUwazHm9sGPVUdz4AYVcUlwc3rWW1rkmzjkb7Rv92iUQyLlIzMqJAfr\nc4jSXVhVCCi511iLAjyaemt5JSM1tpohU1dl5KXw1Bf6/mM/geEpT6+nm50P\nAOC9bo+MDmEmXRCYLR0IF7Gx0+d0yc7dcy0pH9xPG3a8OPMb4wLCAP9ILnMj\nzBP8ojOrqTQbljAmm+MN2FEbfBionMkhQbfT8vN81YNjB6fl0z5N6OGIQ616\nmmp0\r\n=3ngD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDw1El7msEwAvNsmYl0CF6+WkxMidfyTUkqeL1BYJNZSQIhAOd72X7Nrfq1WYVwNxwBRLnbtf/wPEJPVG0w0xLAzh0s"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.3_1526355359309_0.2329486190334984"}, "_hasShrinkwrap": false}, "5.0.4": {"name": "cosmiconfig", "version": "5.0.4", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "6106aeca2d5729d5955c37ab80494e470b712ca1", "_id": "cosmiconfig@5.0.4", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-AmWciHfzwEOUI+4jeXJap/E3Apebr7FZIGBqjzkID+lnIPTMMSNN6WM4o6q4EX1u+5fgcP7G3rEzxSe0FTqtIw==", "shasum": "fe4c1fccf8947ab30911760eace33784749eb51e", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.4.tgz", "fileCount": 10, "unpackedSize": 40230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAF4ECRA9TVsSAnZWagAACxIP/3lJPAFpCwlZa4JI/4O8\nMaLd7sfbufXz7v6f3ZbNK4V0S3buitlJ0zuPSWm1b0kfdl/8tAnQTjyL5Mcr\nyFNl7uIiPMJ9Hf6qzu7GnLKlqES/XsD9NC+uot9g1D443XzL0ybyp1Ny1baj\n2+RU0gJqoZTM9rNUyM6AA5QNfAuCbY/4v+Fn84ZSqdSstj2oj0gLIoawWlfH\nwzeyjHZWOxjVaYCU9gvcxJZti8wvtfDiTD19DoMEURUhnv5uh25XtltqIl+n\nzkdAQEGWQN/HwCLphlpaqzO8cp1GVWt9zMzAp+2UFonR1iKsItdDgg4U1q5/\nkLYryNlpLTAk8EKi34+GRu08rlFMY+0d0l0WplBeKy0kUIIN1L/S2A7dl4Ei\nLnaq7T665OeUwUlbOvFYXvEQUUzHzEFClQihHZ8LbyIxZR7pgC3SiwC7PnVC\nFPwfrpVvh0Z8hpLriCfPYUUAPHSH0Gm8sUnjR8EjvivmiI1Ccg+uEcKmWqH/\nbQSfGvMFbr6xbRg0/ibHfALRE1pMxY7sT0QD72J/lDnsietYBH7OmGO44LH4\nE1jnb7hmzMYrUWBspl2bEJOdI7T62eBicO5yfYcuNWCEfpt0YrxjR7n/a5Ro\nLX4ESjYc+Og+olBF3SXB+aGNQ15bZRn4tXFNFc4vxTX1tDwyKzBBo6JwgE2n\nVodB\r\n=27c7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpnEc1q+1E+KE+J/CMQOFtbKWp3Ns0bIJU9DxNL5WAIwIgccqUiZIVXqRqCNWiqCsHr36Ig/ZUXpM7aF5x/NTLW1Q="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.4_1526750722153_0.24526780173289553"}, "_hasShrinkwrap": false}, "5.0.5": {"name": "cosmiconfig", "version": "5.0.5", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "5ce57f0758a143cffcf52415b5a0d155675332bb", "_id": "cosmiconfig@5.0.5", "_npmVersion": "6.0.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-94j37OtvxS5w7qr7Ta6dt67tWdnOxigBVN4VnSxNXFez9o18PGQ0D33SchKP17r9LAcWVTYV72G6vDayAUBFIg==", "shasum": "a809e3c2306891ce17ab70359dc8bdf661fe2cd0", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.5.tgz", "fileCount": 10, "unpackedSize": 40512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbA3whCRA9TVsSAnZWagAAxw0P/i+MRSIDzs+MMYZg1WlY\nZ2kO1QpVrXnFUhyd6aMESueb4ydRxwPph17Bs3Jp0+Rf7oBUhYc5aUKJ62j0\nssOZopp+GYDHc77WnqDMuOE9k6WI5KpP5eFLXZYYWZnZqal+459ACSvtGnbD\nXi9oozYahxRETvBVyLFCqTv9xQv4cA+wBPIiNLzUCf7LLhjaQ6NK2yUPLSmY\nBI8wq7c+1HHNgppIZ2e1O3nfWwL6FnCBnmkNKdiZkHER22pIPKH+FW03DtXb\nB6LGYmzb27VPLlowbleN9McVOh3inbt2fzQa5ORbGcJ+kj+g6XobU6cVrOlj\n78OWO1xPq1rIvSjhqltTof8Gj4M3DmIDUyF7Is1vXDcvSD0IUL+E6Bst/Ahr\n7DjOP/GE4IxA2kKr8MusB/mEuzkebepRJ+ImmUoCog24DA/wUmK9+yzf4jvo\n9JjrU0mpS5D5kk/WYr6be2Ijxgjkqxy9NgMtQ4iS3FSxhIqq9DC8P8e12/BJ\nywP9F//MqrQyinobMPWrZ4R79sxbe3h9VCX2293I81+z1Wu2K2Wnez3L723F\nnW2kyNXaCJQ2dYjTylJtAbhkLpf1ujqPEU4yQV2OwzxPDby5hEXAWwClfDrw\n0LgYECVwbA3ajXlWZMOxmMAbKshuTw4SgAdFxGOCF7Xog5ksutgCg3FuZ/cn\nTcY7\r\n=irRT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOUHVxfVDu4JqZaTomzmrbK8WnS4HDQDGonsE1XSfSzgIgJHDCHo9OWKjLFFDztTFRSBmVLopGaVXcB25l3q+UnCk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.5_1526955040307_0.5628565018623786"}, "_hasShrinkwrap": false}, "5.0.6": {"name": "cosmiconfig", "version": "5.0.6", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "files": ["dist"], "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "599594916041a66e3963268bb9893e4c7724ca55", "_id": "cosmiconfig@5.0.6", "_npmVersion": "6.3.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6DWfizHriCrFWURP1/qyhsiFvYdlJzbCzmtFWh744+KyWsJo5+kPzUZZaMRSSItoYc0pxFX7gEO7ZC1/gN/7AQ==", "shasum": "dca6cf680a0bd03589aff684700858c81abeeb39", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.6.tgz", "fileCount": 10, "unpackedSize": 40639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbFBkCRA9TVsSAnZWagAArE4P/jI39FyipmmdSBYzgM/u\nE4jipHhOjvvgkPz6g1YP+vXuHd3X2N7XNggN2d6oWx1DurvA8AiM6zwlTxhi\n6BWzbTtvm2pixHhJaZLoca3wZK5W1XX8dwoDOoyKjxGK4AgQrtHaCKMb5QlE\nW0D/bs/vF82ShKh86bVw3XyFILYmnJnjX3+Rk/CT01r7q27lpWc/GFFthRgH\nLMoPCJgcmg3GUvZbGJmSguht+PsN5Dn/2l3jDqFxTXgU83G1igOy/nedxDz0\nBWPqe3SKZX9FK588PB6nRlMbtOrIKRh9xIM9uKJeFb0JVf6F7uJu3kn/i5gH\nOEIkFFJ8ygtOau6r3bvwalnM9z0rAYe5keGiMJsHQwFr41VterIFyrKN/l8x\nx1NN2l0ANdHSBAZP89bLR5/yVPPGgTgKIUxx0v0NTg42FQ5Ntn25qT9mcp5Y\nXFp6NScU1jVXnmRvQ4DpTobJ+ELxk3ffG+tHsgticpQXOcEY/IukzR7zRKXP\nIlcNTFH5DHijNGVyyV9Qe7WBjn06x054uSJWOde0aZROyJIC7UhUYtGg12ox\nBpYP+bOoYN4ctcMta+Tml3a3O25vn34J+X8QfATezeFyw/KptQEapdhgTXOd\nS86UIDGXAc3M+PfEWLZj2l9KQyhlfsFTqI3E/2ht5QxTBmJI0rOMkxX+Rjh3\ny2ws\r\n=xetn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDmtsxsmPo6TwLWrYW/SSqGUltwBfLQx4npBwB37WhEAiEA0Cyj0PIOPJO4RM+JcvBYk8w76oRnKM9hIKN+tRZ4G2E="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.6_1533825124009_0.5502853986113991"}, "_hasShrinkwrap": false}, "5.0.7": {"name": "cosmiconfig", "version": "5.0.7", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "****************************************", "_id": "cosmiconfig@5.0.7", "_npmVersion": "6.4.1", "_nodeVersion": "8.11.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PcLqxTKiDmNT6pSpy4N6KtuPwb53W+2tzNvwOZw0WH9N6O0vLIBq0x8aj8Oj75ere4YcGi48bDFCL+3fRJdlNA==", "shasum": "39826b292ee0d78eda137dfa3173bd1c21a43b04", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.0.7.tgz", "fileCount": 10, "unpackedSize": 41071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb46JKCRA9TVsSAnZWagAAWjkP/1CAbhA/gb20NB4dlp18\nfKAkhk4EA2VvAbJQSw5Ic7HQ+cwn6Ix2+tWZUhln2+vcuku4FnQFkvgypJzF\nbWqyuUO9a+BSfpGUOuIkFZziCblKCModAK5Fd+B35w8RnIoJmv84OWi3yU6v\nVm9cCbY8GrlzP9ZElnPlugHtKrxU/7Bc8fYvO7R/WVRmYSSvjKmcAuKpdH1S\nc/V0eTqQ+2SgUqdAkeRvHTPuyt89tgNVklJvfdQ1vZ6RWtSzyb4ljT1+bIgo\n2ifs3DWvkF+U0pU+qUMk8SBPTYB8x6PQRnVz2c/aUFVXVeRIWFxONUzNSEX4\nPSeJUFN8QAT7d0+4/6yBGsofvH/oUgSqUifDrHMRCa306GmqpwA1u2QXrxQJ\nsTSOW/DlABqJ1+11ejvkEI1A0dQZ9+IIqyM5PNH3mTxXebtWD19DiPdadytJ\nMuPCl6sHByWLQEbjgtPu7gIgWmUfqvh7rlqmSmmB27tRtqN+4Caom4om4roA\nII8NMowWse6/OpooVps1HQIAZ8Xl9Zc02yePit60ldzhwJA8Ge0CqFzIzMzV\niu57Hz+Z6T2MCkqupAQ8gNd9+M4w0dtLtMCVc/ZbYzIQIMMpfbixZZqoumVi\nBXCDs6KJKGPp9dBXAK6T0ul9cEeFhbsUvXuR/jWgbLTMfoL3JKkmbYrVr/B4\nuZj4\r\n=8naF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDejRUfBDu/BTba+CVXo0pzxqR6RUrnWBhjlnxD9IeQjAIgb/xpqqpO9TfBsWQHZDPaM61j9dO4DMxSu/VqhyNz31U="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.0.7_1541644873823_0.5878130624403042"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "cosmiconfig", "version": "5.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.9.0", "lodash.get": "^4.4.2", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "71da3267ff24b3e80bd0e5fc955b4c45942c3548", "_id": "cosmiconfig@5.1.0", "_npmVersion": "6.5.0", "_nodeVersion": "8.14.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kCNPvthka8gvLtzAxQXvWo4FxqRB+ftRZyPZNuab5ngvM9Y7yw7hbEysglptLgpkGX9nAOKTBVkHUAe8xtYR6Q==", "shasum": "6c5c35e97f37f985061cdf653f114784231185cf", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.1.0.tgz", "fileCount": 10, "unpackedSize": 41541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcafUICRA9TVsSAnZWagAAFF8P/Rwx78K07Usel2xLFV1E\n1TCVZJrLGvmPa7blKYbu1wKlcliJcoUThF7yT5uXPEBj1PTh2ODBUnUE4F/w\n7SMI4jtdOimGZVRIqQwwC5pvRwQ8aljCRS4CgQorFPQoGFNXQgMQgS/5/BVa\nevQLT7/4zLW5chNMqJV+Fl/kQJdpclsE4YQTYi12NdbFCptj67ezyMtXl7y8\nrL9H9MuH4VJiX4krnG1e+rl6qXtJq3JHWIKsVjjp+R1DsQaFGqT2fRvpix13\npP0gkoVdyPLDMzQW4sC0PMqARwz1nOUtDfBDNX2Phko0WM2q/oq+Lk1QgdFs\nfJORm9Yr4OwSFUTB6VD60/FR7eNNGIde0AAbe/mtaNlAsnkq5zMkB3ABVkSK\nos//eEhZi/oP4AYw9+i7PkMMt7aRnlWryG9HTaudtJQ4si1nS9uqVLZc7pT5\n0rO/UxXrIUkxsz5QjqtolmNyEQwM/XLIxX1stPaZohCnk0vgddcWLn7dJjKG\nJF+3bNYaVS5p1eCutxPu9cLqFWzRnfRwB9rH4EqYfwqda39BSKdyJYU9FTYg\nBVkSutwfIJVDjD76CqelfgtnrM3KRO5Ymlo+g0p9Wsm3JwGuRE7ZIGDX+qFy\nWR8Ld1f2dYuBEg19kCBGDbra3sqMnFqeVkJig1MiRnXrg0rOJK2IVNGW1xuW\nWpaV\r\n=INII\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBcwXKIdHKwUMXztu86gQPW6WxSbEtJatr9+bipXoM6vAiBL6Q29HjYch30wFABlFwT+Z30CshI0l6kCclPH9vy+Wg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.1.0_1550447879993_0.4726436965960332"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "cosmiconfig", "version": "5.2.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.0", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "f1750cde0b9c53bc577efa60d012a85a4dc4b823", "_id": "cosmiconfig@5.2.0", "_nodeVersion": "8.14.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-nxt+Nfc3JAqf4WIWd0jXLjTJZmsPLrA9DDc4nRw2KFJQJK7DNooqSXrNI7tzLG50CF8axczly5UV929tBmh/7g==", "shasum": "45038e4d28a7fe787203aede9c25bca4a08b12c8", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.0.tgz", "fileCount": 11, "unpackedSize": 43348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcl8oDCRA9TVsSAnZWagAALbAP+QAmTLJfqO01Q34rbf22\nFkow6J+qwjAe1mTuDk/QeJfX70CIRNonPTGUrZe81UaxvrZZQhUQnGE163CA\nOJfsY6rztJu0AFX+QQqRQLRpb538E4gJWD9sP/cpI89IiaN6R0vEUrNipK74\n2g7sLZZIB7iJdLJKOuMLd0SrBOKAj/RnRg0rnyEKqQ3Ol9oE/miymIY4tF0P\nVmkwhRWt+IZgEvd35LHttHzQ/V1lK2k8/8c1D2IGsG7HeTRoEoNstGzFCOX6\nS0y5z161AkAcFxXdvbi7kQATodO9DHY3j+PQ1k8Ba+F1lENXPMuIeAHSD/iA\nmoB9M6S907eRFIiED+avZiTDB1T1yuqpFSBYWTvY+JR5a1oHj84bk49rP2co\nKMNhDrk5BGPz8vEITchUlR9fFTIrLuWi92lrRIkL6vyqglSSCcjBfth0aAPf\n27WHWX4X6ybubme28mro3C+dB9eiFabNTbwKoWKMTqQIBIDrK5h/t5yLzvPR\nexmbXLDBH/kh4xQsAk36myRWwTZ9ORmJi7AlaIxMCR0LaKuA98+LmGrJtoVI\nXRb5YCgDTR7IMHmqWQ1JnvAwiNXD5lYCg5aQ9HY42M7H1ARgf1zJXbbnj+wh\n1e0wfe9/z8Feg6MQMQzdlPDkj5mP/DUoYxtkHCLcoimS60Nx4zvSOFIGSpZt\ntQYU\r\n=Xg0U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF6kSp+5RSRoNWnUCi0AgVK6EoFBrMgg8N6ztl1uHTBGAiEA7DN/qj9n+h1uZxQB9zL/bHL8Af1MdX2N6rlh4UlI+V0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.2.0_1553451522790_0.4067340719793242"}, "_hasShrinkwrap": false}, "5.2.1": {"name": "cosmiconfig", "version": "5.2.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "scripts": {"precommit": "lint-staged && jest && flow check", "lint:md-partial": "remark -u remark-preset-david<PERSON><PERSON><PERSON>k --frail --quiet --no-stdout --output --", "lint:md": "npm run lint:md-partial -- *.md", "lint:fix": "eslint . --fix", "lint": "eslint . && npm run lint:md", "format": "prettier --write \"{src/*.js,test/*.js}\"", "pretest": "npm run lint && flow check", "test": "jest --coverage", "test:watch": "jest --watch", "coverage": "jest --coverage --coverageReporters=html --coverageReporters=text", "build": "flow-remove-types src --out-dir dist --quiet", "prepublishOnly": "npm run build"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"], "*.md": ["npm run lint:md-partial", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/*.js"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true}, "babel": {"plugins": ["transform-flow-strip-types"]}, "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}, "devDependencies": {"babel-eslint": "^8.0.3", "babel-plugin-transform-flow-strip-types": "^6.22.0", "del": "^3.0.0", "eslint": "^4.12.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-node": "^5.2.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "husky": "^0.14.3", "jest": "^21.2.1", "lint-staged": "^6.0.0", "make-dir": "^1.2.0", "parent-module": "^0.1.0", "prettier": "^1.8.2", "remark-cli": "^5.0.0", "remark-preset-davidtheclark": "^0.7.0"}, "engines": {"node": ">=4"}, "gitHead": "f31c3c8623161967bbd1b4d50b16dcc4be1fb503", "_id": "cosmiconfig@5.2.1", "_nodeVersion": "8.14.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==", "shasum": "040f726809c591e77a17c0a3626ca45b4f168b1a", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "fileCount": 11, "unpackedSize": 43414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc22eMCRA9TVsSAnZWagAAXoUP/RQPnGQRVW4W3hRmd3A/\nIsnF1c+hmLt1QUttS2CLg5mNdSLS4mvB308RKCSYfkCK8/1eE0zv55lt+MgV\n0Ew7YOtwKwuAqnzcJt33KynA6Z47dfiylxyoghRyfpNrZA3cbEumWPHZbQgN\ntidlwoyt0hL9n2eZ9aQEI7ei5T2YmHMqLrhe1CRSrwbWKBWcOrOib3GF49R+\n7I+aQd0TtE+VPGdmANn3EUdd4x9y9Z0kLwMUtQGh8ODZOTgCKj/GrUkXrowN\n3efZuIAkPbuUxV69HpI85q1KsqjaUp2n4E9ikAue/oZ+XLVHdN8fiL6Mf3XW\nczO4RABexvpb2yf5UpGnij3l5SolJy3HCYiwK0/1v68KvNkytRHP1o475gsP\nPPgLLscaViOSTBdQzf9GbDztQd3XBzHeWXI9GpDJki56XP0bQqpuGxnB2YBD\nXGQ3LoXIDU8dIuUxJKYxCTHSUpENzZcY/STWnYBnSMF3wdcb2kuu/oPa8t1y\ntqkb9PyCBGuQ1kSDAybmJR+yr5hUmNWV6T5otu1EczT0t2OfGKszv2dTsmH4\n3NXBhz6+cXnkNYLCMTXXX4kCAvD3Jq6l+nWiS5RDLAXTgcSEfnQbZJWz5lLK\nZqw3apwpE/LZCgRIiZXWsGMwb7t04vsv4ZGGAM9Bfj1TPaV1/J80qeEWqHUa\nbgh5\r\n=40Af\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHv4oC0FIMGsD9o0PRWK7knRD3YQR24J6yJOc2b58nuCAiBrzwUOrDg4Bkjtyk0NZtJv38VluC0Q7RZt8VdSTrn3Fg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_5.2.1_1557882764010_0.9977172981055464"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "cosmiconfig", "version": "6.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write", "git add"], "*.{json,yml,yaml}": ["prettier --write", "git add"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "8.9"}}], "@babel/preset-typescript"]}, "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.7.2"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/preset-env": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@types/jest": "^24.0.19", "@types/node": "^12.11.5", "@typescript-eslint/eslint-plugin": "^2.5.0", "@typescript-eslint/parser": "^2.5.0", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.5.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.4.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^22.20.0", "eslint-plugin-node": "^10.0.0", "husky": "^3.0.9", "jest": "^24.9.0", "lint-staged": "^9.4.2", "make-dir": "^3.0.0", "parent-module": "^2.0.0", "prettier": "^1.18.2", "remark-preset-davidtheclark": "^0.10.0", "typescript": "^3.6.4"}, "engines": {"node": ">=8"}, "gitHead": "d26b10550df566648c5a9a05d893d800c9b129c6", "_id": "cosmiconfig@6.0.0", "_nodeVersion": "10.16.3", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-xb3ZL6+L8b9JLLCx3ZdoZy4+2ECphCMo2PwqgP1tlfVq6M6YReyzBJtvWWtbDSpNr9hn96pkCiZqUcFEc+54Qg==", "shasum": "da4fee853c52f6b1e6935f41c1a2fc50bd4a9982", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-6.0.0.tgz", "fileCount": 44, "unpackedSize": 99585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvakICRA9TVsSAnZWagAAJPEP+gLezqhpAJX/BrbyOdfg\nnuS+q3kTDjPHTS60bTwE18YLgPc1AUXLfIXpdCbryNdWCN2SP4xyb0ZHyXC2\nhXd/gmf8Nt3UyvrRBEVh3GI/0eq/LdTkHNX5Cs5iSDrCkf7QYCBclCDQBD5d\nPjd99gQXgqB/wxhsq2TuNd7ycgXGJtVMm8ztx9ALS20AxrWFlJazrzEOyWyv\nR5Ll5KmdX+QcZ0A+b5wW1O3GDoqjFIGIYDDGJ9W7Il0Ak+umhht6VfnSsqbO\nOTrBMopVT8M9yIJcFWv+IYdWL6qB/KpgK6k52KI7a3qB+ArdJKir8885U4qu\nHIUyGI7976DhVYNLm0DjPFfqM2+Z5V2VITRD08p5E5Ahl1GdWL48R85OtIzV\nPmIx7HRtjX7e4UaG+rbjYQqAmTpdJSi3Lws+8LQxzyTUkAgzHTlf+kjpscac\n0AkPmCaKzCg68ittpsU6CdD3a4b2c1Lln5bHVbs3s9Zf0XzCUMHJrrBYR98m\nIqshE9sPZVU29gCOcLVbZ1xrK9lVIBPPq3DmtSTG6v2SDJWhsnPUHKwGyLBO\ntAv9k0IeRhqemkFNC3XEfTahWYONyBCMhynnAqhLZZtCRAkidAPI+8rM6yE9\n9S0AvstEcDlzVt3K0a8plnt7fx/ELRIf828/BVb91CzIAxlmn7MGADHdgTfE\nEkUN\r\n=vRGf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEfKbM3vG55fOTQ2E+Gpg/AIMmlKYwUgvvWtI2cMLq6BAiAUp0j4DIBhI7n4elyDWpSgyCEIHvLZOQcfRNm7BUdmRA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_6.0.0_1572710664428_0.1972864267891421"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "cosmiconfig", "version": "7.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "10"}}], "@babel/preset-typescript"]}, "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/jest": "^26.0.4", "@types/node": "^14.0.22", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.4.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^23.18.0", "eslint-plugin-node": "^11.1.0", "husky": "^4.2.5", "jest": "^26.1.0", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^3.9.6"}, "engines": {"node": ">=10"}, "gitHead": "07e7637b74cb01b23f351bc1586390d20eed881f", "_id": "cosmiconfig@7.0.0", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-pondGvTuVYDk++upghXJabWzL6Kxu6f26ljFw64Swq9v6sQPUL3EUlVDV56diOjpCayKihL6hVe8exIACU4XcA==", "shasum": "ef9b44d773959cae63ddecd122de23853b60f8d3", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.0.tgz", "fileCount": 44, "unpackedSize": 98982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJcokCRA9TVsSAnZWagAAJTAP/3YkJpLzDjYbWcOVTfDR\nQlmG4sSqCQNr/rRG0txTjp8XpwzrH6dDlpFmmfMH5XIisJoTWDUglexhVziK\nZTPrCgHjWl36boOyJCDyENSexQrKEJYD8/fMBfkjmbO675fdTX1NH5mr4B6O\nWZ5XOtCdW8qGSGOqko+vhOJslA55fYPITmafkvqn2UMABwQ7XH/aZwZisGaO\nWUv2gt6z3QG4Y2OVZIqS8pRePWCMizO8YyIw0idUeHQVF4rFq0ZrytSCmsTY\n2VFK6uDHQJ3qEz6r8y0nX2fbs2s3pFXkTfLe1b+GLt/KsvfpSzTCDqrr4KeG\nyojQhVWKbnjFijbe3Q50wRHGlZbr/uKeK00AGBp1IjuTCrdRT/9XiCkT/uQJ\nMMaEXwcxueMXZdrCCL7Aajsao7UYJ2C757wmCkkJuhP3E+3kJ/aeWNVEueG2\nxYrR0KHfK/KtPvvlPeYDM7JyYJpZO9iBgmnS1jfucSv0Q6N0ar2QTQ+ayBgz\noNZeEUnMCoe/a1YwtWvKXggo6gO4x8+tkD0JK4f2iCA19Eb59mBKjjLx+Tsq\nSDI61URPkaNCfzf0ZhAYKNYP6f0dQsScO30TN22IcJ4WzSN9m4FJnyYv4S5L\nnpWqc0d9fzwUb3hr3HGvJjMpDEp01DOWEMos54oIhyTc7eQor3RomjRyfqNO\nzyF+\r\n=p2Va\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTG8QrT8tcDlLWexHm3eyQ0sTWr77Zfs/iL3L168AsNAIhAK+Lsfe+F9hNHeLt0fRfM88M2uHcS7jxeaUz6XC0AYWc"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sudo-suhas"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_7.0.0_1596312099515_0.9850646036429751"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "cosmiconfig", "version": "7.0.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "10"}}], "@babel/preset-typescript"]}, "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/jest": "^26.0.4", "@types/node": "^14.0.22", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.4.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^23.18.0", "eslint-plugin-node": "^11.1.0", "husky": "^4.2.5", "jest": "^26.1.0", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^3.9.6"}, "engines": {"node": ">=10"}, "gitHead": "3aaa667d469a907ffb5904af2f2b04b85b1f22e0", "_id": "cosmiconfig@7.0.1", "_nodeVersion": "16.5.0", "_npmVersion": "7.21.0", "dist": {"integrity": "sha512-a1YWNUV2HwGimB7dU2s1wUMurNKjpx60HxBB6xUM8Re+2s1g1IIfJvFR0/iCF+XHdE0GMTKTuLR32UQff4TEyQ==", "shasum": "714d756522cace867867ccb4474c5d01bbae5d6d", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.0.1.tgz", "fileCount": 43, "unpackedSize": 90941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIYIPCRA9TVsSAnZWagAAoPcP/jEhM1joQH8jWT1BGgOb\n1rRP55o0Y6fIoWOS9CFrAqgi2AQVUy+3asCroTk4Vrb1u+v/k7oNfklRZ0S4\nAJ2a/zK8Aw31kQfiMnCA2kIm3GPZmpz3a+6dz27n5bNypsQbkqbbSo/ZGonH\nRd4suC791rIwlhHW6SlrBXBPc4W5w9QpgyP2wcbvjjpaCwfDsdnFcp7PLlHC\npmLUAz0TmoHReDDerqIW67lOSz4ud08cPsL4G/EzPIlr4XpRntzNzhMn0IB7\nKb4k06pNeSepa8DkJW+d06WxeaBoQtorYQMB1gTZ3AtSEe3fqLEK+LOD/mDZ\ney+g1oWMe+CxukhCD2XBimBFILJHKRUUyxenhueiwgefwRv76RqUjJ49+0gW\n9I70z3Qg2IVJ2ArTJKtnquCedfWxWZox03KPaHzYeRhqtX5guW+2JoTl9aEU\netufoY6isfo6o4T+eRJVWCE6jsFM2Y7V8xhxSMI+bQodCfxgmKoTiRwls15L\ndAh+bdXVRVSWizKDOQphThUng2etY4mqC40XL/4PWuuNFkZo2GRaHF2LHsE8\nq4wSJwPyP/GdD3mJaBn3jjMA+HDftwH40AD5gVEiW8uOsrRXBwjWKmEQ4lHi\nWdzO2XjkJb5eHA6fi4CYex/Rta+H00S4LF+kHI9FyjOFh/WOTT63YDp7z7R7\n9yne\r\n=QB9b\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBDWMEi6bA4eUzCgOb0YHAA7IHwSy929xWtTy/iDxJb3AiEAwA9tMay0STe5wNH7OOggKh6fckEQsmav9dGQ3gn3WIo="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_7.0.1_1629585935045_0.49048214481144425"}, "_hasShrinkwrap": false}, "7.1.0": {"name": "cosmiconfig", "version": "7.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "10"}}], "@babel/preset-typescript"]}, "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/jest": "^26.0.4", "@types/node": "^14.0.22", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.4.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^23.18.0", "eslint-plugin-node": "^11.1.0", "husky": "^4.2.5", "jest": "^26.1.0", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^3.9.6"}, "engines": {"node": ">=10"}, "gitHead": "18f14419aa7015aad11b7dc684947dec7171a9c5", "_id": "cosmiconfig@7.1.0", "_nodeVersion": "18.11.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==", "shasum": "1443b9afa596b670082ea46cbd8f6a62b84635f6", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "fileCount": 43, "unpackedSize": 92011, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGAxxy2NUu0GYDzcSKRRguUw5mlEQkepzN3uWV1OST1IAiAmMaLnMWeeZDtiLLZ8S6JSkwKAK5awqlkK5uFjIivZCg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjb6s2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqULg//UnTkWZYhZYoJrm9R+10SWqIgV5Mm5D4gujUXmFrKmjN+h3yV\r\nfTW91RdhERl7lRwydvuu+Q3P/cBv7audTHY5Ry2WdZ2LOY3HwONsKkjcAF0T\r\nGYc774rbMA2Ra6gHincrEpaMeAbH8IYnfxjeFBcLYanVX5Qx1ryYsB0LCaGr\r\nIquFixXbcgcflWQsZyg6QVT7kf8U2NGVlMhw93r6pdDW455LGJhZv3lLeVFs\r\nk7+yECjoGQALTiwh9o21wu4QfeRM/L0sjXA0rCP//6uUR3MH6FeFNWqOJ98W\r\nWCiMVd+GO8swMgklVCjZBIQGFYfoGF9x0z0ysqZ8CHVafN6dJfxmK5rA6nts\r\n1pSgytKV/i7DmasJYXzlybYZFM9XQiujxRCooSBYkIV2CQ8K/NTNlGl3lyeb\r\n5KIoymyeS3mINeYHKyMjKXljgYTg+XUnghNC9XXTM+ncufhiEFn/a/mEwWgZ\r\nhCJ5Hn59HqwTX40w20fWwVyojsWDIzmyrBBDXgi+DQWE0k6QWXaPlcOuia+2\r\nqbYnoBoCtub17Jc1F3kl5yvbcHR1TEPHc9CUCK3sjBEd8b9xxvC6v3jVDcO4\r\nzkyf4Dgvr2aPGj5dyueb2iPwKcGrd6gNlCADnlRWIogNZGuc6x8nAgMZ2vBp\r\nr+F8ulbxj0U1V2O5QGDaOXH5BeJBoqaAuKY=\r\n=5CDq\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_7.1.0_1668262709828_0.6480970462906401"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "cosmiconfig", "version": "8.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}}], "@babel/preset-typescript"]}, "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/jest": "^26.0.4", "@types/js-yaml": "^4.0.5", "@types/node": "^14.0.22", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.4.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^23.18.0", "eslint-plugin-node": "^11.1.0", "husky": "^4.2.5", "jest": "^26.1.0", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^3.9.6"}, "engines": {"node": ">=14"}, "gitHead": "7d1dce9ae38c57154f00584118f3174969aca6bd", "_id": "cosmiconfig@8.0.0", "_nodeVersion": "18.11.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-da1EafcpH6b/TD8vDRaWV7xFINlHlF6zKsGwS1TsuVJTZRkquaS5HTMq7uq6h31619QjbsYl21gVDOm32KM1vQ==", "shasum": "e9feae014eab580f858f8a0288f38997a7bebe97", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.0.0.tgz", "fileCount": 43, "unpackedSize": 93453, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJhfUHOGI9LoDLvXz4rjLjjj+4ttxZ6P+B3+jlBSIVjAiEAyUvdHL9mVSVRX/BQx6jKPmHOUM5HVOYOfZRPV0+9jEc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJje+O2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6TA/7BoDY4cahCO5aZp8wkge+oXng4sEQGzuIdTL9dpft7ies8EIW\r\nvT1Oj2h0bbu1CC4LZ/9EZ0eCWpteczO1eJd3NNfH7TfCYi0pYcnYdmMY49ua\r\n0pFZGctTx+4Kq0YXC9+XcoY6yLcvdNN8R2j8zwig7OimW5tBK5MpW4T25jTo\r\nBaq2yA2EBtr0rxcR37ypVhlMsgQICY1nsZ09CiSkgOJtP9s4ogxo4qYYdb84\r\nek+Qejhhqq0C48HNS21kiCSyzwkCa+MffZ6xbU5QlqGDFnPq8kpwoItkR6ii\r\n/G3SjFocTx0yaMqE/VVYbpfsb1JSXRVX0SL1Dw52thQC2iW3EuyHpIu03HY9\r\nrqdaWkH1+vVV6FpxpNKGhgNsYaS8EOJtKW0dpa+B52ck1qKP8c0c2NOkIAWm\r\niYGDkMJUjHlsXXnRa7NAS7CBCVvTPamKdoTL3xMBG7lIM5gmtB+yr3bcEcuE\r\ntdQPRDwMptA3mhE0A5+QRDoR5N4aC/v/XkFKDrKsEXxLaFiEsYJX5P7IiY5u\r\ntmwfAdrxHrAHy9MiwhpBnPs8sKcz5Tfzs2eT9e9wEjEK+ROnLiC4yvHuIBKy\r\n1jlUlRELHP6zci69FNL3hOuyEcNNYsVesd3M2E6fz9n8oVNVsEudvEo9XZd+\r\nVcvZAWTi+dHQtT/PIhuCl2i68h+c+slOzjk=\r\n=zo6T\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.0.0_1669063606463_0.9779816113329995"}, "_hasShrinkwrap": false}, "8.1.0": {"name": "cosmiconfig", "version": "8.1.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}}], "@babel/preset-typescript"]}, "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/jest": "^26.0.4", "@types/js-yaml": "^4.0.5", "@types/node": "^14.0.22", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^3.6.0", "@typescript-eslint/parser": "^3.6.0", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^7.4.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^23.18.0", "eslint-plugin-node": "^11.1.0", "husky": "^4.2.5", "jest": "^26.1.0", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^3.9.6"}, "engines": {"node": ">=14"}, "gitHead": "5633110822a5e35e0f6d6e7102ce69875a9ff6bc", "_id": "cosmiconfig@8.1.0", "_nodeVersion": "18.11.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-0tLZ9URlPGU7JsKq0DQOQ3FoRsYX8xDZ7xMiATQfaiGMz7EHowNkbU9u1coAOmnh9p/1ySpm0RB3JNWRXM5GCg==", "shasum": "947e174c796483ccf0a48476c24e4fefb7e1aea8", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.1.0.tgz", "fileCount": 43, "unpackedSize": 103581, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDE9I+Ekh6BAoRNFN/wSIs+v2z7fcSJGWYJ87zlwWs3JwIgKf8dvA9rtsNSg6LoF9J4r1vR3WdygF/aWcYbXgd/d1Q="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+TLUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxCw//YXRjEB83PS0okY145QtIsHhpsNai56WMcmUessKsBKrifdCz\r\n55PagehzReOV2hEZBZ4ScckQlK/GR01HPHlOP2OvFzG8PIRmAJIgEzcxymNC\r\nUF9wLpvY6DwyorXdS7YKZedt3X2HITZR2Zi8x5WOcGshbKZtVjRz0k4gy0Ji\r\nh2njWCSiK12mRAmM3gutnhIGZORXxPVvuj10MnrR3hVX3sxDG34qril+5Z3D\r\nb2C1xXHqauiYmfhrtatCYWLEEshB7woIilniYWBbMpjC5q+toKudQa+LDMKs\r\nGa7FULTJHkw8pP07dkQ5ftX2KAgf2TrSgj94XnSYo1CLjWPW/8D1nA6bI2yQ\r\nmGWF90pqg834cne9JYaxsM+WuJyYpt1YxrrEbCHnGHcDSAjPhf8PrbuR3tKm\r\nB1tiZ20nOMKVSg5LIuojSRkzObKsUjYm1QAOsLMn9Js8TKbRSUYdpmIelTr0\r\nQodl6Yi957Zj1rniVd2gnZIkf9LXKrIUneLY7vTKm1ACRUG8gEQiKu/UVHMv\r\niVvbJkuauMWrFRfBhBRBWwZdunp5wthb2Sco2i5IxXjfBnrxiL4NAKs5r8N8\r\nDACauIZHiQmf7cm+qfyRFpu7nyST7Sdvtu6Kr4A09G2BhO8Lhqzue7/Saz8+\r\nQZrTh5h5TZXesO8dUwgOeRavLYOq7q4MwQk=\r\n=UzxQ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.1.0_1677275860454_0.0031086881552699897"}, "_hasShrinkwrap": false}, "8.1.1": {"name": "cosmiconfig", "version": "8.1.1", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}}], "@babel/preset-typescript"]}, "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/js-yaml": "^4.0.5", "@types/node": "^14.0.22", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vitest/coverage-istanbul": "^0.29.2", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^8.36.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.0.54", "husky": "^4.2.5", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^4.9.5", "vitest": "^0.29.2"}, "engines": {"node": ">=14"}, "gitHead": "193cb57ed96a3c84e14855000aa64f3fcd95ee6f", "_id": "cosmiconfig@8.1.1", "_nodeVersion": "18.15.0", "_npmVersion": "9.6.2", "dist": {"integrity": "sha512-Kw7F5apKYo2Sz12i8sKcuj2iy69VTT5m1WXkdqTjt26YEGbQjJd5WB4JiDjrMB3HBpJ/BAxvYuDuCNXb2NO2eA==", "shasum": "46f82063efe81bb8b8fdb212a45ba785606283e3", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.1.1.tgz", "fileCount": 45, "unpackedSize": 104093, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/wHFENKxap7igm9+1lB7qSn6zXVyzxF3fveD/TR6PpAIhAM/lC316DBxs81yZZRfvxtDK1sWXG1Wvikxg2FkW+dCt"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFHUOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEhA/+Mzm0cOgIR+OQ/BG1ZMbzDv9DCl1ZWLafSVkrX8kupZGdJgqr\r\ncablt3bfB5DfQXTUTuTqg9xr8qFFxQ7SPGxAticEzza2w1q4BhxUpxts+fSF\r\nO8c+G6LIANgfnAiVF/S82zxmYS7xyfMHGrAAI0laSA8QgWOmA0cCH14pR87M\r\nnfcnXrMOX1a7UQTnsssIw8JpARh7Dz87JnXdOjDejQfqOArtCXFWyQlUERgd\r\nK/7WsOea8U1ITCQvyaAMjhedhulBoUfBlWteAaChfMX3VfuaOm17CIUJXwq0\r\n2BYFKIii96y3jjI1UfYF9K5GdgkJ+EenGKcavgS8Z0/aaq3nxmfUSKBL7DNx\r\nksCXEK2NcnxH+nWupk8AgaoYOhqIVwoH/91hYE3WqJu8IaQUNbJfDUiIp3jL\r\nBYJY9lI/0mRjJk/tfSQ9CbIB9XrehX43A43XIAbq9PIESnqlh3ILOujCYm3u\r\n+vdHNaOO12ZQqdQWM603fX08cQADngRjpTsexAGHpGfgza8XF15sjfnSI777\r\n8yBIgGNvhfDvpiIXJdUiOfefSeHM118+t1kPLL9MyRPB4hxSRKzH9Dpsj92X\r\nEPtQ33kdaP+v+wvjMYZThvD2PViHV1ueYlBx8v1dZAnQh8vcaxoV/jBudEN1\r\nL0qmj4k0lCFl81FMRF/MJBzNHEsVrimHFwU=\r\n=lzu5\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.1.1_1679062286154_0.991406064098546"}, "_hasShrinkwrap": false}, "8.1.2": {"name": "cosmiconfig", "version": "8.1.2", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}}], "@babel/preset-typescript"]}, "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/js-yaml": "^4.0.5", "@types/node": "^14.0.22", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vitest/coverage-istanbul": "^0.29.2", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^8.36.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.0.54", "husky": "^4.2.5", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^4.9.5", "vitest": "^0.29.2"}, "engines": {"node": ">=14"}, "gitHead": "49b1586d98cabd7578483a4aaffce40f48780ab8", "_id": "cosmiconfig@8.1.2", "_nodeVersion": "18.15.0", "_npmVersion": "9.6.2", "dist": {"integrity": "sha512-rmpUFKMZiawLfug8sP4NbpBSOpWftZB6UACOLEiNbnRAYM1TzgQuTWlMYFRuPgmoTCkcOxSMwQJQpJmiXv/eHw==", "shasum": "3a9af12d5663d041cd2ab448e9a0e62035ea902a", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.1.2.tgz", "fileCount": 43, "unpackedSize": 103754, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqyxn/yLWj70i9V0WI5x2xO6SRlVrDSWFwZBZF7mIX4wIhAIiKtMzanNDOpQzdFy5UFPNSdElElmuf+STvV59kfHRj"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFJ0wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvuA//QfcSe0FiFnhp5YGzg8KyH5I2OJQGxTTFw4yhGy9gZG0CDMcc\r\nnUSNq7wuA3XWROHpp5qhILuBO0zJ6PKsabuVXbJq8udD8sk14wXeuzk9xLIh\r\nb8bDV+VyXfaMH2PrvFjo3CqZRIVPGn6AAikEqexCpNfAIgqPxIktkfH46WJ4\r\naQqd5fGgRiPFZGZKv7GmxCM8VubBX+rwMQE04dBC/kOi8mtwBIE5ulpMcTxr\r\nkhVtCyfFgg1Cm/+Aoc6gQVRnd2Toov1LQkP518Bl+frfzNcKK8LaY73UGhk9\r\nnJK8f6E4ERLqmNV9mkvXjo/E5ZikRh6Ib+SJ88Y/7rYAKFFw2um3Mc6Go9ve\r\nbAOy9vIxHwRXisR0vHKXKRhbXjh7rmu0S1aIq35lsQ0Ub6OGVpZJlbEELj1l\r\nj9P6AQt2u4DzAU/G1TJAOW1sxdpXBqOlEz7+CV/MVOqhwoOSZmU59T7kfBib\r\nKfoW9SrGtq86IO+BofuMeGCGO2gRAU1jqIsJ2MR5tz/QrX8MKVM41Pna2Sf4\r\nRcAoOhQSran76aAmqHRHjf8yldToQ1lUQT6L1rdp4F8si55udbLVRnIjHnBZ\r\navHu6Z2GIfVEyU16+nwJYu+r6+MB3TnqZktvSxPKX8CxX8GNfJ6+YcBj93AN\r\n/4BBCEAibiSRMaEc84lTjoFFfkXt9tt38To=\r\n=LZan\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.1.2_1679072559924_0.6696259633882069"}, "_hasShrinkwrap": false}, "8.1.3": {"name": "cosmiconfig", "version": "8.1.3", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}}], "@babel/preset-typescript"]}, "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.10.4", "@babel/core": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4", "@types/js-yaml": "^4.0.5", "@types/node": "^14.0.22", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vitest/coverage-istanbul": "^0.29.2", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^8.36.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.0.54", "husky": "^4.2.5", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^4.9.5", "vitest": "^0.29.2"}, "engines": {"node": ">=14"}, "gitHead": "dee5198fd96c3f64b8b004cca9117f06aafe1212", "_id": "cosmiconfig@8.1.3", "_nodeVersion": "18.15.0", "_npmVersion": "9.6.2", "dist": {"integrity": "sha512-/UkO2JKI18b5jVMJUp0lvKFMpa/Gye+ZgZjKD+DGEN9y7NRcf/nK1A0sp67ONmKtnDCNMS44E6jrk0Yc3bDuUw==", "shasum": "0e614a118fcc2d9e5afc2f87d53cd09931015689", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.1.3.tgz", "fileCount": 43, "unpackedSize": 103094, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEeGVIym4SJjVfIS5bCv52BrQ47EWhFissep3k+6VJUtAiEA9Qzh/G0j0i7epGz5vpRY//7mFYIxqxFGogTWIQMcdJI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFiREACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp55xAAj5XFJcQGf6e1nydpde13DW7JUniHI0zQDLAojzBld0NsOm1H\r\nBsYUjxkiiYwSA8uadl5jdOlZeGM8VlUYopYZUOp2LFcD47wKt5QNX/B/czNc\r\no5wiAM+SIk1U81qYKeX18T2/uPH7Lq8m7UELCYdOpqGDzCIf4bsB7MUygljq\r\n/IS8JNhuMbCRSb72Vojc0vKsOq3uYsbG7sBbTZ+5kIwd9uM1jO9O+b6kOMMJ\r\nXKxc59bBky8y7sifUSJqKA8zGO9j2wXhx0kQT0onU5ykHnjiX2V6MQr5aFD9\r\nfpqk4/as8CXfZ/B9V3K86N+Pbq413V2GweGlWXbx7Xea+JakfG/znz2y9A61\r\nM3cP8tsM8JJcbrjHQ05kEQeYy+1gJjIleiJ08p4p5pei/YXUWSKDALkKKhTx\r\nBpGvDuVV+c7nGvDUWSvco4FEU2J0DiN08Pq9ey7lHRUncnlc673Hj6+pdfqS\r\nG1W83tiVF7rHzlJ84bTDDHjgikGhxitl25Dbg+ZUzelXvJUa9xWtXlczMOv0\r\n24P3+SskDZUmTj3QWLAZOATPHGEzhjIg/MG/JeKjpAFVfJPB1VCgpjBJ3oyQ\r\nRKEyZ0h3BLcAYCX3ULrnRKUCa73gfFoAgdcvovWFX/drTIoGXLcFa7pr/2wc\r\nSkdCEz/1IyHd9XzgzqXbo9KgLQxIARTaHIw=\r\n=psgl\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.1.3_1679172675932_0.6114107865329328"}, "_hasShrinkwrap": false}, "8.2.0": {"name": "cosmiconfig", "version": "8.2.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}, "exclude": ["proposal-dynamic-import"]}], "@babel/preset-typescript"]}, "dependencies": {"import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.20.2", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@types/js-yaml": "^4.0.5", "@types/node": "^14.0.22", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "@vitest/coverage-istanbul": "^0.29.2", "cross-env": "^7.0.2", "del": "^5.1.0", "del-cli": "^3.0.1", "eslint": "^8.36.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.11.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.0.54", "husky": "^4.2.5", "lint-staged": "^10.2.11", "make-dir": "^3.1.0", "parent-module": "^2.0.0", "prettier": "^2.0.5", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^4.9.5", "vitest": "^0.29.2"}, "engines": {"node": ">=14"}, "gitHead": "889d3b491b54babf4d816a10a6c6720df5ccd944", "_id": "cosmiconfig@8.2.0", "_nodeVersion": "18.15.0", "_npmVersion": "9.6.2", "dist": {"integrity": "sha512-3rTMnFJA1tCOPwRxtgF4wd7Ab2qvDbL8jX+3smjIbS4HlZBagTlpERbdN7iAbWlrfxE3M8c27kTwTawQ7st+OQ==", "shasum": "f7d17c56a590856cd1e7cee98734dca272b0d8fd", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.2.0.tgz", "fileCount": 47, "unpackedSize": 111478, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAaZIkIGCbMSY6zJmAB2XEfnHn6aDhUQt6G3d3sk4VtsAiBgM6uBFVrDWhk3O74LH/Rsb1U8Pls0yxjBSZ1t+kmylA=="}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ybiquitous", "email": "<EMAIL>"}, {"name": "sudo-suhas", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.2.0_1685905699213_0.5639519352396278"}, "_hasShrinkwrap": false}, "8.3.0": {"name": "cosmiconfig", "version": "8.3.0", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "~18", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=18"}, "gitHead": "6476b36bb266ab69f2df43ee3fa7ac9cca2ab7ea", "_id": "cosmiconfig@8.3.0", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-kLfJx14+dZY3x65/LrdDXNt8r7VkoARRofiMgZhZ9qoKtJsGTjBKrbSBPwtxfNF9oWr7HC9x228/GYrwCnZLrQ==", "shasum": "a90c59a5cf02ca4419f9f2071682e096edb4f6fd", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.0.tgz", "fileCount": 31, "unpackedSize": 77563, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA3ps1B0SaO8a8IIWL66RSYwFtBM8AkUuD/KAo7KTxYjAiAPURFfviavwd22LlTh5rtdcXuui6E69gyijoNdHRyxEQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.0_1693671341805_0.39252977719476734"}, "_hasShrinkwrap": false}, "8.3.1": {"name": "cosmiconfig", "version": "8.3.1", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "~18", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=18"}, "gitHead": "408c9cc41002ba53afe6068441cfc7a80a7f0e24", "_id": "cosmiconfig@8.3.1", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-SB2dDDohny1d/U1CxuBrzSrLXqgwIBFLSFIbXVDWOFnm3CmlMFCNztRStzgZdEzpV/tTINW9XGdc+L6bX7DiSA==", "shasum": "9a505171ad61f777763af9c48d452629a5b087f8", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.1.tgz", "fileCount": 31, "unpackedSize": 77669, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzKvmOGtR6+9tTD8E8Sf6XHWDAZusleRuWDawCzqJb5AiEAjzZjNTYXjyC6J23d7LGx94JUJJzinrSLt0S5R2SZcIs="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.1_1693684656282_0.7497260846325773"}, "_hasShrinkwrap": false}, "8.3.2": {"name": "cosmiconfig", "version": "8.3.2", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "~18", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=18"}, "gitHead": "6cb1e76f9b42f583bf91c9654d7b6d81fc4fc141", "_id": "cosmiconfig@8.3.2", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-/PvU3MjSLVKJMRHsL6GW+wHQ9RaJuMW6fnn56sXNy+kP1L8nI/SHk9F9giIA+dnfzjKNJAulRjOR/fi9kzGuNA==", "shasum": "020213ad7387482a1f3fdd5c77c300ed2c72e162", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.2.tgz", "fileCount": 31, "unpackedSize": 77700, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsH+o6P5VnZfY4Tq9tN14D0fRtbPZ7ZutY9UZGvgnHzAiEAnds5JkQCFkNtAEraH5e9iJ0u9DvjRxVBC/LBNYA1TNQ="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.2_1693686819994_0.537097995712521"}, "_hasShrinkwrap": false}, "8.3.3": {"name": "cosmiconfig", "version": "8.3.3", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "gitHead": "bd90c8975cda6f03069260aa870a3ae1f547a80a", "_id": "cosmiconfig@8.3.3", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-/VY+0IvFoE47hwgKHu8feeBFIb1Z1mcJFiLrNwaJpLoLa9qwLVquMGMr2OUwQmhpJDtsSQSasg/TMv1imec9xA==", "shasum": "45985f9f39f3c9330288ef642b1dcb7342bd76d7", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.3.tgz", "fileCount": 31, "unpackedSize": 78331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3MQvjNeWDg6Zr+YzX5Dp75Zwm7SKm1FBqZ3JZw7pulgIhALE7UJ96xN8lClj76vQC0Q+aplR08pTKAYf/Wwt4sMr1"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.3_1693770797584_0.345946471695898"}, "_hasShrinkwrap": false}, "8.3.4": {"name": "cosmiconfig", "version": "8.3.4", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "gitHead": "f4eb2d78ea2cc52ee766e113011cbc4601c1c706", "_id": "cosmiconfig@8.3.4", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-SF+2P8+o/PTV05rgsAjDzL4OFdVXAulSfC/L19VaeVT7+tpOOSscCt2QLxDZ+CLxF2WOiq6y1K5asvs8qUJT/Q==", "shasum": "ee1356e7f24e248a6bb34ec5d438c3dcebeb410c", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.4.tgz", "fileCount": 31, "unpackedSize": 78088, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC13+0jEfsSnn73x88fQDXZN8m34jUn212dscSgOfRFugIhAIWuVWdhQf2F31gWViTPFC61PS/CZ234AimNaor5xhiW"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.4_1693856008550_0.3381592209287818"}, "_hasShrinkwrap": false}, "8.3.5": {"name": "cosmiconfig", "version": "8.3.5", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "gitHead": "8550adb3da7c91866b3a8862b5b5caf42866b0a4", "_id": "cosmiconfig@8.3.5", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-A5Xry3xfS96wy2qbiLkQLAg4JUrR2wvfybxj6yqLmrUfMAvhS3MZxIP2oQn0grgYIvJqzpeTEWu4vK0t+12NNw==", "shasum": "3b3897ddd042d022d5a207d4c8832e54f5301977", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.5.tgz", "fileCount": 31, "unpackedSize": 78217, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7n+AtZzTN/rKWnxlyzTsu0gDzoYT2upQ6fXqsIoWnZAIgCwLurIEVsxzJ11Pjzp+h0Qb9k0QvaqHQOyVKRVWcsZk="}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.5_1694211127650_0.4523669359290263"}, "_hasShrinkwrap": false}, "8.3.6": {"name": "cosmiconfig", "version": "8.3.6", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "gitHead": "42ca3fab6d6ae593a895c0fe4e2a5f6b297e6361", "_id": "cosmiconfig@8.3.6", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==", "shasum": "060a2b871d66dba6c8538ea1118ba1ac16f5fae3", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz", "fileCount": 31, "unpackedSize": 78453, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYNa0EHphFfV5906MtrnySXotQiqsQrJ9zRDV/i+KnNgIhAJm4JjW0x9P9F7LQ9Ni0Z9eFuauKsDLXWuisNhU96HF4"}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_8.3.6_1694622882491_0.7095452370949751"}, "_hasShrinkwrap": false}, "9.0.0-alpha.0": {"name": "cosmiconfig", "version": "9.0.0-alpha.0", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "readme": "# cosmiconfig\n\n[![codecov](https://codecov.io/gh/cosmiconfig/cosmiconfig/branch/main/graph/badge.svg)](https://codecov.io/gh/cosmiconfig/cosmiconfig)\n\nCosmiconfig searches for and loads configuration for your program.\n\nIt features smart defaults based on conventional expectations in the JavaScript ecosystem.\nBut it's also flexible enough to search wherever you'd like to search, and load whatever you'd like to load.\n\nBy default, Cosmiconfig will check the current directory for the following:\n\n- a `package.json` property\n- a JSON or YAML, extensionless \"rc file\"\n- an \"rc file\" with the extensions `.json`, `.yaml`, `.yml`, `.js`, `.ts`, `.mjs`, or `.cjs`\n- any of the above two inside a `.config` subdirectory\n- a `.config.js`, `.config.ts`, `.config.mjs`, or `.config.cjs` file\n\nFor example, if your module's name is \"myapp\", cosmiconfig will search up the directory tree for configuration in the following places:\n\n- a `myapp` property in `package.json`\n- a `.myapprc` file in JSON or YAML format\n- a `.myapprc.json`, `.myapprc.yaml`, `.myapprc.yml`, `.myapprc.js`, `.myapprc.ts`, `.myapprc.mjs`, or `.myapprc.cjs` file\n- a `myapprc`, `myapprc.json`, `myapprc.yaml`, `myapprc.yml`, `myapprc.js`, `myapprc.ts`, `myapprc.mjs`, or `myapprc.cjs` file inside a `.config` subdirectory\n- a `myapp.config.js`, `myapp.config.ts`, `myapp.config.mjs`, or `myapp.config.cjs` file\n\nOptionally, you can tell it to search up the directory tree using [search strategies],\nchecking each of these places in each directory, until it finds some acceptable configuration (or hits the home directory).\n\n## Table of contents\n\n- [Installation](#installation)\n- [Usage for tooling developers](#usage-for-tooling-developers)\n- [Result](#result)\n- [Asynchronous API](#asynchronous-api)\n  - [cosmiconfig()](#cosmiconfig-1)\n  - [explorer.search()](#explorersearch)\n  - [explorer.load()](#explorerload)\n  - [explorer.clearLoadCache()](#explorerclearloadcache)\n  - [explorer.clearSearchCache()](#explorerclearsearchcache)\n  - [explorer.clearCaches()](#explorerclearcaches)\n- [Synchronous API](#synchronous-api)\n  - [cosmiconfigSync()](#cosmiconfigsync)\n  - [explorerSync.search()](#explorersyncsearch)\n  - [explorerSync.load()](#explorersyncload)\n  - [explorerSync.clearLoadCache()](#explorersyncclearloadcache)\n  - [explorerSync.clearSearchCache()](#explorersyncclearsearchcache)\n  - [explorerSync.clearCaches()](#explorersyncclearcaches)\n- [cosmiconfigOptions](#cosmiconfigoptions)\n  - [searchStrategy](#searchstrategy)\n  - [searchPlaces](#searchplaces)\n  - [loaders](#loaders)\n  - [packageProp](#packageprop)\n  - [stopDir](#stopdir)\n  - [cache](#cache)\n  - [transform](#transform)\n  - [ignoreEmptySearchPlaces](#ignoreemptysearchplaces)\n- [Loading JS modules](#loading-js-modules)\n- [Caching](#caching)\n- [Differences from rc](#differences-from-rc)\n- [Usage for end users](#usage-for-end-users)\n  - [Imports](#imports)\n- [Contributing & Development](#contributing--development)\n\n## Installation\n\n```\nnpm install cosmiconfig\n```\n\nTested in Node 14+.\n\n## Usage for tooling developers\n\n*If you are an end user (i.e. a user of a tool that uses cosmiconfig, like `prettier` or `stylelint`),\nyou can skip down to [the end user section](#usage-for-end-users).*\n\nCreate a Cosmiconfig explorer, then either `search` for or directly `load` a configuration file.\n\n```js\nconst { cosmiconfig, cosmiconfigSync } = require('cosmiconfig');\n// ...\nconst explorer = cosmiconfig(moduleName);\n\n// Search for a configuration by walking up directories.\n// See documentation for search, below.\nexplorer.search()\n  .then((result) => {\n    // result.config is the parsed configuration object.\n    // result.filepath is the path to the config file that was found.\n    // result.isEmpty is true if there was nothing to parse in the config file.\n  })\n  .catch((error) => {\n    // Do something constructive.\n  });\n\n// Load a configuration directly when you know where it should be.\n// The result object is the same as for search.\n// See documentation for load, below.\nexplorer.load(pathToConfig).then(/* ... */);\n\n// You can also search and load synchronously.\nconst explorerSync = cosmiconfigSync(moduleName);\n\nconst searchedFor = explorerSync.search();\nconst loaded = explorerSync.load(pathToConfig);\n```\n\n## Result\n\nThe result object you get from `search` or `load` has the following properties:\n\n- **config:** The parsed configuration object. `undefined` if the file is empty.\n- **filepath:** The path to the configuration file that was found.\n- **isEmpty:** `true` if the configuration file is empty. This property will not be present if the configuration file is not empty.\n\n## Asynchronous API\n\n### cosmiconfig()\n\n```js\nconst { cosmiconfig } = require('cosmiconfig');\nconst explorer = cosmiconfig(moduleName, /* optional */ cosmiconfigOptions)\n```\n\nCreates a cosmiconfig instance (\"explorer\") configured according to the arguments, and initializes its caches.\n\n#### moduleName\n\nType: `string`. **Required.**\n\nYour module name. This is used to create the default [`searchPlaces`] and [`packageProp`].\n\nIf your [`searchPlaces`] value will include files, as it does by default (e.g. `${moduleName}rc`), your `moduleName` must consist of characters allowed in filenames. That means you should not copy scoped package names, such as `@my-org/my-package`, directly into `moduleName`.\n\n**[`cosmiconfigOptions`] are documented below.**\nYou may not need them, and should first read about the functions you'll use.\n\n### explorer.search()\n\n```js\nexplorer.search([searchFrom]).then(result => { /* ... */ })\n```\n\nSearches for a configuration file. Returns a Promise that resolves with a [result] or with `null`, if no configuration file is found.\n\nYou can do the same thing synchronously with [`explorerSync.search()`].\n\nLet's say your module name is `goldengrahams` so you initialized with `const explorer = cosmiconfig('goldengrahams');`.\nHere's how your default [`search()`] will work:\n\n- Starting from `process.cwd()` (or some other directory defined by the `searchFrom` argument to [`search()`]), look for configuration objects in the following places:\n  1. A `goldengrahams` property in a `package.json` file.\n  2. A `.goldengrahamsrc` file with JSON or YAML syntax.\n  3. A `.goldengrahamsrc.json`, `.goldengrahamsrc.yaml`, `.goldengrahamsrc.yml`, `.goldengrahamsrc.js`, `.goldengrahamsrc.ts`, `.goldengrahamsrc.mjs`, or `.goldengrahamsrc.cjs` file. (To learn more about how JS files are loaded, see [\"Loading JS modules\"].)\n  4. A `goldengrahamsrc`, `goldengrahamsrc.json`, `goldengrahamsrc.yaml`, `goldengrahamsrc.yml`, `goldengrahamsrc.js`, `goldengrahamsrc.ts`, `goldengrahamsrc.mjs`, or `goldengrahamsrc.cjs` file in the `.config` subdirectory.\n  5. A `goldengrahams.config.js`, `goldengrahams.config.ts`, `goldengrahams.config.mjs`, or `goldengrahams.config.cjs` file. (To learn more about how JS files are loaded, see [\"Loading JS modules\"].)\n- If none of those searches reveal a configuration object, continue depending on the current search strategy:\n  - If it's `none` (which is the default if you don't specify a [`stopDir`] option), stop here and return/resolve with `null`.\n  - If it's `global` (which is the default if you specify a [`stopDir`] option), move up one directory level and try again,\n    recursing until arriving at the configured [`stopDir`] option, which defaults to the user's home directory.\n    - After arriving at the [`stopDir`], the global configuration directory (as defined by [`env-paths`] without prefix) is also checked,\n      looking at the files `config`, `config.json`, `config.yaml`, `config.yml`, `config.js`, `config.ts`, `config.cjs`, and `config.mjs`\n      in the directory `~/.config/goldengrahams/` (on Linux; see [`env-paths`] documentation for other OSs).\n  - If it's `project`, check whether a `package.json` file is present in the current directory, and if not,\n    move up one directory level and try again, recursing until there is one.\n- If at any point a parsable configuration is found, the [`search()`] Promise resolves with its [result] \\(or, with [`explorerSync.search()`], the [result] is returned).\n- If no configuration object is found, the [`search()`] Promise resolves with `null` (or, with [`explorerSync.search()`], `null` is returned).\n- If a configuration object is found *but is malformed* (causing a parsing error), the [`search()`] Promise rejects with that error (so you should `.catch()` it). (Or, with [`explorerSync.search()`], the error is thrown.)\n\n**If you know exactly where your configuration file should be, you can use [`load()`], instead.**\n\n**The search process is highly customizable.**\nUse the cosmiconfig options [`searchPlaces`] and [`loaders`] to precisely define where you want to look for configurations and how you want to load them.\n\n#### searchFrom\n\nType: `string`.\nDefault: `process.cwd()`.\n\nA filename.\n[`search()`] will start its search here.\n\nIf the value is a directory, that's where the search starts.\nIf it's a file, the search starts in that file's directory.\n\n### explorer.load()\n\n```js\nexplorer.load(loadPath).then(result => { /* ... */ })\n```\n\nLoads a configuration file. Returns a Promise that resolves with a [result] or rejects with an error (if the file does not exist or cannot be loaded).\n\nUse `load` if you already know where the configuration file is and you just need to load it.\n\n```js\nexplorer.load('load/this/file.json'); // Tries to load load/this/file.json.\n```\n\nIf you load a `package.json` file, the result will be derived from whatever property is specified as your [`packageProp`].\n\nYou can do the same thing synchronously with [`explorerSync.load()`].\n\n### explorer.clearLoadCache()\n\nClears the cache used in [`load()`].\n\n### explorer.clearSearchCache()\n\nClears the cache used in [`search()`].\n\n### explorer.clearCaches()\n\nPerforms both [`clearLoadCache()`] and [`clearSearchCache()`].\n\n## Synchronous API\n\n### cosmiconfigSync()\n\n```js\nconst { cosmiconfigSync } = require('cosmiconfig');\nconst explorerSync = cosmiconfigSync(moduleName, /* optional */ cosmiconfigOptions)\n```\n\nCreates a *synchronous* cosmiconfig instance (\"explorerSync\") configured according to the arguments, and initializes its caches.\n\nSee [`cosmiconfig()`](#cosmiconfig-1).\n\n### explorerSync.search()\n\n```js\nconst result = explorerSync.search([searchFrom]);\n```\n\nSynchronous version of [`explorer.search()`].\n\nReturns a [result] or `null`.\n\n### explorerSync.load()\n\n```js\nconst result = explorerSync.load(loadPath);\n```\n\nSynchronous version of [`explorer.load()`].\n\nReturns a [result].\n\n### explorerSync.clearLoadCache()\n\nClears the cache used in [`load()`].\n\n### explorerSync.clearSearchCache()\n\nClears the cache used in [`search()`].\n\n### explorerSync.clearCaches()\n\nPerforms both [`clearLoadCache()`] and [`clearSearchCache()`].\n\n## cosmiconfigOptions\n\nType: `Object`.\n\nPossible options are documented below.\n\n### searchStrategy\n\nType: `string`\nDefault: `global` if [`stopDir`] is specified, `none` otherwise.\n\nThe strategy that should be used to determine which directories to check for configuration files.\n\n- `none`: Only checks in the current working directory.\n- `project`: Starts in the current working directory, traversing upwards until a `package.json` file is found.\n- `global`: Starts in the current working directory, traversing upwards until the configured [`stopDir`]\n  (or the current user's home directory if none is given). Then, if no configuration is found, also look in the\n  operating system's default configuration directory (according to [`env-paths`] without prefix),\n  where a different set of file names is checked:\n\n```js\n[\n  `config`,\n  `config.json`,\n  `config.yaml`,\n  `config.yml`,\n  `config.js`,\n  `config.ts`,\n  `config.cjs`,\n  `config.mjs`\n]\n```\n\n### searchPlaces\n\nType: `Array<string>`.\nDefault: See below.\n\nAn array of places that [`search()`] will check in each directory as it moves up the directory tree.\nEach place is relative to the directory being searched, and the places are checked in the specified order.\n\n**Default `searchPlaces`:**\n\nFor the [asynchronous API](#asynchronous-api), these are the default `searchPlaces`:\n\n```js\n[\n  'package.json',\n  `.${moduleName}rc`,\n  `.${moduleName}rc.json`,\n  `.${moduleName}rc.yaml`,\n  `.${moduleName}rc.yml`,\n  `.${moduleName}rc.js`,\n  `.${moduleName}rc.ts`,\n  `.${moduleName}rc.mjs`,\n  `.${moduleName}rc.cjs`,\n  `.config/${moduleName}rc`,\n  `.config/${moduleName}rc.json`,\n  `.config/${moduleName}rc.yaml`,\n  `.config/${moduleName}rc.yml`,\n  `.config/${moduleName}rc.js`,\n  `.config/${moduleName}rc.ts`,\n  `.config/${moduleName}rc.mjs`,\n  `.config/${moduleName}rc.cjs`,\n  `${moduleName}.config.js`,\n  `${moduleName}.config.ts`,\n  `${moduleName}.config.mjs`,\n  `${moduleName}.config.cjs`,\n];\n```\n\nFor the [synchronous API](#synchronous-api), the only difference is that `.mjs` files are not included. See [\"Loading JS modules\"] for more information.\n\nCreate your own array to search more, fewer, or altogether different places.\n\nEvery item in `searchPlaces` needs to have a loader in [`loaders`] that corresponds to its extension.\n(Common extensions are covered by default loaders.)\nRead more about [`loaders`] below.\n\n`package.json` is a special value: When it is included in `searchPlaces`, Cosmiconfig will always parse it as JSON and load a property within it, not the whole file.\nThat property is defined with the [`packageProp`] option, and defaults to your module name.\n\nExamples, with a module named `porgy`:\n\n```js\n// Disallow extensions on rc files:\n['package.json', '.porgyrc', 'porgy.config.js']\n```\n\n```js\n// Limit the options dramatically:\n['package.json', '.porgyrc']\n```\n\n```js\n// Maybe you want to look for a wide variety of JS flavors:\n[\n  'porgy.config.js',\n  'porgy.config.mjs',\n  'porgy.config.ts',\n  'porgy.config.coffee'\n]\n// ^^ You will need to designate a custom loader to tell\n// Cosmiconfig how to handle `.coffee` files.\n```\n\n```js\n// Look within a .config/ subdirectory of every searched directory:\n[\n  'package.json',\n  '.porgyrc',\n  '.config/.porgyrc',\n  '.porgyrc.json',\n  '.config/.porgyrc.json'\n]\n```\n\n### loaders\n\nType: `Object`.\nDefault: See below.\n\nAn object that maps extensions to the loader functions responsible for loading and parsing files with those extensions.\n\nCosmiconfig exposes its default loaders on the named export `defaultLoaders` and `defaultLoadersSync`.\n\n**Default `loaders`:**\n\n```js\nconst { defaultLoaders, defaultLoadersSync } = require('cosmiconfig');\n\nconsole.log(Object.entries(defaultLoaders));\n// [\n//   [ '.mjs', [Function: loadJs] ],\n//   [ '.cjs', [Function: loadJs] ],\n//   [ '.js', [Function: loadJs] ],\n//   [ '.ts', [Function: loadTs] ],\n//   [ '.json', [Function: loadJson] ],\n//   [ '.yaml', [Function: loadYaml] ],\n//   [ '.yml', [Function: loadYaml] ],\n//   [ 'noExt', [Function: loadYaml] ]\n// ]\n\nconsole.log(Object.entries(defaultLoadersSync));\n// [\n//   [ '.cjs', [Function: loadJsSync] ],\n//   [ '.js', [Function: loadJsSync] ],\n//   [ '.ts', [Function: loadTsSync] ],\n//   [ '.json', [Function: loadJson] ],\n//   [ '.yaml', [Function: loadYaml] ],\n//   [ '.yml', [Function: loadYaml] ],\n//   [ 'noExt', [Function: loadYaml] ]\n// ]\n```\n\n(YAML is a superset of JSON; which means YAML parsers can parse JSON; which is how extensionless files can be either YAML *or* JSON with only one parser.)\n\n**If you provide a `loaders` object, your object will be *merged* with the defaults.**\nSo you can override one or two without having to override them all.\n\n**Keys in `loaders`** are extensions (starting with a period), or `noExt` to specify the loader for files *without* extensions, like `.myapprc`.\n\n**Values in `loaders`** are a loader function (described below) whose values are loader functions.\n\n**The most common use case for custom loaders value is to load extensionless `rc` files as strict JSON**, instead of JSON *or* YAML (the default).\nTo accomplish that, provide the following `loaders` value:\n\n```js\n{\n  noExt: defaultLoaders['.json'];\n}\n```\n\nIf you want to load files that are not handled by the loader functions Cosmiconfig exposes, you can write a custom loader function or use one from NPM if it exists.\n\n**Use cases for custom loader function:**\n\n- Allow configuration syntaxes that aren't handled by Cosmiconfig's defaults, like JSON5, INI, or XML.\n- Parse JS files with Babel before deriving the configuration.\n\n**Custom loader functions** have the following signature:\n\n```ts\n// Sync\ntype SyncLoader = (filepath: string, content: string) => Object | null\n\n// Async\ntype AsyncLoader = (filepath: string, content: string) => Object | null | Promise<Object | null>\n```\n\nCosmiconfig reads the file when it checks whether the file exists, so it will provide you with both the file's path and its content.\nDo whatever you need to, and return either a configuration object or `null` (or, for async-only loaders, a Promise that resolves with one of those).\n`null` indicates that no real configuration was found and the search should continue.\n\nA few things to note:\n\n- If you use a custom loader, be aware of whether it's sync or async: you cannot use async customer loaders with the sync API ([`cosmiconfigSync()`]).\n- **Special JS syntax can also be handled by using a `require` hook**, because `defaultLoaders['.js']` just uses `require`.\n  Whether you use custom loaders or a `require` hook is up to you.\n\nExamples:\n\n```js\n// Allow JSON5 syntax:\ncosmiconfig('foo', {\n  loaders: {\n    '.json': json5Loader\n  }\n});\n\n// Allow a special configuration syntax of your own creation:\ncosmiconfig('foo', {\n  loaders: {\n    '.special': specialLoader\n  }\n});\n\n// Allow many flavors of JS, using custom loaders:\ncosmiconfig('foo', {\n  loaders: {\n    '.coffee': coffeeScriptLoader\n  }\n});\n\n// Allow many flavors of JS but rely on require hooks:\ncosmiconfig('foo', {\n  loaders: {\n    '.coffee': defaultLoaders['.js']\n  }\n});\n```\n\n### packageProp\n\nType: `string | Array<string>`.\nDefault: `` `${moduleName}` ``.\n\nName of the property in `package.json` to look for.\n\nUse a period-delimited string or an array of strings to describe a path to nested properties.\n\nFor example, the value `'configs.myPackage'` or `['configs', 'myPackage']` will get you the `\"myPackage\"` value in a `package.json` like this:\n\n```json\n{\n  \"configs\": {\n    \"myPackage\": {\"option\":  \"value\"}\n  }\n}\n```\n\nIf nested property names within the path include periods, you need to use an array of strings. For example, the value `['configs', 'foo.bar', 'baz']` will get you the `\"baz\"` value in a `package.json` like this:\n\n```json\n{\n  \"configs\": {\n    \"foo.bar\": {\n      \"baz\": {\"option\":  \"value\"}\n    }\n  }\n}\n```\n\nIf a string includes period but corresponds to a top-level property name, it will not be interpreted as a period-delimited path. For example, the value `'one.two'` will get you the `\"three\"` value in a `package.json` like this:\n\n```json\n{\n  \"one.two\": \"three\",\n  \"one\": {\n    \"two\": \"four\"\n  }\n}\n```\n\n### stopDir\n\nType: `string`.\nDefault: Absolute path to your home directory.\n\nDirectory where the search will stop.\n\n### cache\n\nType: `boolean`.\nDefault: `true`.\n\nIf `false`, no caches will be used.\nRead more about [\"Caching\"](#caching) below.\n\n### transform\n\nType: `(Result) => Promise<Result> | Result`.\n\nA function that transforms the parsed configuration. Receives the [result].\n\nIf using [`search()`] or [`load()`] \\(which are async), the transform function can return the transformed result or return a Promise that resolves with the transformed result.\nIf using `cosmiconfigSync`, [`search()`] or [`load()`], the function must be synchronous and return the transformed result.\n\nThe reason you might use this option — instead of simply applying your transform function some other way — is that *the transformed result will be cached*. If your transformation involves additional filesystem I/O or other potentially slow processing, you can use this option to avoid repeating those steps every time a given configuration is searched or loaded.\n\n### ignoreEmptySearchPlaces\n\nType: `boolean`.\nDefault: `true`.\n\nBy default, if [`search()`] encounters an empty file (containing nothing but whitespace) in one of the [`searchPlaces`], it will ignore the empty file and move on.\nIf you'd like to load empty configuration files, instead, set this option to `false`.\n\nWhy might you want to load empty configuration files?\nIf you want to throw an error, or if an empty configuration file means something to your program.\n\n## Loading JS modules\n\nYour end users can provide JS configuration files as ECMAScript modules (ESM) under the following conditions:\n\n- You (the cosmiconfig user) use cosmiconfig's [asynchronous API](#asynchronous-api).\n- Your end user runs a version of Node that supports ESM ([>=12.17.0](https://nodejs.org/en/blog/release/v12.17.0/), or earlier with the `--experimental-modules` flag).\n- Your end user provides an `.mjs` configuration file, or a `.js` file whose nearest parent `package.json` file contains `\"type\": \"module\"`. (See [Node's method for determining a file's module system](https://nodejs.org/api/packages.html#packages_determining_module_system).)\n\nWith cosmiconfig's [asynchronous API](#asynchronous-api), the default [`searchPlaces`] include `.js`, `.ts`, `.mjs`, and `.cjs` files. Cosmiconfig loads all these file types with the [dynamic `import` function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/import#dynamic_imports).\n\nWith the [synchronous API](#synchronous-api), JS configuration files are always treated as CommonJS, and `.mjs` files are ignored, because there is no synchronous API for the dynamic `import` function.\n\n## Caching\n\nAs of v2, cosmiconfig uses caching to reduce the need for repetitious reading of the filesystem or expensive transforms. Every new cosmiconfig instance (created with `cosmiconfig()`) has its own caches.\n\nTo avoid or work around caching, you can do the following:\n\n- Set the `cosmiconfig` option [`cache`] to `false`.\n- Use the cache-clearing methods [`clearLoadCache()`], [`clearSearchCache()`], and [`clearCaches()`].\n- Create separate instances of cosmiconfig (separate \"explorers\").\n\n## Differences from [rc](https://github.com/dominictarr/rc)\n\n[rc](https://github.com/dominictarr/rc) serves its focused purpose well. cosmiconfig differs in a few key ways — making it more useful for some projects, less useful for others:\n\n- Looks for configuration in some different places: in a `package.json` property, an rc file, a `.config.js` file, and rc files with extensions.\n- Built-in support for JSON, YAML, and CommonJS formats.\n- Stops at the first configuration found, instead of finding all that can be found up the directory tree and merging them automatically.\n- Options.\n- Asynchronous by default (though can be run synchronously).\n\n## Usage for end users\n\nWhen configuring a tool, you can use multiple file formats and put these in multiple places.\n\nUsually, a tool would mention this in its own README file,\nbut by default, these are the following places, where `{NAME}` represents the name of the tool:\n\n```\npackage.json\n.{NAME}rc\n.{NAME}rc.json\n.{NAME}rc.yaml\n.{NAME}rc.yml\n.{NAME}rc.js\n.{NAME}rc.ts\n.{NAME}rc.cjs\n.config/{NAME}rc\n.config/{NAME}rc.json\n.config/{NAME}rc.yaml\n.config/{NAME}rc.yml\n.config/{NAME}rc.js\n.config/{NAME}rc.ts\n.config/{NAME}rc.mjs\n.config/{NAME}rc.cjs\n{NAME}.config.js\n{NAME}.config.ts\n{NAME}.config.mjs\n{NAME}.config.cjs\n```\n\nThe contents of these files are defined by the tool.\nFor example, you can configure prettier to enforce semicolons at the end of the line\nusing a file named `.config/prettierrc.yml`:\n\n```yaml\nsemi: true\n```\n\nAdditionally, you have the option to put a property named after the tool in your `package.json` file,\nwith the contents of that property being the same as the file contents. To use the same example as above:\n\n```json\n{\n  \"name\": \"your-project\",\n  \"dependencies\": {},\n  \"prettier\": {\n    \"semi\": true\n  }\n}\n```\n\nThis has the advantage that you can put the configuration of all tools\n(at least the ones that use cosmiconfig) in one file.\n\nYou can also add a `cosmiconfig` key within your `package.json` file or create one of the following files\nto configure `cosmiconfig` itself:\n\n```\n.config/config.json\n.config/config.yaml\n.config/config.yml\n.config/config.js\n.config/config.ts\n.config/config.cjs\n```\n\nThe following properties are currently actively supported in these places:\n\n```yaml\ncosmiconfig:\n  # adds places where configuration files are being searched\n  searchPlaces:\n    - .config/{name}.yml\n  # to enforce a custom naming convention and format, don't merge the above with the tool-defined search places\n  # (`true` is the default setting)\n  mergeSearchPlaces: false\n```\n\n> **Note:** technically, you can overwrite all options described in [cosmiconfigOptions](#cosmiconfigoptions) here,\n> but everything not listed above should be used at your own risk, as it has not been tested explicitly.\n> The only exceptions to this are the `loaders` property, which is explicitly not supported at this time,\n> and the `searchStrategy` property, which is intentionally disallowed.\n\nYou can also add more root properties outside the `cosmiconfig` property\nto configure your tools, entirely eliminating the need to look for additional configuration files:\n\n```yaml\ncosmiconfig:\n  searchPlaces: []\n\nprettier:\n  semi: true\n```\n\n### Imports\n\nWherever you put your configuration (the package.json file, a root config file or a package-specific config file),\nyou can use the special `$import` key to import another file as a base.\n\nFor example, you can import from an npm package (in this example, `@foocorp/config`).\n\n`.prettierrc.base.yml` in said npm package could define some company-wide defaults:\n\n```yaml\nprintWidth: 120\nsemi: true\ntabWidth: 2\n```\n\nAnd then, the `.prettierrc.yml` file in the project itself would just reference that file,\noptionally overriding the defaults with project-specific settings:\n\n```yaml\n$import: node_modules/@foocorp/config/.prettierrc.base.yml\n# we want more space!\nprintWidth: 200\n```\n\nIt is possible to import multiple base files by specifying an array of paths,\nwhich will be processed in declaration order;\nthat means that the last entry will win if there are conflicting properties.\n\nIt is also possible to import file formats other than the importing format\nas long as they are supported by the loaders specified by the developer of the tool you're configuring. \n\n```yaml\n$import: [first.yml, second.json, third.config.js]\n```\n\n## Contributing & Development\n\nPlease note that this project is released with a [Contributor Code of Conduct](CODE_OF_CONDUCT.md). By participating in this project you agree to abide by its terms.\n\nAnd please do participate!\n\n[result]: #result\n\n[`load()`]: #explorerload\n\n[`search()`]: #explorersearch\n\n[`clearloadcache()`]: #explorerclearloadcache\n\n[`clearsearchcache()`]: #explorerclearsearchcache\n\n[`cosmiconfig()`]: #cosmiconfig\n\n[`cosmiconfigSync()`]: #cosmiconfigsync\n\n[`clearcaches()`]: #explorerclearcaches\n\n[`packageprop`]: #packageprop\n\n[`cache`]: #cache\n\n[`stopdir`]: #stopdir\n\n[`searchplaces`]: #searchplaces\n\n[`loaders`]: #loaders\n\n[`cosmiconfigoptions`]: #cosmiconfigoptions\n\n[`explorerSync.search()`]: #explorersyncsearch\n\n[`explorerSync.load()`]: #explorersyncload\n\n[`explorer.search()`]: #explorersearch\n\n[`explorer.load()`]: #explorerload\n\n[\"Loading JS modules\"]: #loading-js-modules\n\n[`env-paths`]: https://github.com/sindresorhus/env-paths\n\n[search strategies]: #searchstrategy\n", "readmeFilename": "README.md", "gitHead": "77e5629c77f96a3f6f15a822a2c17188cb440ba2", "_id": "cosmiconfig@9.0.0-alpha.0", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-IldpaaDlbsAXMNmPHxBYh9hcDPGu/LQsuZ9OTNiHOYjBZqkxX+PZciGOt/UqRdtUAKLTRSWf4gg92cJi2Z5Tig==", "shasum": "8c057d43d7164d9ed7f26872cdecd1e133e32873", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0-alpha.0.tgz", "fileCount": 59, "unpackedSize": 119721, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAbYaQHxd8oBMJMAeo3bPXmCZgcRDRWCErxDgG4gYCpDAiBvUL9w3JPDclxhQODtLqKZ1WOOILpiyATbxyTNPZq2ZQ=="}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_9.0.0-alpha.0_1697489525376_0.011878279186915641"}, "_hasShrinkwrap": false}, "9.0.0-alpha.1": {"name": "cosmiconfig", "version": "9.0.0-alpha.1", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "gitHead": "c6378b3b7ad3c6754d6d878c6bacdd9f1a5d642c", "readme": "# cosmiconfig\n\n[![codecov](https://codecov.io/gh/cosmiconfig/cosmiconfig/branch/main/graph/badge.svg)](https://codecov.io/gh/cosmiconfig/cosmiconfig)\n\nCosmiconfig searches for and loads configuration for your program.\n\nIt features smart defaults based on conventional expectations in the JavaScript ecosystem.\nBut it's also flexible enough to search wherever you'd like to search, and load whatever you'd like to load.\n\nBy default, Cosmiconfig will check the current directory for the following:\n\n- a `package.json` property\n- a JSON or YAML, extensionless \"rc file\"\n- an \"rc file\" with the extensions `.json`, `.yaml`, `.yml`, `.js`, `.ts`, `.mjs`, or `.cjs`\n- any of the above two inside a `.config` subdirectory\n- a `.config.js`, `.config.ts`, `.config.mjs`, or `.config.cjs` file\n\nFor example, if your module's name is \"myapp\", cosmiconfig will search up the directory tree for configuration in the following places:\n\n- a `myapp` property in `package.json`\n- a `.myapprc` file in JSON or YAML format\n- a `.myapprc.json`, `.myapprc.yaml`, `.myapprc.yml`, `.myapprc.js`, `.myapprc.ts`, `.myapprc.mjs`, or `.myapprc.cjs` file\n- a `myapprc`, `myapprc.json`, `myapprc.yaml`, `myapprc.yml`, `myapprc.js`, `myapprc.ts`, `myapprc.mjs`, or `myapprc.cjs` file inside a `.config` subdirectory\n- a `myapp.config.js`, `myapp.config.ts`, `myapp.config.mjs`, or `myapp.config.cjs` file\n\nOptionally, you can tell it to search up the directory tree using [search strategies],\nchecking each of these places in each directory, until it finds some acceptable configuration (or hits the home directory).\n\n## Table of contents\n\n- [Installation](#installation)\n- [Usage for tooling developers](#usage-for-tooling-developers)\n- [Result](#result)\n- [Asynchronous API](#asynchronous-api)\n  - [cosmiconfig()](#cosmiconfig-1)\n  - [explorer.search()](#explorersearch)\n  - [explorer.load()](#explorerload)\n  - [explorer.clearLoadCache()](#explorerclearloadcache)\n  - [explorer.clearSearchCache()](#explorerclearsearchcache)\n  - [explorer.clearCaches()](#explorerclearcaches)\n- [Synchronous API](#synchronous-api)\n  - [cosmiconfigSync()](#cosmiconfigsync)\n  - [explorerSync.search()](#explorersyncsearch)\n  - [explorerSync.load()](#explorersyncload)\n  - [explorerSync.clearLoadCache()](#explorersyncclearloadcache)\n  - [explorerSync.clearSearchCache()](#explorersyncclearsearchcache)\n  - [explorerSync.clearCaches()](#explorersyncclearcaches)\n- [cosmiconfigOptions](#cosmiconfigoptions)\n  - [searchStrategy](#searchstrategy)\n  - [searchPlaces](#searchplaces)\n  - [loaders](#loaders)\n  - [packageProp](#packageprop)\n  - [stopDir](#stopdir)\n  - [cache](#cache)\n  - [transform](#transform)\n  - [ignoreEmptySearchPlaces](#ignoreemptysearchplaces)\n- [Loading JS modules](#loading-js-modules)\n- [Caching](#caching)\n- [Differences from rc](#differences-from-rc)\n- [Usage for end users](#usage-for-end-users)\n  - [Imports](#imports)\n- [Contributing & Development](#contributing--development)\n\n## Installation\n\n```\nnpm install cosmiconfig\n```\n\nTested in Node 14+.\n\n## Usage for tooling developers\n\n*If you are an end user (i.e. a user of a tool that uses cosmiconfig, like `prettier` or `stylelint`),\nyou can skip down to [the end user section](#usage-for-end-users).*\n\nCreate a Cosmiconfig explorer, then either `search` for or directly `load` a configuration file.\n\n```js\nconst { cosmiconfig, cosmiconfigSync } = require('cosmiconfig');\n// ...\nconst explorer = cosmiconfig(moduleName);\n\n// Search for a configuration by walking up directories.\n// See documentation for search, below.\nexplorer.search()\n  .then((result) => {\n    // result.config is the parsed configuration object.\n    // result.filepath is the path to the config file that was found.\n    // result.isEmpty is true if there was nothing to parse in the config file.\n  })\n  .catch((error) => {\n    // Do something constructive.\n  });\n\n// Load a configuration directly when you know where it should be.\n// The result object is the same as for search.\n// See documentation for load, below.\nexplorer.load(pathToConfig).then(/* ... */);\n\n// You can also search and load synchronously.\nconst explorerSync = cosmiconfigSync(moduleName);\n\nconst searchedFor = explorerSync.search();\nconst loaded = explorerSync.load(pathToConfig);\n```\n\n## Result\n\nThe result object you get from `search` or `load` has the following properties:\n\n- **config:** The parsed configuration object. `undefined` if the file is empty.\n- **filepath:** The path to the configuration file that was found.\n- **isEmpty:** `true` if the configuration file is empty. This property will not be present if the configuration file is not empty.\n\n## Asynchronous API\n\n### cosmiconfig()\n\n```js\nconst { cosmiconfig } = require('cosmiconfig');\nconst explorer = cosmiconfig(moduleName, /* optional */ cosmiconfigOptions)\n```\n\nCreates a cosmiconfig instance (\"explorer\") configured according to the arguments, and initializes its caches.\n\n#### moduleName\n\nType: `string`. **Required.**\n\nYour module name. This is used to create the default [`searchPlaces`] and [`packageProp`].\n\nIf your [`searchPlaces`] value will include files, as it does by default (e.g. `${moduleName}rc`), your `moduleName` must consist of characters allowed in filenames. That means you should not copy scoped package names, such as `@my-org/my-package`, directly into `moduleName`.\n\n**[`cosmiconfigOptions`] are documented below.**\nYou may not need them, and should first read about the functions you'll use.\n\n### explorer.search()\n\n```js\nexplorer.search([searchFrom]).then(result => { /* ... */ })\n```\n\nSearches for a configuration file. Returns a Promise that resolves with a [result] or with `null`, if no configuration file is found.\n\nYou can do the same thing synchronously with [`explorerSync.search()`].\n\nLet's say your module name is `goldengrahams` so you initialized with `const explorer = cosmiconfig('goldengrahams');`.\nHere's how your default [`search()`] will work:\n\n- Starting from `process.cwd()` (or some other directory defined by the `searchFrom` argument to [`search()`]), look for configuration objects in the following places:\n  1. A `goldengrahams` property in a `package.json` file.\n  2. A `.goldengrahamsrc` file with JSON or YAML syntax.\n  3. A `.goldengrahamsrc.json`, `.goldengrahamsrc.yaml`, `.goldengrahamsrc.yml`, `.goldengrahamsrc.js`, `.goldengrahamsrc.ts`, `.goldengrahamsrc.mjs`, or `.goldengrahamsrc.cjs` file. (To learn more about how JS files are loaded, see [\"Loading JS modules\"].)\n  4. A `goldengrahamsrc`, `goldengrahamsrc.json`, `goldengrahamsrc.yaml`, `goldengrahamsrc.yml`, `goldengrahamsrc.js`, `goldengrahamsrc.ts`, `goldengrahamsrc.mjs`, or `goldengrahamsrc.cjs` file in the `.config` subdirectory.\n  5. A `goldengrahams.config.js`, `goldengrahams.config.ts`, `goldengrahams.config.mjs`, or `goldengrahams.config.cjs` file. (To learn more about how JS files are loaded, see [\"Loading JS modules\"].)\n- If none of those searches reveal a configuration object, continue depending on the current search strategy:\n  - If it's `none` (which is the default if you don't specify a [`stopDir`] option), stop here and return/resolve with `null`.\n  - If it's `global` (which is the default if you specify a [`stopDir`] option), move up one directory level and try again,\n    recursing until arriving at the configured [`stopDir`] option, which defaults to the user's home directory.\n    - After arriving at the [`stopDir`], the global configuration directory (as defined by [`env-paths`] without prefix) is also checked,\n      looking at the files `config`, `config.json`, `config.yaml`, `config.yml`, `config.js`, `config.ts`, `config.cjs`, and `config.mjs`\n      in the directory `~/.config/goldengrahams/` (on Linux; see [`env-paths`] documentation for other OSs).\n  - If it's `project`, check whether a `package.json` file is present in the current directory, and if not,\n    move up one directory level and try again, recursing until there is one.\n- If at any point a parsable configuration is found, the [`search()`] Promise resolves with its [result] \\(or, with [`explorerSync.search()`], the [result] is returned).\n- If no configuration object is found, the [`search()`] Promise resolves with `null` (or, with [`explorerSync.search()`], `null` is returned).\n- If a configuration object is found *but is malformed* (causing a parsing error), the [`search()`] Promise rejects with that error (so you should `.catch()` it). (Or, with [`explorerSync.search()`], the error is thrown.)\n\n**If you know exactly where your configuration file should be, you can use [`load()`], instead.**\n\n**The search process is highly customizable.**\nUse the cosmiconfig options [`searchPlaces`] and [`loaders`] to precisely define where you want to look for configurations and how you want to load them.\n\n#### searchFrom\n\nType: `string`.\nDefault: `process.cwd()`.\n\nA filename.\n[`search()`] will start its search here.\n\nIf the value is a directory, that's where the search starts.\nIf it's a file, the search starts in that file's directory.\n\n### explorer.load()\n\n```js\nexplorer.load(loadPath).then(result => { /* ... */ })\n```\n\nLoads a configuration file. Returns a Promise that resolves with a [result] or rejects with an error (if the file does not exist or cannot be loaded).\n\nUse `load` if you already know where the configuration file is and you just need to load it.\n\n```js\nexplorer.load('load/this/file.json'); // Tries to load load/this/file.json.\n```\n\nIf you load a `package.json` file, the result will be derived from whatever property is specified as your [`packageProp`].\n\nYou can do the same thing synchronously with [`explorerSync.load()`].\n\n### explorer.clearLoadCache()\n\nClears the cache used in [`load()`].\n\n### explorer.clearSearchCache()\n\nClears the cache used in [`search()`].\n\n### explorer.clearCaches()\n\nPerforms both [`clearLoadCache()`] and [`clearSearchCache()`].\n\n## Synchronous API\n\n### cosmiconfigSync()\n\n```js\nconst { cosmiconfigSync } = require('cosmiconfig');\nconst explorerSync = cosmiconfigSync(moduleName, /* optional */ cosmiconfigOptions)\n```\n\nCreates a *synchronous* cosmiconfig instance (\"explorerSync\") configured according to the arguments, and initializes its caches.\n\nSee [`cosmiconfig()`](#cosmiconfig-1).\n\n### explorerSync.search()\n\n```js\nconst result = explorerSync.search([searchFrom]);\n```\n\nSynchronous version of [`explorer.search()`].\n\nReturns a [result] or `null`.\n\n### explorerSync.load()\n\n```js\nconst result = explorerSync.load(loadPath);\n```\n\nSynchronous version of [`explorer.load()`].\n\nReturns a [result].\n\n### explorerSync.clearLoadCache()\n\nClears the cache used in [`load()`].\n\n### explorerSync.clearSearchCache()\n\nClears the cache used in [`search()`].\n\n### explorerSync.clearCaches()\n\nPerforms both [`clearLoadCache()`] and [`clearSearchCache()`].\n\n## cosmiconfigOptions\n\nType: `Object`.\n\nPossible options are documented below.\n\n### searchStrategy\n\nType: `string`\nDefault: `global` if [`stopDir`] is specified, `none` otherwise.\n\nThe strategy that should be used to determine which directories to check for configuration files.\n\n- `none`: Only checks in the current working directory.\n- `project`: Starts in the current working directory, traversing upwards until a `package.json` file is found.\n- `global`: Starts in the current working directory, traversing upwards until the configured [`stopDir`]\n  (or the current user's home directory if none is given). Then, if no configuration is found, also look in the\n  operating system's default configuration directory (according to [`env-paths`] without prefix),\n  where a different set of file names is checked:\n\n```js\n[\n  `config`,\n  `config.json`,\n  `config.yaml`,\n  `config.yml`,\n  `config.js`,\n  `config.ts`,\n  `config.cjs`,\n  `config.mjs`\n]\n```\n\n### searchPlaces\n\nType: `Array<string>`.\nDefault: See below.\n\nAn array of places that [`search()`] will check in each directory as it moves up the directory tree.\nEach place is relative to the directory being searched, and the places are checked in the specified order.\n\n**Default `searchPlaces`:**\n\nFor the [asynchronous API](#asynchronous-api), these are the default `searchPlaces`:\n\n```js\n[\n  'package.json',\n  `.${moduleName}rc`,\n  `.${moduleName}rc.json`,\n  `.${moduleName}rc.yaml`,\n  `.${moduleName}rc.yml`,\n  `.${moduleName}rc.js`,\n  `.${moduleName}rc.ts`,\n  `.${moduleName}rc.mjs`,\n  `.${moduleName}rc.cjs`,\n  `.config/${moduleName}rc`,\n  `.config/${moduleName}rc.json`,\n  `.config/${moduleName}rc.yaml`,\n  `.config/${moduleName}rc.yml`,\n  `.config/${moduleName}rc.js`,\n  `.config/${moduleName}rc.ts`,\n  `.config/${moduleName}rc.mjs`,\n  `.config/${moduleName}rc.cjs`,\n  `${moduleName}.config.js`,\n  `${moduleName}.config.ts`,\n  `${moduleName}.config.mjs`,\n  `${moduleName}.config.cjs`,\n];\n```\n\nFor the [synchronous API](#synchronous-api), the only difference is that `.mjs` files are not included. See [\"Loading JS modules\"] for more information.\n\nCreate your own array to search more, fewer, or altogether different places.\n\nEvery item in `searchPlaces` needs to have a loader in [`loaders`] that corresponds to its extension.\n(Common extensions are covered by default loaders.)\nRead more about [`loaders`] below.\n\n`package.json` is a special value: When it is included in `searchPlaces`, Cosmiconfig will always parse it as JSON and load a property within it, not the whole file.\nThat property is defined with the [`packageProp`] option, and defaults to your module name.\n\nExamples, with a module named `porgy`:\n\n```js\n// Disallow extensions on rc files:\n['package.json', '.porgyrc', 'porgy.config.js']\n```\n\n```js\n// Limit the options dramatically:\n['package.json', '.porgyrc']\n```\n\n```js\n// Maybe you want to look for a wide variety of JS flavors:\n[\n  'porgy.config.js',\n  'porgy.config.mjs',\n  'porgy.config.ts',\n  'porgy.config.coffee'\n]\n// ^^ You will need to designate a custom loader to tell\n// Cosmiconfig how to handle `.coffee` files.\n```\n\n```js\n// Look within a .config/ subdirectory of every searched directory:\n[\n  'package.json',\n  '.porgyrc',\n  '.config/.porgyrc',\n  '.porgyrc.json',\n  '.config/.porgyrc.json'\n]\n```\n\n### loaders\n\nType: `Object`.\nDefault: See below.\n\nAn object that maps extensions to the loader functions responsible for loading and parsing files with those extensions.\n\nCosmiconfig exposes its default loaders on the named export `defaultLoaders` and `defaultLoadersSync`.\n\n**Default `loaders`:**\n\n```js\nconst { defaultLoaders, defaultLoadersSync } = require('cosmiconfig');\n\nconsole.log(Object.entries(defaultLoaders));\n// [\n//   [ '.mjs', [Function: loadJs] ],\n//   [ '.cjs', [Function: loadJs] ],\n//   [ '.js', [Function: loadJs] ],\n//   [ '.ts', [Function: loadTs] ],\n//   [ '.json', [Function: loadJson] ],\n//   [ '.yaml', [Function: loadYaml] ],\n//   [ '.yml', [Function: loadYaml] ],\n//   [ 'noExt', [Function: loadYaml] ]\n// ]\n\nconsole.log(Object.entries(defaultLoadersSync));\n// [\n//   [ '.cjs', [Function: loadJsSync] ],\n//   [ '.js', [Function: loadJsSync] ],\n//   [ '.ts', [Function: loadTsSync] ],\n//   [ '.json', [Function: loadJson] ],\n//   [ '.yaml', [Function: loadYaml] ],\n//   [ '.yml', [Function: loadYaml] ],\n//   [ 'noExt', [Function: loadYaml] ]\n// ]\n```\n\n(YAML is a superset of JSON; which means YAML parsers can parse JSON; which is how extensionless files can be either YAML *or* JSON with only one parser.)\n\n**If you provide a `loaders` object, your object will be *merged* with the defaults.**\nSo you can override one or two without having to override them all.\n\n**Keys in `loaders`** are extensions (starting with a period), or `noExt` to specify the loader for files *without* extensions, like `.myapprc`.\n\n**Values in `loaders`** are a loader function (described below) whose values are loader functions.\n\n**The most common use case for custom loaders value is to load extensionless `rc` files as strict JSON**, instead of JSON *or* YAML (the default).\nTo accomplish that, provide the following `loaders` value:\n\n```js\n{\n  noExt: defaultLoaders['.json'];\n}\n```\n\nIf you want to load files that are not handled by the loader functions Cosmiconfig exposes, you can write a custom loader function or use one from NPM if it exists.\n\n**Use cases for custom loader function:**\n\n- Allow configuration syntaxes that aren't handled by Cosmiconfig's defaults, like JSON5, INI, or XML.\n- Parse JS files with Babel before deriving the configuration.\n\n**Custom loader functions** have the following signature:\n\n```ts\n// Sync\ntype SyncLoader = (filepath: string, content: string) => Object | null\n\n// Async\ntype AsyncLoader = (filepath: string, content: string) => Object | null | Promise<Object | null>\n```\n\nCosmiconfig reads the file when it checks whether the file exists, so it will provide you with both the file's path and its content.\nDo whatever you need to, and return either a configuration object or `null` (or, for async-only loaders, a Promise that resolves with one of those).\n`null` indicates that no real configuration was found and the search should continue.\n\nA few things to note:\n\n- If you use a custom loader, be aware of whether it's sync or async: you cannot use async customer loaders with the sync API ([`cosmiconfigSync()`]).\n- **Special JS syntax can also be handled by using a `require` hook**, because `defaultLoaders['.js']` just uses `require`.\n  Whether you use custom loaders or a `require` hook is up to you.\n\nExamples:\n\n```js\n// Allow JSON5 syntax:\ncosmiconfig('foo', {\n  loaders: {\n    '.json': json5Loader\n  }\n});\n\n// Allow a special configuration syntax of your own creation:\ncosmiconfig('foo', {\n  loaders: {\n    '.special': specialLoader\n  }\n});\n\n// Allow many flavors of JS, using custom loaders:\ncosmiconfig('foo', {\n  loaders: {\n    '.coffee': coffeeScriptLoader\n  }\n});\n\n// Allow many flavors of JS but rely on require hooks:\ncosmiconfig('foo', {\n  loaders: {\n    '.coffee': defaultLoaders['.js']\n  }\n});\n```\n\n### packageProp\n\nType: `string | Array<string>`.\nDefault: `` `${moduleName}` ``.\n\nName of the property in `package.json` to look for.\n\nUse a period-delimited string or an array of strings to describe a path to nested properties.\n\nFor example, the value `'configs.myPackage'` or `['configs', 'myPackage']` will get you the `\"myPackage\"` value in a `package.json` like this:\n\n```json\n{\n  \"configs\": {\n    \"myPackage\": {\"option\":  \"value\"}\n  }\n}\n```\n\nIf nested property names within the path include periods, you need to use an array of strings. For example, the value `['configs', 'foo.bar', 'baz']` will get you the `\"baz\"` value in a `package.json` like this:\n\n```json\n{\n  \"configs\": {\n    \"foo.bar\": {\n      \"baz\": {\"option\":  \"value\"}\n    }\n  }\n}\n```\n\nIf a string includes period but corresponds to a top-level property name, it will not be interpreted as a period-delimited path. For example, the value `'one.two'` will get you the `\"three\"` value in a `package.json` like this:\n\n```json\n{\n  \"one.two\": \"three\",\n  \"one\": {\n    \"two\": \"four\"\n  }\n}\n```\n\n### stopDir\n\nType: `string`.\nDefault: Absolute path to your home directory.\n\nDirectory where the search will stop.\n\n### cache\n\nType: `boolean`.\nDefault: `true`.\n\nIf `false`, no caches will be used.\nRead more about [\"Caching\"](#caching) below.\n\n### transform\n\nType: `(Result) => Promise<Result> | Result`.\n\nA function that transforms the parsed configuration. Receives the [result].\n\nIf using [`search()`] or [`load()`] \\(which are async), the transform function can return the transformed result or return a Promise that resolves with the transformed result.\nIf using `cosmiconfigSync`, [`search()`] or [`load()`], the function must be synchronous and return the transformed result.\n\nThe reason you might use this option — instead of simply applying your transform function some other way — is that *the transformed result will be cached*. If your transformation involves additional filesystem I/O or other potentially slow processing, you can use this option to avoid repeating those steps every time a given configuration is searched or loaded.\n\n### ignoreEmptySearchPlaces\n\nType: `boolean`.\nDefault: `true`.\n\nBy default, if [`search()`] encounters an empty file (containing nothing but whitespace) in one of the [`searchPlaces`], it will ignore the empty file and move on.\nIf you'd like to load empty configuration files, instead, set this option to `false`.\n\nWhy might you want to load empty configuration files?\nIf you want to throw an error, or if an empty configuration file means something to your program.\n\n## Loading JS modules\n\nYour end users can provide JS configuration files as ECMAScript modules (ESM) under the following conditions:\n\n- You (the cosmiconfig user) use cosmiconfig's [asynchronous API](#asynchronous-api).\n- Your end user runs a version of Node that supports ESM ([>=12.17.0](https://nodejs.org/en/blog/release/v12.17.0/), or earlier with the `--experimental-modules` flag).\n- Your end user provides an `.mjs` configuration file, or a `.js` file whose nearest parent `package.json` file contains `\"type\": \"module\"`. (See [Node's method for determining a file's module system](https://nodejs.org/api/packages.html#packages_determining_module_system).)\n\nWith cosmiconfig's [asynchronous API](#asynchronous-api), the default [`searchPlaces`] include `.js`, `.ts`, `.mjs`, and `.cjs` files. Cosmiconfig loads all these file types with the [dynamic `import` function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/import#dynamic_imports).\n\nWith the [synchronous API](#synchronous-api), JS configuration files are always treated as CommonJS, and `.mjs` files are ignored, because there is no synchronous API for the dynamic `import` function.\n\n## Caching\n\nAs of v2, cosmiconfig uses caching to reduce the need for repetitious reading of the filesystem or expensive transforms. Every new cosmiconfig instance (created with `cosmiconfig()`) has its own caches.\n\nTo avoid or work around caching, you can do the following:\n\n- Set the `cosmiconfig` option [`cache`] to `false`.\n- Use the cache-clearing methods [`clearLoadCache()`], [`clearSearchCache()`], and [`clearCaches()`].\n- Create separate instances of cosmiconfig (separate \"explorers\").\n\n## Differences from [rc](https://github.com/dominictarr/rc)\n\n[rc](https://github.com/dominictarr/rc) serves its focused purpose well. cosmiconfig differs in a few key ways — making it more useful for some projects, less useful for others:\n\n- Looks for configuration in some different places: in a `package.json` property, an rc file, a `.config.js` file, and rc files with extensions.\n- Built-in support for JSON, YAML, and CommonJS formats.\n- Stops at the first configuration found, instead of finding all that can be found up the directory tree and merging them automatically.\n- Options.\n- Asynchronous by default (though can be run synchronously).\n\n## Usage for end users\n\nWhen configuring a tool, you can use multiple file formats and put these in multiple places.\n\nUsually, a tool would mention this in its own README file,\nbut by default, these are the following places, where `{NAME}` represents the name of the tool:\n\n```\npackage.json\n.{NAME}rc\n.{NAME}rc.json\n.{NAME}rc.yaml\n.{NAME}rc.yml\n.{NAME}rc.js\n.{NAME}rc.ts\n.{NAME}rc.cjs\n.config/{NAME}rc\n.config/{NAME}rc.json\n.config/{NAME}rc.yaml\n.config/{NAME}rc.yml\n.config/{NAME}rc.js\n.config/{NAME}rc.ts\n.config/{NAME}rc.mjs\n.config/{NAME}rc.cjs\n{NAME}.config.js\n{NAME}.config.ts\n{NAME}.config.mjs\n{NAME}.config.cjs\n```\n\nThe contents of these files are defined by the tool.\nFor example, you can configure prettier to enforce semicolons at the end of the line\nusing a file named `.config/prettierrc.yml`:\n\n```yaml\nsemi: true\n```\n\nAdditionally, you have the option to put a property named after the tool in your `package.json` file,\nwith the contents of that property being the same as the file contents. To use the same example as above:\n\n```json\n{\n  \"name\": \"your-project\",\n  \"dependencies\": {},\n  \"prettier\": {\n    \"semi\": true\n  }\n}\n```\n\nThis has the advantage that you can put the configuration of all tools\n(at least the ones that use cosmiconfig) in one file.\n\nYou can also add a `cosmiconfig` key within your `package.json` file or create one of the following files\nto configure `cosmiconfig` itself:\n\n```\n.config/config.json\n.config/config.yaml\n.config/config.yml\n.config/config.js\n.config/config.ts\n.config/config.cjs\n```\n\nThe following properties are currently actively supported in these places:\n\n```yaml\ncosmiconfig:\n  # adds places where configuration files are being searched\n  searchPlaces:\n    - .config/{name}.yml\n  # to enforce a custom naming convention and format, don't merge the above with the tool-defined search places\n  # (`true` is the default setting)\n  mergeSearchPlaces: false\n```\n\n> **Note:** technically, you can overwrite all options described in [cosmiconfigOptions](#cosmiconfigoptions) here,\n> but everything not listed above should be used at your own risk, as it has not been tested explicitly.\n> The only exceptions to this are the `loaders` property, which is explicitly not supported at this time,\n> and the `searchStrategy` property, which is intentionally disallowed.\n\nYou can also add more root properties outside the `cosmiconfig` property\nto configure your tools, entirely eliminating the need to look for additional configuration files:\n\n```yaml\ncosmiconfig:\n  searchPlaces: []\n\nprettier:\n  semi: true\n```\n\n### Imports\n\nWherever you put your configuration (the package.json file, a root config file or a package-specific config file),\nyou can use the special `$import` key to import another file as a base.\n\nFor example, you can import from an npm package (in this example, `@foocorp/config`).\n\n`.prettierrc.base.yml` in said npm package could define some company-wide defaults:\n\n```yaml\nprintWidth: 120\nsemi: true\ntabWidth: 2\n```\n\nAnd then, the `.prettierrc.yml` file in the project itself would just reference that file,\noptionally overriding the defaults with project-specific settings:\n\n```yaml\n$import: node_modules/@foocorp/config/.prettierrc.base.yml\n# we want more space!\nprintWidth: 200\n```\n\nIt is possible to import multiple base files by specifying an array of paths,\nwhich will be processed in declaration order;\nthat means that the last entry will win if there are conflicting properties.\n\nIt is also possible to import file formats other than the importing format\nas long as they are supported by the loaders specified by the developer of the tool you're configuring. \n\n```yaml\n$import: [first.yml, second.json, third.config.js]\n```\n\n## Contributing & Development\n\nPlease note that this project is released with a [Contributor Code of Conduct](CODE_OF_CONDUCT.md). By participating in this project you agree to abide by its terms.\n\nAnd please do participate!\n\n[result]: #result\n\n[`load()`]: #explorerload\n\n[`search()`]: #explorersearch\n\n[`clearloadcache()`]: #explorerclearloadcache\n\n[`clearsearchcache()`]: #explorerclearsearchcache\n\n[`cosmiconfig()`]: #cosmiconfig\n\n[`cosmiconfigSync()`]: #cosmiconfigsync\n\n[`clearcaches()`]: #explorerclearcaches\n\n[`packageprop`]: #packageprop\n\n[`cache`]: #cache\n\n[`stopdir`]: #stopdir\n\n[`searchplaces`]: #searchplaces\n\n[`loaders`]: #loaders\n\n[`cosmiconfigoptions`]: #cosmiconfigoptions\n\n[`explorerSync.search()`]: #explorersyncsearch\n\n[`explorerSync.load()`]: #explorersyncload\n\n[`explorer.search()`]: #explorersearch\n\n[`explorer.load()`]: #explorerload\n\n[\"Loading JS modules\"]: #loading-js-modules\n\n[`env-paths`]: https://github.com/sindresorhus/env-paths\n\n[search strategies]: #searchstrategy\n", "readmeFilename": "README.md", "_id": "cosmiconfig@9.0.0-alpha.1", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-y59McVFjw5Ye2PMnBoNjlX66RIszonGnmfkGBW93CDDlpuHaMxwXtjY2pYXy0oDVIhYd+RB5p3BC7suhaiG2EQ==", "shasum": "7588773e767041e4ed774dd01de119ec15e4f134", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0-alpha.1.tgz", "fileCount": 59, "unpackedSize": 120207, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzKqOsvR3tM4BLkizuEGjgqywCVdpFkEff7TtgcGrZrQIhALRL4C3G80qIiMNMmwnp9yUbXGrL98dlN6rG8x8CGPXu"}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_9.0.0-alpha.1_1697549811324_0.3927031695358063"}, "_hasShrinkwrap": false}, "9.0.0-alpha.2": {"name": "cosmiconfig", "version": "9.0.0-alpha.2", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "make-dir": "^4.0.0", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "_id": "cosmiconfig@9.0.0-alpha.2", "gitHead": "76d26bdc397760086c7ff0302b2f154ca94390ae", "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-1FnIGWJQGd4g/Ra4JIXS10Nkj3j2FnBMUlZBWptBTHVfJxeIFgqST+Qnp+a7PAVZwlbzhamF1p+3e7Miy8KGbw==", "shasum": "910b80358afaf9f32c53ea0f5006bf54de4fc330", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0-alpha.2.tgz", "fileCount": 39, "unpackedSize": 106668, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCChtdbFIqa3S0zX4KmiYkd7vU6GwYvH2vlxuS8WBxklQIhAL1TRHY+IsW/LnN/IiDouPqb6hjKsl7MsTW+2tVGRasS"}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_9.0.0-alpha.2_1699808956005_0.7209094685169644"}, "_hasShrinkwrap": false}, "9.0.0-alpha.3": {"name": "cosmiconfig", "version": "9.0.0-alpha.3", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "_id": "cosmiconfig@9.0.0-alpha.3", "gitHead": "5839d5eda86a9f8c05e0ea6dd6052a3edf73181a", "_nodeVersion": "18.18.2", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-tQkA7kUq+6FKyHMzp8tCc0otgr3tFJH0w2xlM7DdJ4Rna1PregHD0aJw+5tmO7TvUhk8hJAbapYK6F+N3NO8yg==", "shasum": "8671d4b0a1e4844373a78515290754cc859f8d59", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0-alpha.3.tgz", "fileCount": 39, "unpackedSize": 108890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDoFTr/sPy0U5yVuGEYZYiikr+cxgFZiVQde9NkaC/IdAiB5ThG+hjpFoZIDkHE8ULiSntBib+TZXcsTeX3N45tDlA=="}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_9.0.0-alpha.3_1699813863029_0.30331521514672466"}, "_hasShrinkwrap": false}, "9.0.0": {"name": "cosmiconfig", "version": "9.0.0", "description": "Find and load configuration from a package.json property, rc file, TypeScript module, and more!", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "git clean -Xdf -e '!node_modules' .", "build": "npm run build:tsc", "build:tsc": "cross-env NODE_ENV=production tsc -b", "dev": "npm run build:tsc -- --watch", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "test": "vitest run --coverage", "test:watch": "vitest", "check:all": "npm run test && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build", "prepare": "husky install"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,yml,yaml}": ["prettier --write"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format"]}, "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "funding": "https://github.com/sponsors/d-fischer", "license": "MIT", "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0"}, "devDependencies": {"@types/js-yaml": "^4.0.5", "@types/node": "^14", "@types/parse-json": "^4.0.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vitest/coverage-istanbul": "^0.34.3", "cross-env": "^7.0.3", "eslint": "^8.48.0", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-vitest": "^0.2.8", "husky": "^8.0.3", "lint-staged": "^14.0.1", "parent-module": "^3.0.0", "prettier": "^3.0.3", "remark-preset-davidtheclark": "^0.12.0", "typescript": "^5.2.2", "vitest": "^0.34.3"}, "engines": {"node": ">=14"}, "gitHead": "006fc0baa0c998a42afc3bff3e8257b9f19a9b22", "_id": "cosmiconfig@9.0.0", "_nodeVersion": "18.17.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==", "shasum": "34c3fc58287b915f3ae905ab6dc3de258b55ad9d", "tarball": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0.tgz", "fileCount": 59, "unpackedSize": 123910, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGx79JWA8lOojP9viT9ZKXmO5t7zXBurFrLfwAZERnk6AiEAlFARxi+TPc0dBG/qGV1XisHddn3MXYClhqz6qASM/z8="}]}, "_npmUser": {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/cosmiconfig_9.0.0_1701002211099_0.8460872335880885"}, "_hasShrinkwrap": false}}, "readme": "# cosmiconfig\n\n[![codecov](https://codecov.io/gh/cosmiconfig/cosmiconfig/branch/main/graph/badge.svg)](https://codecov.io/gh/cosmiconfig/cosmiconfig)\n\nCosmiconfig searches for and loads configuration for your program.\n\nIt features smart defaults based on conventional expectations in the JavaScript ecosystem.\nBut it's also flexible enough to search wherever you'd like to search, and load whatever you'd like to load.\n\nBy default, Cosmiconfig will check the current directory for the following:\n\n- a `package.json` property\n- a JSON or YAML, extensionless \"rc file\"\n- an \"rc file\" with the extensions `.json`, `.yaml`, `.yml`, `.js`, `.ts`, `.mjs`, or `.cjs`\n- any of the above two inside a `.config` subdirectory\n- a `.config.js`, `.config.ts`, `.config.mjs`, or `.config.cjs` file\n\nFor example, if your module's name is \"myapp\", cosmiconfig will search up the directory tree for configuration in the following places:\n\n- a `myapp` property in `package.json`\n- a `.myapprc` file in JSON or YAML format\n- a `.myapprc.json`, `.myapprc.yaml`, `.myapprc.yml`, `.myapprc.js`, `.myapprc.ts`, `.myapprc.mjs`, or `.myapprc.cjs` file\n- a `myapprc`, `myapprc.json`, `myapprc.yaml`, `myapprc.yml`, `myapprc.js`, `myapprc.ts`, `myapprc.mjs`, or `myapprc.cjs` file inside a `.config` subdirectory\n- a `myapp.config.js`, `myapp.config.ts`, `myapp.config.mjs`, or `myapp.config.cjs` file\n\nOptionally, you can tell it to search up the directory tree using [search strategies],\nchecking each of these places in each directory, until it finds some acceptable configuration (or hits the home directory).\n\n## Table of contents\n\n- [Installation](#installation)\n- [Usage for tooling developers](#usage-for-tooling-developers)\n- [Result](#result)\n- [Asynchronous API](#asynchronous-api)\n  - [cosmiconfig()](#cosmiconfig-1)\n  - [explorer.search()](#explorersearch)\n  - [explorer.load()](#explorerload)\n  - [explorer.clearLoadCache()](#explorerclearloadcache)\n  - [explorer.clearSearchCache()](#explorerclearsearchcache)\n  - [explorer.clearCaches()](#explorerclearcaches)\n- [Synchronous API](#synchronous-api)\n  - [cosmiconfigSync()](#cosmiconfigsync)\n  - [explorerSync.search()](#explorersyncsearch)\n  - [explorerSync.load()](#explorersyncload)\n  - [explorerSync.clearLoadCache()](#explorersyncclearloadcache)\n  - [explorerSync.clearSearchCache()](#explorersyncclearsearchcache)\n  - [explorerSync.clearCaches()](#explorersyncclearcaches)\n- [cosmiconfigOptions](#cosmiconfigoptions)\n  - [searchStrategy](#searchstrategy)\n  - [searchPlaces](#searchplaces)\n  - [loaders](#loaders)\n  - [packageProp](#packageprop)\n  - [stopDir](#stopdir)\n  - [cache](#cache)\n  - [transform](#transform)\n  - [ignoreEmptySearchPlaces](#ignoreemptysearchplaces)\n- [Loading JS modules](#loading-js-modules)\n- [Caching](#caching)\n- [Differences from rc](#differences-from-rc)\n- [Usage for end users](#usage-for-end-users)\n  - [Imports](#imports)\n- [Contributing & Development](#contributing--development)\n\n## Installation\n\n```\nnpm install cosmiconfig\n```\n\nTested in Node 14+.\n\n## Usage for tooling developers\n\n*If you are an end user (i.e. a user of a tool that uses cosmiconfig, like `prettier` or `stylelint`),\nyou can skip down to [the end user section](#usage-for-end-users).*\n\nCreate a Cosmiconfig explorer, then either `search` for or directly `load` a configuration file.\n\n```js\nconst { cosmiconfig, cosmiconfigSync } = require('cosmiconfig');\n// ...\nconst explorer = cosmiconfig(moduleName);\n\n// Search for a configuration by walking up directories.\n// See documentation for search, below.\nexplorer.search()\n  .then((result) => {\n    // result.config is the parsed configuration object.\n    // result.filepath is the path to the config file that was found.\n    // result.isEmpty is true if there was nothing to parse in the config file.\n  })\n  .catch((error) => {\n    // Do something constructive.\n  });\n\n// Load a configuration directly when you know where it should be.\n// The result object is the same as for search.\n// See documentation for load, below.\nexplorer.load(pathToConfig).then(/* ... */);\n\n// You can also search and load synchronously.\nconst explorerSync = cosmiconfigSync(moduleName);\n\nconst searchedFor = explorerSync.search();\nconst loaded = explorerSync.load(pathToConfig);\n```\n\n## Result\n\nThe result object you get from `search` or `load` has the following properties:\n\n- **config:** The parsed configuration object. `undefined` if the file is empty.\n- **filepath:** The path to the configuration file that was found.\n- **isEmpty:** `true` if the configuration file is empty. This property will not be present if the configuration file is not empty.\n\n## Asynchronous API\n\n### cosmiconfig()\n\n```js\nconst { cosmiconfig } = require('cosmiconfig');\nconst explorer = cosmiconfig(moduleName, /* optional */ cosmiconfigOptions)\n```\n\nCreates a cosmiconfig instance (\"explorer\") configured according to the arguments, and initializes its caches.\n\n#### moduleName\n\nType: `string`. **Required.**\n\nYour module name. This is used to create the default [`searchPlaces`] and [`packageProp`].\n\nIf your [`searchPlaces`] value will include files, as it does by default (e.g. `${moduleName}rc`), your `moduleName` must consist of characters allowed in filenames. That means you should not copy scoped package names, such as `@my-org/my-package`, directly into `moduleName`.\n\n**[`cosmiconfigOptions`] are documented below.**\nYou may not need them, and should first read about the functions you'll use.\n\n### explorer.search()\n\n```js\nexplorer.search([searchFrom]).then(result => { /* ... */ })\n```\n\nSearches for a configuration file. Returns a Promise that resolves with a [result] or with `null`, if no configuration file is found.\n\nYou can do the same thing synchronously with [`explorerSync.search()`].\n\nLet's say your module name is `goldengrahams` so you initialized with `const explorer = cosmiconfig('goldengrahams');`.\nHere's how your default [`search()`] will work:\n\n- Starting from `process.cwd()` (or some other directory defined by the `searchFrom` argument to [`search()`]), look for configuration objects in the following places:\n  1. A `goldengrahams` property in a `package.json` file.\n  2. A `.goldengrahamsrc` file with JSON or YAML syntax.\n  3. A `.goldengrahamsrc.json`, `.goldengrahamsrc.yaml`, `.goldengrahamsrc.yml`, `.goldengrahamsrc.js`, `.goldengrahamsrc.ts`, `.goldengrahamsrc.mjs`, or `.goldengrahamsrc.cjs` file. (To learn more about how JS files are loaded, see [\"Loading JS modules\"].)\n  4. A `goldengrahamsrc`, `goldengrahamsrc.json`, `goldengrahamsrc.yaml`, `goldengrahamsrc.yml`, `goldengrahamsrc.js`, `goldengrahamsrc.ts`, `goldengrahamsrc.mjs`, or `goldengrahamsrc.cjs` file in the `.config` subdirectory.\n  5. A `goldengrahams.config.js`, `goldengrahams.config.ts`, `goldengrahams.config.mjs`, or `goldengrahams.config.cjs` file. (To learn more about how JS files are loaded, see [\"Loading JS modules\"].)\n- If none of those searches reveal a configuration object, continue depending on the current search strategy:\n  - If it's `none` (which is the default if you don't specify a [`stopDir`] option), stop here and return/resolve with `null`.\n  - If it's `global` (which is the default if you specify a [`stopDir`] option), move up one directory level and try again,\n    recursing until arriving at the configured [`stopDir`] option, which defaults to the user's home directory.\n    - After arriving at the [`stopDir`], the global configuration directory (as defined by [`env-paths`] without prefix) is also checked,\n      looking at the files `config`, `config.json`, `config.yaml`, `config.yml`, `config.js`, `config.ts`, `config.cjs`, and `config.mjs`\n      in the directory `~/.config/goldengrahams/` (on Linux; see [`env-paths`] documentation for other OSs).\n  - If it's `project`, check whether a `package.json` file is present in the current directory, and if not,\n    move up one directory level and try again, recursing until there is one.\n- If at any point a parsable configuration is found, the [`search()`] Promise resolves with its [result] \\(or, with [`explorerSync.search()`], the [result] is returned).\n- If no configuration object is found, the [`search()`] Promise resolves with `null` (or, with [`explorerSync.search()`], `null` is returned).\n- If a configuration object is found *but is malformed* (causing a parsing error), the [`search()`] Promise rejects with that error (so you should `.catch()` it). (Or, with [`explorerSync.search()`], the error is thrown.)\n\n**If you know exactly where your configuration file should be, you can use [`load()`], instead.**\n\n**The search process is highly customizable.**\nUse the cosmiconfig options [`searchPlaces`] and [`loaders`] to precisely define where you want to look for configurations and how you want to load them.\n\n#### searchFrom\n\nType: `string`.\nDefault: `process.cwd()`.\n\nA filename.\n[`search()`] will start its search here.\n\nIf the value is a directory, that's where the search starts.\nIf it's a file, the search starts in that file's directory.\n\n### explorer.load()\n\n```js\nexplorer.load(loadPath).then(result => { /* ... */ })\n```\n\nLoads a configuration file. Returns a Promise that resolves with a [result] or rejects with an error (if the file does not exist or cannot be loaded).\n\nUse `load` if you already know where the configuration file is and you just need to load it.\n\n```js\nexplorer.load('load/this/file.json'); // Tries to load load/this/file.json.\n```\n\nIf you load a `package.json` file, the result will be derived from whatever property is specified as your [`packageProp`].\n`package.yaml` will work as well if you specify these file names in your [`searchPlaces`]. \n\nYou can do the same thing synchronously with [`explorerSync.load()`].\n\n### explorer.clearLoadCache()\n\nClears the cache used in [`load()`].\n\n### explorer.clearSearchCache()\n\nClears the cache used in [`search()`].\n\n### explorer.clearCaches()\n\nPerforms both [`clearLoadCache()`] and [`clearSearchCache()`].\n\n## Synchronous API\n\n### cosmiconfigSync()\n\n```js\nconst { cosmiconfigSync } = require('cosmiconfig');\nconst explorerSync = cosmiconfigSync(moduleName, /* optional */ cosmiconfigOptions)\n```\n\nCreates a *synchronous* cosmiconfig instance (\"explorerSync\") configured according to the arguments, and initializes its caches.\n\nSee [`cosmiconfig()`](#cosmiconfig-1).\n\n### explorerSync.search()\n\n```js\nconst result = explorerSync.search([searchFrom]);\n```\n\nSynchronous version of [`explorer.search()`].\n\nReturns a [result] or `null`.\n\n### explorerSync.load()\n\n```js\nconst result = explorerSync.load(loadPath);\n```\n\nSynchronous version of [`explorer.load()`].\n\nReturns a [result].\n\n### explorerSync.clearLoadCache()\n\nClears the cache used in [`load()`].\n\n### explorerSync.clearSearchCache()\n\nClears the cache used in [`search()`].\n\n### explorerSync.clearCaches()\n\nPerforms both [`clearLoadCache()`] and [`clearSearchCache()`].\n\n## cosmiconfigOptions\n\nType: `Object`.\n\nPossible options are documented below.\n\n### searchStrategy\n\nType: `string`\nDefault: `global` if [`stopDir`] is specified, `none` otherwise.\n\nThe strategy that should be used to determine which directories to check for configuration files.\n\n- `none`: Only checks in the current working directory.\n- `project`: Starts in the current working directory, traversing upwards until a `package.{json,yaml}` file is found.\n- `global`: Starts in the current working directory, traversing upwards until the configured [`stopDir`]\n  (or the current user's home directory if none is given). Then, if no configuration is found, also look in the\n  operating system's default configuration directory (according to [`env-paths`] without prefix),\n  where a different set of file names is checked:\n\n```js\n[\n  `config`,\n  `config.json`,\n  `config.yaml`,\n  `config.yml`,\n  `config.js`,\n  `config.ts`,\n  `config.cjs`,\n  `config.mjs`\n]\n```\n\n### searchPlaces\n\nType: `Array<string>`.\nDefault: See below.\n\nAn array of places that [`search()`] will check in each directory as it moves up the directory tree.\nEach place is relative to the directory being searched, and the places are checked in the specified order.\n\n**Default `searchPlaces`:**\n\nFor the [asynchronous API](#asynchronous-api), these are the default `searchPlaces`:\n\n```js\n[\n  'package.json',\n  `.${moduleName}rc`,\n  `.${moduleName}rc.json`,\n  `.${moduleName}rc.yaml`,\n  `.${moduleName}rc.yml`,\n  `.${moduleName}rc.js`,\n  `.${moduleName}rc.ts`,\n  `.${moduleName}rc.mjs`,\n  `.${moduleName}rc.cjs`,\n  `.config/${moduleName}rc`,\n  `.config/${moduleName}rc.json`,\n  `.config/${moduleName}rc.yaml`,\n  `.config/${moduleName}rc.yml`,\n  `.config/${moduleName}rc.js`,\n  `.config/${moduleName}rc.ts`,\n  `.config/${moduleName}rc.mjs`,\n  `.config/${moduleName}rc.cjs`,\n  `${moduleName}.config.js`,\n  `${moduleName}.config.ts`,\n  `${moduleName}.config.mjs`,\n  `${moduleName}.config.cjs`,\n];\n```\n\nFor the [synchronous API](#synchronous-api), the only difference is that `.mjs` files are not included. See [\"Loading JS modules\"] for more information.\n\nCreate your own array to search more, fewer, or altogether different places.\n\nEvery item in `searchPlaces` needs to have a loader in [`loaders`] that corresponds to its extension.\n(Common extensions are covered by default loaders.)\nRead more about [`loaders`] below.\n\n`package.json` is a special value: When it is included in `searchPlaces`, Cosmiconfig will always parse it as JSON and load a property within it, not the whole file.\nThat property is defined with the [`packageProp`] option, and defaults to your module name.\n\n`package.yaml` (used by pnpm) works the same way.\n\nExamples, with a module named `porgy`:\n\n```js\n// Disallow extensions on rc files:\n['package.json', '.porgyrc', 'porgy.config.js']\n```\n\n```js\n// Limit the options dramatically:\n['package.json', '.porgyrc']\n```\n\n```js\n// Maybe you want to look for a wide variety of JS flavors:\n[\n  'porgy.config.js',\n  'porgy.config.mjs',\n  'porgy.config.ts',\n  'porgy.config.coffee'\n]\n// ^^ You will need to designate a custom loader to tell\n// Cosmiconfig how to handle `.coffee` files.\n```\n\n```js\n// Look within a .config/ subdirectory of every searched directory:\n[\n  'package.json',\n  '.porgyrc',\n  '.config/.porgyrc',\n  '.porgyrc.json',\n  '.config/.porgyrc.json'\n]\n```\n\n### loaders\n\nType: `Object`.\nDefault: See below.\n\nAn object that maps extensions to the loader functions responsible for loading and parsing files with those extensions.\n\nCosmiconfig exposes its default loaders on the named export `defaultLoaders` and `defaultLoadersSync`.\n\n**Default `loaders`:**\n\n```js\nconst { defaultLoaders, defaultLoadersSync } = require('cosmiconfig');\n\nconsole.log(Object.entries(defaultLoaders));\n// [\n//   [ '.mjs', [Function: loadJs] ],\n//   [ '.cjs', [Function: loadJs] ],\n//   [ '.js', [Function: loadJs] ],\n//   [ '.ts', [Function: loadTs] ],\n//   [ '.json', [Function: loadJson] ],\n//   [ '.yaml', [Function: loadYaml] ],\n//   [ '.yml', [Function: loadYaml] ],\n//   [ 'noExt', [Function: loadYaml] ]\n// ]\n\nconsole.log(Object.entries(defaultLoadersSync));\n// [\n//   [ '.cjs', [Function: loadJsSync] ],\n//   [ '.js', [Function: loadJsSync] ],\n//   [ '.ts', [Function: loadTsSync] ],\n//   [ '.json', [Function: loadJson] ],\n//   [ '.yaml', [Function: loadYaml] ],\n//   [ '.yml', [Function: loadYaml] ],\n//   [ 'noExt', [Function: loadYaml] ]\n// ]\n```\n\n(YAML is a superset of JSON; which means YAML parsers can parse JSON; which is how extensionless files can be either YAML *or* JSON with only one parser.)\n\n**If you provide a `loaders` object, your object will be *merged* with the defaults.**\nSo you can override one or two without having to override them all.\n\n**Keys in `loaders`** are extensions (starting with a period), or `noExt` to specify the loader for files *without* extensions, like `.myapprc`.\n\n**Values in `loaders`** are a loader function (described below) whose values are loader functions.\n\n**The most common use case for custom loaders value is to load extensionless `rc` files as strict JSON**, instead of JSON *or* YAML (the default).\nTo accomplish that, provide the following `loaders` value:\n\n```js\n{\n  noExt: defaultLoaders['.json'];\n}\n```\n\nIf you want to load files that are not handled by the loader functions Cosmiconfig exposes, you can write a custom loader function or use one from NPM if it exists.\n\n**Use cases for custom loader function:**\n\n- Allow configuration syntaxes that aren't handled by Cosmiconfig's defaults, like JSON5, INI, or XML.\n- Parse JS files with Babel before deriving the configuration.\n\n**Custom loader functions** have the following signature:\n\n```ts\n// Sync\ntype SyncLoader = (filepath: string, content: string) => Object | null\n\n// Async\ntype AsyncLoader = (filepath: string, content: string) => Object | null | Promise<Object | null>\n```\n\nCosmiconfig reads the file when it checks whether the file exists, so it will provide you with both the file's path and its content.\nDo whatever you need to, and return either a configuration object or `null` (or, for async-only loaders, a Promise that resolves with one of those).\n`null` indicates that no real configuration was found and the search should continue.\n\nA few things to note:\n\n- If you use a custom loader, be aware of whether it's sync or async: you cannot use async customer loaders with the sync API ([`cosmiconfigSync()`]).\n- **Special JS syntax can also be handled by using a `require` hook**, because `defaultLoaders['.js']` just uses `require`.\n  Whether you use custom loaders or a `require` hook is up to you.\n\nExamples:\n\n```js\n// Allow JSON5 syntax:\ncosmiconfig('foo', {\n  loaders: {\n    '.json': json5Loader\n  }\n});\n\n// Allow a special configuration syntax of your own creation:\ncosmiconfig('foo', {\n  loaders: {\n    '.special': specialLoader\n  }\n});\n\n// Allow many flavors of JS, using custom loaders:\ncosmiconfig('foo', {\n  loaders: {\n    '.coffee': coffeeScriptLoader\n  }\n});\n\n// Allow many flavors of JS but rely on require hooks:\ncosmiconfig('foo', {\n  loaders: {\n    '.coffee': defaultLoaders['.js']\n  }\n});\n```\n\n### packageProp\n\nType: `string | Array<string>`.\nDefault: `` `${moduleName}` ``.\n\nName of the property in `package.json` (or `package.yaml`) to look for.\n\nUse a period-delimited string or an array of strings to describe a path to nested properties.\n\nFor example, the value `'configs.myPackage'` or `['configs', 'myPackage']` will get you the `\"myPackage\"` value in a `package.json` like this:\n\n```json\n{\n  \"configs\": {\n    \"myPackage\": {\"option\":  \"value\"}\n  }\n}\n```\n\nIf nested property names within the path include periods, you need to use an array of strings. For example, the value `['configs', 'foo.bar', 'baz']` will get you the `\"baz\"` value in a `package.json` like this:\n\n```json\n{\n  \"configs\": {\n    \"foo.bar\": {\n      \"baz\": {\"option\":  \"value\"}\n    }\n  }\n}\n```\n\nIf a string includes period but corresponds to a top-level property name, it will not be interpreted as a period-delimited path. For example, the value `'one.two'` will get you the `\"three\"` value in a `package.json` like this:\n\n```json\n{\n  \"one.two\": \"three\",\n  \"one\": {\n    \"two\": \"four\"\n  }\n}\n```\n\n### stopDir\n\nType: `string`.\nDefault: Absolute path to your home directory.\n\nDirectory where the search will stop.\n\n### cache\n\nType: `boolean`.\nDefault: `true`.\n\nIf `false`, no caches will be used.\nRead more about [\"Caching\"](#caching) below.\n\n### transform\n\nType: `(Result) => Promise<Result> | Result`.\n\nA function that transforms the parsed configuration. Receives the [result].\n\nIf using [`search()`] or [`load()`] \\(which are async), the transform function can return the transformed result or return a Promise that resolves with the transformed result.\nIf using `cosmiconfigSync`, [`search()`] or [`load()`], the function must be synchronous and return the transformed result.\n\nThe reason you might use this option — instead of simply applying your transform function some other way — is that *the transformed result will be cached*. If your transformation involves additional filesystem I/O or other potentially slow processing, you can use this option to avoid repeating those steps every time a given configuration is searched or loaded.\n\n### ignoreEmptySearchPlaces\n\nType: `boolean`.\nDefault: `true`.\n\nBy default, if [`search()`] encounters an empty file (containing nothing but whitespace) in one of the [`searchPlaces`], it will ignore the empty file and move on.\nIf you'd like to load empty configuration files, instead, set this option to `false`.\n\nWhy might you want to load empty configuration files?\nIf you want to throw an error, or if an empty configuration file means something to your program.\n\n## Loading JS modules\n\nYour end users can provide JS configuration files as ECMAScript modules (ESM) under the following conditions:\n\n- You (the cosmiconfig user) use cosmiconfig's [asynchronous API](#asynchronous-api).\n- Your end user runs a version of Node that supports ESM ([>=12.17.0](https://nodejs.org/en/blog/release/v12.17.0/), or earlier with the `--experimental-modules` flag).\n- Your end user provides an `.mjs` configuration file, or a `.js` file whose nearest parent `package.json` file contains `\"type\": \"module\"`. (See [Node's method for determining a file's module system](https://nodejs.org/api/packages.html#packages_determining_module_system).)\n\nWith cosmiconfig's [asynchronous API](#asynchronous-api), the default [`searchPlaces`] include `.js`, `.ts`, `.mjs`, and `.cjs` files. Cosmiconfig loads all these file types with the [dynamic `import` function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/import#dynamic_imports).\n\nWith the [synchronous API](#synchronous-api), JS configuration files are always treated as CommonJS, and `.mjs` files are ignored, because there is no synchronous API for the dynamic `import` function.\n\n## Caching\n\nAs of v2, cosmiconfig uses caching to reduce the need for repetitious reading of the filesystem or expensive transforms. Every new cosmiconfig instance (created with `cosmiconfig()`) has its own caches.\n\nTo avoid or work around caching, you can do the following:\n\n- Set the `cosmiconfig` option [`cache`] to `false`.\n- Use the cache-clearing methods [`clearLoadCache()`], [`clearSearchCache()`], and [`clearCaches()`].\n- Create separate instances of cosmiconfig (separate \"explorers\").\n\n## Differences from [rc](https://github.com/dominictarr/rc)\n\n[rc](https://github.com/dominictarr/rc) serves its focused purpose well. cosmiconfig differs in a few key ways — making it more useful for some projects, less useful for others:\n\n- Looks for configuration in some different places: in a `package.json` property, an rc file, a `.config.js` file, and rc files with extensions.\n- Built-in support for JSON, YAML, and CommonJS formats.\n- Stops at the first configuration found, instead of finding all that can be found up the directory tree and merging them automatically.\n- Options.\n- Asynchronous by default (though can be run synchronously).\n\n## Usage for end users\n\nWhen configuring a tool, you can use multiple file formats and put these in multiple places.\n\nUsually, a tool would mention this in its own README file,\nbut by default, these are the following places, where `{NAME}` represents the name of the tool:\n\n```\npackage.json\n.{NAME}rc\n.{NAME}rc.json\n.{NAME}rc.yaml\n.{NAME}rc.yml\n.{NAME}rc.js\n.{NAME}rc.ts\n.{NAME}rc.cjs\n.config/{NAME}rc\n.config/{NAME}rc.json\n.config/{NAME}rc.yaml\n.config/{NAME}rc.yml\n.config/{NAME}rc.js\n.config/{NAME}rc.ts\n.config/{NAME}rc.mjs\n.config/{NAME}rc.cjs\n{NAME}.config.js\n{NAME}.config.ts\n{NAME}.config.mjs\n{NAME}.config.cjs\n```\n\nThe contents of these files are defined by the tool.\nFor example, you can configure prettier to enforce semicolons at the end of the line\nusing a file named `.config/prettierrc.yml`:\n\n```yaml\nsemi: true\n```\n\nAdditionally, you have the option to put a property named after the tool in your `package.json` file,\nwith the contents of that property being the same as the file contents. To use the same example as above:\n\n```json\n{\n  \"name\": \"your-project\",\n  \"dependencies\": {},\n  \"prettier\": {\n    \"semi\": true\n  }\n}\n```\n\nThis has the advantage that you can put the configuration of all tools\n(at least the ones that use cosmiconfig) in one file.\n\nYou can also add a `cosmiconfig` key within your `package.json` file or create one of the following files\nto configure `cosmiconfig` itself:\n\n```\n.config/config.json\n.config/config.yaml\n.config/config.yml\n.config/config.js\n.config/config.ts\n.config/config.cjs\n```\n\nThe following properties are currently actively supported in these places:\n\n```yaml\ncosmiconfig:\n  # adds places where configuration files are being searched\n  searchPlaces:\n    - .config/{name}.yml\n  # to enforce a custom naming convention and format, don't merge the above with the tool-defined search places\n  # (`true` is the default setting)\n  mergeSearchPlaces: false\n```\n\n> **Note:** technically, you can overwrite all options described in [cosmiconfigOptions](#cosmiconfigoptions) here,\n> but everything not listed above should be used at your own risk, as it has not been tested explicitly.\n> The only exceptions to this are the `loaders` property, which is explicitly not supported at this time,\n> and the `searchStrategy` property, which is intentionally disallowed.\n\nYou can also add more root properties outside the `cosmiconfig` property\nto configure your tools, entirely eliminating the need to look for additional configuration files:\n\n```yaml\ncosmiconfig:\n  searchPlaces: []\n\nprettier:\n  semi: true\n```\n\n### Imports\n\nWherever you put your configuration (the package.json file, a root config file or a package-specific config file),\nyou can use the special `$import` key to import another file as a base.\n\nFor example, you can import from an npm package (in this example, `@foocorp/config`).\n\n`.prettierrc.base.yml` in said npm package could define some company-wide defaults:\n\n```yaml\nprintWidth: 120\nsemi: true\ntabWidth: 2\n```\n\nAnd then, the `.prettierrc.yml` file in the project itself would just reference that file,\noptionally overriding the defaults with project-specific settings:\n\n```yaml\n$import: node_modules/@foocorp/config/.prettierrc.base.yml\n# we want more space!\nprintWidth: 200\n```\n\nIt is possible to import multiple base files by specifying an array of paths,\nwhich will be processed in declaration order;\nthat means that the last entry will win if there are conflicting properties.\n\nIt is also possible to import file formats other than the importing format\nas long as they are supported by the loaders specified by the developer of the tool you're configuring. \n\n```yaml\n$import: [first.yml, second.json, third.config.js]\n```\n\n## Contributing & Development\n\nPlease note that this project is released with a [Contributor Code of Conduct](CODE_OF_CONDUCT.md). By participating in this project you agree to abide by its terms.\n\nAnd please do participate!\n\n[result]: #result\n\n[`load()`]: #explorerload\n\n[`search()`]: #explorersearch\n\n[`clearloadcache()`]: #explorerclearloadcache\n\n[`clearsearchcache()`]: #explorerclearsearchcache\n\n[`cosmiconfig()`]: #cosmiconfig\n\n[`cosmiconfigSync()`]: #cosmiconfigsync\n\n[`clearcaches()`]: #explorerclearcaches\n\n[`packageprop`]: #packageprop\n\n[`cache`]: #cache\n\n[`stopdir`]: #stopdir\n\n[`searchplaces`]: #searchplaces\n\n[`loaders`]: #loaders\n\n[`cosmiconfigoptions`]: #cosmiconfigoptions\n\n[`explorerSync.search()`]: #explorersyncsearch\n\n[`explorerSync.load()`]: #explorersyncload\n\n[`explorer.search()`]: #explorersearch\n\n[`explorer.load()`]: #explorerload\n\n[\"Loading JS modules\"]: #loading-js-modules\n\n[`env-paths`]: https://github.com/sindresorhus/env-paths\n\n[search strategies]: #searchstrategy\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d-<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-11-26T12:36:51.493Z", "created": "2015-11-12T02:06:06.728Z", "0.1.0": "2015-11-12T02:06:06.728Z", "0.2.0": "2015-11-12T14:14:36.859Z", "0.3.0": "2015-11-13T04:35:37.790Z", "0.4.0": "2015-11-13T13:57:53.031Z", "0.4.1": "2015-11-13T14:10:00.493Z", "0.5.0": "2015-11-15T14:56:40.794Z", "1.0.0": "2015-11-18T02:48:57.711Z", "1.0.1": "2015-11-27T15:46:00.803Z", "1.0.2": "2016-01-10T17:47:06.120Z", "1.1.0": "2016-01-13T03:06:08.295Z", "2.0.0": "2016-10-08T14:31:10.642Z", "2.0.1": "2016-10-11T15:03:03.419Z", "2.0.2": "2016-10-11T18:12:31.606Z", "2.1.0": "2016-10-14T02:30:20.337Z", "2.1.1": "2016-12-04T20:40:00.515Z", "2.1.2": "2017-04-26T01:35:33.689Z", "2.1.3": "2017-04-29T16:51:18.660Z", "2.2.0": "2017-07-21T14:00:58.618Z", "2.2.1": "2017-07-21T15:19:53.616Z", "2.2.2": "2017-07-23T14:30:16.541Z", "3.0.0": "2017-09-16T01:33:56.982Z", "3.0.1": "2017-09-17T15:07:07.586Z", "3.1.0": "2017-10-02T01:16:26.525Z", "4.0.0": "2018-01-16T03:23:05.317Z", "5.0.0": "2018-05-05T16:27:30.286Z", "5.0.1": "2018-05-05T17:01:10.859Z", "5.0.2": "2018-05-07T22:11:30.894Z", "5.0.3": "2018-05-15T03:35:59.445Z", "5.0.4": "2018-05-19T17:25:22.785Z", "5.0.5": "2018-05-22T02:10:40.433Z", "5.0.6": "2018-08-09T14:32:04.107Z", "5.0.7": "2018-11-08T02:41:13.971Z", "5.1.0": "2019-02-17T23:58:00.136Z", "5.2.0": "2019-03-24T18:18:42.999Z", "5.2.1": "2019-05-15T01:12:44.145Z", "6.0.0": "2019-11-02T16:04:24.585Z", "7.0.0": "2020-08-01T20:01:39.727Z", "7.0.1": "2021-08-21T22:45:35.231Z", "7.1.0": "2022-11-12T14:18:30.020Z", "8.0.0": "2022-11-21T20:46:46.615Z", "8.1.0": "2023-02-24T21:57:40.671Z", "8.1.1": "2023-03-17T14:11:26.390Z", "8.1.2": "2023-03-17T17:02:40.228Z", "8.1.3": "2023-03-18T20:51:16.095Z", "8.2.0": "2023-06-04T19:08:19.474Z", "8.3.0": "2023-09-02T16:15:42.036Z", "8.3.1": "2023-09-02T19:57:36.519Z", "8.3.2": "2023-09-02T20:33:40.209Z", "8.3.3": "2023-09-03T19:53:17.789Z", "8.3.4": "2023-09-04T19:33:28.736Z", "8.3.5": "2023-09-08T22:12:07.893Z", "8.3.6": "2023-09-13T16:34:42.755Z", "9.0.0-alpha.0": "2023-10-16T20:52:05.814Z", "9.0.0-alpha.1": "2023-10-17T13:36:51.632Z", "9.0.0-alpha.2": "2023-11-12T17:09:16.181Z", "9.0.0-alpha.3": "2023-11-12T18:31:03.191Z", "9.0.0": "2023-11-26T12:36:51.267Z"}, "homepage": "https://github.com/cosmiconfig/cosmiconfig#readme", "keywords": ["load", "configuration", "config"], "repository": {"type": "git", "url": "git+https://github.com/cosmiconfig/cosmiconfig.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cosmiconfig/cosmiconfig/issues"}, "license": "MIT", "readmeFilename": "README.md", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "users": {"3846masa": true, "abhisekp": true, "sbruchmann": true, "netweb": true, "arniu": true, "andreaspizsa": true, "garrickajo": true, "kodekracker": true, "knksmith57": true, "davidbwaters": true, "arcticicestudio": true, "hualei": true, "dm.dymov": true, "xiechao06": true, "flumpus-dev": true}}