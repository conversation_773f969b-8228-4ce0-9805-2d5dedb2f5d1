{"_id": "pac-resolver", "_rev": "40-699bc88bfdae654dbc787fab4c68927f", "name": "pac-resolver", "description": "Generates an asynchronous resolver function from a PAC file", "dist-tags": {"latest": "7.0.1"}, "versions": {"0.0.1": {"name": "pac-resolver", "version": "0.0.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~2.3.0", "netmask": "0.0.2", "degenerator": "~0.0.2", "regenerator": "~0.3.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@0.0.1", "dist": {"shasum": "f64d77caa78f7230ca57dce429cf4004c404b865", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-0.0.1.tgz", "integrity": "sha512-Ocg31RM9FMeExKXOHAr1zW3Y2IkWAK8I+GLb8ddjwlTG4AHzn4VpwIWRNyt977NtIuyYOdy8eMuvjClBRh3Azg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBUqR72tAidnqmmH0excT83x69u4iYBbioe1m7poR8VQIgfAy2TD4IiZFeREyCg+kKK6DnVoJP94u8xbJSoj//RUA="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "pac-resolver", "version": "0.0.2", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~2.3.0", "netmask": "1.0.4", "degenerator": "~0.0.2", "regenerator": "~0.3.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@0.0.2", "dist": {"shasum": "663b741b1368d16df75138f514d54d76cdc2ff27", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-0.0.2.tgz", "integrity": "sha512-+W8ZI9E260zYmOibIJ11ijs/azPArlA/j6V8C8d2zsCNRzlP7or5O0ozUHxrMBFNLKt+CGousgD0oS514ZrGwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGigkTIsKuvs5d+xz4wpYCpZwXWYc2ayOTGbP1bKXZKnAiAJpbuiWAb8pl+2z5VomRWiRPISjFHI0WIXI86o+1keCg=="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "pac-resolver", "version": "1.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~2.3.0", "netmask": "1.0.4", "degenerator": "~0.0.2", "regenerator": "~0.3.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.0.0", "dist": {"shasum": "a4b0ce3c81f1b0816e1f9aaa54fdfe57ee56c6b4", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.0.0.tgz", "integrity": "sha512-Vilkjw3Q74B1BT9kHsGCQqdyXR+cpf+z9DbLs0jeqY7wqogD5rG5HQ5cr6vIjUJi0wsifm/33IGdoNd5ffpYRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsX+xjEqQrL9b+iiiyvyz7E8GlCU4SpVcNsZSEpzRtzAiEA4joC0fW1E84sLMGrjEo0XJrXFRFZ9spNUFSsLhFyAqQ="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "pac-resolver", "version": "1.1.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~2.3.0", "netmask": "1.0.4", "degenerator": "~0.0.2", "regenerator": "~0.3.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.1.0", "dist": {"shasum": "a3b1c2d220e0fe1493196f68bffbec48ad2e5816", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.1.0.tgz", "integrity": "sha512-yGKMuHsPHp73REAm5VYvYrzhvoV85IGy9oddEQC0bymZ/vCXMrb0zo1lj7hN+4JrZ5VGHV3PEq9fLbwD2uprgA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCzGrOiqrPqL2WFCsjbHZTPpaaXi2GgaYXpHysUjkYs9QIhALy8//nlqZdlTP2/y5WRt9UBUSWO6S6jMBxRLocOvzLF"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "pac-resolver", "version": "1.2.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~2.3.0", "netmask": "1.0.4", "degenerator": "~0.0.2", "regenerator": "~0.3.1", "thunkify": "0.0.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.0", "dist": {"shasum": "9408488425ea143f1f9ce2c9ff5c742a45b62860", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.0.tgz", "integrity": "sha512-L13lTrmuSygHYc5G5GtXB0CfpAcwZ+3Rl48pgQHcl3RtlePu9cNjjsRGcSxbF7JTsj4VJl/MOnM0qnvaiJ5wMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7Tny/BI8sRMX8QVXc3wOHVjOl1EdlfnWaa6mFdBqmUwIgP7kDKH9l80F84zhAW2tAeo+f5Xb6aMFwVi6BL8uVYCg="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "pac-resolver", "version": "1.2.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.5", "netmask": "1.0.4", "degenerator": "~0.0.3", "regenerator": "~0.4.5", "thunkify": "0.0.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.1", "dist": {"shasum": "3845bd5cfc5934f7fc9edcd468e6560cad0e8343", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.1.tgz", "integrity": "sha512-pM9zYgBcblEAO95pG9srecBzb72keQoHBZXzPB6FmfcGf7Z5zMe+302aS9ecMANIYtAi7A/7rkSjVZ9KEpY7Bw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVP0U2vTsTn6/6grTF5nR/RcRP3HJY0uQ060+P6Wme3AiBIa32DDtDdxZMMhB0e2u5GMIi/DpYDyIR7OZ2M6+FeHA=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "1.2.2": {"name": "pac-resolver", "version": "1.2.2", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.6", "netmask": "~1.0.4", "degenerator": "~0.0.3", "regenerator": "~0.4.7", "thunkify": "~2.1.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.2", "_shasum": "6eccedeabbe9b7a115fa3478db855de0f44de1c7", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "6eccedeabbe9b7a115fa3478db855de0f44de1c7", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.2.tgz", "integrity": "sha512-37IxTyIXq1Ph6/vyy+Csqi+AeNDCsKeZ/XqfA+q+1E+uoNfziAVDePJY9H6tLJixgyPW/NSzl5Va2VbvmPIwiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH0ABhWRoh633Ekh2dcrYovky7Imf8r4Dp8lYhVA6TVnAiBl+hmBRbTxM4L3lhWUW9AbYb9vt1JHPYbwMuPnRCG/SQ=="}]}, "directories": {}}, "1.2.3": {"name": "pac-resolver", "version": "1.2.3", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.1.0", "netmask": "~1.0.4", "degenerator": "~1.0.0", "regenerator": "~0.4.7", "thunkify": "~2.1.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "624840e868bb29b59d335967dcb90c3352defbde", "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.3", "_shasum": "19d2eee77c0bf8006fa3bd21950289dcf8a515d4", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "19d2eee77c0bf8006fa3bd21950289dcf8a515d4", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.3.tgz", "integrity": "sha512-YkJtokOH4U7fHkOE3WVzxIQyO2m574u59b9jFFw1bHhP1iEQE8rGQ2iUSFbwBCHq28w7h7GKUfuivY7dG5Incw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCS1aY2ITaXc7af+9gUNer2Sz3tlp1Np/GLgqburr9RDQIgeR0CPvJJfE1i5jYjVcQ/75cWHH+kf0kOiome28qncEE="}]}, "directories": {}}, "1.2.4": {"name": "pac-resolver", "version": "1.2.4", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.6", "netmask": "~1.0.4", "degenerator": "~1.0.0", "regenerator": "~0.4.7", "thunkify": "~2.1.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "a2b6036006e921d31e97e1143895d888d1e8c9d0", "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.4", "_shasum": "c6948acc57a1e78c8104ccdc8a4c89de90189008", "_from": ".", "_npmVersion": "2.1.2", "_nodeVersion": "0.10.32", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "c6948acc57a1e78c8104ccdc8a4c89de90189008", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.4.tgz", "integrity": "sha512-rOw2Nb+2vv2zZn9TjLf/Ukf0o12b8lLfxedE06OMXpuHw0pTjNgvTaXQniqxAGajESyE+yJMi6kylFUz+xLA9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEAkn55I3k<PERSON><PERSON>b<PERSON>lu4ttenIGBTEWIg1Mp80pKdBU9aWHAiBrM8yMBv39GIyBeV2BdG0zSRP3cowE/5vqIUJWgt7OZQ=="}]}, "directories": {}}, "1.2.5": {"name": "pac-resolver", "version": "1.2.5", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.6", "netmask": "~1.0.4", "degenerator": "~1.0.0", "regenerator": "~0.8.11", "thunkify": "~2.1.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "8f9640e3cb7084f08366be16490ead85a105b803", "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.5", "_shasum": "4eb2676ca413c87449f8b04419066af8f4726219", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "4eb2676ca413c87449f8b04419066af8f4726219", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.5.tgz", "integrity": "sha512-IPNRKz8NZNkwdw//e4qfTz0FdOP8PH51kjZJ4IsPoO6pAAuIU5ynTwcMtmrygkrnYFP2SCqIMOkEV6cmHRZ81Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbVCL0/r2Ua69XonUDqOdxdstxBBVUkbej0WOp7TSr1wIhAIo9BuEdBeyc6yM7qaZUQ8H+mbEWR87Jy24RsENwp4NS"}]}, "directories": {}}, "1.2.6": {"name": "pac-resolver", "version": "1.2.6", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.6", "netmask": "~1.0.4", "degenerator": "~1.0.0", "regenerator": "~0.8.13", "thunkify": "~2.1.1"}, "devDependencies": {"mocha": "*"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "f6e4fa5faa2a140d2a95e75b626c87be62512ca3", "homepage": "https://github.com/TooTallNate/node-pac-resolver", "_id": "pac-resolver@1.2.6", "_shasum": "ed03af0c5b5933505bdd3f07f75175466d5e7cfb", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "ed03af0c5b5933505bdd3f07f75175466d5e7cfb", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.2.6.tgz", "integrity": "sha512-hgYjw6snhgn68Rdb2Sgrd/rUKMkZ9EpXiA7G9Gqr9BsC6Bb+l1tAZ11WoYfNYzqamdkXU4m3VOb29H4VHOinyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFCsDUPVqZw05wUMmL0YlBJWoL88qvTG2HMgQXiY769AIhALNnp4inQWDYX4FZDr05G64iLd6HjpomlHvlrP7vsHPq"}]}, "directories": {}}, "1.3.0": {"name": "pac-resolver", "version": "1.3.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.6", "netmask": "~1.0.4", "degenerator": "~1.0.2", "regenerator": "~0.8.13", "thunkify": "~2.1.1", "ip": "1.0.1"}, "devDependencies": {"mocha": "2"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "cfb9d4794d9cf80f7e0ac191d6f8d5448a428f06", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@1.3.0", "_shasum": "ae451b2fb7827ca1205d3bd10c610c97403fadf3", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "ae451b2fb7827ca1205d3bd10c610c97403fadf3", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-1.3.0.tgz", "integrity": "sha512-MsSdbonKlZqtCSCSGyIojdOdNiXDTl0h0O7o6GMXboLBw1K5IdxOq5ZgzqQFJyRxsVitSOufstLnkZqPYY4YYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt9NkMW+/YpOjVPD1Ix8+p3A7SY1OZtCkXvVL/GaTy0wIhANlKhLcsKWdfSF/2esiQUHqmS1neI0c57TNu7AC7QnrX"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/pac-resolver-1.3.0.tgz_1474907412658_0.2247973382472992"}, "directories": {}}, "2.0.0": {"name": "pac-resolver", "version": "2.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "~3.0.6", "netmask": "~1.0.4", "degenerator": "~1.0.2", "thunkify": "~2.1.1", "ip": "1.0.1"}, "devDependencies": {"mocha": "2"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "1781358d73f2c68608c13f93376d4fc6d7505868", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@2.0.0", "_shasum": "99b88d2f193fbdeefc1c9a529c1f3260ab5277cd", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.1.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "99b88d2f193fbdeefc1c9a529c1f3260ab5277cd", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-2.0.0.tgz", "integrity": "sha512-wKZkFUj72S0djMZHwMkC5uyu4rl77jNKtsQnCAdjPLBHhvWNR/MPw5RL/BgXRh8v9EKG2Ce1oTIwSNdezo79fg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYYE9DWWgUX6YLFWusiaEQ1t+8n0aI/gb60Nx8adwbewIhANmrIJICSBpa7dm00xo77d2KIjAeg/6UK84EFDA1xULb"}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/pac-resolver-2.0.0.tgz_1478735283997_0.03368917992338538"}, "directories": {}}, "3.0.0": {"name": "pac-resolver", "version": "3.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "index.js", "dependencies": {"co": "^4.6.0", "degenerator": "^1.0.4", "ip": "^1.1.5", "netmask": "^1.0.6", "thunkify": "^2.1.2"}, "devDependencies": {"mocha": "^3.4.2"}, "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "e352de32eb2062a316a7b5644e02ff8029a6c48e", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@3.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tcc38bsjuE3XZ5+4vP96OfhOugrX+JcnpUbhfuc4LuXBLQhoTthOstZeoQJBDnQUDYzYmdImKsbz0xSl1/9qeA==", "shasum": "6aea30787db0a891704deb7800a722a7615a6f26", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEe3F1jxKsDN/MUzcW1KmRS76OShtRTyh1EIC0eeZtNQIgaF75jQ3G7GjYAw1H9CxDH0DlIn+IaFEE1QgE0pmQNcQ="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver-3.0.0.tgz_1497379495716_0.4557954950723797"}, "directories": {}}, "4.0.0": {"name": "pac-resolver", "version": "4.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^2.2.0", "ip": "^1.1.5", "netmask": "^1.0.6"}, "devDependencies": {"@types/debug": "4", "@types/ip": "^1.1.0", "@types/netmask": "^1.0.30", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "rimraf": "^3.0.0", "typescript": "^3.7.3"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "engines": {"node": ">= 6"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "8823438563ba8b8f1f2693cc87297ee32ebfb815", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@4.0.0", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-8LPTRFMbtMAjMETOdzB/eP68l8aD0iv0hT8BuScZ7FHLSX3njMU/5TdVqChLyLiVRfmrECRWBdBA2GkdUbNWrA==", "shasum": "40563c73d8af43b8e57e19628bf28ed413d21d4e", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-4.0.0.tgz", "fileCount": 44, "unpackedSize": 52748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOi29CRA9TVsSAnZWagAA6g0P/1JO47/tNqOWU88IL0si\nhQ8Me9KyHFOF4TZI/YtQ6Csp4lkjbx9AQp0VclcTeqWOANeQ74N4So/DCI4H\nNJICW8u5VkVn1rPG6SwOD4v0Ro8uvowEu8Wncoro8HseiaqtuzKWAcjPPSuj\n879s11ZNbxlnPBw35wHqJv6DVRflPA/iBFajbNjlh9SXCDkZDjXKse1Q9x8Y\nuDqbqmullNSNhKTS3EgNeWDed/ecXpfSuP4r0SI3c81cQAA3+lBj/PI88YW9\nTXIZM1eG4lIpSEb1lOvfHFx7Cl6CCrXG4nM0jsspzC61aU2h7a9J9JVr9S+9\nRdEvXBa8PEnFv/HwTZrxZowu1ZCV3SLJ9/YDzCQhSWwXPw66wOSNfWlcI77i\nIIpmCw6QagDO8ORbsuUmwCyEhQHxlyWBi6xbkTLmJo6T/c9cT4dcacYfm/nt\nRNgSvPQ/Ki5nC5jhDmt7Jrp6UDPuuvkOFjjFngqXZyokI1UAyRouy+CPE551\nOWYqtyqsutjWfHRCtpYn/24A1p74oHCd1iZETiYTmcw8wq62WDt5Hlqrfrxg\nD2f4zMlKDoMFYFYL3el/+V61CYXSu57zmiruaWLLw61ezbW5FSgL4n31yV4b\n8pxZQGgkDvGQG0FPA9WHx/u9/TtfO6M7b+LjEmK8pkMFVydcsBI8efP6Y0qt\nYlZe\r\n=iSLM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAS6z8WVfWrHHYAsGbV5fOMhgbHEGAmuI6Tn5PmPlMKeAiBFqg7QDIZonKyXTXbamVUD35hiWQSx4ADSEeArxVfITw=="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_4.0.0_1580871100581_0.128315174624003"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "pac-resolver", "version": "4.1.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^2.2.0", "ip": "^1.1.5", "netmask": "^1.0.6"}, "devDependencies": {"@types/debug": "4", "@types/ip": "^1.1.0", "@types/netmask": "^1.0.30", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "rimraf": "^3.0.0", "typescript": "^3.7.3"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "engines": {"node": ">= 6"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "b71ef4157a762533e9043e846f54f3876dce9faf", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@4.1.0", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-d6lf2IrZJJ7ooVHr7BfwSjRO1yKSJMaiiWYSHcrxSIUtZrCa4KKGwcztdkZ/E9LFleJfjoi1yl+XLR7AX24nbQ==", "shasum": "4b12e7d096b255a3b84e53f6831f32e9c7e5fe95", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-4.1.0.tgz", "fileCount": 44, "unpackedSize": 52895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePjHVCRA9TVsSAnZWagAAz14P/jwLNI26lsSEt0zcKe+P\nA4WpVTyXOaltVk9ECMvJc5kQiV1Wdi74TDRv1gRfJvXCg7S/SUivtMBohiKv\naYp2UZV195+nKMg0gkcyMUz6g68drhFgqkUQDRu2LXNfx2guzPE/cd422Qvc\nkCQjwof88OQGqYQCZjZNDp73BcO50ff+E/HQPqatwP90Gr4iy5+cd1ut0YMZ\nQTeJ5iiUZQ3iFZD/wpB2pKWrt/PNpK9drLigOiTh+y8gOJfSkmAOaOfBgrp5\nlNdtL/Sr8qtAqLwNLqu7lFpf5Nnl78U3mYaeqqfugDALOOnedIkYEKLDf5sm\nt/m+A7T/BGlyOZXi4Bh8B7p6WDe3q1eUelebtjncZHmaOlqorFjOfM39zK55\n0WHdxWXiz/uw/XPR216gow4Ymw9XnPk2qLI1SQpzvK20BCMl12us0NNNuCrQ\nb9gQOLQOz8g25YGm2KUywyX733UX9kZzOLQmi0Uq008qZVNt8hwTLZMGNrHD\n9AAHOlj5mdrK5/XADE6chFzN6CgnoRg7ewJMPrw9C/HFvxI21nLBgVkujkpH\nVY4T0gOeKtHv2Bndt4VGlN1/aK+HII+xWG6D8BpEv6HfOVl9NrO5vnbatzwe\n35O4wJfPYvcLALs2umxJESH4QWCh6Quarzb802JAskA5FjSSCvD+w1Mx9/KI\ne5US\r\n=PyPk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGY3E0oX1WvK3V0vd6BKoBTJZJkDD+CQMmrEqeOeCueRAiBKZiFhjgICdWqXlXXxJ+zlcg6pfouyPBRCGnXT+5mpFw=="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_4.1.0_1581134292883_0.05512118728357929"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "pac-resolver", "version": "4.1.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^2.2.0", "ip": "^1.1.5", "netmask": "^2.0.1"}, "devDependencies": {"@types/debug": "4", "@types/ip": "^1.1.0", "@types/netmask": "^1.0.30", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "rimraf": "^3.0.0", "typescript": "^3.7.3"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "engines": {"node": ">= 6"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "6be6ed5662caf5e5d3b8eb194d80c3be6f998acf", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@4.1.1", "_nodeVersion": "12.21.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-c+KyL8F9+FXzjonNAUNmx55r+85frgVCUrSkgkp2PdUquwBvEE3VKJwfvF7g8SOLOrLolYXnDhkIt/fftAKM1Q==", "shasum": "dfc8f133988900ed447ae78873126ba325077a89", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-4.1.1.tgz", "fileCount": 44, "unpackedSize": 52895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZMl4CRA9TVsSAnZWagAAnNAP/A0LTuplavk4tfixKgOp\nOYkEr6QzzTeRwvLsP9bmfqfHiVC49q3ywuFL/z9YYVy/R2a6Fp678CPGGJgU\nTEq5Zgdci+x2XMGGAFIvhsU/gOxWHRWZY0Tc8KTDpiKsN6zHaA9i4d7JeoUU\nlclUE0J58i25hDqWAVaVBd8IWYmzFoL4R17VbKBWEkHx15ZMHac5Rqxd/oqo\nwo3uX1PdJNwXwJt495DJ8R5z223IJT6tA8dckfJEvA3sYV/K3T9VmG8h3Agp\n4WWe2XSGzjIks+yAUCCVKl3yNrB4sgB6u0INmLqeE2xw4E7OzMvb53b6MOjf\nJ7yeRB4E/vzPIrNh5X10R1l46a2btw/t7N+SzyKgTyhYy5XHr7thK5sPx0WA\nsK/k8eHhcLPWGPYlm71854/CxEInoWYz3KNdYJyB910nUVBdKjp7xhci77vL\nGq6zu/aGVe80RJOfy00I2jV16mOBxvKSPD2t/AAIT5Sxsv3qyGT0wTti8gu1\nss4tSHkh3mJA7OVBz5O91goZVa/TkamJB6sNyKzlWkM4bk2WLeuN6AUCM9pF\nmzhU0XgqEsqDT2tQPTlCNf8X9/Hr/p0MAYgqccS8PqnqKcAyaSYgEmGBB58M\nDOuWKwuO0GDdAnwF8LbF1r1KU4VZDrOX6Omz5VvlTa2eEDI88mAKRHXD2EXq\nFj47\r\n=fNFH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHikTcCK9INZZiBxj0IOOjfJgitkZrsihFoGIOrGSWGUAiEA0YcgqLMAfIyHzhjW/k6iUTvJrNUrxiRHRnc7vwfTlQI="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_4.1.1_1617217912540_0.9874956051465396"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "pac-resolver", "version": "4.2.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^2.2.0", "ip": "^1.1.5", "netmask": "^2.0.1"}, "devDependencies": {"@types/debug": "4", "@types/ip": "^1.1.0", "@types/netmask": "^1.0.30", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "rimraf": "^3.0.0", "typescript": "^3.7.3"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "engines": {"node": ">= 6"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "da9aed4fb477175bf191c66cf402446826a9ba4a", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@4.2.0", "_nodeVersion": "12.21.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-rPACZdUyuxT5Io/gFKUeeZFfE5T7ve7cAkE5TUZRRfuKP0u5Hocwe48X7ZEm6mYB+bTB0Qf+xlVlA/RM/i6RCQ==", "shasum": "b82bcb9992d48166920bc83c7542abb454bd9bdd", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-4.2.0.tgz", "fileCount": 44, "unpackedSize": 52965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZMoiCRA9TVsSAnZWagAAaV4P/jmPyehwH/s/CsRqOGxD\nqokmVEapvG2qYxEj6cDlhMgFB0IGhQmKyvvF6ykF3UQjNp1v92B4Kq4TOB8+\nsAqhsmhytsjd00ZLt6KH12wYVDltWIFr0ON1x4g8dj5Iw66ZjWQ4dd3QSoUd\n9cXRdLwXR8CJckhA1noEEXKW1ObMSeln6uq1225/GiI4vo97uzZqms0mmDAG\nJnOCfyD4l1EKg/O9LiiyHGnf9ub40pK3HaVacDg0CH7Epgfc9QMAMgOgQvTD\nup0WL8cuqGfvtRf8TcCQBvHO/UcYMRP6Ma1rpjiceSuRuptTHCM+22nCRzYY\ntxnTcQCqD6KxxehKp0Bn7pWh+GeYukgJy2WIWVCVXM5EV5GNIEcoM2wl3Ao/\nWg0VSM4i1w2/4XWm2wmsObOz8AgvJla2AwtfC1yZ6cBANxHjAGNaRysaaS0N\nWC5vPzzjqi3cqCOgSZrD134qQdCuerKubPu0CY7Yf+corH5nMwrj2iCksoxA\nqA6KGrxSWuNK2BIMMyHNYVbaSHQXMSF1irLkZGwAtF7OpvkmINKj/7zkUivf\n76Xb4USII1ip3BUepcw33FDSFtKMZC+i3P8Crca+Xo5UMbHZ9iefyzXWBtcM\nJdMwEX6r/wN94zQ133ZcVL8PG2OTM2A2Wys39YGQpZ92ULrHqiyb39oey+7s\n/3R7\r\n=HVyY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC51mJ0ulMseOl4TQB4JmMO4pOLZ1COH4WWe0krsFgsdAIgCcz0iCfWJfh8+p91m5ETLx9Le9r5sbhTj+nOwCWAY2M="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_4.2.0_1617218081879_0.3859281385901163"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "pac-resolver", "version": "5.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^3.0.1", "ip": "^1.1.5", "netmask": "^2.0.1"}, "devDependencies": {"@types/debug": "4", "@types/ip": "^1.1.0", "@types/netmask": "^1.0.30", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "rimraf": "^3.0.0", "typescript": "^4.3.5"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "engines": {"node": ">= 8"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "0439ff2eb790cffd812b2abf8a5b3343ea3c89db", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@5.0.0", "_nodeVersion": "14.17.3", "_npmVersion": "7.19.1", "dist": {"integrity": "sha512-H+/A6KitiHNNW+bxBKREk2MCGSxljfqRX76NjummWEYIat7ldVXRU3dhRIE3iXZ0nvGBk6smv3nntxKkzRL8NA==", "shasum": "1d717a127b3d7a9407a16d6e1b012b13b9ba8dc0", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-5.0.0.tgz", "fileCount": 44, "unpackedSize": 53027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7LgDCRA9TVsSAnZWagAAMfgQAIuyX+WxlHTu30a1W+9i\n3bVbWpw7wlCad3fM58mWn9HpvEPJQCgnrSC7AYwFVeh9IWBMx8SLIaxoJ3le\nqSNGdbIZDUV2H5+53yqWOpMQHcQ2o0v3ZS5RvNo7VzXQpgeXxN3dA1GU9Tc+\nvRXVTTOOqkz8w59pM41+xeHqhtWj9qrm9FgBoLDYCO6DDY7op0fLoNbGS12/\n4aPuwLT2HsImXV19lF4jQLUoyM3z7jycCNwTm2ZtdTiOiq3hn2OdfiHwiJYu\nlOkGicQulRB92g/LOlcGg9e8T05fpNvET5g29O2KUdxEQuLVr+c856aG2XNM\n8geoiC8QwU1jCe75HUuiN3INNTMw/JZ3IIMfDJk84S8nzqk5rokVe/15wnHg\nCAw0L2yeXBtN9rWpCmtiq7LA0SrxsSBowjuWW7s4AWu+/kP/sZGPLFciaJww\nopD7hSZdl8IXS3AsT8N12nrAhtSN/ShqmvPkOow2eKWX/MdNUWI1qPGVlxgb\nw12zBp8Dduy6iE3iRRu4piNPsBgqyTyMf9g0oSOMKlpeMg9uRPTkiYj53/gn\nXjgSogXzlPb7oWaIgHD0IIoybUpyMC27Rq4vVmV6lvdrWUfS0RtB5oy683HQ\nxCXq+qmGPcYvTaKYMtVnTqeyHmkccIQDnOUyajA6Oe1TgW8VkYTx5aSaUF5H\ncX6T\r\n=yGYK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6eMbmaRH6rOJ15TJ65XQZnoXP52ClnwOhARpbaOE5PAiAzaaqxLhUPSGDqSTtxSpO+ODD/DGNB5USyCdVT3gyBkg=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_5.0.0_1626126339016_0.7277654948978483"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "pac-resolver", "version": "5.0.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^3.0.2", "ip": "^1.1.5", "netmask": "^2.0.2"}, "devDependencies": {"@types/debug": "4", "@types/ip": "^1.1.0", "@types/netmask": "^1.0.30", "@types/node": "^17.0.19", "@typescript-eslint/eslint-plugin": "5.12.0", "@typescript-eslint/parser": "5.12.0", "eslint": "^8.9.0", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "8.4.0", "eslint-import-resolver-typescript": "2.5.0", "eslint-plugin-import": "2.25.4", "mocha": "^9.2.1", "rimraf": "^3.0.2", "typescript": "^4.5.5"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha --reporter spec", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-pac-resolver.git"}, "engines": {"node": ">= 8"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-pac-resolver/issues"}, "gitHead": "f483e349205a9a13f7f6ff3445c2187a3b77a55f", "homepage": "https://github.com/TooTallNate/node-pac-resolver#readme", "_id": "pac-resolver@5.0.1", "_nodeVersion": "16.15.0", "_npmVersion": "8.10.0", "dist": {"integrity": "sha512-cy7u00ko2KVgBAjuhevqpPeHIkCIqPe1v24cydhWjmeuzaBfmUWFCZJ1iAh5TuVzVZoUzXIW7K8sMYOZ84uZ9Q==", "shasum": "c91efa3a9af9f669104fa2f51102839d01cde8e7", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-5.0.1.tgz", "fileCount": 44, "unpackedSize": 53180, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICaXbcHuXu1O0I59BpwXDfiomiiX83y7+5MIHg0T+tvaAiA7b39qR0sjg28FLxeabGkIlqeijmz05V0o9c0FiNlgIQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijm1fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqv3A//as8S7qWRHJRsG4UtuyRSRom9kykewxVMuJ+qAW+n8ZpJxLGy\r\ndW3YFB9pYrW8prk6q20o95YQUsoXDHzZZXFjUaFl0nWBSFpq3p6m0KdszOsm\r\nE0iKBCehRqILSwe+dhp9Q8hySmzCmppEzpHiiBhl8r4miFJIhiYViVYUWTfn\r\nGmenVIQAclCV9NurLe5MDyVF0Zsxw2cmuEHw04HcjjeaYXZ8h4ws4QuYIjhC\r\nJ49E6vJ4NHqjjsfPJnsLR5u7oIh+0LLVhSI0DJ1zOdXklAus+7d4FzPCuQEQ\r\neXcSHY67QRrsBjTkDMvAl9CajDec3lVOviTrQzlUMB/rw7+jU+VFrzyoEZC7\r\nfl7s9kbpktXrLoOQkDSuUy4QfZ29LPy19XTodQJj/Sjcq7/3uUJQwmjZnTo8\r\nYobJ2V9JsF1z+8XAFjR9eYTW+A0bnbArn301MLG1ahg9BTTPJ9pRo2EmEfds\r\nFRawp1AcNy05+BJK9DyVC500h3Ug4tq+VV5xaHsKnM/8dZCXUHhzeG1QD8e3\r\nqyIw33JzztyBZfI6oW2nV942/XOGg2g6CRDuAgDFXpmsoyktcXrW/6pf1EKN\r\nPw3OGBK0paCORc27BVZyt3N4qR4vGD+XjdUPINN4uKWGr/OIDEIm3gcbaMMk\r\nkBndlJA22HlVU5bb38tbdlDWwAaGNpO3H+A=\r\n=TiAI\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_5.0.1_1653501279273_0.12191128596165335"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "pac-resolver", "version": "6.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^4.0.0", "ip": "^1.1.5", "netmask": "^2.0.2"}, "devDependencies": {"@types/ip": "^1.1.0", "@types/jest": "^29.5.1", "@types/netmask": "^1.0.30", "@types/node": "^14.18.43", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "engines": {"node": ">= 14"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "pac-resolver@6.0.0", "_integrity": "sha512-1RjGH42MfLpVN58LRmUz1AOBOTkHy/Z8GT+7zgfqJ7M2KgJdCwjU7hiOnVcw/AYZLbsC32cVvL8O6hnfni3lSA==", "_resolved": "/tmp/91de22462b4ff21e799cfef43daefcc9/pac-resolver-6.0.0.tgz", "_from": "file:pac-resolver-6.0.0.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-1RjGH42MfLpVN58LRmUz1AOBOTkHy/Z8GT+7zgfqJ7M2KgJdCwjU7hiOnVcw/AYZLbsC32cVvL8O6hnfni3lSA==", "shasum": "df1ad3e89b2588460f3d0d55b13e311936bb6ab1", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-6.0.0.tgz", "fileCount": 59, "unpackedSize": 58174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNOBCk5ANUn4hhupVP6PGc2yUoA+UH4iMl7XV3SvV2hAiEAq6C4y/Hg/WnLbeeXM1IPBhYthwJ7grPsJejgMM+TKVc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLpw//dSW1aobMJHt6QpKUkCpSAm7fd07DY3vI0FAHZAbcCvh6SHTX\r\nyRxHRRo/GQFUn/fw6qTOQdKfdvQJrNHJgmdlGJ087qtyodH9UQOTK9qG8O7V\r\n3fzyOz/2b/Y3qQYbp4FpqLttEXd5OIi8UP/UP1zhFaFvK2IS7eWU4PXk6s1J\r\nGsNX9MyxVbkHu4GXKEzimqcANVW1d6p/GVXrQEarUWdXo6z4r4pIdgE5HZZQ\r\ndsFvfMYGMCQFFzOpDyslGUGmYLTU/VJjGIhk9AGeAyuD+jsesCFyqShlAIlY\r\nkXh7ueH8+Wja8HHlpgkw6w3h60x0L07yl1XAJD3tnY+HL7lLUgXM01YitsLL\r\nsZoShYogCwoa5Z04qxxlwA04ZXiv0wiru3sq9nf+BuUUYh4MXQjZE9O0K6RE\r\nYKt6BBLRyeCYEV0+oZPsJzaz0N3EhT60p51GG4Fb16SLrmuqmJWTr7Q3NONL\r\ngoOLQ4hyqdwCxunJJwXN3sFOONrgxLHtzBmXHxyrkIRt7l6USfwqvU8XEmOp\r\nL4qPrLadgM7h55B8QuL6DnGDrThnm/0vToCl6n6/MaHWNujoOBYnJX5sy0/G\r\nOZ4IqhGgV2qpBgQpmsv76TPa/Og9o6y4Tw8nrgongIz5D9EHpvGUtrnFQb/0\r\n/aiFcBmcZHBWuyBxE0D8UqtSB14VNtDyjQA=\r\n=TmB8\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_6.0.0_1683232399694_0.5609737294869028"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "pac-resolver", "version": "6.0.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^4.0.1", "ip": "^1.1.5", "netmask": "^2.0.2"}, "devDependencies": {"@types/ip": "^1.1.0", "@types/jest": "^29.5.1", "@types/netmask": "^1.0.30", "@types/node": "^14.18.45", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "engines": {"node": ">= 14"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "pac-resolver@6.0.1", "_integrity": "sha512-dg497MhVT7jZegPRuOScQ/z0aV/5WR0gTdRu1md+Irs9J9o+ls5jIuxjo1WfaTG+eQQkxyn5HMGvWK+w7EIBkQ==", "_resolved": "/tmp/602c002e3ac682594eabbc4564e6a275/pac-resolver-6.0.1.tgz", "_from": "file:pac-resolver-6.0.1.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-dg497MhVT7jZegPRuOScQ/z0aV/5WR0gTdRu1md+Irs9J9o+ls5jIuxjo1WfaTG+eQQkxyn5HMGvWK+w7EIBkQ==", "shasum": "319c182d3db4e6782e79519cb4dd1dda46579292", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-6.0.1.tgz", "fileCount": 59, "unpackedSize": 58174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCay5XacZIJSjCw+RR82zxj+YzzIV7YA19Z9oAWziwNPAIgabAeXt4XQKKdFH4lmI7yeOCS6bedwXMFeDmIVjxtjFw="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_6.0.1_1683324250263_0.2035624169904009"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "pac-resolver", "version": "6.0.2", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^4.0.4", "ip": "^1.1.8", "netmask": "^2.0.2"}, "devDependencies": {"@types/ip": "^1.1.0", "@types/jest": "^29.5.2", "@types/netmask": "^1.0.30", "@types/node": "^14.18.52", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.6", "tsconfig": "0.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "engines": {"node": ">= 14"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "pac-resolver@6.0.2", "_integrity": "sha512-EQpuJ2ifOjpZY5sg1Q1ZeAxvtLwR7Mj3RgY8cysPGbsRu3RBXyJFWxnMus9PScjxya/0LzvVDxNh/gl0eXBU4w==", "_resolved": "/tmp/64f035b75a0dd7fe8f667cf89ea66b62/pac-resolver-6.0.2.tgz", "_from": "file:pac-resolver-6.0.2.tgz", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-EQpuJ2ifOjpZY5sg1Q1ZeAxvtLwR7Mj3RgY8cysPGbsRu3RBXyJFWxnMus9PScjxya/0LzvVDxNh/gl0eXBU4w==", "shasum": "742ef24d2805b18c0a684ac02bcb0b5ce9644648", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-6.0.2.tgz", "fileCount": 59, "unpackedSize": 58174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhBFvFuZxUPuGWnagKpsoAdNE3HYSX60Jxrn4jTzuXJAiBrdu6MUxlxLCsRxQ0mvY+eA9y2dJF7Yx12zeSOkQ+xSA=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_6.0.2_1688076078280_0.2537146201090754"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "pac-resolver", "version": "7.0.0", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^5.0.0", "ip": "^1.1.8", "netmask": "^2.0.2"}, "devDependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "@types/ip": "^1.1.0", "@types/jest": "^29.5.2", "@types/netmask": "^1.0.30", "@types/node": "^14.18.52", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.6", "tsconfig": "0.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "engines": {"node": ">= 14"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "pac-resolver@7.0.0", "_integrity": "sha512-Fd9lT9vJbHYRACT8OhCbZBbxr6KRSawSovFpy8nDGshaK99S/EBhVIHp9+crhxrsZOuvLpgL1n23iyPg6Rl2hg==", "_resolved": "/tmp/18baf3454c85b8d9b25a7da4aa139b93/pac-resolver-7.0.0.tgz", "_from": "file:pac-resolver-7.0.0.tgz", "_nodeVersion": "20.4.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-Fd9lT9vJbHYRACT8OhCbZBbxr6KRSawSovFpy8nDGshaK99S/EBhVIHp9+crhxrsZOuvLpgL1n23iyPg6Rl2hg==", "shasum": "79376f1ca26baf245b96b34c339d79bff25e900c", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-7.0.0.tgz", "fileCount": 59, "unpackedSize": 58245, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcNry6vOXNEbzhTBHAGViFRgWV6X+YovWC29QeVi9bxAIgB05UI/86Ov9IITwvtD9Ij0U2GXrkf96M+bgKko8sPQ8="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_7.0.0_1689671055121_0.6187582781443213"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "pac-resolver", "version": "7.0.1", "description": "Generates an asynchronous resolver function from a PAC file", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"degenerator": "^5.0.0", "netmask": "^2.0.2"}, "devDependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "@types/jest": "^29.5.2", "@types/netmask": "^1.0.30", "@types/node": "^14.18.52", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.1.6", "tsconfig": "0.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "engines": {"node": ">= 14"}, "keywords": ["pac", "file", "proxy", "resolve", "dns"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "pac-resolver@7.0.1", "_integrity": "sha512-5NPgf87AT2STgwa2ntRMr45jTKrYBGkVU36yT0ig/n/GMAa3oPqhZfIQ2kMEimReg0+t9kZViDVZ83qfVUlckg==", "_resolved": "/tmp/2a13877ef12d672f31db2dc5fd862322/pac-resolver-7.0.1.tgz", "_from": "file:pac-resolver-7.0.1.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-5NPgf87AT2STgwa2ntRMr45jTKrYBGkVU36yT0ig/n/GMAa3oPqhZfIQ2kMEimReg0+t9kZViDVZ83qfVUlckg==", "shasum": "54675558ea368b64d210fd9c92a640b5f3b8abb6", "tarball": "https://registry.npmjs.org/pac-resolver/-/pac-resolver-7.0.1.tgz", "fileCount": 64, "unpackedSize": 61877, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGOZhjeerMOECxMgSsksXkMez/T+Kl9HJ6ttsRQftVTrAiBrrCdsOlgbc6X2nJTz4JO3LZ+/ovtZIfNNBSjTdlSMUA=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/pac-resolver_7.0.1_1707762281214_0.7474798611777915"}, "_hasShrinkwrap": false}}, "readme": "pac-resolver\n============\n### Generates an asynchronous resolver function from a [PAC file][pac-wikipedia]\n\n\nThis module accepts a JavaScript String of code, which is meant to be a\n[PAC proxy file][pac-wikipedia], and returns a generated asynchronous\n`FindProxyForURL()` function.\n\nExample\n-------\n\nGiven the PAC proxy file named `proxy.pac`:\n\n```js\nfunction FindProxyForURL(url, host) {\n  if (isInNet(myIpAddress(), \"*********\", \"*************\")) {\n    return \"PROXY *******:8080\";\n  } else {\n    return \"DIRECT\";\n  }\n}\n```\n\nYou can consume this PAC file with `pac-resolver` like so:\n\n```ts\nimport { readFileSync } from 'fs';\nimport { createPacResolver } from 'pac-resolver';\n\nconst FindProxyForURL = createPacResolver(readFileSync('proxy.pac'));\n\nconst res = await FindProxyForURL('http://foo.com/');\nconsole.log(res);\n// \"DIRECT\"\n```\n\n\nAPI\n---\n\n### pac(qjs: QuickJSWASMModule, pacFileContents: string | Buffer, options?: PacResolverOptions) → Function\n\nReturns an asynchronous `FindProxyForURL()` function based off of the given JS\nstring `pacFileContents` PAC proxy file. An optional `options` object may be\npassed in which respects the following options:\n\n * `filename` - String - the filename to use in error stack traces. Defaults to `proxy.pac`.\n * `sandbox` - Object - a map of functions to include in the sandbox of the\n JavaScript environment where the JS code will be executed. i.e. if you wanted to\n include the common `alert` function you could pass `alert: console.log`. For\n async functions, you must set the `async = true` property on the function\n instance, and the JS code will be able to invoke the function as if it were\n synchronous.\n\n The `qjs` parameter is a QuickJS module instance as returned from `getQuickJS()` from the `quickjs-emscripten` module.\n\n[pac-file-docs]: https://web.archive.org/web/20070602031929/http://wp.netscape.com/eng/mozilla/2.0/relnotes/demo/proxy-live.html\n[pac-wikipedia]: http://wikipedia.org/wiki/Proxy_auto-config\n", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "time": {"modified": "2024-02-12T18:24:41.610Z", "created": "2013-12-09T21:44:42.130Z", "0.0.1": "2013-12-09T21:44:50.924Z", "0.0.2": "2013-12-30T04:36:41.433Z", "1.0.0": "2014-01-08T22:17:07.171Z", "1.1.0": "2014-01-25T18:52:51.513Z", "1.2.0": "2014-01-28T22:27:58Z", "1.2.1": "2014-04-04T18:54:18.474Z", "1.2.2": "2014-05-21T05:24:29.908Z", "1.2.3": "2014-11-22T20:32:17.568Z", "1.2.4": "2014-11-22T20:37:05.972Z", "1.2.5": "2015-02-20T20:58:07.035Z", "1.2.6": "2015-02-22T05:29:12.430Z", "1.3.0": "2016-09-26T16:30:14.432Z", "2.0.0": "2016-11-09T23:48:05.994Z", "3.0.0": "2017-06-13T18:44:55.822Z", "4.0.0": "2020-02-05T02:51:40.830Z", "4.1.0": "2020-02-08T03:58:13.091Z", "4.1.1": "2021-03-31T19:11:52.666Z", "4.2.0": "2021-03-31T19:14:42.092Z", "5.0.0": "2021-07-12T21:45:39.175Z", "5.0.1": "2022-05-25T17:54:39.460Z", "6.0.0": "2023-05-04T20:33:19.893Z", "6.0.1": "2023-05-05T22:04:10.467Z", "6.0.2": "2023-06-29T22:01:18.454Z", "7.0.0": "2023-07-18T09:04:15.307Z", "7.0.1": "2024-02-12T18:24:41.429Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-resolver"}, "readmeFilename": "README.md", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["pac", "file", "proxy", "resolve", "dns"], "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "license": "MIT"}