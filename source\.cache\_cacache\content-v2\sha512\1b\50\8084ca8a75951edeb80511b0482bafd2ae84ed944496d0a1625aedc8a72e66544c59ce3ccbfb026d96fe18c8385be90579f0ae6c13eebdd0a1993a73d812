{"_id": "debug", "_rev": "960-4cdf626858b30d308cc71aaeb189ff89", "name": "debug", "dist-tags": {"beta": "4.3.3", "latest": "4.4.1"}, "versions": {"0.0.1": {"name": "debug", "version": "0.0.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0faa51ad6dec7587159b532cdf18d74261376417", "tarball": "https://registry.npmjs.org/debug/-/debug-0.0.1.tgz", "integrity": "sha512-wm1jCOnbiFSvX8u+NMV+mD6CSOFgGlAPTYw3aoJgDoh2OBSIwMuz0ayedqbNhU3irew6bDBDA+9ia313ZPAEZA==", "signatures": [{"sig": "MEYCIQDuay0fYjn6UmRkk1waVRl8oHeMW1CYg8wJkYKiHzoJ0AIhAIWA7WKkaBpZ20RtA3h2omGhFbOqqjE7Sext22nCwbSK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.104", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.6.3", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.1.0": {"name": "debug", "version": "0.1.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3026f197b98b823cb51209f3758eb1498a66442c", "tarball": "https://registry.npmjs.org/debug/-/debug-0.1.0.tgz", "integrity": "sha512-NLrWdwRTCljTP6KSk3Lg5EL0QKKt9nqfM12NSPXAlvxtCq+w5OEnHkj+8qDgaKFZ3DX3Bp20MwmSBY1lGkkyLA==", "signatures": [{"sig": "MEUCIQC/NjKJTGYgppm43LuDNAYhFGgNj/hvq2XLFLM0G8VBxgIgTbwjjTDcC/SPrX6CvvSJeahcd1d5N3EClrtBgmjM4Dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.104", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.2.0": {"name": "debug", "version": "0.2.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b6bbca5a6b41f6d3f3ba6604e2737a9ea3329e7f", "tarball": "https://registry.npmjs.org/debug/-/debug-0.2.0.tgz", "integrity": "sha512-GHNutIi2PtfsfkaFV12nt2iG2KI5GDsHcv/KWanLqQxWj1s6hrC2Ihyqr9wTn/7AscXbPquJ1C/sEbhJhAxRlg==", "signatures": [{"sig": "MEUCIDN40IFZw6R/+76LmY5pLEDKfc7QrpNmeTLxSTUmN5g/AiEAoAxen3d0Vy1XyWACkdNW+Ck9CDqZSJWyLweXaTjgIoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.6.7", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.3.0": {"name": "debug", "version": "0.3.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.3.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7818fac891b38d81bb7dd97e3d969992e654e835", "tarball": "https://registry.npmjs.org/debug/-/debug-0.3.0.tgz", "integrity": "sha512-yFnB6fZDgWBNalpbJusPhWBxamQIyLCm2czSKFphn1Efrbgsoh7FNfVpdFBee0ZVMO90Uq32fRn/8LNu00n1PQ==", "signatures": [{"sig": "MEQCIHBmSXrDUqgdhsLOGTA92iXAx3lwjw3FtIoEJj3UMCNUAiB2zlLebEWwWNbv/ZhVVOJbuqCvz0U+A6Kz+KNNekDnnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.4.0": {"name": "debug", "version": "0.4.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.4.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c7e8c63eb391820133869d3cc43aa9992fea288f", "tarball": "https://registry.npmjs.org/debug/-/debug-0.4.0.tgz", "integrity": "sha512-fN7BwVwD6luMIhe0x3sZpUBA3kmi7Y1WrYkuBSM9y7SNVbTyPJftbF0S/f02vTl9jSPHw5G3DKhREKtXSKT6DA==", "signatures": [{"sig": "MEYCIQCzJaVWcIdTAeHIxzu2GNs1aTG70NdKhCouvtL4V+aRsQIhALridRiNOrJkTNcR35kskTLU5mnynlMl5EG4IRqunocW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.4.1": {"name": "debug", "version": "0.4.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.4.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "33a47f028daa312d885be4db8649fa1b4280125d", "tarball": "https://registry.npmjs.org/debug/-/debug-0.4.1.tgz", "integrity": "sha512-vvcfF/pEWD7QnwQZ7nX3T6nTbJ+tYdMK3vHi8DCivuw9se3hoHo1DCYoSTxXXpOBAH1tKi3prJ3e7V3Jw5Ckzg==", "signatures": [{"sig": "MEYCIQCAclaLCmbaT625zbEd6DWUCCCqmPORmu+VeVg19Dau1QIhANf2Xn6EB6RoH/b0KBI3HBmRKnaDkaChDWXkRmdZTZkT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.5.0": {"name": "debug", "version": "0.5.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.5.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9d48c946fb7d7d59807ffe07822f515fd76d7a9e", "tarball": "https://registry.npmjs.org/debug/-/debug-0.5.0.tgz", "integrity": "sha512-5xwa00fC8jw+qiSnXWrjzqtNyTwDIC+N9BPQHKaj0rzPclk4HJ//H1aAta1+YVjc1+z3yj3giHI93fr+4vvOBQ==", "signatures": [{"sig": "MEYCIQCNLqHzy19o35OaakCTa6d9cdO0oLXPUbgvrbksmexXnwIhAKmxrIISxQpUVEjFxlM08VPwV+AOjH5Q3Ec+UVRDPq90", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.0.106", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true}, "0.6.0": {"name": "debug", "version": "0.6.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.6.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ce9d5d025d5294b3f0748a494bebaf3c9fd8734f", "tarball": "https://registry.npmjs.org/debug/-/debug-0.6.0.tgz", "integrity": "sha512-2vIZf67+gMicLu8McscD1NNhMWbiTSJkhlByoTA1Gw54zOb/9IlxylYG+Kr9z1X2wZTHh1AMSp+YiMjYtLkVUA==", "signatures": [{"sig": "MEYCIQCQHcbLA3nClRIksWsULYCfoHldkAkkLa6q/8cKKg/V2gIhAPEEYwNGJ7y/JYVxG9xm5lV5d7oDDzGbBagbQXnVMVJI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "1.1.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "v0.6.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "*"}, "_engineSupported": true, "optionalDependencies": {}}, "0.7.0": {"name": "debug", "version": "0.7.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.7.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f5be05ec0434c992d79940e50b2695cfb2e01b08", "tarball": "https://registry.npmjs.org/debug/-/debug-0.7.0.tgz", "integrity": "sha512-UWZnvGiX9tQgtrsA+mhGLKnUFvr1moempl9IvqQKyFnEgN0T4kpCE+KJcqTLcVxQjRVRnLF3VSE1Hchki5N98g==", "signatures": [{"sig": "MEUCIQDMsFUCVvtu8IxS3ZUv7RW+hau5743N3/dFtlGunDMXOQIgYD0T/M6f40w6Pp6RFzkQn87Xg72fI/BfjBDWnmvRz3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index", "engines": {"node": "*"}, "component": {"scripts": {"debug": "debug.component.js"}}, "browserify": "debug.component.js", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "0.7.1": {"name": "debug", "version": "0.7.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.7.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d2253d37f2da6618f95c353a55fe0ab28c1c1e96", "tarball": "https://registry.npmjs.org/debug/-/debug-0.7.1.tgz", "integrity": "sha512-Zuj7MDrrvChh4QJt1x03j3PAJQcHi9iGSG15E59H/I+I3AtDOsZh+I6NG2KpddCBy/zQlBuoehXvBtywwKWe1A==", "signatures": [{"sig": "MEUCIQDxjywCGtVNgsVeKScuALb7Kig2zVOK5cvMv9MtriiGAgIgQ3NzHWAkvhxzraZJ1SmeiY7F1hNkPqCe4dK/tdUiCXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/debug.js", "_from": ".", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "browserify": "debug.js", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "0.7.2": {"name": "debug", "version": "0.7.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.7.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "056692c86670977f115de82917918b8e8b9a10f0", "tarball": "https://registry.npmjs.org/debug/-/debug-0.7.2.tgz", "integrity": "sha512-Ch0X6QrHzrNiWwLsBJj9KgXL5IK67pfDyTsXXVPyrdObVyKuj/rPdCtZg761nHZM1GQ7wW/o9cAZf5JeTN/vRg==", "signatures": [{"sig": "MEQCICNm9qHiSXIPG2Mmi8l/7vCWbeUUrixXq0sMLfjCnjWNAiBNICHbzM2NlOBRLSff0NjHa3zSUGfJhvgzJGGBAaP3RQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/debug.js", "_from": ".", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "index.js"}}, "browserify": "debug.js", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.2.2", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "0.7.3": {"name": "debug", "version": "0.7.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.7.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "ba7ae369799066a28d234fb8dad6f05837839da8", "tarball": "https://registry.npmjs.org/debug/-/debug-0.7.3.tgz", "integrity": "sha512-kmMlLkXbeTeQlihhfXraOJMDImxDpFyo36vGq4LBepdq5+TwLwnupy1hI0ykK1A52WfDgmO4XJ0iYIiEkmSquA==", "signatures": [{"sig": "MEYCIQCmSp8Fe8A9ZsKwKGkDC77hoQHfVw67MJXR2Uq+DG6IFwIhAMVNEx8RDgNetxTqXjHOwVkuLYZWK6jRPxKIQuDoNq6p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/debug.js", "_from": ".", "files": ["lib/debug.js", "debug.js", "index.js"], "browser": "./debug.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "index.js"}}, "browserify": "debug.js", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "0.7.4": {"name": "debug", "version": "0.7.4", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.7.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "06e1ea8082c2cb14e39806e22e2f6f757f92af39", "tarball": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz", "integrity": "sha512-EohAb3+DSHSGx8carOSKJe8G0ayV5/i609OD0J2orCkuyae7SyZSz2aoLmQF2s0Pj5gITDebwPH7GFBlqOUQ1Q==", "signatures": [{"sig": "MEQCICkeWz6458EaJADy7dQLi7ui9ftLhoxnZuqTWTNzC7x9AiBiZIWgGZg0GZIFcikmAvogqSytPiOSw1r782hEJp2bEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/debug.js", "_from": ".", "files": ["lib/debug.js", "debug.js", "index.js"], "browser": "./debug.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "index.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.3.13", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "0.8.0": {"name": "debug", "version": "0.8.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.8.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "0541ea91f0e503fdf0c5eed418a32550234967f0", "tarball": "https://registry.npmjs.org/debug/-/debug-0.8.0.tgz", "integrity": "sha512-jR+JRuwlhTwNPpLU6/JhiMydD6+GnL/33WE8LlmnBUqPHXkEpG2iNargYBO/Wxx7wXn7oxU6XwYIZyH4YuSW9Q==", "signatures": [{"sig": "MEQCIBf0fOu4Jg4LZeR0298qmNgiUw2R2CPwkKw4ZKHUr9RCAiAkJ7SEDFM6BuwT+UxcE03B7s7zUZmVnCTwwCfCKP5jMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/debug.js", "_from": ".", "files": ["lib/debug.js", "debug.js"], "browser": "./debug.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.3.15", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "0.8.1": {"name": "debug", "version": "0.8.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@0.8.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "20ff4d26f5e422cb68a1bacbbb61039ad8c1c130", "tarball": "https://registry.npmjs.org/debug/-/debug-0.8.1.tgz", "integrity": "sha512-HlXEJm99YsRjLJ8xmuz0Lq8YUwrv7hAJkTEr6/Em3sUlSUNl0UdFA+1SrY4fnykeq1FVkUEUtwRGHs9VvlYbGA==", "signatures": [{"sig": "MEUCIQDdkl85fIbnLZdTtzAvbLJMT9LLyaaIPhe4Pncj4W012wIgYC1CZp4bp5DcrhEkVPhKMUdbOWbMAYMOQFaf6fMWpLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/debug.js", "_from": ".", "files": ["lib/debug.js", "debug.js"], "browser": "./debug.js", "engines": {"node": "*"}, "_npmUser": {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"debug/index.js": "debug.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "small debugging utility", "directories": {}, "dependencies": {}, "devDependencies": {"mocha": "*"}}, "1.0.0": {"name": "debug", "version": "1.0.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@1.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "553678b67494cacc2d5330c24dfb2f275b1ceb5a", "tarball": "https://registry.npmjs.org/debug/-/debug-1.0.0.tgz", "integrity": "sha512-90ovcUGlapDDKhEeeAlmT/+/R+BECtyGz+l3dYyl05HOaMj/s03bQpOScs49ouWNnpcDQVeBk28h/vuDnbvdxw==", "signatures": [{"sig": "MEYCIQDx5CgUTRbjyhs52h4JOJgEpOQsTAnW+LY+qfq5JuDonwIhAP9WAiLlY1aAxaT/sRFjD9CiXzDQbIGlQDI2Hi0iaJ2z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "553678b67494cacc2d5330c24dfb2f275b1ceb5a", "browser": "./browser.js", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "4.1.6"}}, "1.0.1": {"name": "debug", "version": "1.0.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@1.0.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "3c03d40462f0be20468e4f77dd3f2bf7a722cfb7", "tarball": "https://registry.npmjs.org/debug/-/debug-1.0.1.tgz", "integrity": "sha512-Se3uhnI9TBNE+wy7bD2kQHvJR5oY6ARosn0UWOHZkcq5TKG7GYzThuluyJ+UbjAztbtm/XlBrvQtnFx+Ll/pxg==", "signatures": [{"sig": "MEUCIQDtu/CEGuaayxVxG2LdWWDOBijjtiPx3vL34jd51mhbTAIgFgMKMbFZ4A2bIzw5YuVxGXgwiHYjgPvITSwTNl8XKBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "3c03d40462f0be20468e4f77dd3f2bf7a722cfb7", "browser": "./browser.js", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "4.1.6"}}, "1.0.2": {"name": "debug", "version": "1.0.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@1.0.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "3849591c10cce648476c3c7c2e2e3416db5963c4", "tarball": "https://registry.npmjs.org/debug/-/debug-1.0.2.tgz", "integrity": "sha512-T9bufXIzQvCa4VrTIpLvvwdLhH+wuBtvIJJA3xgzVcaVETGmTIWMfEXQEd1K4p8BaRmQJPn6MPut38H7YQ+iIA==", "signatures": [{"sig": "MEUCIGGC1XZco0xkNQ9Auya9IjhIHw3Cb6BD1mI3IJso/vREAiEAsi7HBFM3gVC30/j18oL2iKXGX8zk8aGz7N2Ah4/2pYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "3849591c10cce648476c3c7c2e2e3416db5963c4", "browser": "./browser.js", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "4.1.6"}}, "1.0.3": {"name": "debug", "version": "1.0.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@1.0.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "fc8c6b2d6002804b4081c0208e0f6460ba1fa3e4", "tarball": "https://registry.npmjs.org/debug/-/debug-1.0.3.tgz", "integrity": "sha512-MltK7Ykj/udtD728gD/RrONStwVnDpBNIP1h+CBcnwnJdHqHxfWHI1E8XLootUl7NOPAYTCCXlb8/Qmy7WyB1w==", "signatures": [{"sig": "MEQCIGQWLj1KYCR8QfuK0qwP0vbuD0mCeRrCYc9Y8xhgXyqBAiBcfRmDoZkUXJ5m+u4LG1NuPP5om+24bz8XLTacWu1dig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "fc8c6b2d6002804b4081c0208e0f6460ba1fa3e4", "browser": "./browser.js", "gitHead": "93c759961d53ad7f06a1892a8dd0bf4be4a7b9df", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "4.1.6"}}, "1.0.4": {"name": "debug", "version": "1.0.4", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@1.0.4", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "5b9c256bd54b6ec02283176fa8a0ede6d154cbf8", "tarball": "https://registry.npmjs.org/debug/-/debug-1.0.4.tgz", "integrity": "sha512-plA8d2GHafT7kXzMDs5r7NSfYP7IKHdO8rZPVAqI33Eum7Vq/Ef/ETXm6NncF/RMif4fzI0RetSArZ6PMIxP0g==", "signatures": [{"sig": "MEYCIQCm7SG7YoT8UWfNHfEKKpD8uisrowyLGDNGp1TVFP0pyAIhAKHzpLZolillsf8yjHcTL8oLoPhJFMmqhfOUrp50sco/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "5b9c256bd54b6ec02283176fa8a0ede6d154cbf8", "browser": "./browser.js", "gitHead": "abc10a5912f79d251752d18350e269fe0b0fbbf9", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "4.1.6"}}, "2.0.0": {"name": "debug", "version": "2.0.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@2.0.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "89bd9df6732b51256bc6705342bba02ed12131ef", "tarball": "https://registry.npmjs.org/debug/-/debug-2.0.0.tgz", "integrity": "sha512-jRxFR0Fb657ikmm6IjHY32v/Nqp9Ndcx4LBISXPfpguNaHh5JJnb+x37qalKPTu4fxMFnVBIyEGi72mmvl0BCw==", "signatures": [{"sig": "MEUCIES2PR3V2i9gupa5FUYgVKHKTPXHFEji0JxHZaiv/mGCAiEA0tjFO6psg0EsAHb60w1eQUUPLkZAaQNuqB2FU/GY/ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "89bd9df6732b51256bc6705342bba02ed12131ef", "browser": "./browser.js", "gitHead": "c61ae82bde19c6fdedfc6684817ff7eb541ff029", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "5.11.0"}}, "2.1.0": {"name": "debug", "version": "2.1.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.1.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "33ab915659d8c2cc8a41443d94d6ebd37697ed21", "tarball": "https://registry.npmjs.org/debug/-/debug-2.1.0.tgz", "integrity": "sha512-mXKNuRulIxh5zRPbJ0znN6gOJljoA1I/pQaZS9QYCwM4LdeInk5sEioHFeLayLJg8YL+FNrwPZbbltDR/HIdGA==", "signatures": [{"sig": "MEQCIG5W1DE04uDl2qreKrTC+q/QryusXSKi9M65wlsOVLQDAiAZhLJNNcR0dpUjTlW44zJ8SNVIr+4hiKQwggX3KYHJgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "33ab915659d8c2cc8a41443d94d6ebd37697ed21", "browser": "./browser.js", "gitHead": "953162b4fa8849268d147f4bac91c737baee55bb", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "2.1.3", "description": "small debugging utility", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "6.1.0"}}, "2.1.1": {"name": "debug", "version": "2.1.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.1.1", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "e0c548cc607adc22b537540dc3639c4236fdf90c", "tarball": "https://registry.npmjs.org/debug/-/debug-2.1.1.tgz", "integrity": "sha512-<PERSON>O<PERSON><PERSON><PERSON>+gc7PHrK3cZSYzASfIbTK0bMRs78/Bkjnu+sfSPxEbh/b2qcl27EKRYSK73W6Ju4QfaNHz5fnLXQKEhg==", "signatures": [{"sig": "MEYCIQCkwincfTYHKtPbwSg0taDIyY4W23O2F9ixfhr2ESPhugIhANHTZtAmdTO4TPMdBaBjQKk5zVIPYe6F2YrEco5RIPnx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "e0c548cc607adc22b537540dc3639c4236fdf90c", "browser": "./browser.js", "gitHead": "24cc5c04fc8886fa9afcadea4db439f9a6186ca4", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.6.2"}, "devDependencies": {"mocha": "*", "browserify": "6.1.0"}}, "2.1.2": {"name": "debug", "version": "2.1.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.1.2", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "d5853ec48011eafd9ec80a5c4733332c1e767a43", "tarball": "https://registry.npmjs.org/debug/-/debug-2.1.2.tgz", "integrity": "sha512-1MYjCALu7t4xPIWMoEDkUMpNpLl9WRZYWO2oXqq+zuQkBUokH5YwbKCCoNUBWwrG4uxXp2gwShVh5nxd0dgxYg==", "signatures": [{"sig": "MEUCIQCkF33Zvf4MYTWaAgK9lV8EfdBor/lUyaPsCa/a3YENwwIgbdbBnzMc/wOVFEy+GNV1+5ELiKCKhjU+Nq72PBfTHyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "d5853ec48011eafd9ec80a5c4733332c1e767a43", "browser": "./browser.js", "gitHead": "ef0b37817e88df724511e648c8c168618e892530", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "small debugging utility", "directories": {}, "dependencies": {"ms": "0.7.0"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}}, "2.1.3": {"name": "debug", "version": "2.1.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.1.3", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "ce8ab1b5ee8fbee2bfa3b633cab93d366b63418e", "tarball": "https://registry.npmjs.org/debug/-/debug-2.1.3.tgz", "integrity": "sha512-KWau3VQmxO3YwQCjJzMPPusOtI0hx3UGsqnY7RS+QHQjUeawpOVtJvAdeTrI2Ja5DTR8KH3xaEN8c+ADbXJWeg==", "signatures": [{"sig": "MEUCIQDgaGTPhMvwU8Iu1VaqmY5zbroySKtg9s+iNW+bA90fagIgIJkTF36bQ5fYZRk0IVXV5ydF0ijnKJFwpjRDNAKX3bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "ce8ab1b5ee8fbee2bfa3b633cab93d366b63418e", "browser": "./browser.js", "gitHead": "0a8e4b7e0d2d1b55ef4e7422498ca24c677ae63a", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "small debugging utility", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"ms": "0.7.0"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}}, "2.2.0": {"name": "debug", "version": "2.2.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.2.0", "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "f87057e995b1a1f6ae6a4960664137bc56f039da", "tarball": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz", "integrity": "sha512-X0rGvJcskG1c3TgSCPqHJ0XJgwlcvOC7elJ5Y0hYuKBZoVqWpAMfLOeIh2UI/DCQ5ruodIjvsugZtjUYUw2pUw==", "signatures": [{"sig": "MEUCIEuhP9WjzXLEU+wz6nrEm7IttXDJNxOMpxOpRtp3I3E6AiEAo28avcgEyRY9InUOctdE7j0r0u/LOOedP08gWjbOju4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "f87057e995b1a1f6ae6a4960664137bc56f039da", "browser": "./browser.js", "gitHead": "b38458422b5aa8aa6d286b10dfe427e8a67e2b35", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "2.7.4", "description": "small debugging utility", "directories": {}, "_nodeVersion": "0.12.2", "dependencies": {"ms": "0.7.1"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}}, "2.3.0": {"name": "debug", "version": "2.3.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.3.0", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "3912dc55d7167fc3af17d2b85c13f93deaedaa43", "tarball": "https://registry.npmjs.org/debug/-/debug-2.3.0.tgz", "integrity": "sha512-tb2o33z/sdAvVhiszTuGQHgEww24WFBT+ZzK5jNML+pnF83fDnsE9z2/eoKsxLuhIg9x2VW6IY6TlepmvjELIA==", "signatures": [{"sig": "MEYCIQDNazlPxr5j9syMMQF8bvky88K2g+uOCi5V5NbVHC5INgIhANGvUBypBJDiX+xMMrScFB0Dc7r9J4g40B0CQUaRNgyo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "3912dc55d7167fc3af17d2b85c13f93deaedaa43", "browser": "./browser.js", "gitHead": "8e5a6b3c3d436843ed8c2d9a02315d9d5f9e9442", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.0", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.3.0.tgz_1478540435945_0.11468172585591674", "host": "packages-18-east.internal.npmjs.com"}}, "2.3.1": {"name": "debug", "version": "2.3.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.3.1", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "4b206c092eb4e5b090e429a15d1d89083737ab2b", "tarball": "https://registry.npmjs.org/debug/-/debug-2.3.1.tgz", "integrity": "sha512-QUobWzjY1Q8Jnv+S4m6po0sGD0PXNXDbHhiouGd5tm66/j2l2vYc2E0GrS2V6rBFVc0QU+w42N9GuFrdnoSpDw==", "signatures": [{"sig": "MEUCIDoAyzzWamtz8pQaJPdxGv1cqwkqZnitABHrUNmoUZqWAiEA0m8Iu68TSui/62ZCPwARJjaamCQLchegvyeUwEkwHcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "4b206c092eb4e5b090e429a15d1d89083737ab2b", "browser": "./browser.js", "gitHead": "6b45c3a15510ad67a9bc79b1309c1e75c3ab6e0a", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.0", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.3.1.tgz_1478736862830_0.16602304461412132", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.2": {"name": "debug", "version": "2.3.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.3.2", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "94cb466ef7d6d2c7e5245cdd6e4104f2d0d70d30", "tarball": "https://registry.npmjs.org/debug/-/debug-2.3.2.tgz", "integrity": "sha512-Pi2B3gZGhfmFd8vAAYAI8XTtRrNNkSD3xqwBTjzjNqeVTAcHc8uVMz854KTowlR+Ulzfbz5gu3pudWFGo3LFUQ==", "signatures": [{"sig": "MEYCIQDVQenN0y5zIMXY0BTBVrDpRoynrrGETAKjK8/nBNEjXwIhAIXzWS/49Kti9+pB33qVCP2pu2tiOYSpK1vF4n6DCTHO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "94cb466ef7d6d2c7e5245cdd6e4104f2d0d70d30", "browser": "./browser.js", "gitHead": "1c6f45840d0dba8cb14f9975b4633bb685fda400", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.3.2.tgz_1478759402178_0.8417916153557599", "host": "packages-18-east.internal.npmjs.com"}}, "2.3.3": {"name": "debug", "version": "2.3.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.3.3", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "40c453e67e6e13c901ddec317af8986cda9eff8c", "tarball": "https://registry.npmjs.org/debug/-/debug-2.3.3.tgz", "integrity": "sha512-dCHp4G+F11zb+RtEu7BE2U8R32AYmM/4bljQfut8LipH3PdwsVBVGh083MXvtKkB7HSQUzSwiXz53c4mzJvYfw==", "signatures": [{"sig": "MEUCIQDEoOZt5a7JzQtiJwETq84zoJBfT/aBiZOgCo/uSv41sQIgN+fPxvv4U/UqTaVXChm7gjk3NMRj2lhzKBMqxt2JW5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "40c453e67e6e13c901ddec317af8986cda9eff8c", "browser": "./browser.js", "gitHead": "3ad8df75614cd4306709ad73519fd579971fb8d9", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.0.0", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.3.3.tgz_1479585558307_0.03629564819857478", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.0": {"name": "debug", "version": "2.4.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.4.0", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "80db5e490a43bff63958e712ba88ba4e4121840f", "tarball": "https://registry.npmjs.org/debug/-/debug-2.4.0.tgz", "integrity": "sha512-qkWsCdZuL12DHM6juOa8etzUxQlW0ybWh23sS6QKo7wWyaPAP62udxguN/gTGO2LgXlRy5vXbEuYWNYUsKNTEA==", "signatures": [{"sig": "MEUCIH/pWm5DtDWCk1ngRS09YHVBLrWj7oZ2f0bGD4N6wPZvAiEA1KRIRkTc+SmpmPRnRRDZjHZzC4R1oYuMiPRTZc1mSgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "80db5e490a43bff63958e712ba88ba4e4121840f", "browser": "./browser.js", "gitHead": "b82d4e6c799198b3d39b05265bf68da9a9aacd41", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "deprecated": "critical bug fixed in 2.4.1", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.4.0.tgz_1481698324494_0.7391216848045588", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.1": {"name": "debug", "version": "2.4.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.4.1", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "ef2532d2753d282045c13c82ce47a09e56b91d53", "tarball": "https://registry.npmjs.org/debug/-/debug-2.4.1.tgz", "integrity": "sha512-3KDO2nvteNg5RLHQA/ABlmfGfNHjYIfvxFxEHM8BP/yLZe8Ne8Deb0bC02ENfFuKuF5dSuHR2k/WFodW1fleMQ==", "signatures": [{"sig": "MEYCIQC9YYKxmSkANbOO2zhQz6CEh1Kt3rKV8iiYGAaCVJjhugIhAOjzoxC/J19A3Hd25LZgj8uMhNC8WlJ1YZI1eEUf+Y2L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "ef2532d2753d282045c13c82ce47a09e56b91d53", "browser": "./browser.js", "gitHead": "803fb05785675e262feb37546858c411b56dc35a", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"mocha": "*", "browserify": "9.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.4.1.tgz_1481700338914_0.6147811473347247", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.2": {"name": "debug", "version": "2.4.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.4.2", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "a255d4489f58a2d7b6aaaddb9b7c60828f6ba27a", "tarball": "https://registry.npmjs.org/debug/-/debug-2.4.2.tgz", "integrity": "sha512-ej23QcDyiKBa/ABIamf1KPW5CDF4BfVOkQsQo3ePht3nXTo52Ik6YjJLcpaN8SqMevVCyFzkMXgbLHvFpRUydA==", "signatures": [{"sig": "MEUCIQDR1o2sUlUNlFVnFXdKxHLQdg+UCOQTtmxvd4nyGwD+sgIgK4Fq5mM3U2e/RJZeqTmDRk7opoKFWfRX/6u0TpVPPkc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "a255d4489f58a2d7b6aaaddb9b7c60828f6ba27a", "browser": "./browser.js", "gitHead": "4c3e80dfaaa499b451619201130c6c2ff07068c2", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "babel": "^6.5.2", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "browserify": "9.0.3", "babel-eslint": "^7.1.1", "babel-runtime": "^6.20.0", "babel-polyfill": "^6.20.0", "babel-register": "^6.18.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-babel": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.4.2.tgz_1481744419649_0.1140342962462455", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.3": {"name": "debug", "version": "2.4.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.4.3", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "3fe67c5588e724d0f5d9e48c8f08ff69b4b20643", "tarball": "https://registry.npmjs.org/debug/-/debug-2.4.3.tgz", "integrity": "sha512-1Iaac9+DapEN6iCcv2af9k1cKIh5LEUpr5w74bMIQViBEGkME1wQTq+pdAfWaX92xQFYct6fBSfcVKnPoZj61g==", "signatures": [{"sig": "MEUCIHWk8AGyI5gsf8rNd1VQzPWPnWZq+auZ9nOsln+bzMrhAiEA9xeQmOgZVMbO24y5KLhOLvLPKT7F4AdkWnKxlzZmBRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "3fe67c5588e724d0f5d9e48c8f08ff69b4b20643", "browser": "./browser.js", "gitHead": "e1ee4d546a3c366146de708a9c1bf50939f0f425", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "babel": "^6.5.2", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "browserify": "9.0.3", "babel-eslint": "^7.1.1", "babel-runtime": "^6.20.0", "babel-polyfill": "^6.20.0", "babel-register": "^6.18.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-babel": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.4.3.tgz_1481752200538_0.17475450248457491", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.4": {"name": "debug", "version": "2.4.4", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.4.4", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "c04d17a654e9202464803f096153f70a6f31f4be", "tarball": "https://registry.npmjs.org/debug/-/debug-2.4.4.tgz", "integrity": "sha512-pmVI0UTP+XSYRUUJgz09db0M1cAcuUlGQyHxsQh8j1yQ6/zHY21A1JTZskBAIRQbJtxoCC9tq0psn8pcb8gjqA==", "signatures": [{"sig": "MEUCIQDVh38kHQkw2FPMUmHCSTyxWcIE7y4OWijC7E/r51eHPwIgUqwAmzkiPR6ytRVigRg37nJeYiY/pb265sDByP6s1OE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "c04d17a654e9202464803f096153f70a6f31f4be", "browser": "./browser.js", "gitHead": "f1ca2ab80b824c6bb5d58dade36b587bd2b80272", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "babel": "^6.5.2", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "browserify": "9.0.3", "babel-eslint": "^7.1.1", "babel-runtime": "^6.20.0", "babel-polyfill": "^6.20.0", "babel-register": "^6.18.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-babel": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.4.4.tgz_1481765223703_0.8797183907590806", "host": "packages-18-east.internal.npmjs.com"}}, "2.4.5": {"name": "debug", "version": "2.4.5", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.4.5", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "34c7b12a1ca96674428f41fe92c49b4ce7cd0607", "tarball": "https://registry.npmjs.org/debug/-/debug-2.4.5.tgz", "integrity": "sha512-dKKhHsZva2Re+65VIn/PUZJaDmIOjgo98JrgrTVNYmINJIxxLMk0aNIUezJ4NTDf53JvGAxB9JpUjKr31icuIw==", "signatures": [{"sig": "MEYCIQC04QXWKfHQonTWODiB8PAGouKzxxa1D46ZIq/T0LhyqwIhAOpt1gLnurC1/4AVpoSyA7049D1AKCoH7ILcf7llTS8x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "34c7b12a1ca96674428f41fe92c49b4ce7cd0607", "browser": "./browser.js", "gitHead": "7e741fcc2f834672796333c97aa15f27f0ea2b5c", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "babel": "^6.5.2", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "browserify": "9.0.3", "babel-eslint": "^7.1.1", "babel-runtime": "^6.20.0", "babel-polyfill": "^6.20.0", "babel-register": "^6.18.0", "babel-preset-es2015": "^6.18.0", "eslint-plugin-babel": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.4.5.tgz_1482045228863_0.4158463138155639", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.0": {"name": "debug", "version": "2.5.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.5.0", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "94434a384a615a75db92fa734d2c994ec75c7b55", "tarball": "https://registry.npmjs.org/debug/-/debug-2.5.0.tgz", "integrity": "sha512-vXPxQlAbKSvGhu2Ys3+DX7XTMkYdoSg32xTyg4sqcF/XNRYLu/B/foqncVlYqGPdtFrc5YWDSSUhoaDN5ogWng==", "signatures": [{"sig": "MEYCIQCHwdTloXn8EP64D+3gjWG8Bj5QpnZnfoZfQrLus1oOCgIhAKWATu9zp5xoYhRMGgpn5Wbs9YlzWO5bIRm0Lwvz6obz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "94434a384a615a75db92fa734d2c994ec75c7b55", "browser": "./src/browser.js", "gitHead": "355e327c94e03aaf0c215b32244eeeacd8a298c8", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "deprecated": "incompatible with babel-core", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.5.0.tgz_1482296609452_0.780945998383686", "host": "packages-12-west.internal.npmjs.com"}}, "2.5.1": {"name": "debug", "version": "2.5.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.5.1", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "9107bb4a506052ec2a02314bc606313ed2b921c1", "tarball": "https://registry.npmjs.org/debug/-/debug-2.5.1.tgz", "integrity": "sha512-kcuXHZHHIrMikExr5bEIkDUOhXrqvMlKrAd7P34OdiDR0K4ZxG0gpT3arvATP8QgZy1bdTun1/d6nOX9TM3z9w==", "signatures": [{"sig": "MEYCIQCIKc7sUl1rgZL9r1UIAxcwKCgfElhgd4px5YB5bztUeQIhAMnLULLWZ3E0FqqDIZF2LadzTVsZ1CUMOjcvmMkYBZvb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "9107bb4a506052ec2a02314bc606313ed2b921c1", "browser": "./src/browser.js", "gitHead": "3950daef4c63058e4c2c130b7e90666416b3d5d1", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.5.1.tgz_1482298398476_0.08919672318734229", "host": "packages-18-east.internal.npmjs.com"}}, "2.5.2": {"name": "debug", "version": "2.5.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.5.2", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "50c295a53dbf1657146e0c1b21307275e90d49cb", "tarball": "https://registry.npmjs.org/debug/-/debug-2.5.2.tgz", "integrity": "sha512-iHrIBaTK1JzBz5WvitFmZGaTCO/mHiU3NKi8UKjh7rU2JboIbVMZU7pFSCpvc2NxfkrvyaQ5zfdNRJnft/TcoQ==", "signatures": [{"sig": "MEYCIQCmqS7i4Z/Eo92CaPUhr6FP4g4CQKRKifbzgVTYVTdiEwIhAKqlz4lvMwyrJziyQA+1odhpTYImNex6wO1h9aocOIdc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "50c295a53dbf1657146e0c1b21307275e90d49cb", "browser": "./src/browser.js", "gitHead": "9a18d66282caa2e237d270a0f2cd150362cbf636", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.5.2.tgz_1482719984651_0.9355534017086029", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.0": {"name": "debug", "version": "2.6.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.0", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "bc596bcabe7617f11d9fa15361eded5608b8499b", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.0.tgz", "integrity": "sha512-XMYwiKKX0jdij1QRlpYn0O6gks0hW3iYUsx/h/RLPKouDGVeun2wlMYl29C85KBjnv1vw2vj+yti1ziHsXd7cg==", "signatures": [{"sig": "MEUCIQCD1jRkFZZi4zDp6v03GQuaG3HEoLTwXXKIYIlu/n7IzwIgQiEWY97F/KVONs+frF31+JhllGb42hIEzwguHu2OYxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "bc596bcabe7617f11d9fa15361eded5608b8499b", "browser": "./src/browser.js", "gitHead": "ac5ccae70358a2bccc71d288e5f9c656a7678748", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.0.tgz_1482990633625_0.042889281176030636", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.1": {"name": "debug", "version": "2.6.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.1", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "79855090ba2c4e3115cc7d8769491d58f0491351", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.1.tgz", "integrity": "sha512-BmFi/QgceF1MztznXEqbZXATlMwzrsfWR9Iahbp4j7vTK+Sel84Mt3SZ/btENs22PSm0bw6NOoZOd2fbOczPRQ==", "signatures": [{"sig": "MEQCIFucFI+dNUGJix5XivQPn2BqgQhVgLEYL36UDkl2L2qmAiBO9KjbfGbIZMuuPKDi1pgI1x2GWHesJZHvBuonUw6Y+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "79855090ba2c4e3115cc7d8769491d58f0491351", "browser": "./src/browser.js", "gitHead": "941653e3334e9e3e2cca87cad9bbf6c5cb245215", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.0", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.1.tgz_1486753226738_0.07569954148493707", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.2": {"name": "debug", "version": "2.6.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.2", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "dfa96a861ee9b8c2f29349b3bcc41aa599a71e0f", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.2.tgz", "integrity": "sha512-P3nUmoQmRAgPRGyRWfQxnWcUEwoxznn/4+B1XKgqagoOoC/oQAkkFeOwqQmBgqNxdJwengQ382Tl67gfVLRWPQ==", "signatures": [{"sig": "MEQCIEgzVr7G3d5M/Kj3IVQ5jnxqH+Ac7np27Vhv+/GsIuUfAiAHx0EuCvYElXbpS0g7D4ivzZPYhQTWdPt6srTJ3JE0Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "dfa96a861ee9b8c2f29349b3bcc41aa599a71e0f", "browser": "./src/browser.js", "gitHead": "017a9d68568fd24113bd73a477e0221aa969b732", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.0", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.2.tgz_1489175064296_0.5892612014431506", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.3": {"name": "debug", "version": "2.6.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.3", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "0f7eb8c30965ec08c72accfa0130c8b79984141d", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.3.tgz", "integrity": "sha512-9k275CFA9z/NW+7nojeyxyOCFYsc+Dfiq4Sg8CBP5WjzmJT5K1utEepahY7wuWhlsumHgmAqnwAnxPCgOOyAHA==", "signatures": [{"sig": "MEUCIQCKYGiPwpCx/aUHOZV6VKS4HCTv3DPBKOAaodfN7Jo0fwIgA3mile25CZ3grNlCpd7wooaSK5HXNsoPL71VFjuwQB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "0f7eb8c30965ec08c72accfa0130c8b79984141d", "browser": "./src/browser.js", "gitHead": "9dc30f8378cc12192635cc6a31f0d96bb39be8bb", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.2"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.3.tgz_1489463433800_0.9440390267409384", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.4": {"name": "debug", "version": "2.6.4", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.4", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "7586a9b3c39741c0282ae33445c4e8ac74734fe0", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.4.tgz", "integrity": "sha512-jhHoN6DHsKWoWHqswimxiToCuB4ClIbDw4lXDHzJmXGJb0sO3tynCdLe9JHqTXPP5d3oKgp9ynKKsf79765Ilg==", "signatures": [{"sig": "MEUCIQDj2qbChftf0LIgE3ZOAoqSBKriJoDUkY/5IgvST3Z87QIgC+6/DYd1Uuu66YrTe3aEHR5GzO5NLwFBqKtm1hl5fMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "7586a9b3c39741c0282ae33445c4e8ac74734fe0", "browser": "./src/browser.js", "gitHead": "f311b10b7b79efb33f4e23898ae6bbb152e94b16", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.0", "dependencies": {"ms": "0.7.3"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.4.tgz_1492711686326_0.05656863120384514", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.5": {"name": "debug", "version": "2.6.5", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.5", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "7a76247781acd4ef2a85f0fb8abf763cd1af249e", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.5.tgz", "integrity": "sha512-uW/FlKTTFXEY+RPb8gfK/qVsMfYDN0xL28H02x67FZ2RpShWEQ5nQhF0IQpZsbPfwCrwelcB4M68I6bs8ry+xQ==", "signatures": [{"sig": "MEUCIE9Pfa/AQJtX0H0F6R4z8RBO1KsNy+dIMysGROoYbzWfAiEAs+vApH7GljVROQEEhAO+objFj871YyOXuV8T0MiRZGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "7a76247781acd4ef2a85f0fb8abf763cd1af249e", "browser": "./src/browser.js", "gitHead": "14df14c3585bbeb10262f96f5ce61549669709d8", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "deprecated": "critical regression for web workers", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.3"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.5.tgz_1493309048647_0.05605892837047577", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.6": {"name": "debug", "version": "2.6.6", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.6", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "a9fa6fbe9ca43cf1e79f73b75c0189cbb7d6db5a", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.6.tgz", "integrity": "sha512-ED4LYbzHt4IiPgIVjfUFfsvI5Et133QsXvQuMWw0ygFaPdvE8aeX6nfI+5ZVfyMuP8vZBk9Lv3yn6MPvGnzO9Q==", "signatures": [{"sig": "MEQCIBXfwa30cNT4ONK7XXv3CURXL4GFNgriJv7z23mQ3kwYAiA3+RV3j0LSauP8LctsghuVAdxs01TxR7WA1rhngUQw1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "a9fa6fbe9ca43cf1e79f73b75c0189cbb7d6db5a", "browser": "./src/browser.js", "gitHead": "c90a2b3c6c17300f3c183f0d665092c16388c7ff", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "deprecated": "invalid release", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"ms": "0.7.3"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.6.tgz_1493336101823_0.35170009173452854", "host": "packages-12-west.internal.npmjs.com"}}, "2.6.7": {"name": "debug", "version": "2.6.7", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.7", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "92bad1f6d05bbb6bba22cca88bcd0ec894c2861e", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.7.tgz", "integrity": "sha512-7YoSmTDGnXYkFJOvaYXfxcvNE25Y11uZ0X8Mo+pSXjHz/9WUlbCS4O6q+wj7lhubdNQQXxxsSOnlqlDG8SenXQ==", "signatures": [{"sig": "MEQCICoU1PDC4Sw3GxErsIYGMobmcMqqdYEFu3YwKVlUS/Q7AiBMTywlhZuyKHGwi72RGS4dE6IZWhwGmBwlZUMvkq4RVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "92bad1f6d05bbb6bba22cca88bcd0ec894c2861e", "browser": "./src/browser.js", "gitHead": "6bb07f7e1bafa33631d8f36a779f17eb8abf5fea", "scripts": {}, "_npmUser": {"name": "thebigredgeek", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "small debugging utility", "directories": {}, "_nodeVersion": "6.9.5", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.7.tgz_1494995629479_0.5576471360400319", "host": "packages-18-east.internal.npmjs.com"}}, "2.6.8": {"name": "debug", "version": "2.6.8", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.8", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "e731531ca2ede27d188222427da17821d68ff4fc", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.8.tgz", "integrity": "sha512-E22fsyWPt/lr4/UgQLt/pXqerGMDsanhbnmqIS3VAXuDi1v3IpiwXe2oncEIondHSBuPDWRoK/pMjlvi8FuOXQ==", "signatures": [{"sig": "MEYCIQCeuXf4/xB1X71ksOfu6FcXrchhfK0/tNriYxxTIaINfAIhALxCN3nyXH5tfxhR8U7AI1mgQdUOvKqBKBqdQSvt4mPu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "_from": ".", "_shasum": "e731531ca2ede27d188222427da17821d68ff4fc", "browser": "./src/browser.js", "gitHead": "52e1f21284322f167839e5d3a60f635c8b2dc842", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.8.tgz_1495138020906_0.5965513256378472", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.5": {"name": "debug", "version": "1.0.5", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "debug@1.0.5", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "f7241217430f99dec4c2b473eab92228e874c2ac", "tarball": "https://registry.npmjs.org/debug/-/debug-1.0.5.tgz", "integrity": "sha512-SIKSrp4+XqcUaNWhwaPJbLFnvSXPsZ4xBdH2WRK0Xo++UzMC4eepYghGAVhVhOwmfq3kqowqJ5w45R3pmYZnuA==", "signatures": [{"sig": "MEYCIQCxcH0znyOLAAA2O0dGScyzbBlsizzCU7xXj7rYwx9W2QIhAMGecDjDBfJU5KanaUDBjxXFs/EL3GludbjXg3AMDocB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./node.js", "_from": ".", "_shasum": "f7241217430f99dec4c2b473eab92228e874c2ac", "browser": "./browser.js", "gitHead": "7e2d77fcd1ebf7773190d51f24e6647ee8f3fa0d", "scripts": {}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"mocha": "*", "browserify": "4.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/debug-1.0.5.tgz_1497485664264_0.9653687297832221", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "debug", "version": "3.0.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.0.0", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "kolban", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "1d2feae53349047b08b264ec41906ba17a8516e4", "tarball": "https://registry.npmjs.org/debug/-/debug-3.0.0.tgz", "integrity": "sha512-XQkHxxqbsCb+zFurCHbotmJZl5jXsxvkRt952pT6Hpo7LmjWAJF12d9/kqBg5owjbLADbBDli1olravjSiSg8g==", "signatures": [{"sig": "MEYCIQDp5gSyHxnDInop8nXtn63ndARpGW0kaEImEhhIfX/5ogIhAJ5CZIAEUaNwZwIGXvYX4qUMoH6izXGi+w3chMs5fsvY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "52b894cd798f492ead1866fca4d76a649f0e62c6", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "14.4.0", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-3.0.0.tgz_1502229358950_0.15122428792528808", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "debug", "version": "3.0.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.0.1", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "kolban", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "0564c612b521dc92d9f2988f0549e34f9c98db64", "tarball": "https://registry.npmjs.org/debug/-/debug-3.0.1.tgz", "integrity": "sha512-6nVc6S36qbt/mutyt+UGMnawAMrPDZUPQjRZI3FS9tCtDRhvxJbK79unYBLPi+z5SLXQ3ftoVBFCblQtNSls8w==", "signatures": [{"sig": "MEUCIQD4UJZ+PwzRKKW1CYGngnpWmRaTrSwFUHECNSIydutYWAIgaBoxIq9eZEQCW5Jov6wAnhioW1J8YS4COhR+Z6OP0qM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "3e1849d3aaa1b9a325ad6d054acf695fddb4efe9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "14.4.0", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-3.0.1.tgz_1503603871771_0.21796362148597836", "host": "s3://npm-registry-packages"}}, "2.6.9": {"name": "debug", "version": "2.6.9", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@2.6.9", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "kolban", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "5d128515df134ff327e90a4c93f4e077a536341f", "tarball": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "signatures": [{"sig": "MEYCIQC/LTf6UK62VWqwtmetEmhZ6D2NkJptC8+1MpUsNbGrCAIhAMgeWOEZ9T88UGQ5uldEbxn7p6uw1hgFNqzD5spMVkR8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "13abeae468fea297d0dccc50bc55590809241083", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "component": {"scripts": {"debug/debug.js": "debug.js", "debug/index.js": "browser.js"}}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "9.0.3", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-2.6.9.tgz_1506087154503_0.5196126794908196", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "debug", "version": "3.1.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.1.0", "maintainers": [{"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "kolban", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "5bb5a0672628b64149566ba16819e61518c67261", "tarball": "https://registry.npmjs.org/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "signatures": [{"sig": "MEQCIGvBszkxPxDYcbBED6Ar5Px/aYYETaLx7VhwNpE0FojrAiBy5zcsR0xnw3wAXjvVYmwPBu19WhL0fQqaBOn+b7NonQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "f073e056f33efdd5b311381eb6bca2bc850745bf", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"ms": "2.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.6", "eslint": "^3.12.1", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^2.11.15", "browserify": "14.4.0", "karma-chai": "^0.1.0", "sinon-chai": "^2.8.0", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.5", "concurrently": "^3.1.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug-3.1.0.tgz_1506453230282_0.13498495938256383", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "debug", "version": "3.2.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.0", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "cfba3c774175ee0f59a51cf8e0849aca9bfd8a9c", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.0.tgz", "fileCount": 9, "integrity": "sha512-Ii8hOmyHANEZ4wnsj5ZKeWUQRLt+ZD6WSb1e+/RK0BNIrVaZQrN0r2qxl8ZvnFgb4TQpd9nsjGUxdPIUXo6Snw==", "signatures": [{"sig": "MEUCIGTsVhEHWDr+S5L/beUtz7SmpXmPF82EftGAApXkA3YQAiEAgpNwcND+szJFoL8ERDrpgGz7qwGrrkDemQAOD4cnJ2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbl15jCRA9TVsSAnZWagAAsAcP/1CgyRvxCKQpPCU26k3n\nDgy2TMSVi2EDvkxsIVx3hqQzzfuAwIcxSiK95YceW3SzjpxzUaIGuUjFfHe7\nlZHw3bJBCp5N9ZJOxz79CxF5SAcGUHL0rKKgti77ljKGQeG4xBoL4jSxJXqe\nZNFReUWucQI2PlaZAo5g0ybkZ86l6DRlhupOTWGfwKY8Y2qDBad4SOFz4sd5\nQckokjEuZ0//f/aoLfQfcjTKNdIDPvYZCt7SVFVgbgyYkA09c5runQ5LVQ6u\neC+/aAXNY7CJsd9DMnFO8ea5NMDloTU6UYBsr1LAzWgPGRoriFqRgRlMkBk1\n4yZK3fAbP7BzXc6ZmmfDaZE8Xcrqmn8HEbEkUxUr1g6d8n3SIkO5Mz5iplH7\nSZl7lYChtB9bdfuz6egMUhKG7eD5W3Ov4Or3JmJO7/7OTI0KCHQhinVb2Z4U\nmq82aqHdZRrS9zxp2EuIge3pHFNrzch3UKI3RVRx3f0PSGxSXcPJyNdsrvmZ\n71YzQaK7FKfB9VX7FYtqNK9NOd+hYFI3QZM0ZNLYudGFbWc5BauV7pDzma0i\ndx0MNp29DvWQUhjfy/i7ob8SWpas59zHpvRV3Fgz4aAwYqNe7qlSrPrdHZ9J\nZA9OpMmanYMJnhXiSYAHJh+5ZBe0EbXH5G+grIJLSvx2AqJMQSFN1ghG7if5\nm+EF\r\n=diB2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "gitHead": "dec4b159ddf63915c94cd9d8421ad11cd06f0e76", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.0_1536646754464_0.27788234878197926", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "debug", "version": "3.2.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.1", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "30b2d86ecc0f169034c260a84b295fcb51eb88ec", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.1.tgz", "fileCount": 9, "integrity": "sha512-P5CJ6AGKlnMM3Rd1V4xmQ1SNn5VQ/4UoIiVmDzJxliKCeG1ANIj6ThcWWsefqZ4WdzGdmhG3WdeKrcjx9eNUYA==", "signatures": [{"sig": "MEUCIQChWILWq6Iyzyr0O19cek93BPLTx/rAdiyLge8q2wAqKgIgbJMuBvV3/MQk25QGAOdDy0BLmvVnqXnAajzvF4qBR4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbl2CmCRA9TVsSAnZWagAAsuQP/3F/T64Gvm9t2NPQcUlO\nuW5FB9KuyGDRmUNA7NCprDtalNQfa/ShZevjK9MjB4jp5sEuUDAa9uisovhn\nSF4SoNgWEr/wwN64/utAK9v64QazpGaGRYgJXqvcAi+SPQUhSMAGQiUFPLu9\nW9hkKJLRqUR26FenZQTpjoL7DBFhMzchXsrmmwZxsXU1okF5dXH3wUCKmmJv\nhmEAYW3qbiX4R/ORDFBZGwdn4uWN6fZOxQF9oFMmALRTOW6CGUhIfAZEqpiL\nhpMTgtbjNkQU/yIfcYj2FSb6WvqGwVXxSCo78ExQJa6rh09kvmpClapaUOOy\n3nWUosKPyF9GPUIsbgneXObinn+nCXOgRoHHdwYlmluk2xC3+9Tr2PsjblIt\nXH8SrgScxnnCs1YajPMHatHj9oGQhfWrS0Czj7UhF6ir5kpwseG4GSpsy4C+\nXJgLBj4n+VOZImvs9PO3z2Sbkgeo3HunIY4Yj1cwq+vjaraBCTUw5x5m+Cao\nMYdP2lzCI7NovH35/zw70HUVubLCpIe9hXu+YhYVOjrdh4POhCB4V4VGpj8Y\nV1odXjGN/Kp/AecfC9I1KkSFtlg65hwOHLy5pAjGvgnopbdcpewJYDMIRK6H\nEuBESkCEqegcVXfNn95SlLhO5DlVYwkERh0YP0wkUZCKbFRYsbYGOyVqb2X4\nqzaP\r\n=Sk51\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "84e41d52acfdaa00ac724277f8c73a550be6916d", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.1_1536647333594_0.8913865427473033", "host": "s3://npm-registry-packages"}}, "3.2.2": {"name": "debug", "version": "3.2.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.2", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "1e7ad4d9714bb51e6745c0f15559ee82c0236010", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.2.tgz", "fileCount": 9, "integrity": "sha512-VDxmXLtYzTDjwoB/XfDYCgN5RaHUEAknclvDu17QVvkID8mpWUt2+I3zAc98xu1XOSCQY9yl072oHFXDhY9gxg==", "signatures": [{"sig": "MEYCIQCu1RPXGrwxBmus13G2316VK2026Tk7m9lS2LOE50+5awIhAIR1+AgIjbSu6EzU3Aztob6UqJ3U37naGwTKURPrHWIH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbl3PGCRA9TVsSAnZWagAAfw4P/0AbS4UO0xtHtNKXS/Ig\n0LBe4TZuRT3frlo4gCVIGplfK1sqsBWS1jkNEH7MZIb52GiB6CDZadrrMjBI\nZ12azSvVoJD83fnakr8/9VnsT5VdF29B8SLrmaeMPVozKd/TkigQAfEw0HBo\nTkPvfE8Ole1nLaikPySf6qReLi8Fe90FTGFWuLlk3jqMeXwmbSD+LMyBIf3B\n9JWvcVYXsxwrw42hxz9cjBvXnTBSd2H1r2XT7ToboI76y5TCL5iY3AQC+23A\nIZ6EufJA+FoicsiPtxuQpx9TK3l9xyr87Wf4tzzmyWhhJh0SHQkIs8LA+fIf\nrBS52zg1MoXBrxsrXLgYmwx1Saf6oXabY4YizdVQiQx8ngTqksa7LDzCtILz\neAHcRS+RF4MARNYJW5n9NbAPS5eocnG/uAp/fqweZ1Djw/9tSCr4D+pYhHPs\nBaNk+HDRbG+Wr2APVgLayI8nWtM3OCt8zHIYUfw4HwsBraSTh3/tprdX8+j0\nrp+bGBdUoSHQrLZo5wcBQdYMA1VcWle35Um1p7wF/aDXtBeVy7fr8dsF/2/v\nZ3jG9h5TJlclxYlE4Kr8XlplAPhVWIrf9r8mas4g5VuEx0Gpe12f4dCNbPSH\n3XUrEhWLSsRmG75+6Kw8QHA0igD15DGQ6UeDPCFrvKUjcn5R0FZuSCZGaKE6\ntZId\r\n=aiGX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "622e5798cbe1b9b48930435f2960d6c2f4684300", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.2_1536652229813_0.9493398657476286", "host": "s3://npm-registry-packages"}}, "3.2.3": {"name": "debug", "version": "3.2.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.3", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "3f2a4bf7afb0a73a960fed83a197692dba736b83", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.3.tgz", "fileCount": 9, "integrity": "sha512-4J+rux2EAUIbVjYOvNx/6tQxCSrKDAFm4qUDNTnflVPCj/jgCb5xbt7jfAkMC2iGmie1i/+ViS/QpAziYe+rPQ==", "signatures": [{"sig": "MEYCIQCpdmPt6tKDKVdfhnZ/I8GmxnFKFk0+FHtUbtEbw1+/XwIhALCzpRK4aprUz+DGC2jc0IAGSNaZ+FSOR8c2h4KVeS+R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbl30vCRA9TVsSAnZWagAAm1EP/3Sf15V6tTlS1qfZkaLu\nPbhyI30yiXGR8IWc9SsygjqQYdrmFbDbKAafvkqK6SMf+mmxhVpA840kobzD\n3xdbDKDNloU6hNJybwVp47nmtNGJiqh1fWtuRKZgTMf6QrT0bMN/faoUYY39\nUqZqOVypEKAuga8IVkAaYtVcN1xMQo0fK2tFvNwYc/YY2EwRvvddULnOBeqY\nFMhUXZ/LX2aNXPCuLPX1YNDUntJgm+Me46rAf1ZismNgBDuorTBi4BD2Z1KP\n8OAB2uYOurruiwCwC/QtcY4CXZoMuWzVob3BgMopVppMc+uCmxmYG+mwxklY\nZ6P5RYv40/FQ3lgWtrpbEMagcXBivUnolaCJP5yckVESAd6mQdfQ1eyMH6RL\n275UhYhQIg2c8wZlTdv+v06Nr/JARiz1G2cl18NPB6xk7wEkv9gLsSv2aTi4\nCkNTrW+Mwc869Efil+cWc1J/sghjNHU3t/Dmf0VSMVGpdsWmjLZzPzhSHTZ0\nHEEyCImFTWNBj0yjK+rm+RlSpFFZeT1Yd8D1xYvph8Xjrom4sGWcOawROriX\nvLMaywIcOKfXMCgyKhA8pDDzKumKec+VmjYO8ozqDtZ3GorS3Cs2mGqEVA8h\n2aogV2TzNDUAVI8ePS4v7BSk9eqV7vP9f3DQIP+nHJJSNafg/BFLQ+rf/WDN\navyG\r\n=npte\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "700a01074456ac42922392c7f327b2d7dfe23dc8", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.3_1536654638619_0.36104977498228963", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "debug", "version": "4.0.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.0.0", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "ef7592a30b1c5e05a78e96926df2e6b36b5544e7", "tarball": "https://registry.npmjs.org/debug/-/debug-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-PlYAp+yaKUjcs6FIDv1G2kU9jh4+OOD7AniwnWEvdoeHSsi5X6vRNuI9MDZCl8YcF/aNsvuF5EDOjY/v90zdrg==", "signatures": [{"sig": "MEQCIEwrwLY8VcpCIrtWysObLCosm7lgkGdQMy5HB5avEFALAiBNzbjc+laSca273MJicfzkNnh5PIPxcAg6FmaqfhcazA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbl4OnCRA9TVsSAnZWagAAKc4P/2AiTrAEAvKdytHEYJOo\ntJMZ5ssVvEdfTFxZFFlvVtB75s2hLB38Ux2MyBNVZHZQY/KPpg5kJLFJMa+W\nBBbYhaZl33N/1O31clRbL1ePUL+/7F1rlLzoNuTFy4uJkh0kG9HfXluQC7v6\nhPd5svIhLu9crBbt9ChZui9tbeGzXK9Se4mzqjBY0VqTGZMXJhqkRo7p3fIW\nvnLHB2ZaGtpJap3x6bzCJC/Ev03PbTIHEyuDQ8v8bJkchPUTKNn1vqRRvrHm\nPNDZOL321V414JZggjESVHogh7ppF/cqsb6i0U0cVhw3n4I4ptuL5CyNNTtd\n7QpNz7O+r10cFsiVqw/E9qByfxNx3HEjOwxco/DGfYzcTXSgDChDvdf66/cw\nWtCtZ6kV0GrIk9vLWysaUo316k3OA0Gn8QHrIDZafPM3f34k0jGC+LGh5bg0\nCJYlguqw5lhEIE8m78pHRwlqtbug+RjeLQbgPNC3XEXNSJFY3rWXTwHacCll\nv8RX+H/4O6IH13P6F9mNI2LBOzqQyEqh0AxYUZcRFhp3HYedHDCLGMT45OJT\nNo+Cl76HD7Km4v18My+NyovmBdYPv+obKasS4bqi0SecCLyEfzKv4IKs+81d\nAG7SZn2alKK8HEW5qttB1ojWTfv9O2yR6KnUlbPmX3PVQkDsmCtq0ItIC+gU\nnCAV\r\n=pi46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "7fb104b8cfcbc3a91d8e4a6727638c3fe24be8d2", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.0.0_1536656294677_0.496757503110125", "host": "s3://npm-registry-packages"}}, "3.2.4": {"name": "debug", "version": "3.2.4", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.4", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "82123737c51afbe9609a2b5dfe9664e7487171f0", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.4.tgz", "fileCount": 10, "integrity": "sha512-fCEG5fOr7m/fhgOD3KurdAov706JbXZJYXAsAOEJ7GgasGr0GO4N+1NsIcrjlIUcyvJ9oZlnelTzN3Ix8z1ecw==", "signatures": [{"sig": "MEUCIQCGQQi3LQx6fbjPM4HOhP+UuENMcvznYLW767P9K4k2nQIgPbkjWxDDLYpeOXWo5oRvLtv8XbNNt3y5pWU1ZNOk5gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbl4b+CRA9TVsSAnZWagAAKPEQAI/m2QBy4BI6dKRonUC5\nJzDLv1jphFcp3ziXqxFRr1BGxa04vHk5ZNa9KJjNcnFJ+lgwI7cCz/5g5DEO\nnv0pwKWlNpsHycviVUaa618WkZ7OtzdTPrmORKgFDP39O6lzBwinWtJlGgAn\nUKH1MQwOvQZkbYMgz5G8pjzDkaij8gDRDpCV9/Xdk/LWLykxO3ymJhFIFJIM\n58WilADFonalQDGDJLh+1Hhi1Hb1WbBdASUDhpWH3QqZgzrxVTVPB2ajYYaO\nsgad00GbigiCtRASLzQRg99OlCepmtsR4rFhu0V/XE3YZ4XSViuRNwrW1HBE\nRH2RjRxVGgfmcZd0N0Q1DzthxwIP4z5FJW4P16ODYgaTHdqCSUa4iIWc6WfW\ng8xlYDU3RgFQBcdIBq2jKxmsKU7W9KC5Fz7o8ldVW1NLbN/rPWi/qC2XKn0s\nwpl/oxroAW8enbpG3K4+kU8cizBvL3QQv3lWwEV0O5BMLmNFBZUufCYiARS1\nsYeyVEj2U+KvHWTSd/e0jB+995DbNsUbyvnfmP+O6VmsfxbrbDItBuTFUEZi\nmqN58fZ+rttNr2rqamRVq/Q0QmUHifH2+KQDm3wPzdooTHDRpWULW2ncg4dp\ngE8XRJu6L+QzCiXcVOgTv7UPhDs0erbPDYR3M+eweEUOaWQD5s8Rf5tjkOif\nQeQf\r\n=ybCb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./dist/debug.js", "gitHead": "78741cceaa01780ad2b4ba859e65ad4c9f52d65a", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.4_1536657149964_0.37588515769822384", "host": "s3://npm-registry-packages"}}, "3.2.5": {"name": "debug", "version": "3.2.5", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.5", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "c2418fbfd7a29f4d4f70ff4cea604d4b64c46407", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.5.tgz", "fileCount": 10, "integrity": "sha512-D61LaDQPQkxJ5AUM2mbSJRbPkNs/TmdmOeLAi1hgDkpDfIfetSrjmWhccwtuResSwMbACjx/xXQofvM9CE/aeg==", "signatures": [{"sig": "MEUCIGSeDyEUFxgQz8gxdE+L86oZGk9vreOdf1YbrP85iJgeAiEA8GBW50xUpACNcs33m61qWUfazf08wUpXu1UzMKbZX0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmEvWCRA9TVsSAnZWagAAtKYP/iHj/bV5z85g7wFJ/ses\nINfZRQk3gqPOpVyzb6GKr4YPQyzGCgDwo1luliwFVJ5iyOcFjYoDI0PyFqTs\ng8ZfY9t3fTl4rE6dnwsBMgslPxVrGFvBKcVuZoHVquUTH7cN+wDsU1bHGFXU\nzjKDGpSrMmC287HrBIlYPXuOy+J6jCtJa3mnJrjl20zTkHrxodQu1xol+1kC\ncNr455c99Adb1n/h7LlnYAjfLCsVBNXiPRREdhGHHf637v6fx410hnvTtxUf\nspr20o42bhvQxietxMnDmKAKRb6Buq1k8hLki7xPHfj1/PgrEMtdONYYFytG\n9QDFALnHKhCfh23qUA3+KmJtAYdcgz10+bcYbdx7ytULnQk0mldYaBe9NAFZ\n8YJ0ohZ7RL7Awt+Kf1aNM2A9uDZfgns7q6V96Gcl//+hsOt9MpMdYciJP39F\nynl7b0R8vxB1lSxq8v7rZ+Nm3I1OF7m6lFhkecEAzze7txTPlCNpfKYX2sNt\nj5SOIpXU6Mem9cve1RdzeYvGBqx9qHbpNCY33HP9p/Jvb/XfaB+E+FuOFc9T\nrc6VemaJB9uDO78sDDVQk28OliLy3cAFdFlUMrLe55Jbq2phIx+/KTN/3acH\ns50bpPpqido2tWSsTCZb4pPvMValF57ODUybu70XNxhuCznpApSNZuqRfC4Y\nOxHe\r\n=dcBJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "unpkg": "./dist/debug.js", "browser": "./src/browser.js", "gitHead": "9a6d8c20a8b92f7df1f10f343c8238760ec4902f", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.5_1536707541447_0.29116777864588417", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "debug", "version": "4.0.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.0.1", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "f9bb36d439b8d1f0dd52d8fb6b46e4ebb8c1cd5b", "tarball": "https://registry.npmjs.org/debug/-/debug-4.0.1.tgz", "fileCount": 9, "integrity": "sha512-K23FHJ/Mt404FSlp6gSZCevIbTMLX0j3fmHhUEhQ3Wq0FMODW3+cUSoLdy1Gx4polAf4t/lphhmHH35BB8cLYw==", "signatures": [{"sig": "MEYCIQCA95EspXkN2Ry3B0X6kt9SGvrbT6qmn8X5R1LdU3pgVwIhAM3YjU2IJXl4SsI2kZ9L/jUq/lBVj0YoHLLgBws28OVX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmEzQCRA9TVsSAnZWagAA5pYP/22BYzFRtIegg2BuM1G1\nkV8O9zlERv0EsFnNEyVpWKIt1wmMsPHNKJJuIpf1utE2LCyp5RC1bmrXz9LH\nPrKcmAVrsJbnXMmxahJhFbY8Wa8Vn8TCj3tAjVXKIiEIT6Np9gAb8z7ZIKXe\nxG8Su/gT4Mnm3o3KQF0FEsNHzVThP8ZUK4m9kUe4PKE5TLbsynUBW35wgW23\nQgqvM0z6gosQbnIKxeXkG/C25cRLfrGMKIU26YmAWxqpkTDGk1QvD0mPXlsd\n8UP4r1UR1eNMuRwsQOxY99kmoQ/zAoLkwqG16bl/97NkVQEOkGmmQSts8xer\nn3Ma8nsHUyTTcHafn7rBU/WUDf2uY5CdJZbjivoYVJ0weZFBw2VSOs4OGdck\nFgFtXK6nCZTWf7RL2C/75eFZoUUcjk0jK8XIrO1DK0dMojEG9gku3FqrMxk1\nCgPCZyRQV9PwcdHuZtbSBtngiKgyYmd7njibjphsjiav7ItSOx9H3YvneQbM\nSCxx+ONzX7bpIrnDqBm8htOCNUffSicZrNbgfLU8rfDjqRKNMVkiN61QKkp4\nV5V74Dlieg1nvF2jBeYU0qydeLnkzl6sa/5X8Te8Hs8juYLYPUVuO7bHEc99\nQWGYfnKrTCss21JfvPEj9qmCccyoLIscgHSmaltXQEE0dVFGlKQ20gbBmaQO\nfgDb\r\n=9EFN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "unpkg": "./dist/debug.js", "browser": "./src/browser.js", "gitHead": "4490cd95bfb952e1ed756914ac225ddc987b2ba3", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.0.1_1536707791890_0.14124621815358362", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "debug", "version": "4.1.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.1.0", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "373687bffa678b38b1cd91f861b63850035ddc87", "tarball": "https://registry.npmjs.org/debug/-/debug-4.1.0.tgz", "fileCount": 9, "integrity": "sha512-heNPJUJIqC+xB6ayLAMHaIrmN9HKa7aQO8MGqKpvCA+uJYVcvR6l5kgdrhRuwPFHU7P5/A1w0BjByPHwpfTDKg==", "signatures": [{"sig": "MEUCIGvDfzcHw21q2IQck04ISXgqOEuP6yqt2RUsavyCbF+eAiEAhdc6B3dthWEMKQgyQ17wQrIqu6U9HqEeMbGvnsN/OVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbu5kwCRA9TVsSAnZWagAADmsP/0nsKNUuT8Lrrhhn3JhQ\nRMCsVRRyKtHE3W9Rspr7TtK2EJCB2ql1Olf74KwAeqfhmrgkrxw7cPjlyzK/\nQPhAdjVF3kaKzjD5h8WdP5671EvZ8W/Jkb13keh8AggdY3pQ2QGjzXblAOnQ\n5gpHrTvbrVqW7sXeqJMAIarIA/6w+u2dWyoSPm+xM00IEW6kjV6W2gmNI1Hw\nlxLXujYghkDjJLocu9/CKpw/YJlUcxA3pL70cLwNRN/np70d5YreTaX4w2ck\nTcoWFGU3VZYsFSSDiSGJmx5WkssqQIG/i0EK6YcdWIfw80mBWroXVPuZ8N8Y\n4dvfYTtobDUqVOd/t8+y+/wRH8xxlRvdkZlayHv6R6wlTtFaY0OF1XSrpdQE\nuXjv4ze4PXHvbvnu2ZfGD5LsjNM+yEzwowUB/hSYb6DYs+piHejPH0uSmD29\ncA4PDa08spTRAcpo0eso9tXZyLHDZyBrWqqErvzH8N+HqSUUFcVJJp6K6g7T\nMzpNDBaffVrY+1U/U176lBCgDsJClqZQFqB8bYYZHCP0na6AMWsIGNF2yX6M\npYyx9RpMJLU69cBtpCvQb5dE6MRBlCPdkoE0jiF7b/IyU9WNNwESUI/C4p86\nDhhuitPmoQRvLC0CvF810ecR8I3E+JSlbOTgyYK4b5NER4KJiJk5iFtoccSQ\n0TI3\r\n=XCSo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "unpkg": "./dist/debug.js", "browser": "./src/browser.js", "gitHead": "e30e8fdbc92c4cf6b3007cd1c3ad2c3cbb82be85", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser", "build": "npm run build:debug && npm run build:test", "clean": "rimraf dist coverage", "test:node": "istanbul cover _mocha -- test.js", "build:test": "babel -d dist test.js", "build:debug": "babel -o dist/debug.js dist/debug.es6.js > dist/debug.js", "test:browser": "karma start --single-run", "posttest:node": "cat ./coverage/lcov.info | coveralls", "prebuild:debug": "mkdir -p dist && browserify --standalone debug -o dist/debug.es6.js .", "pretest:browser": "npm run build"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.1.0_1539021103154_0.029486584589888398", "host": "s3://npm-registry-packages"}}, "3.2.6": {"name": "debug", "version": "3.2.6", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.6", "maintainers": [{"name": "kolban", "email": "<EMAIL>"}, {"name": "qix", "email": "<EMAIL>"}, {"name": "qix-", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "e83d17de16d8a7efb7717edbe5fb10135eee629b", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.6.tgz", "fileCount": 10, "integrity": "sha512-mel+jf7nrtEl5Pn1Qx46zARXKDpBbvzezse7p7LqINmdoIk8PYP5SySaxEmYv6TZ0JyEKA1hsCId6DIhgITtWQ==", "signatures": [{"sig": "MEQCIHIijGksiciAGHNGP7Bl8bCTvi0L/d4T0wl68sD6akOIAiB0zPnuRu0N595zLWLqrkU3F2zgYDsHQBdTQM7Y/EjViQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvaCgCRA9TVsSAnZWagAAhucQAJjxFVneDDxkspue4yaR\nlpOoIrP8ms/Oek53bmD1y/qlwsfpS/Y/KnS1yL/qSQkEAL2qYAETIMie3Op5\niP03s2xTTrVwCUDCEtTO7oMnl6kApMiexgUkop4eznA+AfEdz/jInFiN/yDU\ntHoR1UV0bIOSccVhrjH2aTt/96AETu8B7x8J722gjPUGDni2luYM+r2V8XYh\nN/9/kNn6HN1FjfOUl662iZTLE8OAVbrnmfVS6etTiG2SSLI898s+goyOD32K\nduwANgqjKrlvSxlMxKFr46G8GR+Otk9rOwsFO7yiufJ62dYefJIw6sfWYGXa\n8joI2StGT6p9T6iTN9959Y6naoSrN0cb3DocOjGQMrxUoBugoBWKd4fD+ACU\nMN3VA2PN8cZIh5eLu5IsJAECy4frUKpzbPYR129/Rtx0K7syhcvSwBqRE4Pb\nPNDHdddx1bix8m+xyT6sAiaWym9x+WX7GPuQAlCM5xZ4lFzjNpu5LgxpIbHo\njtRF6SLdMDBSImhH6NyPcw374QT+zwOc8l2G506RfBA5bB2fsYUBLrIXLkxm\n/CzEphFk++bPvYDiCy2EFUJW5MjnschBV65QUZBdBSg2YJnl3yGS8QHC8AR0\nM1IcRqI8NUlpTByPVoc7GoevruOjXxk+B+dflYRvBNwBMDatSA0eYT9aILK0\nJp/G\r\n=QKY3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "unpkg": "./dist/debug.js", "browser": "./src/browser.js", "gitHead": "a7a17c9955460435592de2a4d3c722e9b32047a8", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.6_1539154080132_0.756699343768702", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "debug", "version": "4.1.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.1.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "3b72260255109c6b589cee050f1d516139664791", "tarball": "https://registry.npmjs.org/debug/-/debug-4.1.1.tgz", "fileCount": 9, "integrity": "sha512-pYA<PERSON>zeRo8J6KPEaJ0VWOh5Pzkbw/RetuzehGM7QRRX5he4fPHx2rdKMB256ehJCkX+XRQm16eZLqLNS8RSZXZw==", "signatures": [{"sig": "MEUCIQDkVa+IZnVIVEx2IdokPYBerr3YC/KT2D0VTyn4RLQfawIgZIb0cyBWtjg/fOudI1bPW/0bgaj0abitgNmpy1wrSz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHmj3CRA9TVsSAnZWagAAXQIP/RgGgdDQtTyIZha6xfTv\nN2WoeoMBONJYjsPPd/2uxClRNtMBRC2jUZawva5LoMkBhhsFExLCF68Di8jq\n4l1tKfSnQsCDZzprFSVJIcAEHryGPU7ZVTZC+h/HQa/QU8m+AnSCFjGtsDyy\np4I7DOsLSpBireRfB6BCZgk00ftuM+dOkof+dTKg4GVQDbYLbzMzhIRzpvjv\n2QtIickzjgRjwp8QuiEBIhf8/p4WnXrubOz4Y6LewqAbAKEHzEHXSxgiDCnY\n+vhuojGVLSdrfBS/+bYUCJxGpyCfcFivdRKJW8GG40RCKltOQhpUBIWfbfbJ\nVJ8gwfl6/A6/7RbdfRHRBwoyrpi03D5EFr0htHqrQIkeEmeU73szxti2Sag/\n3tpk2+Evcoed5tz2Vb9ZSCV7AOd3N0L5pUlZH4lrCtiIQWRnVetKwZ+mdZuE\nHWFJK6CNLyHoHw6HS+bBCUk/iLu+384UFgPb/GThxwosLpo2GXRUBncFHtTA\ngFNkRXtKJtG+MOHozkzWsmKNhsn8q4J26zpgI3snwfOqUx63sPvHkP3gcMl2\n60ZU9mCxDAtK5xmpXpzmV/ac+c3Wp2azRglbhSdfAB/RWji+KS5192bTb0nk\n+zvu6AJ2KGREgbxosEEZLVkbBvf3XtIRN1Ts3mIVLrx4rSDsDbJYqZ0IrJKl\nYAHO\r\n=bQum\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "unpkg": "./dist/debug.js", "browser": "./src/browser.js", "gitHead": "68b4dc8d8549d3924673c38fccc5d594f0a38da1", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser", "build": "npm run build:debug && npm run build:test", "clean": "rimraf dist coverage", "test:node": "istanbul cover _mocha -- test.js", "build:test": "babel -d dist test.js", "build:debug": "babel -o dist/debug.js dist/debug.es6.js > dist/debug.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls", "prebuild:debug": "mkdir -p dist && browserify --standalone debug -o dist/debug.es6.js .", "pretest:browser": "npm run build"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "small debugging utility", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.1.1_1545496822417_0.37311624175986635", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "debug", "version": "4.2.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "7f150f93920e94c58f5574c2fd01a3110effe7f1", "tarball": "https://registry.npmjs.org/debug/-/debug-4.2.0.tgz", "fileCount": 7, "integrity": "sha512-IX2ncY78vDTjZMFUdmsvIRFY2Cf4FnD0wRs+nQwJU8Lu99/tPFdb0VybiiMTPe3I6rQmwsqQqRBvxU+bZ/I8sg==", "signatures": [{"sig": "MEUCIQCMA403hjhm4im6XmOKiYBzQqj77KOAYRomJOU2C3GyiQIgN35ZoHrq7+2Icj/InlnM/zsChKR/H7eXqezdq7C1uWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJew6wfCRA9TVsSAnZWagAAEHMP/AjhPmRTEPdXCjOUZItS\nDIIrO1B0F6jnHx8QWlqxAQGIYvbYDbAvBRYrqQ3VGpY3nLWA/B93wZ0BHDV4\n4bxDUfQT1xarF5wy3NeqAU+dOrwm/5whlcpGpqPevjnpYEWXzE1YNU4Fpp+c\nJ9pJR3uLzmUpdVAFoNW44bwNRs6I0AWo/HRcR5XEo3aTI0NjAilrpTW+aWDd\n4SArxWLBZ3JZyyn3DiOMGfn9fpXWXywvD1Z4zXJ0K4BKsGN7PrmwJpQijkt5\n/kK5dGAdpzuC9+4eMV/gWD5AaNwkASRx4uTLCZLRZWIKo4FUrFl1zu0VC+Xa\nHNx439MiRA0C6fP4NLy0GGWPEum/0DC07oxICw/RbJqwCV9dGiwFNzGR+Go9\nrBBr2tC2o6ZkBG1aK34IGh6uZskGSqwD3war6H8mYqownbmZx7u+QB8fxXkS\nocdz86u4hW7w9Yhwbcs12ES/mKhQyJlwhfXBVqWL1gkHxLmH7VKMqwbZ+dZ1\nofWhSGtkvjpUTgEFe0Z9cNkVhlZ+GpunrRVDH7STtkX5m6X3rHRNAYUC/Wvr\nCT52lMahJZy8uCew+/R3P5smzvbEHBjJRb1u7JCBPhtP7hEsD1VeRlaJQWtK\nGNA6T4TV8FdrnJ9iuRjeQ+Zz6aFahkyuLezqtW+lqQpmMKll3NO4OV/4sqff\ntXxb\r\n=puc/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "80ef62a3af4df95250d77d64edfc3d0e1667e7e8", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "small debugging utility", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.2.0_1589881887045_0.10965141172270587", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "debug", "version": "4.3.0", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.3.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "efa41cbf14fc9448075367fdaaddf82376da211e", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.0.tgz", "fileCount": 7, "integrity": "sha512-jjO6JD2rKfiZQnBoRzhRTbXjHLGLfH+UtGkWLc/UXAh/rzZMyjbgn0NcfFpqT8nd1kTtFnDiJcrIFkq4UKeJVg==", "signatures": [{"sig": "MEQCIF8fO/KNUc8iXTYLbygP3aZDcmHf4eK2AsKugWlhm8MeAiBr5R7gcN3mq65W59vSPLo4uonWnZbBs+NkTc+KtUiYtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZcMNCRA9TVsSAnZWagAAca0P/0uEMRazUGSIRnfEa4kU\npiVvI22IM6VpAb961QfclCfHQtFi/MN7Ejx8Sr7moQkA4tDgqoYcQI0Vp0iI\nmp0n8MGnMZBE5Dzlg1jBjXZUXu/DNbNMEN4y9o3GQ5l1NDprQh3Q+2uLsqrU\nk5eunsfydEs9/O8kW+2W3a5vw/u0rw3YBMOvhD3kGhAs9tK1szf22Kl66MXb\n/uKhPylOnpwL08bAPoj6pVZ3yF8XTT4gRaDwMH6kykeioV518dU36SCMZSXC\njJW8WKzsIawZt3hPm0XKUJrnexDEs9/4ixkwyCDW1aAtncYLZaw3K3AXvQnX\nxTZQ3KJ7JqdQRmnfhaWPBVCHN0tZuzuNqSoNtYWRxPWjC4upgmNMQLSvAORY\nPf5nPv4m+A50UyXT5/szOmZySjZ+5CmGWWGlymM+qGV8d6u/7E8cB4+sjeSq\nRGLlHZi1yglJ8GJoSVzkE85Tqm6klZ4GO58sc60u03uouBT1njdedCCvY9A6\nEWVgv+p4aTMwjDt8A/0/TyM25958YAvzgURWKJLGIAv++vsMUsaK9SAO+9D/\ne8MBPcmZFEohHq2n30HUUAhBPVIglId+q5YsKGXgrsziEFr8mLs5HKIZQGyH\nkhXGn0HmoIQDvtVwbEQyhlXfG8ERr0Jmg/NDZ/BuIj7AvoPn/GN9GYv6S7Lb\nYETR\r\n=W19k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "3f56313c1e4a0d59c1054fb9b10026b6903bfba7", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "small debugging utility", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.0_1600504589377_0.15241949557753798", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "debug", "version": "4.3.1", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.3.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "f0d229c505e0c6d8c49ac553d1b13dc183f6b2ee", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.1.tgz", "fileCount": 7, "integrity": "sha512-doEwdvm4PCeK4K3RQN2ZC2BYUBaxwLARCqZmMjtF8a51J2Rb0xpVloFRnCODwqjpwnAoao4pelN8l3RJdv3gRQ==", "signatures": [{"sig": "MEUCIQDAofjydiAewTdyrk3Au8X9qlbfQcywTtj1KNcERBMLgAIgT1RHrHEZMrgle67COqJg2aFE0RUAzj6+eT3+CWrSIfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftmOtCRA9TVsSAnZWagAAj+UQAI46jfq9jCyyTtvPRNi6\ntJev81EWBZqIXCAegu23mYMeO53xpUS01hd8D6oL76LuJU/Tx0crXt4EAF7h\nLqS1JaKE2mLRY/+lMUbLlWtazs3wNSVZXAZRWw0j3xd9IIJieFjH9XGg0C55\nxA3iZJFC82udvs0N5/t+oLeZl/NLSXoYuvReD7CCDFJ15kjnMFXGtTyorjdc\nyoM9reoNDDb6tyPu4fCStQYNoWCNbgzI+aWMT8BEpGbTQccJyfgeF91knj8Z\na1HTcH45a/ObH9qikU7oEYJGUzDlSXkQPwOGL+qNzgBrO3fNFXwaj/f+/gL/\nTlCTQT7B1NELVdFOaLDH0xM7K7ClBgu3UAsigkkQwD2A3XoV7gohVVwN6EoE\nhc2mkQ3PZtAEJdHQQO8k6r5QrGDUmg+NPH/lfzftgOS0NyI3P6VUp0J7eRqV\nfr2me/gvppOyhlOf0QKQd7zNnmoS93GbkYgknZt3lwKosuIaLiFjFxjmmHcH\nfwzJqJyNQprWF8WTnB9v5EDk2FtaSUNFfwKF8hPYP+qMbLoAEwpfbpgMPeTu\ndt98b+rHXtvg8TOyRlhSdNeQzjSOi81nRD4C1crkQHmwQzMBQR4KBjvmOlA4\nkX+Hpfcu/phrL8PJTWdztxHfnizlc0ou00j6EkTZtiAkSDl9WQ5T0+MpoOLv\no8Bb\r\n=0Meu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "0d3d66b0eb47c5d34e1a940e8a204446fdd832cd", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "small debugging utility", "directories": {}, "_nodeVersion": "14.13.1", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.1_1605788588845_0.34190574191611", "host": "s3://npm-registry-packages"}}, "3.2.7": {"name": "debug", "version": "3.2.7", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@3.2.7", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "72580b7e9145fb39b6676f9c5e5fb100b934179a", "tarball": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "fileCount": 9, "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "signatures": [{"sig": "MEYCIQDxaBtSc6AglVVEBnfK59RqVZfqRoptzZJyX4utho0cbQIhAN/jMrSo39iuGjRg3NCRbEukH/eMtWVSVH5wTQGN4AVz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftmutCRA9TVsSAnZWagAA/CYQAJTJhsuwdX+jkmmJEglO\nuw+gVkh8mJU3SymbhfVU8HMQUN5N926iDLVwTMt4YrNNKmR7g8wkcOd9wdXh\ns7Nl4EYX5JvNp+HLxQz2yECVN1Xu80BczImTdU5BVRHuvngPZvdsCqSfSiJ1\nGLx1HgVwBXKOv9UpVUy9251MsEGt5g+1j6RGFoSn6KB4EYuLBidrga7BdXa1\nqkZ4FzCCczQB7hvN63hvPw1Y9YS9pXY6BAdGzt90qoAE7dbKCM87F2FucNXc\nClRLWxJWSaMdeSiC31cMhcj3SrVPj0V4MXqj0A86kN3hejL4WEsu/cAlySET\n6PmAWkSKdqYYnHyZEzEeQW2QoV1I5D8DLXvtgqnyRyRps9JQvfdq6ruXGyV1\nS7EWaoaCEAAg0gbO3YK5rnMza1j5hvx/2aoL7Sq8ANq8KgGbiSoYyGXCzu0y\n3Hi7HMj0q/WnDV8gYHMws11ywl9CAofYYl0KU38Aj+WtEHH0G+YlbhWzVULD\n51AJk/QZ23oyh9+/j0JwvztPJfr/caI2uP8lzZ1VPyopWSdG34oNs6oKcbWh\nWPJcbhgNzlgxcHmQdI4Wntu3vk9eey1CiHLOO7UnZXmZi8m7tGE9YS72PvEU\nmyQ1ZRatc0LnkBXTSZoR6mgjU5x07N8xgYs7KnjXYXsjO681ttYSxcvU52LF\naEE6\r\n=6xjz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "unpkg": "./dist/debug.js", "browser": "./src/browser.js", "gitHead": "338326076faaf6d230090903de97f459c4bccabc", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "small debugging utility", "directories": {}, "_nodeVersion": "14.13.1", "dependencies": {"ms": "^2.1.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"xo": "^0.23.0", "chai": "^3.5.0", "karma": "^3.0.0", "mocha": "^5.2.0", "rimraf": "^2.5.4", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "@babel/cli": "^7.0.0", "browserify": "14.4.0", "karma-chai": "^0.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "concurrently": "^3.1.0", "@babel/preset-env": "^7.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-phantomjs-launcher": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/debug_3.2.7_1605790637206_0.4902544321634046", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "debug", "version": "4.3.2", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.3.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/visionmedia/debug#readme", "bugs": {"url": "https://github.com/visionmedia/debug/issues"}, "dist": {"shasum": "f0a49c18ac8779e31d4a0c6029dfb76873c7428b", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.2.tgz", "fileCount": 7, "integrity": "sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==", "signatures": [{"sig": "MEQCIDk84u/XSlniWz6RBstoVOmq5+yKqOdqPY5pAXZowjd0AiAbN1DEtpHc0bestjnQ90X91qaZCA0Aw2C5PjGC1blkQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0O71CRA9TVsSAnZWagAA2VIP/jFd5mnKJhac3h0hU+3g\ngtEQ6wE2z+CO505w07KCt3x5zbWFXWgat9CrpA0rdePv6c42NrBNAqsLxeIM\nghlzPJjEu5sca1vuhIuog5PHjbWlpEP9w4LOiuajXmGr0K9dbuR965MGOnlj\nw/6rPxeLgxlMOutMOAeKGbvhulJrVYWG6A6PVQ4R1oAYVoNCjrvnGmKAtcRH\nAoRHHCGDAuySLkDL8j/GoCn+t2DrzNt+tqXlP4hCBUyG16FcR7pqlfEiAgbG\nkoB65zO29uBSxAoC+z82/cJySqQYFmwGGASv9t+VbNtQpmEGKOQuY6nTrCVN\nDpLVKoBwX1oKWX4TnB6bU86nyAsdVIXvE2KsKY6zq3EYpjglIsRHX4VVcWeE\nSyJ20JJRoiMFpta0fZ6r09XnswB/dvMzYtyzmvS6yLbB+OFQJNh7stMlj5rV\n4vR5ld8q6+7zLSXGji8jrViWzFPtpgDPVigDXuZsvw/g2suWkNlwTfBCHiH4\nq1sMyZBXCkd7Od7IkImXD7ImHTohCpW8K0A6zxrSaaOhSUcvMPgm7DcOrCxv\ngbUSZimviyzgaPEj82WihJBkNUrGvCT/ysB95vuSI1vVipViPkr9+TTjIQfk\nKxmNCGfjnPSvgKBqsPn50uG9zdx5btHDlVEG8z+JGayJvqPhkQBztdSor/aq\nwc6A\r\n=GyIi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "e47f96de3de5921584364b4ac91e2769d22a3b1f", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/visionmedia/debug.git", "type": "git"}, "_npmVersion": "7.0.14", "description": "small debugging utility", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.2_1607528180776_0.655691523543942", "host": "s3://npm-registry-packages"}}, "4.3.3": {"name": "debug", "version": "4.3.3", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.3.3", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "dist": {"shasum": "04266e0b70a98d4462e6e288e38259213332b664", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.3.tgz", "fileCount": 7, "integrity": "sha512-/zxw5+vh1Tfv+4Qn7a5nsbcJKPaSvCDhojn6FEl9vupwK2VCSDtEiEtqr8DFtzYFOdz63LBkxec7DYuc2jon6Q==", "signatures": [{"sig": "MEUCIDgwLgAI7itIaQdIOaujVhQRAS2WPkbeANCzrou1tqTFAiEAr1GYNDj09Vuvs8FWn8J3vBrOAWIyAYknyEcKVh3tp4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoi8wCRA9TVsSAnZWagAAMQIQAIBVX5eU9r89GpL0l/jh\nAeLvydJ2+QkHftmS+SqCXxx9d2YYiPGdSdvSt+iVR4oR1YFpa815iJAXeI6Q\n3HvInF5rZh2Xdh4JJN3eJjm0A93jIs0LHpRQw2LCUbbNQfXLprzVcrlFNSJX\nfdTA6yPTU2xJfbE0vCUI7eqY/fopE7j+VywFz1t74k3yQLHjvO6lZdvvH9zZ\nVlGjZA0YRiDVvnHckjiK3req/qxeDHchRaXvOCy7NFk0KWjBNjB4fU+skDJa\nl1TtnsRC4ApvXVKdb4FtzKjVSnybA0pOa6ZCWLwzk1rEKAiKOy6HRka579UX\n5DAv8vfWe6ssdkhTWP76b92TCorKJZGP7V2odekPXMuh5HdD9YyWERWQk+jH\npOQ9nSRriSdGvpq1EpSm35nB/PMe6x/MlRCdTRfWtqXbZrShcuiZQ2DJkpjV\nXXhAl8edrt5mDGS8K6/6ToSFjyApNfdtzBBvtAOmKZnd55prw3CndkAGyA9W\n9h8RTIjaAcN7mfB/VC5rcIqKwHZu3UrYRlDqAAXjR/KWKlLkGal9EPJBUuy8\nTqjXrAQJRh23WFHnskZFwcZ1Jzbq82DgN8j6k+o3Dg5yMXA/uziM0wuf6LKe\nbduB5k1PPHnHusUiJt7L5GQs7JNmQ7nBq1spTjChbMU6LsAt7sxfb+BaUJO4\nmtBR\r\n=LlaO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "043d3cd17d30af45f71d2beab4ec7abfc9936e9e", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.3_1638018864282_0.4082672439443171", "host": "s3://npm-registry-packages"}}, "4.3.4": {"name": "debug", "version": "4.3.4", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "debug@4.3.4", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "dist": {"shasum": "1319f6579357f2338d3337d2cdd4914bb5dcc865", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "fileCount": 7, "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "signatures": [{"sig": "MEYCIQCmcTC92IvJtPeBp+GZTfC1ozrd1sQ/d3Se3U7wCosyfAIhAKHyskAwrm7eSg/krxHRnxiCP8ig1GxbJ5psYv8Pwfn9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMznnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAgw/+NHpSQ+LWMx6GQJ9qSYwtaT3YjCidhAMmCsTrWdsCd2GjfWmr\r\nZMbQmyd8ACK1L6dxCegWa8bOms8vCiQCFukzFRYxeXSYkVqPueHtfXbEDVwk\r\nC8uVdifmtXYp7bamROLrRzKilsVAshn19poz28DRH48pbaNh7yVnvT89DJji\r\nMx0u7xOZHJ7dhniwenivY1zd+gnvAgoXAioWJg1echYSfVCzfrex1KPzwc0I\r\n1Eo+qtSCgotylm37OYVPLoY/yDSeIydL3F56XtzVXpukZ1G3fKD6o9zSfrqt\r\nbrujSxRo0xys4J5kbj5ONaiwLhUpTxh7UdOLhrdZBM3/D29Hz9Do076WngmQ\r\nUoCg2Qh3b05eOvVSuU1KLPg25NDM3wXNWctFyoGFBvbor5ITWZY1W4IqcDvC\r\nxpYYOlJ75evHmouPikVJXEd67qSzs0Lb7jAhrewoBY7YH8Imljk4mzt++cJ6\r\nm69zCwbiQLULYUieLcON/Aplb//9pvQUycP2604gcdgf45NyPx08vjMmnWCt\r\nv0szJjclPl/UQr9w4yg9Tf4YZtgcNfEOnUVKZ9TH/w8B9sOWEg0Qx/diaroJ\r\nN0KZVXaIWvVMKUioK40VYUILPOjoAx9KIT0m6KuMGx+eZtPN1PXO64cpWxlB\r\nzUJ3TguaRW12vECh6zyfkyUXh0C4ADFDYp4=\r\n=9E9I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "da66c86c5fd71ef570f36b5b1edfa4472149f1bc", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "17.3.1", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.4_1647524327516_0.6784624450052874", "host": "s3://npm-registry-packages"}}, "4.3.5": {"name": "debug", "version": "4.3.5", "keywords": ["debug", "log", "debugger"], "author": {"url": "https://github.com/qix-", "name": "<PERSON>"}, "license": "MIT", "_id": "debug@4.3.5", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "dist": {"shasum": "e83444eceb9fedd4a1da56d671ae2446a01a6e1e", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.5.tgz", "fileCount": 7, "integrity": "sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==", "signatures": [{"sig": "MEUCIQD2eFpf3p60i2+rFrwBiP8ctewWXYfqZxZvMEU/XyX/xAIgJXRFOrWWj+tLfHrd400HHT/bz+yV4Edh8cAmQFDP3Jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42430}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "5464bdddbc6f91b2aef2ad20650d3a6cfd9fcc3a", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "21.3.0", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "sinon": "^14.0.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.5_1717155615743_0.8263391721249294", "host": "s3://npm-registry-packages"}}, "4.3.6": {"name": "debug", "version": "4.3.6", "keywords": ["debug", "log", "debugger"], "author": {"url": "https://github.com/qix-", "name": "<PERSON>"}, "license": "MIT", "_id": "debug@4.3.6", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "dist": {"shasum": "2ab2c38fbaffebf8aa95fdfe6d88438c7a13c52b", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.6.tgz", "fileCount": 7, "integrity": "sha512-O/09Bd4Z1fBrU4VzkhFqVgpPzaGbw6Sm9FEkBT1A/YBXQFGuuSxa1dN2nxgxS34JmKXqYx8CZAwEVoJFImUXIg==", "signatures": [{"sig": "MEUCICM4wOOFMyB8VKp5QzbkLY6P1j0pTYU1aD3/Y1GyMqLJAiEA3cJIP+BsJ2L4IL9vHlNz7sFwmwazGOpJnO/vx2qCI0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42440}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "c33b464a797d6cf8c72b8d84d87e02b2822494c9", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"ms": "2.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "sinon": "^14.0.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.6_1722072204675_0.7142707761781839", "host": "s3://npm-registry-packages"}}, "4.3.7": {"name": "debug", "version": "4.3.7", "keywords": ["debug", "log", "debugger"], "author": {"url": "https://github.com/qix-", "name": "<PERSON>"}, "license": "MIT", "_id": "debug@4.3.7", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "dist": {"shasum": "87945b4151a011d76d95a198d7111c865c360a52", "tarball": "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz", "fileCount": 7, "integrity": "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ==", "signatures": [{"sig": "MEUCIQCMQx9T+oP2ZGPLho8hL50/ibLz9EuJF2iS6VVaDwS96QIgVC4PdM67/9mYyo/GRZXILo/xg+gUPfdsrHsM5IOOBsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42060}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "bc60914816e5e45a5fff1cd638410438fc317521", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"ms": "^2.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "sinon": "^14.0.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.3.7_1725583977461_0.3189367575089168", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "debug", "version": "4.4.0", "keywords": ["debug", "log", "debugger"], "author": {"url": "https://github.com/qix-", "name": "<PERSON>"}, "license": "MIT", "_id": "debug@4.4.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "thebigredgeek", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "http://n8.io", "name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/debug-js/debug#readme", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "xo": {"rules": {"import/extensions": "off"}}, "dist": {"shasum": "2b3f2aea2ffeb776477460267377dc8710faba8a", "tarball": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "fileCount": 7, "integrity": "sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==", "signatures": [{"sig": "MEQCIAOUmdup8dsL7tpvMHDHweICku/LOxrG65yY7CZtqlhJAiAfx4wuksiMJlmkVdO/tCvfps4NFVnFhyH7k/lD64R/Sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42798}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "gitHead": "7e3814cc603bf64fdd69e714e0cf5611ec31f43b", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "istanbul cover _mocha -- test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/debug-js/debug.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Lightweight debugging utility for Node.js and the browser", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"ms": "^2.1.3"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "brfs": "^2.0.1", "karma": "^3.1.4", "mocha": "^5.2.0", "sinon": "^14.0.0", "istanbul": "^0.4.5", "coveralls": "^3.0.2", "browserify": "^16.2.3", "karma-mocha": "^1.3.0", "karma-browserify": "^6.0.0", "mocha-lcov-reporter": "^1.2.0", "karma-chrome-launcher": "^2.2.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/debug_4.4.0_1733488366116_0.7027776010484381", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "debug", "version": "4.4.1", "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "keywords": ["debug", "log", "debugger"], "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "scripts": {"lint": "xo", "test": "npm run test:node && npm run test:browser && npm run lint", "test:node": "mocha test.js test.node.js", "test:browser": "karma start --single-run", "test:coverage": "cat ./coverage/lcov.info | coveralls"}, "dependencies": {"ms": "^2.1.3"}, "devDependencies": {"brfs": "^2.0.1", "browserify": "^16.2.3", "coveralls": "^3.0.2", "karma": "^3.1.4", "karma-browserify": "^6.0.0", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "mocha": "^5.2.0", "mocha-lcov-reporter": "^1.2.0", "sinon": "^14.0.0", "xo": "^0.23.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}, "main": "./src/index.js", "browser": "./src/browser.js", "engines": {"node": ">=6.0"}, "xo": {"rules": {"import/extensions": "off"}}, "_id": "debug@4.4.1", "gitHead": "33330fa8616b9b33f29f7674747be77266878ba6", "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "homepage": "https://github.com/debug-js/debug#readme", "_nodeVersion": "22.15.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "shasum": "e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b", "tarball": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "fileCount": 7, "unpackedSize": 42793, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFYe34T3MTMz7GUYtvascO8kejy1IQelEEiXvtCY7ZJzAiEA65tNrHJkzI1TkrE1u4sq8InCFcWUG4iblPW3GCW5Ni8="}]}, "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/debug_4.4.1_1747169796263_0.8130823384432215"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-11-29T01:11:23.618Z", "modified": "2025-05-13T20:56:36.586Z", "0.0.1": "2011-11-29T01:11:25.405Z", "0.1.0": "2011-12-02T23:16:56.971Z", "0.2.0": "2012-01-22T18:26:41.329Z", "0.3.0": "2012-01-27T00:37:12.739Z", "0.4.0": "2012-02-01T21:20:47.417Z", "0.4.1": "2012-02-02T19:54:44.139Z", "0.5.0": "2012-02-03T00:56:44.457Z", "0.6.0": "2012-03-16T21:58:51.296Z", "0.7.0": "2012-07-09T19:11:59.699Z", "0.7.1": "2013-02-06T21:53:43.587Z", "0.7.2": "2013-02-06T23:40:19.513Z", "0.7.3": "2013-10-31T00:51:26.848Z", "0.7.4": "2013-11-13T20:08:37.779Z", "0.8.0": "2014-03-30T16:00:17.026Z", "0.8.1": "2014-04-15T02:04:45.652Z", "1.0.0": "2014-06-05T03:55:56.207Z", "1.0.1": "2014-06-06T20:23:09.807Z", "1.0.2": "2014-06-11T00:50:47.529Z", "1.0.3": "2014-07-09T16:16:47.588Z", "1.0.4": "2014-07-15T23:16:08.284Z", "2.0.0": "2014-09-01T07:21:43.687Z", "2.1.0": "2014-10-15T21:58:41.028Z", "2.1.1": "2014-12-29T21:51:01.149Z", "2.1.2": "2015-03-02T01:39:40.274Z", "2.1.3": "2015-03-13T18:50:21.566Z", "2.2.0": "2015-05-10T07:21:25.639Z", "2.3.0": "2016-11-07T17:40:37.812Z", "2.3.1": "2016-11-10T00:14:23.056Z", "2.3.2": "2016-11-10T06:30:04.055Z", "2.3.3": "2016-11-19T19:59:18.541Z", "2.4.0": "2016-12-14T06:52:06.597Z", "2.4.1": "2016-12-14T07:25:40.783Z", "2.4.2": "2016-12-14T19:40:21.566Z", "2.4.3": "2016-12-14T21:50:00.788Z", "2.4.4": "2016-12-15T01:27:05.600Z", "2.4.5": "2016-12-18T07:13:49.109Z", "2.5.0": "2016-12-21T05:03:29.680Z", "2.5.1": "2016-12-21T05:33:20.503Z", "2.5.2": "2016-12-26T02:39:46.961Z", "2.6.0": "2016-12-29T05:50:33.866Z", "2.6.1": "2017-02-10T19:00:28.639Z", "2.6.2": "2017-03-10T19:44:26.365Z", "2.6.3": "2017-03-14T03:50:34.042Z", "2.6.4": "2017-04-20T18:08:07.089Z", "2.6.5": "2017-04-27T16:04:12.415Z", "2.6.6": "2017-04-27T23:35:02.119Z", "2.6.7": "2017-05-17T04:33:51.578Z", "2.6.8": "2017-05-18T20:07:01.168Z", "1.0.5": "2017-06-15T00:14:24.388Z", "3.0.0": "2017-08-08T21:55:59.088Z", "3.0.1": "2017-08-24T19:44:31.890Z", "2.6.9": "2017-09-22T13:32:35.541Z", "3.1.0": "2017-09-26T19:13:51.492Z", "3.2.0": "2018-09-11T06:19:14.567Z", "3.2.1": "2018-09-11T06:28:53.798Z", "3.2.2": "2018-09-11T07:50:29.987Z", "3.2.3": "2018-09-11T08:30:38.788Z", "4.0.0": "2018-09-11T08:58:14.825Z", "3.2.4": "2018-09-11T09:12:30.102Z", "3.2.5": "2018-09-11T23:12:21.584Z", "4.0.1": "2018-09-11T23:16:32.204Z", "4.1.0": "2018-10-08T17:51:43.321Z", "3.2.6": "2018-10-10T06:48:00.226Z", "4.1.1": "2018-12-22T16:40:22.538Z", "4.2.0": "2020-05-19T09:51:27.149Z", "4.3.0": "2020-09-19T08:36:29.497Z", "4.3.1": "2020-11-19T12:23:08.941Z", "3.2.7": "2020-11-19T12:57:17.399Z", "4.3.2": "2020-12-09T15:36:20.909Z", "4.3.3": "2021-11-27T13:14:24.425Z", "4.3.4": "2022-03-17T13:38:47.641Z", "4.3.5": "2024-05-31T11:40:15.895Z", "4.3.6": "2024-07-27T09:23:24.842Z", "4.3.7": "2024-09-06T00:52:57.702Z", "4.4.0": "2024-12-06T12:32:46.280Z", "4.4.1": "2025-05-13T20:56:36.431Z"}, "bugs": {"url": "https://github.com/debug-js/debug/issues"}, "author": {"name": "<PERSON>", "url": "https://github.com/qix-"}, "license": "MIT", "homepage": "https://github.com/debug-js/debug#readme", "keywords": ["debug", "log", "debugger"], "repository": {"type": "git", "url": "git://github.com/debug-js/debug.git"}, "description": "Lightweight debugging utility for Node.js and the browser", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}], "readme": "# debug\n[![OpenCollective](https://opencollective.com/debug/backers/badge.svg)](#backers)\n[![OpenCollective](https://opencollective.com/debug/sponsors/badge.svg)](#sponsors)\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/71256/29091486-fa38524c-7c37-11e7-895f-e7ec8e1039b6.png\">\n\nA tiny JavaScript debugging utility modelled after Node.js core's debugging\ntechnique. Works in Node.js and web browsers.\n\n## Installation\n\n```bash\n$ npm install debug\n```\n\n## Usage\n\n`debug` exposes a function; simply pass this function the name of your module, and it will return a decorated version of `console.error` for you to pass debug statements to. This will allow you to toggle the debug output for different parts of your module as well as the module as a whole.\n\nExample [_app.js_](./examples/node/app.js):\n\n```js\nvar debug = require('debug')('http')\n  , http = require('http')\n  , name = 'My App';\n\n// fake app\n\ndebug('booting %o', name);\n\nhttp.createServer(function(req, res){\n  debug(req.method + ' ' + req.url);\n  res.end('hello\\n');\n}).listen(3000, function(){\n  debug('listening');\n});\n\n// fake worker of some kind\n\nrequire('./worker');\n```\n\nExample [_worker.js_](./examples/node/worker.js):\n\n```js\nvar a = require('debug')('worker:a')\n  , b = require('debug')('worker:b');\n\nfunction work() {\n  a('doing lots of uninteresting work');\n  setTimeout(work, Math.random() * 1000);\n}\n\nwork();\n\nfunction workb() {\n  b('doing some work');\n  setTimeout(workb, Math.random() * 2000);\n}\n\nworkb();\n```\n\nThe `DEBUG` environment variable is then used to enable these based on space or\ncomma-delimited names.\n\nHere are some examples:\n\n<img width=\"647\" alt=\"screen shot 2017-08-08 at 12 53 04 pm\" src=\"https://user-images.githubusercontent.com/71256/29091703-a6302cdc-7c38-11e7-8304-7c0b3bc600cd.png\">\n<img width=\"647\" alt=\"screen shot 2017-08-08 at 12 53 38 pm\" src=\"https://user-images.githubusercontent.com/71256/29091700-a62a6888-7c38-11e7-800b-db911291ca2b.png\">\n<img width=\"647\" alt=\"screen shot 2017-08-08 at 12 53 25 pm\" src=\"https://user-images.githubusercontent.com/71256/29091701-a62ea114-7c38-11e7-826a-2692bedca740.png\">\n\n#### Windows command prompt notes\n\n##### CMD\n\nOn Windows the environment variable is set using the `set` command.\n\n```cmd\nset DEBUG=*,-not_this\n```\n\nExample:\n\n```cmd\nset DEBUG=* & node app.js\n```\n\n##### PowerShell (VS Code default)\n\nPowerShell uses different syntax to set environment variables.\n\n```cmd\n$env:DEBUG = \"*,-not_this\"\n```\n\nExample:\n\n```cmd\n$env:DEBUG='app';node app.js\n```\n\nThen, run the program to be debugged as usual.\n\nnpm script example:\n```js\n  \"windowsDebug\": \"@powershell -Command $env:DEBUG='*';node app.js\",\n```\n\n## Namespace Colors\n\nEvery debug instance has a color generated for it based on its namespace name.\nThis helps when visually parsing the debug output to identify which debug instance\na debug line belongs to.\n\n#### Node.js\n\nIn Node.js, colors are enabled when stderr is a TTY. You also _should_ install\nthe [`supports-color`](https://npmjs.org/supports-color) module alongside debug,\notherwise debug will only use a small handful of basic colors.\n\n<img width=\"521\" src=\"https://user-images.githubusercontent.com/71256/29092181-47f6a9e6-7c3a-11e7-9a14-1928d8a711cd.png\">\n\n#### Web Browser\n\nColors are also enabled on \"Web Inspectors\" that understand the `%c` formatting\noption. These are WebKit web inspectors, Firefox ([since version\n31](https://hacks.mozilla.org/2014/05/editable-box-model-multiple-selection-sublime-text-keys-much-more-firefox-developer-tools-episode-31/))\nand the Firebug plugin for Firefox (any version).\n\n<img width=\"524\" src=\"https://user-images.githubusercontent.com/71256/29092033-b65f9f2e-7c39-11e7-8e32-f6f0d8e865c1.png\">\n\n\n## Millisecond diff\n\nWhen actively developing an application it can be useful to see when the time spent between one `debug()` call and the next. Suppose for example you invoke `debug()` before requesting a resource, and after as well, the \"+NNNms\" will show you how much time was spent between calls.\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/71256/29091486-fa38524c-7c37-11e7-895f-e7ec8e1039b6.png\">\n\nWhen stdout is not a TTY, `Date#toISOString()` is used, making it more useful for logging the debug information as shown below:\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/71256/29091956-6bd78372-7c39-11e7-8c55-c948396d6edd.png\">\n\n\n## Conventions\n\nIf you're using this in one or more of your libraries, you _should_ use the name of your library so that developers may toggle debugging as desired without guessing names. If you have more than one debuggers you _should_ prefix them with your library name and use \":\" to separate features. For example \"bodyParser\" from Connect would then be \"connect:bodyParser\".  If you append a \"*\" to the end of your name, it will always be enabled regardless of the setting of the DEBUG environment variable.  You can then use it for normal output as well as debug output.\n\n## Wildcards\n\nThe `*` character may be used as a wildcard. Suppose for example your library has\ndebuggers named \"connect:bodyParser\", \"connect:compress\", \"connect:session\",\ninstead of listing all three with\n`DEBUG=connect:bodyParser,connect:compress,connect:session`, you may simply do\n`DEBUG=connect:*`, or to run everything using this module simply use `DEBUG=*`.\n\nYou can also exclude specific debuggers by prefixing them with a \"-\" character.\nFor example, `DEBUG=*,-connect:*` would include all debuggers except those\nstarting with \"connect:\".\n\n## Environment Variables\n\nWhen running through Node.js, you can set a few environment variables that will\nchange the behavior of the debug logging:\n\n| Name      | Purpose                                         |\n|-----------|-------------------------------------------------|\n| `DEBUG`   | Enables/disables specific debugging namespaces. |\n| `DEBUG_HIDE_DATE` | Hide date from debug output (non-TTY).  |\n| `DEBUG_COLORS`| Whether or not to use colors in the debug output. |\n| `DEBUG_DEPTH` | Object inspection depth.                    |\n| `DEBUG_SHOW_HIDDEN` | Shows hidden properties on inspected objects. |\n\n\n__Note:__ The environment variables beginning with `DEBUG_` end up being\nconverted into an Options object that gets used with `%o`/`%O` formatters.\nSee the Node.js documentation for\n[`util.inspect()`](https://nodejs.org/api/util.html#util_util_inspect_object_options)\nfor the complete list.\n\n## Formatters\n\nDebug uses [printf-style](https://wikipedia.org/wiki/Printf_format_string) formatting.\nBelow are the officially supported formatters:\n\n| Formatter | Representation |\n|-----------|----------------|\n| `%O`      | Pretty-print an Object on multiple lines. |\n| `%o`      | Pretty-print an Object all on a single line. |\n| `%s`      | String. |\n| `%d`      | Number (both integer and float). |\n| `%j`      | JSON. Replaced with the string '[Circular]' if the argument contains circular references. |\n| `%%`      | Single percent sign ('%'). This does not consume an argument. |\n\n\n### Custom formatters\n\nYou can add custom formatters by extending the `debug.formatters` object.\nFor example, if you wanted to add support for rendering a Buffer as hex with\n`%h`, you could do something like:\n\n```js\nconst createDebug = require('debug')\ncreateDebug.formatters.h = (v) => {\n  return v.toString('hex')\n}\n\n// …elsewhere\nconst debug = createDebug('foo')\ndebug('this is hex: %h', new Buffer('hello world'))\n//   foo this is hex: 68656c6c6f20776f726c6421 +0ms\n```\n\n\n## Browser Support\n\nYou can build a browser-ready script using [browserify](https://github.com/substack/node-browserify),\nor just use the [browserify-as-a-service](https://wzrd.in/) [build](https://wzrd.in/standalone/debug@latest),\nif you don't want to build it yourself.\n\nDebug's enable state is currently persisted by `localStorage`.\nConsider the situation shown below where you have `worker:a` and `worker:b`,\nand wish to debug both. You can enable this using `localStorage.debug`:\n\n```js\nlocalStorage.debug = 'worker:*'\n```\n\nAnd then refresh the page.\n\n```js\na = debug('worker:a');\nb = debug('worker:b');\n\nsetInterval(function(){\n  a('doing some work');\n}, 1000);\n\nsetInterval(function(){\n  b('doing some work');\n}, 1200);\n```\n\nIn Chromium-based web browsers (e.g. Brave, Chrome, and Electron), the JavaScript console will—by default—only show messages logged by `debug` if the \"Verbose\" log level is _enabled_.\n\n<img width=\"647\" src=\"https://user-images.githubusercontent.com/7143133/152083257-29034707-c42c-4959-8add-3cee850e6fcf.png\">\n\n## Output streams\n\n  By default `debug` will log to stderr, however this can be configured per-namespace by overriding the `log` method:\n\nExample [_stdout.js_](./examples/node/stdout.js):\n\n```js\nvar debug = require('debug');\nvar error = debug('app:error');\n\n// by default stderr is used\nerror('goes to stderr!');\n\nvar log = debug('app:log');\n// set this namespace to log via console.log\nlog.log = console.log.bind(console); // don't forget to bind to console!\nlog('goes to stdout');\nerror('still goes to stderr!');\n\n// set all output to go via console.info\n// overrides all per-namespace log settings\ndebug.log = console.info.bind(console);\nerror('now goes to stdout via console.info');\nlog('still goes to stdout, but via console.info now');\n```\n\n## Extend\nYou can simply extend debugger \n```js\nconst log = require('debug')('auth');\n\n//creates new debug instance with extended namespace\nconst logSign = log.extend('sign');\nconst logLogin = log.extend('login');\n\nlog('hello'); // auth hello\nlogSign('hello'); //auth:sign hello\nlogLogin('hello'); //auth:login hello\n```\n\n## Set dynamically\n\nYou can also enable debug dynamically by calling the `enable()` method :\n\n```js\nlet debug = require('debug');\n\nconsole.log(1, debug.enabled('test'));\n\ndebug.enable('test');\nconsole.log(2, debug.enabled('test'));\n\ndebug.disable();\nconsole.log(3, debug.enabled('test'));\n\n```\n\nprint :   \n```\n1 false\n2 true\n3 false\n```\n\nUsage :  \n`enable(namespaces)`  \n`namespaces` can include modes separated by a colon and wildcards.\n   \nNote that calling `enable()` completely overrides previously set DEBUG variable : \n\n```\n$ DEBUG=foo node -e 'var dbg = require(\"debug\"); dbg.enable(\"bar\"); console.log(dbg.enabled(\"foo\"))'\n=> false\n```\n\n`disable()`\n\nWill disable all namespaces. The functions returns the namespaces currently\nenabled (and skipped). This can be useful if you want to disable debugging\ntemporarily without knowing what was enabled to begin with.\n\nFor example:\n\n```js\nlet debug = require('debug');\ndebug.enable('foo:*,-foo:bar');\nlet namespaces = debug.disable();\ndebug.enable(namespaces);\n```\n\nNote: There is no guarantee that the string will be identical to the initial\nenable string, but semantically they will be identical.\n\n## Checking whether a debug target is enabled\n\nAfter you've created a debug instance, you can determine whether or not it is\nenabled by checking the `enabled` property:\n\n```javascript\nconst debug = require('debug')('http');\n\nif (debug.enabled) {\n  // do stuff...\n}\n```\n\nYou can also manually toggle this property to force the debug instance to be\nenabled or disabled.\n\n## Usage in child processes\n\nDue to the way `debug` detects if the output is a TTY or not, colors are not shown in child processes when `stderr` is piped. A solution is to pass the `DEBUG_COLORS=1` environment variable to the child process.  \nFor example:\n\n```javascript\nworker = fork(WORKER_WRAP_PATH, [workerPath], {\n  stdio: [\n    /* stdin: */ 0,\n    /* stdout: */ 'pipe',\n    /* stderr: */ 'pipe',\n    'ipc',\n  ],\n  env: Object.assign({}, process.env, {\n    DEBUG_COLORS: 1 // without this settings, colors won't be shown\n  }),\n});\n\nworker.stderr.pipe(process.stderr, { end: false });\n```\n\n\n## Authors\n\n - TJ Holowaychuk\n - Nathan Rajlich\n - Andrew Rhyne\n - Josh Junon\n\n## Backers\n\nSupport us with a monthly donation and help us continue our activities. [[Become a backer](https://opencollective.com/debug#backer)]\n\n<a href=\"https://opencollective.com/debug/backer/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/backer/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/backer/29/avatar.svg\"></a>\n\n\n## Sponsors\n\nBecome a sponsor and get your logo on our README on Github with a link to your site. [[Become a sponsor](https://opencollective.com/debug#sponsor)]\n\n<a href=\"https://opencollective.com/debug/sponsor/0/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/0/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/1/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/1/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/2/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/2/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/3/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/3/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/4/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/4/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/5/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/5/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/6/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/6/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/7/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/7/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/8/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/8/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/9/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/9/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/10/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/10/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/11/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/11/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/12/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/12/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/13/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/13/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/14/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/14/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/15/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/15/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/16/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/16/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/17/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/17/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/18/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/18/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/19/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/19/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/20/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/20/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/21/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/21/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/22/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/22/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/23/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/23/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/24/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/24/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/25/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/25/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/26/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/26/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/27/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/27/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/28/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/28/avatar.svg\"></a>\n<a href=\"https://opencollective.com/debug/sponsor/29/website\" target=\"_blank\"><img src=\"https://opencollective.com/debug/sponsor/29/avatar.svg\"></a>\n\n## License\n\n(The MIT License)\n\nCopyright (c) 2014-2017 TJ Holowaychuk &lt;<EMAIL>&gt;\nCopyright (c) 2018-2021 Josh Junon\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "users": {"af": true, "po": true, "yi": true, "52u": true, "azz": true, "gvn": true, "jwv": true, "pid": true, "pwn": true, "sdt": true, "viz": true, "ymk": true, "andr": true, "binq": true, "cdll": true, "cedx": true, "cisc": true, "dbck": true, "detj": true, "doxy": true, "etiv": true, "faas": true, "fank": true, "ferx": true, "fibo": true, "fill": true, "gher": true, "glek": true, "hain": true, "hema": true, "huyz": true, "iolo": true, "iroc": true, "j3kz": true, "jits": true, "kai_": true, "meeh": true, "mkoc": true, "n1kk": true, "naij": true, "npsm": true, "qmmr": true, "ramy": true, "rgbz": true, "shan": true, "tpkn": true, "usex": true, "vasz": true, "vwal": true, "z164": true, "2lach": true, "abdul": true, "alimd": true, "antjw": true, "arefm": true, "assaf": true, "brpaz": true, "clemo": true, "conzi": true, "cshao": true, "dec_f": true, "dmitr": true, "dralc": true, "etsit": true, "eyson": true, "garek": true, "geilt": true, "goody": true, "haeck": true, "hndev": true, "holly": true, "iisii": true, "irnnr": true, "jream": true, "julon": true, "junos": true, "kremr": true, "laiff": true, "laomu": true, "lgh06": true, "lijsh": true, "lusai": true, "m42am": true, "miloc": true, "mkany": true, "nagra": true, "nak2k": true, "necr0": true, "nelix": true, "noah_": true, "oroce": true, "patoi": true, "raulb": true, "ritsu": true, "sbskl": true, "slurm": true, "stany": true, "subso": true, "suddi": true, "temsa": true, "tht13": true, "weisk": true, "xrush": true, "xyyjk": true, "yatsu": true, "yikuo": true, "71emj1": true, "ackhub": true, "adamlu": true, "adeelp": true, "ajduke": true, "akarem": true, "aolu11": true, "appnus": true, "arttse": true, "bhedge": true, "binnng": true, "budnix": true, "buzuli": true, "clwm01": true, "cr8tiv": true, "ctlnrd": true, "d-band": true, "darosh": true, "dbendy": true, "dkblay": true, "dreamh": true, "drudge": true, "edjroz": true, "endsun": true, "eswat2": true, "fgmnts": true, "figroc": true, "gabeio": true, "glebec": true, "godion": true, "gomoto": true, "h0ward": true, "haroun": true, "helcat": true, "huarse": true, "hyzhak": true, "iamwiz": true, "isa424": true, "isayme": true, "itcorp": true, "itesic": true, "itsakt": true, "joni3k": true, "kachar": true, "kazem1": true, "knoja4": true, "koslun": true, "kungkk": true, "leakon": true, "legacy": true, "ljmf00": true, "maschs": true, "mattqs": true, "maxidr": true, "mdang8": true, "mig38m": true, "minghe": true, "monjer": true, "mrzmmr": true, "nanook": true, "nhz.io": true, "nuwaio": true, "orkisz": true, "pigram": true, "quafoo": true, "rahman": true, "sajera": true, "sdove1": true, "seanjh": true, "semir2": true, "shriek": true, "sprjrx": true, "tcrowe": true, "tedyhy": true, "thefox": true, "tigefa": true, "tribou": true, "true0r": true, "vcboom": true, "vidhuz": true, "vutran": true, "womjoy": true, "xsilen": true, "xu_q90": true, "yhui02": true, "zhoutk": true, "8legged": true, "algonzo": true, "asaupup": true, "atomgao": true, "barenko": true, "boyw165": true, "carlton": true, "chrisco": true, "chzhewl": true, "cphayim": true, "cslater": true, "deubaka": true, "dkannan": true, "donkino": true, "donotor": true, "dr_blue": true, "drewigg": true, "dyakovk": true, "evkline": true, "fatelei": true, "funroll": true, "gpuente": true, "holyzfy": true, "itonyyo": true, "janoskk": true, "jhairau": true, "jonyweb": true, "joylobo": true, "jrejaud": true, "keenwon": true, "kontrax": true, "kxbrand": true, "laoshaw": true, "laudeon": true, "liunian": true, "markoni": true, "nadimix": true, "nogirev": true, "nohomey": true, "nromano": true, "nwinant": true, "onbjerg": true, "paroczi": true, "passcod": true, "pdedkov": true, "perrywu": true, "pobrien": true, "preco21": true, "rafamel": true, "rich-97": true, "rlgomes": true, "rmjames": true, "robsoer": true, "sahilsk": true, "skarlso": true, "sprying": true, "stuwest": true, "tangchr": true, "tbeseda": true, "tellnes": true, "thiagoh": true, "tin-lek": true, "tooyond": true, "tophsic": true, "ubenzer": true, "udnisap": true, "ungurys": true, "vivekrp": true, "wenbing": true, "xfloops": true, "xtx1130": true, "y-a-v-a": true, "yakumat": true, "yangtze": true, "yokubee": true, "1cr18ni9": true, "abhisekp": true, "aggrotek": true, "ahvonenj": true, "akinjide": true, "alexkval": true, "arifulhb": true, "bapinney": true, "bblackwo": true, "behumble": true, "benzaita": true, "buritica": true, "buru1020": true, "cetincem": true, "cmechlin": true, "colageno": true, "danielye": true, "daskepon": true, "deerflow": true, "demian85": true, "dexteryy": true, "dingdean": true, "djviolin": true, "dresende": true, "edalorzo": true, "elussich": true, "erickeno": true, "ethancai": true, "evandrix": true, "gaganblr": true, "heineiuo": true, "helderam": true, "hex20dec": true, "honzajde": true, "hugovila": true, "huiyifyj": true, "hyanghai": true, "iamninad": true, "isalutin": true, "jdpagley": true, "jianping": true, "jimmyboh": true, "joelbair": true, "johniexu": true, "jon_shen": true, "jonathas": true, "josudoey": true, "kilkelly": true, "klombomb": true, "koobitor": true, "koulmomo": true, "krabello": true, "kruemelo": true, "krzych93": true, "ksangita": true, "leonzhao": true, "losymear": true, "lousando": true, "luislobo": true, "lunelson": true, "masonwan": true, "meggesje": true, "mhaidarh": true, "mhfrantz": true, "millercl": true, "mohokh67": true, "nalindak": true, "nightire": true, "nketchum": true, "nohjoono": true, "nukisman": true, "oboochin": true, "pandabao": true, "pddivine": true, "philosec": true, "piascikj": true, "pldin601": true, "popomore": true, "qbylucky": true, "qddegtya": true, "rbartoli": true, "rdmclin2": true, "rochejul": true, "rogeruiz": true, "ronin161": true, "santihbc": true, "satans17": true, "sbrajesh": true, "shiva127": true, "sibawite": true, "slowfish": true, "slowmove": true, "ssljivic": true, "sumit270": true, "szymex73": true, "thekuzia": true, "thor_bux": true, "tomasmax": true, "vishwasc": true, "voischev": true, "wangfeia": true, "wendellm": true, "wkaifang": true, "woverton": true, "wuwenbin": true, "wynfrith": true, "xdream86": true, "xgheaven": true, "xiaobing": true, "xiaochao": true, "xueboren": true, "yutwatan": true, "zalithka": true, "zhangaz1": true, "ads901119": true, "alexc1212": true, "allen_lyu": true, "aronblake": true, "benpptung": true, "bian17888": true, "bobxuyang": true, "bradnauta": true, "ccastelli": true, "cilindrox": true, "codebyren": true, "darkowlzz": true, "daviddias": true, "dbendavid": true, "debashish": true, "dennisgnl": true, "developit": true, "dracochou": true, "edmondnow": true, "edwardxyt": true, "errhunter": true, "fcred_dev": true, "fgribreau": true, "flockonus": true, "furzeface": true, "fwoelffel": true, "gavinning": true, "gilson004": true, "gonzalofj": true, "grabantot": true, "heartnett": true, "hunter524": true, "iceriver2": true, "illbullet": true, "jacopkane": true, "jefftudor": true, "jesusgoku": true, "jfedyczak": true, "jhillacre": true, "jokarlist": true, "jorgemsrs": true, "karuppiah": true, "kizzlebot": true, "kleintobe": true, "kulakowka": true, "l8niteowl": true, "largepuma": true, "larrychen": true, "leahcimic": true, "leomperes": true, "luiscauro": true, "lukaserat": true, "maxwelldu": true, "megadrive": true, "mgesmundo": true, "mjurincic": true, "mojaray2k": true, "mondalaci": true, "mr-smiley": true, "nayrangnu": true, "nice_body": true, "nickeljew": true, "nickgogan": true, "nikovitto": true, "ninozhang": true, "npmmurali": true, "operandom": true, "piixiiees": true, "pwaleczek": true, "qingleili": true, "qqcome110": true, "rbecheras": true, "rkopylkov": true, "rossdavis": true, "rylan_yan": true, "sasquatch": true, "shakakira": true, "shyamguth": true, "sierisimo": true, "spences10": true, "steel1990": true, "sternelee": true, "stone-jin": true, "sunkeyhub": true, "thimoteus": true, "tomgao365": true, "trewaters": true, "twierbach": true, "unstunted": true, "whitelynx": true, "zacbarton": true, "abdihaikal": true, "ahmetertem": true, "aitorllj93": true, "alin.alexa": true, "aquiandres": true, "avivharuzi": true, "beenorgone": true, "brainpoint": true, "button0501": true, "cestrensem": true, "cheapsteak": true, "clarenceho": true, "craneleeon": true, "davidbraun": true, "dduran1967": true, "dh19911021": true, "domjtalbot": true, "drhoffmann": true, "echaouchna": true, "fabien0102": true, "goodseller": true, "greganswer": true, "guyharwood": true, "henryorrin": true, "instazapas": true, "isaacvitor": true, "jakedetels": true, "jasoncheng": true, "jessaustin": true, "jkrusinski": true, "joelwallis": true, "johnstru16": true, "jshcrowthe": true, "junjiansyu": true, "k-kuwahara": true, "kaiquewdev": true, "kankungyip": true, "kingtrocki": true, "kletchatii": true, "ksyrytczyk": true, "kuzmicheff": true, "lijinghust": true, "liushoukai": true, "lsjroberts": true, "luhalvesbr": true, "luisgamero": true, "lwgojustgo": true, "manikantag": true, "martinkock": true, "maxmaximov": true, "meshaneian": true, "miroklarin": true, "morogasper": true, "muukii0803": true, "nerdybeast": true, "piecioshka": true, "pmbenjamin": true, "princetoad": true, "radumilici": true, "raycharles": true, "redsparrow": true, "ricardweii": true, "rocket0191": true, "saitodisse": true, "samhou1988": true, "seasons521": true, "shuoshubao": true, "simplyianm": true, "srksumanth": true, "stonestyle": true, "stormcrows": true, "sylvain261": true, "tangweikun": true, "tomasgvivo": true, "tonyljl526": true, "tylerbrock": true, "vapeadores": true, "xieranmaya": true, "yesseecity": true, "zhanghaili": true, "ziliwesley": true, "acollins-ts": true, "aereobarato": true, "ahsanshafiq": true, "alexey-mish": true, "arnoldstoba": true, "chinmay2893": true, "codeinpixel": true, "coolhanddev": true, "danielheene": true, "diogocapela": true, "dushanminic": true, "ergunozyurt": true, "felipeplets": true, "flumpus-dev": true, "galenandrew": true, "garenyondem": true, "hal9zillion": true, "heyimeugene": true, "highgravity": true, "jeremy_yang": true, "jmanuelrosa": true, "joannerpena": true, "juliomarcos": true, "karlbateman": true, "knownasilya": true, "kobleistvan": true, "kodekracker": true, "luuhoangnam": true, "mikermcneil": true, "morishitter": true, "mwurzberger": true, "naokikimura": true, "neaker15668": true, "ovuncozturk": true, "phoward8020": true, "polarpython": true, "pumpersonda": true, "rakeshmakam": true, "roylewis123": true, "sabrina.luo": true, "schwartzman": true, "scytalezero": true, "sessionbean": true, "shangsinian": true, "soenkekluth": true, "takethefire": true, "thangakumar": true, "tonerbarato": true, "tootallnate": true, "vparaskevas": true, "wangnan0610": true, "xinwangwang": true, "battlemidget": true, "brandondoran": true, "brentonhouse": true, "code-curious": true, "creativeadea": true, "darrentorpey": true, "digitalsadhu": true, "donecharlton": true, "einfallstoll": true, "eshaanmathur": true, "evanshortiss": true, "fanchangyong": true, "ivan.marquez": true, "ivangaravito": true, "kamirdjanian": true, "kostya.fokin": true, "michaelermer": true, "mjuliana2308": true, "mpinteractiv": true, "nathanbuchar": true, "natterstefan": true, "npm-packages": true, "paulomcnally": true, "ruchirgodura": true, "stringparser": true, "stylemistake": true, "victorzimmer": true, "wfalkwallace": true, "yowainwright": true, "andrewconnell": true, "chinawolf_wyp": true, "codecounselor": true, "crazyjingling": true, "curioussavage": true, "diegorbaquero": true, "eduardocereto": true, "james.talmage": true, "jasonwang1888": true, "jordan-carney": true, "lulubozichang": true, "markthethomas": true, "mdedirudianto": true, "miadzadfallah": true, "parkerproject": true, "pauljmartinez": true, "philippwiddra": true, "piyushmakhija": true, "program247365": true, "robinblomberg": true, "roboterhund87": true, "serge-nikitin": true, "shrimpseaweed": true, "slicethendice": true, "spanishtights": true, "vishnuvathsan": true, "anton-rudeshko": true, "arnold-almeida": true, "coryrobinson42": true, "divyanshbatham": true, "eirikbirkeland": true, "imaginegenesis": true, "jonniespratley": true, "kamikadze4game": true, "karzanosman984": true, "konstantin.kai": true, "marcin.operacz": true, "maycon_ribeiro": true, "nika.interisti": true, "shahabkhalvati": true, "shanewholloway": true, "suryasaripalli": true, "thebearingedge": true, "thevikingcoder": true, "troels.trvo.dk": true, "usingthesystem": true, "alexbaumgertner": true, "andygreenegrass": true, "cyma-soluciones": true, "daniel-zahariev": true, "danielchatfield": true, "gestoria-madrid": true, "icodeforcookies": true, "joaquin.briceno": true, "leonardodavinci": true, "sametsisartenep": true, "subinvarghesein": true, "bursalia-gestion": true, "carlosvillademor": true, "gresite_piscinas": true, "nasser-torabzade": true, "shashankpallerla": true, "travelingtechguy": true, "nguyenmanhdat2903": true, "scott.m.sarsfield": true, "vision_tecnologica": true, "azulejosmetrosubway": true, "granhermandadblanca": true}}