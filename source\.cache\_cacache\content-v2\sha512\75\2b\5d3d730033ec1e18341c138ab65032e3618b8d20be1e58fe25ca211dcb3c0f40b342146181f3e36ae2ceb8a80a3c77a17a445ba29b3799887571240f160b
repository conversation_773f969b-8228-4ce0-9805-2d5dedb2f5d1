{"_id": "bare-path", "_rev": "11-b941f01b54c53892e975d2aa86d4e737", "name": "bare-path", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.6": {"name": "bare-path", "version": "1.0.6", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "bd601533bdc9e17bf3078d97623896d1d829e2d1", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-1.0.6.tgz", "fileCount": 4, "integrity": "sha512-tncR+9Crs1yy0wiX7kgsGU6t53gVqCV6H2gy1KXo52ug9DwhHu9WqUdah1MRQ6lrK2sw4TcgcsIYUqHKv7zgow==", "signatures": [{"sig": "MEUCIDrAYuvSyy01/ZlsQIqItRDcN7yWeG5lPH+Jy4HS2XVhAiEA0J+tz3o9rTyck/vLFn3vNtbr5ZJ51Nt2p+spy+lgZyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15226}, "main": "index.js", "gitHead": "a279027acbf0634ca6a2bc07ac655f980b175a45", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "devDependencies": {"standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_1.0.6_1684224883683_0.8750765166214105", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "bare-path", "version": "1.1.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@1.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "bd13ab1af49efeb7da7c71e03567ead525de9969", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-1.1.0.tgz", "fileCount": 4, "integrity": "sha512-d7NLPQMybsoe3uobGU0wdVe5zV0QHjH1yDK3hsQLk75hq7rZQuK5A63zAXMLIzvfwNRql5+CtE3xgPaAmlNkig==", "signatures": [{"sig": "MEUCIQCh8cv/BkUuIU3Q91faHDAhKlUT0b4zQccjYa5sH21uHAIgadkKj+qcJFg1EQbTTy3C7mr56HkZC2t6KmvNl8H0LSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15712}, "main": "index.js", "gitHead": "1b8db6ed837375e82ea82b7d5a09ad7e453d214e", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "20.4.0", "_hasShrinkwrap": false, "devDependencies": {"standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_1.1.0_1695293764617_0.2661022639311137", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "bare-path", "version": "1.1.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@1.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "a940ebc74147e5344e2a63826b9b23b1ec1f8618", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-1.1.1.tgz", "fileCount": 4, "integrity": "sha512-t1/JhgTWDmhMCMu8/WjjcWjROU2iV5PTTGOhJB316aNgdqcxHdzIAsHBjgJVvCaqh5HuVwvrgfkoVIC/1rxyLw==", "signatures": [{"sig": "MEQCIC3zvWbPw42TerPk6MK2bZPy+LIaTcFUpD3fZmgkLtsoAiABuusEssqrc3Y+k2ndmYhGIJlNYplfELdr/mbg/IR8ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15823}, "main": "index.js", "gitHead": "d662d583d5d0963674fbb823c48bf9512e47fbae", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "20.8.0", "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_1.1.1_1698048194712_0.17906552862291325", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "bare-path", "version": "1.1.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@1.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "9872d1d7981c8f1f5ae81dc932cd3b3f25d780e8", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-1.1.2.tgz", "fileCount": 4, "integrity": "sha512-apriL9wxF67YdjFSoXuXIqGGQ2zN3f4citfJMcqhFA/EbER9iLBL+V/iAi0ze6ZKmqSaLBuzxnhh387AObNEVQ==", "signatures": [{"sig": "MEQCIFmwtePtwPgN7lz5ZLKAqiahBuUmUR7sFyAj2YBqXcRKAiBKZYuYO4qvJDfXDj9nDhh6pn2pGgtZ7suIYOFDffEIzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15913}, "main": "index.js", "gitHead": "671858741c67bacc22f2717c974f8b7c7ce4d4ce", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.0.0", "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_1.1.2_1698231752090_0.6100432726766469", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "bare-path", "version": "2.0.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@2.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "c2d54a12012c8f37cdac3f21d43a6044300014ea", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-iUVZzYmXnYm7b1c4zptX+jggHaZLzDnbE5arT1hBca3/S9PHL4B53tfw5ylerDgwKluSt+tPNCZTPjzu286rSw==", "signatures": [{"sig": "MEUCIQCwb1Am4M+O6BP0hoG5vP9eRSmIhKpmuNCUKDBd8aGLFQIgQCkDRqIZDAF7OB7vlSUUtuYBpg6j3HD+Iv44uaC89vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15985}, "main": "index.js", "gitHead": "81a12ade7bf19518f339b1c01f6601e59d7fdc4f", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"bare-os": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_2.0.0_1699867461906_0.9973467484140326", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "bare-path", "version": "2.0.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@2.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "5928ff88f2d68b52bb07b7d42078c85429955ef9", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-XOCl7BuGnRqsyvG6JCtz5hsNITW/8/4Aqaq8wR9a1CIlGD7n6kjtbdubWsYgL3S9yet2Y9KgRhVttejGMY8K2w==", "signatures": [{"sig": "MEUCIQCBU7aZPv9uslnsY5TD9xazqu26/zsfC7KZS8bMI1S/CAIgUa6W1Pk5gxOkVR4ONV33r2tTzRNsNmKTdBYjCuJOqiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16277}, "main": "index.js", "gitHead": "23ea0f8800815b6a86c77c08e85c3ed66e8c5c13", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"bare-os": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_2.0.1_1700834962947_0.7066032903518", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "bare-path", "version": "2.1.0", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@2.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "830f17fd39842813ca77d211ebbabe238a88cb4c", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-2.1.0.tgz", "fileCount": 9, "integrity": "sha512-DIIg7ts8bdRKwJRJrUMy/PICEaQZaPGZ26lsSx9MJSwIhSrcdHn7/C8W+XmnG/rKi6BaRcz+JO00CjZteybDtw==", "signatures": [{"sig": "MEQCIEtcn7SR2rsFhQtwZoCFEqu/IYGVfy9ouKxjWz5Yb9lpAiAYT4l50F/8+aLsLZJIgtrlN4fG+bIeBxhEotHAs2KlCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35132}, "main": "index.js", "gitHead": "651180a12e73b5315cfac596b9db2c0c2f0cfbe9", "scripts": {"test": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.0.0", "dependencies": {"bare-os": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_2.1.0_1701083029600_0.21242591258946986", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "bare-path", "version": "2.1.1", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@2.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "111db5bf2db0aed40081aa4ba38b8dfc2bb782eb", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-2.1.1.tgz", "fileCount": 9, "integrity": "sha512-OHM+iwRDRMDBsSW7kl3dO62JyHdBKO3B25FB9vNQBPcGHMo4+eA8Yj41Lfbk3pS/seDY+siNge0LdRTulAau/A==", "signatures": [{"sig": "MEQCID8/z+bkAQoHWu0zQs4CRnYRwukAW7QEcqpBQiXpHnFKAiBeL78/Ck5KpcasoFusi4F4xiFI23sb6F9M7NjAbiEKtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35136}, "main": "index.js", "gitHead": "1f609b9ad539638812b9ed241c85b20e5e78d028", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.7.1", "dependencies": {"bare-os": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_2.1.1_1712221807380_0.8936037055774457", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "bare-path", "version": "2.1.2", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@2.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "7a0940d34ebe65f7e179fa61ed8d49d9dc151d67", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-2.1.2.tgz", "fileCount": 9, "integrity": "sha512-o7KSt4prEphWUHa3QUwCxUI00R86VdjiuxmJK0iNVDHYPGo+HsDaVCnqCmPbf/MiW1ok8F4p3m8RTHlWk8K2ig==", "signatures": [{"sig": "MEUCIQC2UaamCM5ZZ2VT9CZtROzBp5gZSFIrR/ufLfQQvmxOpwIgRydcuMB+cqHOcUms+5onwOtFlBBPrNr6ik4WpXEexhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35257}, "exports": {".": "./index.js", "./posix": "./lib/posix.js", "./win32": "./lib/win32.js", "./package": "./package.json"}, "gitHead": "20ffcf10caa627e0d8c652ddd7da63808409d31e", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.7.3", "dependencies": {"bare-os": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_2.1.2_1714033162254_0.2546324822689563", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "bare-path", "version": "2.1.3", "author": {"name": "Holepunch"}, "license": "Apache-2.0", "_id": "bare-path@2.1.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/holepunchto/bare-path#readme", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "dist": {"shasum": "594104c829ef660e43b5589ec8daef7df6cedb3e", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-2.1.3.tgz", "fileCount": 9, "integrity": "sha512-lh/eITfU8hrj9Ru5quUp0Io1kJWIk1bTjzo7JH1P5dWmQ2EL4hFUlfI8FonAhSlgIfhn63p84CDY/x+PisgcXA==", "signatures": [{"sig": "MEUCIQDTSckdX/4bEwRZhB44g0srDdYejwMOcF/0e/zAquT4dwIgOjfmRNUHm2e4vH3jbMwFRMHawXfcoOcswj4keMcqp5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35411}, "exports": {".": "./index.js", "./posix": "./lib/posix.js", "./win32": "./lib/win32.js", "./package": "./package.json"}, "gitHead": "f93cd5d5dccd53bd8c7fe84e6dd072eef775a62c", "scripts": {"test": "standard && bare test.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/holepunchto/bare-path.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Path manipulation library for JavaScript", "directories": {}, "_nodeVersion": "21.7.3", "dependencies": {"bare-os": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bare-path_2.1.3_1716457703071_0.35685960687449403", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "bare-path", "version": "3.0.0", "description": "Path manipulation library for JavaScript", "exports": {".": "./index.js", "./package": "./package.json", "./posix": "./lib/posix.js", "./win32": "./lib/win32.js"}, "scripts": {"test": "standard && bare test.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-path.git"}, "author": {"name": "Holepunch"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "homepage": "https://github.com/holepunchto/bare-path#readme", "dependencies": {"bare-os": "^3.0.1"}, "devDependencies": {"brittle": "^3.3.2", "standard": "^17.0.0"}, "_id": "bare-path@3.0.0", "gitHead": "d0085ec04eaba5357c32c5cfa8c5f7e78c1ed3be", "_nodeVersion": "20.17.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-tyfW2cQcB5NN8Saijrhqn0Zh7AnFNsnczRcuWODH0eYAXBsJ5gVxAUuNr7tsHSC6IZ77cA0SitzT+s47kot8Mw==", "shasum": "b59d18130ba52a6af9276db3e96a2e3d3ea52178", "tarball": "https://registry.npmjs.org/bare-path/-/bare-path-3.0.0.tgz", "fileCount": 9, "unpackedSize": 35411, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAi2NQxWtljeBTqZ2OekNfr+UycKjm5RgWRXtedi413pAiBPWIaWD4Scq86+jJDKDXrByZ8+Xdk0o2FOPhLqo5bUUQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/bare-path_3.0.0_1726041074169_0.27557945674491324"}, "_hasShrinkwrap": false}}, "time": {"created": "2023-05-16T08:14:43.683Z", "modified": "2024-09-11T07:51:14.565Z", "1.0.6": "2023-05-16T08:14:43.829Z", "1.1.0": "2023-09-21T10:56:04.790Z", "1.1.1": "2023-10-23T08:03:14.845Z", "1.1.2": "2023-10-25T11:02:32.233Z", "2.0.0": "2023-11-13T09:24:22.043Z", "2.0.1": "2023-11-24T14:09:23.109Z", "2.1.0": "2023-11-27T11:03:49.751Z", "2.1.1": "2024-04-04T09:10:07.556Z", "2.1.2": "2024-04-25T08:19:22.472Z", "2.1.3": "2024-05-23T09:48:23.253Z", "3.0.0": "2024-09-11T07:51:14.404Z"}, "bugs": {"url": "https://github.com/holepunchto/bare-path/issues"}, "author": {"name": "Holepunch"}, "license": "Apache-2.0", "homepage": "https://github.com/holepunchto/bare-path#readme", "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-path.git"}, "description": "Path manipulation library for JavaScript", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# bare-path\n\nPath manipulation library for JavaScript.\n\n```\nnpm i bare-path\n```\n\n## Usage\n\n``` js\nconst path = require('bare-path')\n\npath.join('foo', 'bar') // foo/bar on posix, foo\\bar on windows\n```\n\n## License\n\nApache-2.0\n", "readmeFilename": "README.md"}