import fs from 'fs';
import ora from 'ora';
import 'dotenv/config';
import puppeteer from 'puppeteer';
const categoryTopics: Record<string, string> = { World: 'WORLD', National: 'NATION', Business: 'BUSINESS', Technology: 'TECHNOLOGY', Entertainment: 'ENTERTAINMENT', Science: 'SCIENCE', Sports: 'SPORTS', Health: 'HEALTH' };
interface Article {
	link: string;
	title: string;
	source: string;
	pubDate: string | null;
	isoDate?: string | null;
	snippet?: string | null;
}
const getDateKey = (dateStr: string | null): string | null => {
	if (!dateStr) return null;
	const date = new Date(dateStr);
	return isNaN(date.getTime()) ? null : date.toISOString().slice(0, 10);
};
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
const getArticlesFromGoogleNews = async (country: string, topicName: string): Promise<Article[]> => {
	// Try the main Google News page first
	const url = `https://news.google.com/?hl=en-${country}&gl=${country}&ceid=${country}:en`;
	let browser;
	try {
		browser = await puppeteer.launch({
			headless: false, // Set to false to see the browser
			args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
		});
		const page = await browser.newPage();
		await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
		await page.setViewport({ width: 1366, height: 768 });
		console.log(`Navigating to: ${url}`);
		await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
		await delay(5000); // Increased delay to let page load

		// Debug: Check what elements are available
		const pageInfo = await page.evaluate(() => {
			const articles = document.querySelectorAll('article');
			const divs = document.querySelectorAll('div[data-n-tid]');
			const allLinks = document.querySelectorAll('a');
			const articlesLinks = document.querySelectorAll('a[href*="/articles/"]');
			const storiesLinks = document.querySelectorAll('a[href*="/stories/"]');
			const readLinks = document.querySelectorAll('a[href*="/read/"]');

			// Sample some article structures
			const sampleArticles = Array.from(articles).slice(0, 3).map((article, i) => {
				const links = article.querySelectorAll('a');
				const firstLink = links[0];
				return {
					index: i,
					linkCount: links.length,
					firstLinkHref: firstLink?.getAttribute('href'),
					firstLinkText: firstLink?.textContent?.trim().substring(0, 50),
					innerHTML: article.innerHTML.substring(0, 200)
				};
			});

			return {
				articleCount: articles.length,
				divCount: divs.length,
				allLinksCount: allLinks.length,
				articlesLinksCount: articlesLinks.length,
				storiesLinksCount: storiesLinks.length,
				readLinksCount: readLinks.length,
				title: document.title,
				url: window.location.href,
				sampleArticles
			};
		});
		console.log('Page info:', JSON.stringify(pageInfo, null, 2));

		const articles = await page.evaluate(() => {
			const articleElements = document.querySelectorAll('article');
			const extractedArticles: any[] = [];
			console.log(`Found ${articleElements.length} article elements`);

			articleElements.forEach((article, index) => {
				if (index >= 20) return;
				try {
					console.log(`Processing article ${index}`);
					// Look for links with ./read/ or ./stories/
					const linkElement = article.querySelector('a[href*="./read/"]') || article.querySelector('a[href*="./stories/"]') || article.querySelector('a');
					if (!linkElement) {
						console.log(`No link element found for article ${index}`);
						return;
					}

					// Look for title in various possible locations
					const titleElement = article.querySelector('h3') || article.querySelector('h4') || article.querySelector('[role="heading"]') || article.querySelector('.JheGif') || article.querySelector('.ipQwMb');
					const title = titleElement?.textContent?.trim();
					const relativeLink = linkElement.getAttribute('href');

					if (!title || !relativeLink) {
						console.log(`Missing title or link for article ${index}: title="${title}", link="${relativeLink}"`);
						return;
					}
					const link = relativeLink.startsWith('http') ? relativeLink : `https://news.google.com${relativeLink}`;
					// Look for source in various possible locations
					const sourceElement = article.querySelector('[data-n-tid]') || article.querySelector('.wEwyrc') || article.querySelector('.vr1PYe') || article.querySelector('.MgUUmf');
					const source = sourceElement?.textContent?.trim() || 'Unknown';
					const timeElement = article.querySelector('time') || article.querySelector('[datetime]') || article.querySelector('.WW6dff');
					let pubDate = null;
					if (timeElement) {
						const datetime = timeElement.getAttribute('datetime');
						if (datetime) {
							pubDate = new Date(datetime).toISOString();
						} else {
							const timeText = timeElement.textContent?.trim();
							if (timeText) {
								const now = new Date();
								if (timeText.includes('hour')) {
									const hours = parseInt(timeText.match(/(\d+)/)?.[1] || '0');
									now.setHours(now.getHours() - hours);
									pubDate = now.toISOString();
								} else if (timeText.includes('day')) {
									const days = parseInt(timeText.match(/(\d+)/)?.[1] || '0');
									now.setDate(now.getDate() - days);
									pubDate = now.toISOString();
								} else if (timeText.includes('minute')) {
									const minutes = parseInt(timeText.match(/(\d+)/)?.[1] || '0');
									now.setMinutes(now.getMinutes() - minutes);
									pubDate = now.toISOString();
								}
							}
						}
					}
					if (!pubDate) pubDate = new Date().toISOString();
					const snippetElement = article.querySelector('.xBbh9') || article.querySelector('.JheGif') || article.querySelector('p');
					const snippet = snippetElement?.textContent?.trim() || null;
					extractedArticles.push({ title, link, source, pubDate, isoDate: pubDate, snippet });
				} catch (error) {
					console.error('Error extracting article:', error);
				}
			});
			return extractedArticles;
		});
		const seen = new Set<string>();
		const uniqueArticles: Article[] = [];
		for (const article of articles) {
			const uniqueKey = `${article.title}-${article.source}`.toLowerCase().trim();
			if (!seen.has(uniqueKey)) {
				seen.add(uniqueKey);
				uniqueArticles.push(article);
			}
		}
		return uniqueArticles;
	} catch (error) {
		console.error(`Failed to scrape news for ${country}/${topicName}:`, (error as Error).message);
		return [];
	} finally {
		if (browser) {
			await browser.close();
		}
	}
};
(async () => {
	const spinner = ora('Scraping Google News with Puppeteer...').start();
	const groupedNews: Record<string, Record<string, Record<string, Article[]>>> = {};
	const countries = ['IN']; // Test with just one country first
	try {
		for (const country of countries) {
			groupedNews[country] = {};
			// Test with just one category first
			const testCategories = { World: 'WORLD' };
			for (const [category, topicName] of Object.entries(testCategories)) {
				spinner.text = `Scraping ${category} news for ${country}...`;
				const articles = await getArticlesFromGoogleNews(country, topicName);
				for (const article of articles) {
					const dateKey = getDateKey(article.pubDate);
					if (!dateKey) continue;
					if (!groupedNews[country][dateKey]) groupedNews[country][dateKey] = {};
					if (!groupedNews[country][dateKey][category]) groupedNews[country][dateKey][category] = [];
					groupedNews[country][dateKey][category].push(article);
				}
				await delay(2000);
			}
		}
		fs.writeFileSync('output.json', JSON.stringify(groupedNews, null, 2), 'utf-8');
		spinner.succeed('News successfully scraped and saved to output.json');
		process.exit(0);
	} catch (err) {
		spinner.fail(`Error: ${(err as Error).message}`);
		process.exit(1);
	}
})();