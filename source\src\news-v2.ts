import fs from 'fs';
import ora from 'ora';
import 'dotenv/config';
import puppeteer from 'puppeteer';

const categoryTopics: Record<string, string> = { 
    World: 'WORLD', 
    National: 'NATION', 
    Business: 'BUSINESS', 
    Technology: 'TECHNOLOGY', 
    Entertainment: 'ENTERTAINMENT', 
    Science: 'SCIENCE', 
    Sports: 'SPORTS', 
    Health: 'HEALTH' 
};

interface Article {
    link: string;
    title: string;
    source: string;
    pubDate: string | null;
    isoDate?: string | null;
    snippet?: string | null;
}

const getDateKey = (dateStr: string | null): string | null => {
    if (!dateStr) return null;
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date.toISOString().slice(0, 10);
};

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const getArticlesFromGoogleNews = async (country: string, topicName: string): Promise<Article[]> => {
    const url = `https://news.google.com/topics/${topicName}?hl=en-${country}&gl=${country}&ceid=${country}:en`;
    
    let browser;
    try {
        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu'
            ]
        });

        const page = await browser.newPage();
        
        // Set user agent to avoid detection
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // Set viewport
        await page.setViewport({ width: 1366, height: 768 });
        
        // Navigate to the page
        await page.goto(url, { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });

        // Wait for content to load
        await delay(3000);

        // Extract articles
        const articles = await page.evaluate(() => {
            const articleElements = document.querySelectorAll('article');
            const extractedArticles: any[] = [];
            
            articleElements.forEach((article, index) => {
                if (index >= 20) return; // Limit to first 20 articles
                
                try {
                    // Try to find the title link
                    const titleElement = article.querySelector('a[href*="/articles/"]') || 
                                       article.querySelector('a[href*="/stories/"]') ||
                                       article.querySelector('h3 a') ||
                                       article.querySelector('h4 a') ||
                                       article.querySelector('a');
                    
                    if (!titleElement) return;
                    
                    const title = titleElement.textContent?.trim();
                    const relativeLink = titleElement.getAttribute('href');
                    
                    if (!title || !relativeLink) return;
                    
                    // Convert relative link to absolute
                    const link = relativeLink.startsWith('http') ? 
                                relativeLink : 
                                `https://news.google.com${relativeLink}`;
                    
                    // Try to find source
                    const sourceElement = article.querySelector('[data-n-tid]') ||
                                        article.querySelector('div[data-n-tid]') ||
                                        article.querySelector('.xBbh9');
                    
                    const source = sourceElement?.textContent?.trim() || 'Unknown';
                    
                    // Try to find time/date
                    const timeElement = article.querySelector('time') ||
                                      article.querySelector('[datetime]') ||
                                      article.querySelector('.WW6dff');
                    
                    let pubDate = null;
                    if (timeElement) {
                        const datetime = timeElement.getAttribute('datetime');
                        if (datetime) {
                            pubDate = new Date(datetime).toISOString();
                        } else {
                            const timeText = timeElement.textContent?.trim();
                            if (timeText) {
                                // Try to parse relative time (e.g., "2 hours ago")
                                const now = new Date();
                                if (timeText.includes('hour')) {
                                    const hours = parseInt(timeText.match(/(\d+)/)?.[1] || '0');
                                    now.setHours(now.getHours() - hours);
                                    pubDate = now.toISOString();
                                } else if (timeText.includes('day')) {
                                    const days = parseInt(timeText.match(/(\d+)/)?.[1] || '0');
                                    now.setDate(now.getDate() - days);
                                    pubDate = now.toISOString();
                                } else if (timeText.includes('minute')) {
                                    const minutes = parseInt(timeText.match(/(\d+)/)?.[1] || '0');
                                    now.setMinutes(now.getMinutes() - minutes);
                                    pubDate = now.toISOString();
                                }
                            }
                        }
                    }
                    
                    // If no date found, use current date
                    if (!pubDate) {
                        pubDate = new Date().toISOString();
                    }
                    
                    // Try to find snippet/description
                    const snippetElement = article.querySelector('.xBbh9') ||
                                         article.querySelector('.JheGif') ||
                                         article.querySelector('p');
                    
                    const snippet = snippetElement?.textContent?.trim() || null;
                    
                    extractedArticles.push({
                        title,
                        link,
                        source,
                        pubDate,
                        isoDate: pubDate,
                        snippet
                    });
                } catch (error) {
                    console.error('Error extracting article:', error);
                }
            });
            
            return extractedArticles;
        });

        // Remove duplicates
        const seen = new Set<string>();
        const uniqueArticles: Article[] = [];
        
        for (const article of articles) {
            const uniqueKey = `${article.title}-${article.source}`.toLowerCase().trim();
            if (!seen.has(uniqueKey)) {
                seen.add(uniqueKey);
                uniqueArticles.push(article);
            }
        }

        return uniqueArticles;

    } catch (error) {
        console.error(`Failed to scrape news for ${country}/${topicName}:`, (error as Error).message);
        return [];
    } finally {
        if (browser) {
            await browser.close();
        }
    }
};

(async () => {
    const spinner = ora('Scraping Google News with Puppeteer...').start();
    const groupedNews: Record<string, Record<string, Record<string, Article[]>>> = {};
    const countries = ['IN', 'UK'];

    try {
        for (const country of countries) {
            groupedNews[country] = {};
            
            for (const [category, topicName] of Object.entries(categoryTopics)) {
                spinner.text = `Scraping ${category} news for ${country}...`;
                
                const articles = await getArticlesFromGoogleNews(country, topicName);
                
                for (const article of articles) {
                    const dateKey = getDateKey(article.pubDate);
                    if (!dateKey) continue;
                    
                    if (!groupedNews[country][dateKey]) groupedNews[country][dateKey] = {};
                    if (!groupedNews[country][dateKey][category]) groupedNews[country][dateKey][category] = [];
                    
                    groupedNews[country][dateKey][category].push(article);
                }
                
                // Add delay between requests to be respectful
                await delay(2000);
            }
        }

        fs.writeFileSync('output.json', JSON.stringify(groupedNews, null, 2), 'utf-8');
        spinner.succeed('News successfully scraped and saved to output.json');
        process.exit(0);
        
    } catch (err) {
        spinner.fail(`Error: ${(err as Error).message}`);
        process.exit(1);
    }
})();
