import fs from 'fs';
import ora from 'ora';
import 'dotenv/config';
import puppeteer from 'puppeteer';
const categoryTopics: Record<string, string> = { World: 'WORLD', National: 'NATION', Business: 'BUSINESS', Technology: 'TECHNOLOGY', Entertainment: 'ENTERTAINMENT', Science: 'SCIENCE', Sports: 'SPORTS', Health: 'HEALTH' };
interface Article {
	link: string;
	title: string;
	source: string;
	pubDate: string | null;
	isoDate?: string | null;
	snippet?: string | null;
}
const getDateKey = (dateStr: string | null): string | null => {
	if (!dateStr) return null;
	const date = new Date(dateStr);
	return isNaN(date.getTime()) ? null : date.toISOString().slice(0, 10);
};
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
const getArticlesFromGoogleNews = async (country: string, topicName: string): Promise<Article[]> => {
	const url = `https://news.google.com/?hl=en-${country}&gl=${country}&ceid=${country}:en`;
	let browser;
	try {
		browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'] });
		const page = await browser.newPage();
		await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
		await page.setViewport({ width: 1366, height: 768 });
		console.log(`Navigating to: ${url}`);
		await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
		await delay(5000);
		const articles = await page.evaluate(() => {
			const articleElements = document.querySelectorAll('article');
			const extractedArticles: any[] = [];
			console.log(`Found ${articleElements.length} article elements`);
			for (let i = 0; i < Math.min(20, articleElements.length); i++) {
				const article = articleElements[i];
				const allText = article.textContent?.trim();
				const links = article.querySelectorAll('a');
				const firstLink = links[0];
				if (firstLink) {
					const href = firstLink.getAttribute('href');
					if (href && (href.includes('./read/') || href.includes('./stories/'))) {
						const rawText = allText || '';
						const lines = rawText.split('\n').filter((line) => line.trim().length > 0);
						let title = '';
						let source = 'Unknown';
						let timeInfo = '';
						for (const line of lines) {
							const trimmed = line.trim();
							if (trimmed.length > 20 && !trimmed.includes('ago') && !trimmed.includes('hour') && !trimmed.includes('minute')) {
								if (!title || trimmed.length > title.length) title = trimmed;
							}
							if (trimmed.includes('NDTV') || trimmed.includes('The Guardian') || trimmed.includes('The Hindu') || trimmed.includes('BBC') || trimmed.includes('CNN') || trimmed.includes('Reuters') || trimmed.includes('AP') || trimmed.includes('PTI') || trimmed.includes('Mint')) {
								source = trimmed.split(/\s+/)[0];
							}
							if (trimmed.includes('ago') || trimmed.includes('hour') || trimmed.includes('minute')) timeInfo = trimmed;
						}
						title = title.replace(/^(NDTV|The Guardian|The Hindu|BBC|CNN|Reuters|AP|PTI|Mint)More/i, '');
						title = title.replace(/\d+\s+(hours?|minutes?|days?)\s+ago.*$/i, '');
						title = title.replace(/By\s+[\w\s]+$/i, '');
						title = title.trim();
						const link = href.startsWith('http') ? href : `https://news.google.com${href.replace('./', '/')}`;
						let pubDate = new Date().toISOString();
						if (timeInfo) {
							const now = new Date();
							const hoursMatch = timeInfo.match(/(\d+)\s+hours?\s+ago/i);
							const minutesMatch = timeInfo.match(/(\d+)\s+minutes?\s+ago/i);
							const daysMatch = timeInfo.match(/(\d+)\s+days?\s+ago/i);
							if (hoursMatch) {
								now.setHours(now.getHours() - parseInt(hoursMatch[1]));
								pubDate = now.toISOString();
							} else if (minutesMatch) {
								now.setMinutes(now.getMinutes() - parseInt(minutesMatch[1]));
								pubDate = now.toISOString();
							} else if (daysMatch) {
								now.setDate(now.getDate() - parseInt(daysMatch[1]));
								pubDate = now.toISOString();
							}
						}
						extractedArticles.push({ title: title || 'No title found', link, source, pubDate, isoDate: pubDate, snippet: rawText.substring(0, 200) || null });
					}
				}
			}
			return extractedArticles;
		});
		const seen = new Set<string>();
		const uniqueArticles: Article[] = [];
		for (const article of articles) {
			const uniqueKey = `${article.title}-${article.source}`.toLowerCase().trim();
			if (!seen.has(uniqueKey)) {
				seen.add(uniqueKey);
				uniqueArticles.push(article);
			}
		}
		return uniqueArticles;
	} catch (error) {
		console.error(`Failed to scrape news for ${country}/${topicName}:`, (error as Error).message);
		return [];
	} finally {
		if (browser) await browser.close();
	}
};
(async () => {
	const spinner = ora('Scraping Google News with Puppeteer...').start();
	const groupedNews: Record<string, Record<string, Record<string, Article[]>>> = {};
	const countries = ['IN', 'UK'];
	try {
		for (const country of countries) {
			groupedNews[country] = {};
			for (const [category, topicName] of Object.entries(categoryTopics)) {
				spinner.text = `Scraping ${category} news for ${country}...`;
				const articles = await getArticlesFromGoogleNews(country, topicName);
				for (const article of articles) {
					const dateKey = getDateKey(article.pubDate);
					if (!dateKey) continue;
					if (!groupedNews[country][dateKey]) groupedNews[country][dateKey] = {};
					if (!groupedNews[country][dateKey][category]) groupedNews[country][dateKey][category] = [];
					groupedNews[country][dateKey][category].push(article);
				}
				await delay(2000);
			}
		}
		fs.writeFileSync('output.json', JSON.stringify(groupedNews, null, 2), 'utf-8');
		spinner.succeed('News successfully scraped and saved to output.json');
		process.exit(0);
	} catch (err) {
		spinner.fail(`Error: ${(err as Error).message}`);
		process.exit(1);
	}
})();