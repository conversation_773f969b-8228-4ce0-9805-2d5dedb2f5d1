{"css.lint.unknownAtRules": "ignore", "scss.lint.unknownAtRules": "ignore", "less.lint.unknownAtRules": "ignore", "git.ignoreLimitWarning": true, "WillLuke.nextjs.hasPrompted": true, "WillLuke.nextjs.addTypesOnSave": true, "files.exclude": {"**/.git": true, "**/.next": true, "**/.yarn": true, "**/.venv": true, "**/.cache": true, "**/.mypy_cache": true, "**/.ruff_cache": true, "**/.pytest_cache": true, "**/.ipynb_checkpoints": true, "**/__pycache__": true, "**/node_modules": true, "**/dist": true, "**/build": true, "**/out": true, "**/venv": true, "**/coverage": true, "**/htmlcov": true, "**/jupyter_cache": true, "**/pip-wheel-metadata": true, "**/unsloth_compiled_cache": true, "**/package-lock.json": true, "**/yarn.lock": true, "**/pnpm-lock.yaml": true, "**/bun.lock": true, "**/*.pyc": true, "**/*.pyo": true, "**/next-env.d.ts": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "editor.formatOnSave": true, "prettier.requireConfig": true, "python.analysis.typeCheckingMode": "basic", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "node_modules/typescript/lib", "editor.defaultFormatter": "esbenp.prettier-vscode"}