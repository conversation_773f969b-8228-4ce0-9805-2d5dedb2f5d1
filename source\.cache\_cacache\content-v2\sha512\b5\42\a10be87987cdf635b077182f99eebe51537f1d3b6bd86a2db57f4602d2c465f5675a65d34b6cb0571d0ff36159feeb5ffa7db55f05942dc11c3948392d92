{"_id": "socks-proxy-agent", "_rev": "49-9a4c96899b6c9c5e06ba202ed863fd2f", "name": "socks-proxy-agent", "dist-tags": {"beta": "6.2.0-beta.1", "latest": "8.0.5"}, "versions": {"0.0.1": {"name": "socks-proxy-agent", "version": "0.0.1", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@0.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "4d3f0cfb41078a95c91eb4b8264f9d264a3f7aa9", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-0.0.1.tgz", "integrity": "sha512-e5GWQbn4LZwwua8TyrDDDgFpK6JnBFDqiTpTu1DabPcUkr+4mHy8CdKG8tBJN7N3AfgY83l81wID5uFGaoCTIQ==", "signatures": [{"sig": "MEUCIQDlgEkdMZUzq2OXWkcEvKU0SVZJ2HdYmFnAtjSyZM2fWQIgequfwutLXAH2mcOv1Uk+XA9AX8QpW8vvUeNfcwnF9b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "dependencies": {"agent-base": "~0.0.1", "rainbowsocks": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.0.2": {"name": "socks-proxy-agent", "version": "0.0.2", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@0.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "402809baa1c9c9cc9f7536c0e2adec24e84c7bc0", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-0.0.2.tgz", "integrity": "sha512-3btBp+qJokp8zouTFqyybTJeafpw5/nV8Yd4wltUE6yati1Cruwxo4zQmlKyF4n+qR1UMrchG5ufGwGZOGi38A==", "signatures": [{"sig": "MEUCIFQ6+RC+aP3URRqaO8H+933vV6uU0044xY6xMElMMb/gAiEAsN/qOtTRyP6YxbeEPg4ZPMecWP8Me1iNDAwTWGevqdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "dependencies": {"agent-base": "~0.0.1", "rainbowsocks": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.1.0": {"name": "socks-proxy-agent", "version": "0.1.0", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@0.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "755311942271b17e7512dfd9a1a63d77384f94d4", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-0.1.0.tgz", "integrity": "sha512-qgZaVaIlycHzM1VimMRtBQUHjsmvWYI3MdLKmvYkyGUKy0NZO8U1IXjgNFFbBS1ir5WO4a4hgAwpv720FFTAGw==", "signatures": [{"sig": "MEQCIADivL4PDeLbxB4VTYwgmeN0ww8juCv4azWb/QJJSg7NAiBG+AmUbyL9NdeCV+q1Nej8cKgf7s1UXgLld4MnbgnQsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "dependencies": {"extend": "~1.2.0", "agent-base": "~1.0.1", "rainbowsocks": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}}, "0.1.1": {"name": "socks-proxy-agent", "version": "0.1.1", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@0.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "022c537b9d96e874a624f174ee884e25352def6e", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-0.1.1.tgz", "integrity": "sha512-h3LFlAiSfjEqeI5YZ4/ugzDysZNoJB37QgwmPx237/bnHRm4JeFjL3mWoArHc0x7Z9PmBwrWOU55TK2nZ/MzOA==", "signatures": [{"sig": "MEUCIA0eYaGzN3SwCfbj2r9P24HhQ/durNItKJH30jtkENPcAiEA4xHeiNj/6zjWGonBxW0JxAGtq3SFPyFf4SPegR289VY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "dependencies": {"extend": "~1.2.1", "agent-base": "~1.0.1", "rainbowsocks": "~0.1.1"}, "devDependencies": {"mocha": "~1.18.2"}}, "0.1.2": {"name": "socks-proxy-agent", "version": "0.1.2", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@0.1.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "e8981486360896f692f600ba52a974c8b23dc121", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-0.1.2.tgz", "integrity": "sha512-A6HCJpilsagbgSHmNbd6SdW5grzIn+UwJwughjFtSM9FldW44DJcdONeqVU74JNsJca5SDj7ParjW9aLKkNc4g==", "signatures": [{"sig": "MEUCIEHU0fdta0auzHioQF4v9NaCx76Zc4yapLFkgy5XbVB3AiEA42E38RScmhuHkhtgsWKP5xpDIvfJL8c9eCpIcX9yHec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "_shasum": "e8981486360896f692f600ba52a974c8b23dc121", "gitHead": "07dca51d4ade77dfa251c39052c2bac28801e46f", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "dependencies": {"extend": "~1.2.1", "agent-base": "~1.0.1", "rainbowsocks": "~0.1.2"}, "devDependencies": {"mocha": "~1.18.2"}}, "1.0.0": {"name": "socks-proxy-agent", "version": "1.0.0", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@1.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "54191abb267e1305cf0e300422f8980bb3a05c50", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-1.0.0.tgz", "integrity": "sha512-IMIi2fxWqKC/EjUYa7WsszHHgFEQzM3rRJR5CsZLu4xnP8hSBlru+YCCthNR7QHIlBRRt/4QqhDQ4ZPzDEnWEQ==", "signatures": [{"sig": "MEYCIQDeh9QVdWdKTUoZzUqqUrV8Ev9r70lcqqllb64nY3heugIhAI/EVnY7DB/kPWknmWaJyYdVWovdW2kVrmvQzuOiMs8t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "_shasum": "54191abb267e1305cf0e300422f8980bb3a05c50", "gitHead": "8e4f6b02226aa60c923e204f20c017666e9af560", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"extend": "~1.2.1", "agent-base": "~1.0.1", "socks-client": "~1.1.2"}, "devDependencies": {"mocha": "2"}}, "1.0.1": {"name": "socks-proxy-agent", "version": "1.0.1", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@1.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "d272c153bb3fb6a4e09cee5fb37fdc34cd0ca981", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-1.0.1.tgz", "integrity": "sha512-TD348ffismbVZFZ/ZhrcA1XXGhhUhEKmWtfFvgJNferGzVf9Hw7G9yU+37pVHPqmlLvmUUJ0GLRFx9UIkNhaOw==", "signatures": [{"sig": "MEYCIQCDgY9jq0+MbbVlKogJHzNrmUCJ3dnQj+Nur3Z8Sl7cTQIhANUEegc2aZC8hi4onyFrNkRCXcrF4KWt7aEWH2BIIcsR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "_shasum": "d272c153bb3fb6a4e09cee5fb37fdc34cd0ca981", "gitHead": "4f4419014dd7a6a3c744b71157409c7b32624cc4", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "A SOCKS (v4a) proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "dependencies": {"socks": "~1.1.5", "extend": "~1.2.1", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "2"}}, "1.0.2": {"name": "socks-proxy-agent", "version": "1.0.2", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@1.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "67e06b447fe5637417fde5733cbfdfec9ffe117f", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-1.0.2.tgz", "integrity": "sha512-F8xR1C4uplta4XIwHvvpIxhLO8oCWLuC48hSgFpMqps45WqtOZQ4JadJ5YGo0eGMvIp94qV3bX3Tqi2iHGlNvw==", "signatures": [{"sig": "MEUCIBnuIzle6od/uxUrhzd1ZCGvvJSkBjqvN7HoodKR6s8RAiEA+ObMQWaAK2RVgcR26AGbAIgjEu8dFQZffP6tL6AjsUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "_shasum": "67e06b447fe5637417fde5733cbfdfec9ffe117f", "gitHead": "a6315dbeaf7d6310067307eed0b5b1b7d467b5ac", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"socks": "~1.1.5", "extend": "~1.2.1", "agent-base": "~1.0.1"}, "devDependencies": {"mocha": "2", "socksv5": "0.0.6"}}, "2.0.0": {"name": "socks-proxy-agent", "version": "2.0.0", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@2.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "c674842d70410fb28ae1e92e6135a927854bc275", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-2.0.0.tgz", "integrity": "sha512-pYjjilNenvaMXk6JuyOz1M+01C6Ay/OIzhVzo3wnxpWbQXsLW3e4UvIgkxJZpgMsIBTGB0/BQZFF3D6qCPuJzA==", "signatures": [{"sig": "MEYCIQDA1yORfbCjkTOrucgI+cJ8Tu0qHqeHHs562/HLdHaoLgIhAPM0xJUpLr+k/d/8Nh+58g53cC1wiDjVI+bIvK4CW2p5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "_shasum": "c674842d70410fb28ae1e92e6135a927854bc275", "gitHead": "23f3b4ad9fcaac4191597cd87647f23014739e3b", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"socks": "~1.1.5", "extend": "3", "agent-base": "2"}, "devDependencies": {"mocha": "2", "socksv5": "0.0.6"}}, "2.1.0": {"name": "socks-proxy-agent", "version": "2.1.0", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@2.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "ddfb01b5dbea5fc879490ca38a25fe87d3d15912", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-2.1.0.tgz", "integrity": "sha512-tdeG3TxD+1oAIwU7GFj9NLjB7qJKtMVqWzHVOdPlJBczqMGo6ujE/dBwv+ZNkozcTANng6WWSDZ5gdWxPJmTqA==", "signatures": [{"sig": "MEUCIQDIypvS7LcG0xUFboqDcED8YThvdMJSmOqCGOtC2PVnwQIgWbYdpmoACLePjUu4KMJ5dVAuKerL5Cxwhh2wxAkQZ4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "_from": ".", "_shasum": "ddfb01b5dbea5fc879490ca38a25fe87d3d15912", "gitHead": "ee963a21e72c89e00dd74dc58aa17a860e37ad0f", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"socks": "~1.1.5", "extend": "3", "agent-base": "2"}, "devDependencies": {"mocha": "2", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent-2.1.0.tgz_1495673239103_0.35169737064279616", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "socks-proxy-agent", "version": "2.1.1", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@2.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "86ebb07193258637870e13b7bd99f26c663df3d3", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-2.1.1.tgz", "integrity": "sha512-sFtmYqdUK5dAMh85H0LEVFUCO7OhJJe1/z2x/Z6mxp3s7/QPf1RkZmpZy+BpuU0bEjcV9npqKjq9Y3kwFUjnxw==", "signatures": [{"sig": "MEQCIAuCIKNloeHRRLkUaDLlgXFxJZwkeSvKaM1cx8K6ye2aAiBtSzuJ2Dh3rC42Fl8GsrItJS+yq9bCT5Rl0OK5274w2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "socks-proxy-agent.js", "gitHead": "98ccaf180362f67a1f5f14233a0aa0d0475ead89", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"socks": "~1.1.5", "extend": "3", "agent-base": "2"}, "devDependencies": {"mocha": "2", "socksv5": "0.0.6", "raw-body": "^2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent-2.1.1.tgz_1497381370026_0.4509972701780498", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "socks-proxy-agent", "version": "3.0.0", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@3.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "ea23085cd2bde94d084a62448f31139ca7ed6245", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-3.0.0.tgz", "integrity": "sha512-YJcT+SNNBgFoK/NpO20PChz0VnBOhkjG3X10BwlrYujd0NZlSsH1jbxSQ1S0njt3sOvzwQ2PvGqqUIvP4rNk/w==", "signatures": [{"sig": "MEQCIHbZJA0kpuCgi/Z1nPpqhBmjdP5HAgwL3NFp8mVB04DXAiA7N33Xq/LBbY0SF6uqtb740CabPqyyt/TDI5Ga0fOBaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "gitHead": "3438f63a9eab6e8b459a519165a164c630c4a1ac", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"socks": "^1.1.10", "agent-base": "^4.0.1"}, "devDependencies": {"mocha": "^3.4.2", "socksv5": "0.0.6", "raw-body": "^2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent-3.0.0.tgz_1497385582000_0.7844010631088167", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "socks-proxy-agent", "version": "3.0.1", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@3.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "2eae7cf8e2a82d34565761539a7f9718c5617659", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-3.0.1.tgz", "integrity": "sha512-ZwEDymm204mTzvdqyUqOdovVr2YRd2NYskrYrF2LXyZ9qDiMAoFESGK8CRphiO7rtbo2Y757k2Nia3x2hGtalA==", "signatures": [{"sig": "MEUCIQDl9+a2dI8WCnvi9t2isOqJoeVuYft4kX20zj23sPR/YQIgXxZPs/4UKoW4MMbOXPCmQ25EHXIlV2gSc4j6N7zYkZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "gitHead": "25af8c88859418725a78865a31b73d0cf82f1696", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"socks": "^1.1.10", "agent-base": "^4.1.0"}, "devDependencies": {"mocha": "^3.4.2", "socksv5": "0.0.6", "raw-body": "^2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent-3.0.1.tgz_1505751239688_0.4514315372798592", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "socks-proxy-agent", "version": "4.0.0", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@4.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "e85e713bf30d5412364fbbcb6d628ff3437a41c0", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-M0x7LYYRzKOEn5NchNPkUeVQ98hvUgwKI6URgnzB9L1Xwe1PBzX8pnThw5JYumzdLWW4qiY1XtBH7iFN21859A==", "signatures": [{"sig": "MEYCIQCzhB7WqmsNm0V2Ga8H8WN0jH8MU/XgYHA5+E4F0pCe7QIhAM7gLikrxdOs85L6QuD+bhSVrY3R1sE8/WWJ8TiTp9VL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17530}, "main": "./index.js", "engines": {"node": ">= 6"}, "gitHead": "84f6ce65bedaade580b4f7436fb11f45c773baeb", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"socks": "~2.1.6", "agent-base": "~4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~3.4.2", "socksv5": "0.0.6", "raw-body": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_4.0.0_1522311118237_0.9672776364369804", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "socks-proxy-agent", "version": "4.0.1", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@4.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "5936bf8b707a993079c6f37db2091821bffa6473", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-4.0.1.tgz", "fileCount": 8, "integrity": "sha512-Kezx6/VBguXOsEe5oU3lXYyKMi4+gva72TwJ7pQY5JfqUx2nMk7NXA6z/mpNqIlfQjWYVfeuNvQjexiTaTn6Nw==", "signatures": [{"sig": "MEUCIANwJPrLzBpJOhCcbV5iMcEU22n6XQZhAohp0sKuwvNpAiEAyyHPX2jm/4nlXr4hfRfckEfu2oJ4ppXSubwgiuUFfoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa0TBECRA9TVsSAnZWagAAtssQAJlODCDl1PmbO/I/LVt+\nJ/Z7kCm4MioFw6ZI9xoXm4oGF/RS7tYrlrerQFRLs/yPeydTDb141z3FVKw/\nN7pdwxhV+onbTPQxB2/6qUnYUK5as/RBrGW4lL32O4bRvnphIZAZrFdCRu3Q\nsWknk0LR5UstrFC833ZPqnfUaCcLFeGqswcTH+/JgeekcjoFVQSkicO6uCf7\nHkNhWcWK8nsuUNI37Nk0jF/mhMh6R+Bti3w795UIII0XZcnaj4j/WvvFKYqo\nG3ueH8/e3jlMzUO0AVPdc55HMj2bjiPfmH2NmJDn72Yn+r+rlFrlXvI+FlKh\nVTV5mxdVsztKpI66TPxfEet6d6mTBU7I8284t/lXvXbkhpxpa4go2NxddHMs\nY6Ylof7OA1lQJomaDydGnmWLU3SW42KohPK4CudxVbCkZLGS4sAZx/246cnc\nPJp38Zr+jCMZyDoFivUqhwflHGvpv+5xQJ2eRKyYTz58PLiJqKBKUxZl/hCZ\nDh9TXo52C20bhyXMoqD24ggEu+imEda4lDyKcRi8hsyxe0PVhkEw+x6xvjlS\n3ql0yvPgqrhIXaL9qyLMGyzZEqbRG7ylswWJm9X31JuM7IvD7wc19WvEyNMz\nsJKW4CeEBHJrEmQfnJUgF1NlokRrypxN3zL2Sxqc8aMtzjJMu2MJ71+mxE7/\nGNcF\r\n=6cf3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">= 6"}, "gitHead": "5867dd04e92b51af0a35d5b3cb6a82f8f5590b6f", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"socks": "~2.2.0", "agent-base": "~4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.1.0", "socksv5": "0.0.6", "raw-body": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_4.0.1_1523658820094_0.18619031542704056", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "socks-proxy-agent", "version": "4.0.2", "keywords": ["socks", "socks4", "socks4a", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@4.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "3c8991f3145b2799e70e11bd5fbc8b1963116386", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-4.0.2.tgz", "fileCount": 9, "integrity": "sha512-NT6syHhI9LmuEMSK6Kd2V7gNv5KFZoLE7V5udWmn0de+3Mkj3UMA/AJPLyeNUVmElCurSHtUdM3ETpR3z770Wg==", "signatures": [{"sig": "MEQCIFlELxKczQ4+DM/9roEPFxp42tK3VSXzoOUDeo8jvCoNAiAc3Zr1ooObEoQb/QYMDy4yhdcleTO67QuBgMkPqY8sAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgzEgCRA9TVsSAnZWagAAKPEP/jLCBJmRkBTDCKE82QDr\nH/A/0BjW1vBiYvYnogieJswCY/U4c0Rw1sYXBi3jxzke2tVv+HuVc08uCbZj\nfVVkXibTePuJRnXqATS9b5lJbmNffbAqcd+nlQ4trhB6srJz2aa0NaLJSsLA\nIYfsV0PmbhWyVENQKi1eHl9OumsSwt1OiyEk8OqVg6apvA550u1OrZkvWEy0\nK3jbtIwqeww7f9Dbb72CHudcQGa33O2Vff9Ie7/nqBqUrmdliIecAgcXgXHX\nOamVFE1TUaDr/pW4CmzQEWuIOBZw7JASXCTpa8Fy4womBKwtSlaFMFekMKIw\noxZlKtcklkX4+FQauPFHubRCT9X6JHgzOXe4yeGFltpae3xO5LkepXmlA+kw\nIWDeEeeJrIkt+/DRkJyagW9MYtlNWVV4ojSkFk0xJnespPSgWEWaOJUkBRnx\noHqhnS5LaXQSzTXM4CkR0xapqvbxe0HMt5UmtIteSEDp0Dzgq7kQaNTjgqQ6\n/codBCK04QizphY4ZsYArrAZkH5oAYaBLuEQIFPbjII+esEIv12l+RD5tFWC\nWx8zWV6ZyOyM0YEqr46SdISGhBbde6Z+zdG2d7irF9XgoEfG1Hvy+4bjQnVW\n2PcnZsTaK/Fb+0khKUFAdpyEChpzq5fgWNuPc14ZjwAMoE/4eNjnZeN1wkRQ\nKaAj\r\n=4byf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">= 6"}, "gitHead": "ea7539231774dd4c7f378ac49a7530e713cc6dcd", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"socks": "~2.3.2", "agent-base": "~4.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.1.0", "socksv5": "0.0.6", "raw-body": "~2.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_4.0.2_1552101663389_0.02899957152532684", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "socks-proxy-agent", "version": "5.0.0", "keywords": ["socks", "socks4", "socks4a", "socks5", "socks5h", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@5.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "7c0f364e7b1cf4a7a437e71253bed72e9004be60", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-lEpa1zsWCChxiynk+lCycKuC502RxDWLKJZoIhnxrWNjLSDGYRFflHA1/228VkRcnv9TIb8w98derGbpKxJRgA==", "signatures": [{"sig": "MEUCIQDRk+Uvo0rTqlGYztl1B+E1qZ6dXLnWscJafo6xL0GvlAIgeOdA3YLKpas960dXq7PGBElqKMdeBEQfZp8kJN0mNc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOck6CRA9TVsSAnZWagAABBoQAIsKgt28tJJRzF9cU0ka\nBhdF8suwKRJi1OX5PafQV0zymjG6iBypN89t2JzbEMTQ5IwW5+i5KHLe6Ss/\n6v4p28rnPxb2bhiimBX7jPrFUv0PjAv9iAzKp465xM3vspmtT8S5tsk1ZoVe\nKSAnqSisTpnZGrtsE0k074ju4PGmEdRs0Pw8bP+VrD/4GwYCH7boKbQS5Yqe\nOx6Y4D1eCCrzloZLfxpy0wsUscDMz9SktleDpALraJ6Hj09A1gQxhT55oAwi\nalx2zzmSm2K5rqX6dG7so2Bbm2r4NxQ6dtpF8s17aJahJcFrTNKmBBYkRBeT\nICRnwSS8b31IrcvhaWzV4co0mZDJzgpF106n123DQPJukh2Bk8T+j6h67D7b\nd9y4prXz+caitWQ9ObJxPqh4vOE5f2+cRHgzEWnH8qo7CDHbPckUxuwA1CWO\nbr+97ECP7u3nNUb8j1R3y+IzlFI8mTjcqlRR1lueJKIgqTHd+txts2b+bSfY\nfD/4uXoQqzMKl5eepxjlVCfVVIKb43EjAMxDKt2eIlZubaLhAvzSSdeBqwV0\nk1rdAy+YQzrJ5lFAoljEnwwuQPiuF6QVbKTACxHmsGU6mJa6xOMP6vYYW4BS\nLRWx2bKNL7cLJx90UqkfLBEjwNNP/+F/PDZL8+WmJTff39slDEPjRlZdOZl4\nH5Ci\r\n=8/YF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">= 6"}, "gitHead": "e388ff61f24c25f46e03e515ee68adf8731fd408", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"debug": "4", "socks": "^2.3.3", "agent-base": "6"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "raw-body": "^2.3.2", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_5.0.0_1580845369916_0.36738986918128425", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "socks-proxy-agent", "version": "5.0.1", "keywords": ["socks", "socks4", "socks4a", "socks5", "socks5h", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@5.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "032fb583048a29ebffec2e6a73fca0761f48177e", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-vZdmnjb9a2Tz6WEQVIurybSwElwPxMZaIc7PzqbJTrezcKNznv6giT7J7tZDZ1BojVaa1jvO/UiUdhDVB0ACoQ==", "signatures": [{"sig": "MEUCIG92Ufcnmhnck2mWFcm8KH0HAkosSBEQzkuusAIKFuUTAiEA01Q30Pil5tn4nBCpUkSNPp6LXcFAAVAlPH66ldch+YY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0O5SCRA9TVsSAnZWagAAjcIP/RXb/5uF42adMYdOuA8o\nfSFuDr8oum2vYoialNPY/OGLnZWAK+DBGuIINAC5DegThUrdVbPWDLqG4X+k\nUOuSiOBeIKAYRglhNEQKiZjUS21Q/qkuPdUY44UDnaGUPjESNkDo6L6Sovkl\nFTRkVzoLc0AjWMGnzKqMKcZk6NzQk4a/nZASh3va5m58EW/x/j0ZNLfv1Vg9\nTk0cGg22Q5+BvmeWBolt3/e/H1MlmlPLk8mmneNsjCIGz+c4DO+lZxSGCQtt\nHheA46Jfc0uC/BtjTUtY5pGtYizp148NW2o/0H55GPa4ztivTgi+jbuzvQL7\nGeGY6Ty4mTU8R+gMEOSFz2MS8FM+JHc/tgnWHETntSZhRWx+CSSPWyp08xgA\nG7/4gWzyQIuIwPgPWAnb3xatBKmdoSb1EE1cKzPmoKXEj6gVwc4GL2dVupEP\n87I4OQYXyo7e1c1GYT92JV3zFApBwZy4sBsjKqlwHWoi9IedxaBhNL7FKMij\n/+VQcOPUlzikiEarz+H1urqrP9yl1RLm+ONe6UToUykF10aE1vLpUJ9J9hrY\n+/eqly/8ywbWziBvqXaciXGdY7GZVWN+ssAUypkGS3lrz2ORb/vCbEDDYS08\nzrBUWInSMXO3u+NxRYeofoqxDvvyLH3xh3Fzz8GLrK/C+ls+sWNFgj+gNEFW\nn6cb\r\n=+1Gd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">= 6"}, "gitHead": "d1cb60f45bcdc2bef8dbe374d5d3972beaed74e4", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {"debug": "4", "socks": "^2.3.3", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "raw-body": "^2.3.2", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_5.0.1_1624305234503_0.9183007225806115", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "socks-proxy-agent", "version": "6.0.0", "keywords": ["socks", "socks4", "socks4a", "socks5", "socks5h", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "9f8749cdc05976505fa9f9a958b1818d0e60573b", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-FIgZbQWlnjVEQvMkylz64/rUggGtrKstPnx8OZyYFG0tAFR8CSBtpXxSwbFLHyeXFn/cunFL7MpuSOvDSOPo9g==", "signatures": [{"sig": "MEYCIQDiUckFiB5f+Qu9N9dBP/+pPq7/IkXPXO9fCxYPQ1uqKQIhANqDzlbBAw0R8HBoH6+tTFit1U1OIScb8B9Hf3N+jT7g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4YdLCRA9TVsSAnZWagAAqdQQAI5/892WMlgCTskzVpRS\n6nK9Kzhffs/5AKtolIhKM7kFIf4UP+3alcbEuFbc8S0BcSpH2gx9OKx4GVnE\nJpdQxy5fLxFvllCOEE2Nz4GLdHSuXzOQdO782nY0fQtZ+yjBpMy69gGy2eoL\niwR1RA0VNDEE0XwqvzGpiLMEbophBWognK2RdGQaowRFN4N24le+NEJqXfcK\nHWNfvOEcSyuV3Noj90M3vDXgw3Lgw3vsHjgzaNyJ2+NVlJd0S5p9epyn4X9u\nw0JQyToJENlpAZVSnWEFG9TnE2bHO6re8Db5NVmbdJzE4YchBd14FGlmkW2G\nDcSL1LFmHxSg9gaAIoZJVRMQ9wxTON9zMY/pUmfmwxw6I3hRvj/LWRy+geEf\nY7QLlYhmb6SbsfG5/HsaI6VIjLNAuEQLdWT2Ny6fGWmHWZ3Fu4aCkVtCezYi\nC0QliOWhulExKhd+2XgEBRYHBhWnm8KDdv63Jiaaux60zU1JsyKAG0gzMOxn\nIq6I1ani6flMgCn0aZBiiOT+erEzSc5x9FSHcimmB8yIbziaPbbUxpBUWIUI\nMF6A+QMTqfgT5dSC5CZ4pfglj7zOIislR+QVMXfFYd60MK1m+PLsbWxsJE5F\nhUKUFE3byg+nWXG72O9HOLGWdQZkX/ovB8jrpx6/NNsQFza3ZOoMBzJCIEZd\nv+cw\r\n=DaVn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">= 10"}, "gitHead": "7a628bbf43034f86a9c3e9f9215692b72cdc7a4d", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/index", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "16.4.0", "dependencies": {"debug": "^4.3.1", "socks": "^2.6.1", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "latest", "proxy": "latest", "eslint": "latest", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "raw-body": "latest", "typescript": "latest", "@types/node": "latest", "@types/debug": "latest", "eslint-plugin-react": "latest", "eslint-config-airbnb": "latest", "eslint-plugin-import": "latest", "eslint-config-prettier": "latest", "eslint-plugin-jsx-a11y": "latest", "@typescript-eslint/parser": "latest", "@typescript-eslint/eslint-plugin": "latest", "eslint-import-resolver-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.0.0_1625392970604_0.6393042967124536", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "socks-proxy-agent", "version": "6.1.0", "keywords": ["socks", "socks4", "socks4a", "socks5", "socks5h", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "869cf2d7bd10fea96c7ad3111e81726855e285c3", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.1.0.tgz", "fileCount": 8, "integrity": "sha512-57e7lwCN4Tzt3mXz25VxOErJKXlPfXmkMLnk310v/jwW20jWRVcgsOit+xNkN3eIEdB47GwnfAEBLacZ/wVIKg==", "signatures": [{"sig": "MEYCIQDX9I1yiHr0iZKbAndWPo3jmmvB0vhaA8MC8j+qecaqtQIhALyEhvfZmPBopHHvRLOuFD43+GsXg4G0cjzUF9cM2mYu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQgVYCRA9TVsSAnZWagAANUgQAJodWwyGHXL2tSvU5wXd\nc5UsEsdCrHxHG+Mm+fII+cW2KYPnPmPdkf5ADCeRVhcn3aOzcE9qaJaKpUJB\nNRBJ5lI0pelRd4I9pzYaXACkwiWpN2EP33eCtuKb3HORxC8lh/ABCkxTIDzN\nq6hLQRHwXzrKvxSVT6D4uoG6BEkDVdJSOcfquXdF3bMCvdoqk2igGsczUdDU\nCuIXNG4ftKshgwBydIMmkFLiTN261RiPVG+0SRizjTRPTTbXqC5w/AsmlqE0\nZkd6Va2NRVgxDLwVV4Ux6zHSckKOYQWskpt9K4F9C8s+LTsbKQYWMv+iJvzG\ny724x8dR1/63RRp85Zd1txhkXvYtR9b4m5YGxtM0ZKWJf5mROzJjSJtE4U1C\nVMtxA/owsebrE+Xn/w56+FCEgAREKPDwZZcgYRsW5pxuVqWmjQuNjNZnQbuo\nbkVFa93yUZDDGTfNLf+c6sfOkjCeFSN+NqRGnzf5PMs6s5K7cCIdLUCXuIxI\nCUmT+/9R15rpyuLbfgOPeOZhIU8Vhd9j9X7lVNj6oQrREWJ1HJG2TWF6jLVO\niXdJNKYbgoGsTeN8PVgK/yJo5dfTudp8N9ANVKvORNecytzk72MxmDdGSZRA\n03grCqXF4slE4fH7GbMVuF//CHGGyRiWBcWS+LA+6tuckWtqd8dNdBMvf/YV\n2Cn3\r\n=gRjP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">= 10"}, "gitHead": "2cc879df8b16a76c928a3ebb9e3e113c74958f70", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/index", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"debug": "^4.3.1", "socks": "^2.6.1", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "latest", "proxy": "latest", "eslint": "latest", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "raw-body": "latest", "typescript": "latest", "@types/node": "latest", "@types/debug": "latest", "eslint-plugin-react": "latest", "eslint-config-airbnb": "latest", "eslint-plugin-import": "latest", "eslint-config-prettier": "latest", "eslint-plugin-jsx-a11y": "latest", "@typescript-eslint/parser": "latest", "@typescript-eslint/eslint-plugin": "latest", "eslint-import-resolver-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.1.0_1631716696583_0.1348484923425961", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "socks-proxy-agent", "version": "6.1.1", "keywords": ["socks", "socks4", "socks4a", "socks5", "socks5h", "proxy", "http", "https", "agent"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "e664e8f1aaf4e1fb3df945f09e3d94f911137f87", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.1.1.tgz", "fileCount": 8, "integrity": "sha512-t8J0kG3csjA4g6FTbsMOWws+7R7vuRC8aQ/wy3/1OWmsgwA68zs/+cExQ0koSitUDXqhufF/YJr9wtNMZHw5Ew==", "signatures": [{"sig": "MEYCIQDEr2LrwhPAWIgQ+mJIbmiJrbPd/3rEHPRTJ1YlfWkxywIhAPkL36kf8JB+1N/t5qLH9cYu/BX2SlQCklmJDPDwzN4G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm+AVCRA9TVsSAnZWagAAnmMP/ird84JYY98C+YvQ4wQB\nvZUc6/KymvMo7hGluWKz8xDfSV4U1AVgSPDkC1oIxGdaVwr3i2otgBtx8E0e\nuy/+Vj8XjRIIq89VyoYrp0v7v/9H9upiy81IlHBzlDLFAIL7wX5cSxCtiPOD\n8uIH05KaWpwbDrHkMCy2CXJt0KaNT/5oMIpjoPPYYB+CGcdUW/9CbSnjHqvJ\nUs2kkHKyBqQQ53qNfPBdtE1QW6Jb6OR5n51MlF4Dq59pObORYhFuBRvek/HB\nPrR4e532EBFT/BWTSx5IBD5Jkv2OX1dULJSwkAtHEaKe9EiXOezjlaeBollE\neiBoD4fK00W5oJPeRuS+aAoeejIKtNy7zTkq6ivlHF5hhuRHGMx98/Rf2sXO\n+4denXd2yLubTChVyJFvD90fzUaMfLBiJbu/coJBcy5/4zGav7RH49zuiGsM\nlc4gom7vdmF3dqC7Szh/UwvgLKq1iZPUAikMCFltip4Rt7rlq2sUbGeywQs5\notEdw8tyD6fQqRBv+FFSZEg5Hetzr46yHRKoUFhSur71lcp8KZ3QQXQkSLeW\n0ZRnzlmJazthIroE+9htstTc7TQgZP8GJTnmMz/X9GrlfTwR9WxPPVtv4219\nucvNB7dkJgTc7TO+JqN8UCRq3j0DwnIbfpi5WvWkjbppmxTLXCTubcSWxnae\nUP7x\r\n=Opes\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index", "engines": {"node": ">= 10"}, "gitHead": "d5dc368564164cc2dc5d44126ad132e53544ac07", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"debug": "^4.3.1", "socks": "^2.6.1", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "latest", "proxy": "latest", "eslint": "latest", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "raw-body": "latest", "typescript": "latest", "@types/node": "latest", "@types/debug": "latest", "eslint-plugin-react": "latest", "eslint-config-airbnb": "latest", "eslint-plugin-import": "latest", "eslint-config-prettier": "latest", "eslint-plugin-jsx-a11y": "latest", "@typescript-eslint/parser": "latest", "@typescript-eslint/eslint-plugin": "latest", "eslint-import-resolver-typescript": "latest"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.1.1_1637605397088_0.3982720923253651", "host": "s3://npm-registry-packages"}}, "6.2.0-beta.0": {"name": "socks-proxy-agent", "version": "6.2.0-beta.0", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.2.0-beta.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "f099f38025ce2f0d18a6faf2cf7e0bc2ebb3b79c", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.2.0-beta.0.tgz", "fileCount": 5, "integrity": "sha512-vJVDGsyaBh7cP8BfynQV1sSqiZ045FkNTyaWLz1g4Ut3sCIuO52sxK0ix8yvqf5n0teDyY1Pw4NRclRiuGTV+w==", "signatures": [{"sig": "MEYCIQCsUEOPLa4/WEcWtv94bguYsgtvhtOWYEykAYtUP72rIQIhAOVG94g10JHp2cSLazRkv27JBH8Vq722B0BmD1Jf0x9X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCEc4CRA9TVsSAnZWagAAibYP/0ud3JN98p5RFf65JOHP\nMdhfDVVtTl1WnqnXC2BNNSmfqX11obDGJ2XH0f+5D5XI8kFCwDXULTKPbEAJ\nwduF8jYnqkXMg3EMUFmjGAtktxabFZpX6dVZvrbQIBu+TIqJHA4lSfWR8dQF\nYu4Dad8m1Znj6BeLmGvrOpKFTGh8H6v8GCJvifvu7IHcqEmlACShwbK6zVFf\nrAb1yexikfqrLfq3kX9rFckpAEw4fQXKntumkGedA2WWZM5W0Z5shHYCJbuA\nh/q6SWgGzj9VYfvAajceYKcCwMIsdtDyx6Hok9vSwmVB4GF5MrByCLowQKlr\nl6Bs769woBH+m2fUipXfoHc0XckVNECdk15cVqYnvDvzDrebvb/YPWX7iGy5\nMzd45ZKR/xKdv9gauC2BUZsp1vOik/OyHuJJsCdVQ4WFwz2GzVp2rFbtcWsN\nErZEVntRZ8wwFRUaUVcNv3XWXU8GpdlwBYT5eb1DROTzRzof/AxxIsJblhTK\nIH2InMcC2WrzlTWMwonNiW5gUaLvnqUMN5WQtY+fwYVq6QCT+yp3MAoUtHYV\nDikYBvJlG04l0JFAUBH7OYS8vcyJ8puC+EuvmNxd98W2o1wYPeZwtQNhW7JY\nzytDZTv3i1JOBYJqnyZPXXnawrIVYyidOfFsGSF2U+Lg/ttIuXynm7pQZ+Pl\nGSJd\r\n=WrCg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:socks-proxy-agent-6.2.0-beta.0.tgz", "engines": {"node": ">= 10"}, "scripts": {"lint": "ts-standard", "test": "mocha --reporter spec", "build": "tsc", "clean": "rimraf node_modules", "update": "ncu -u", "release": "standard-version -a", "prebuild": "<PERSON><PERSON><PERSON> dist", "prerelease": "npm run update:check && npm run contributors", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "release:tags": "git push --follow-tags origin HEAD:master", "update:check": "ncu -- --error-level 2", "release:github": "conventional-github-releaser -p angular"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/Users/<USER>/Projects/node-socks-proxy-agent/socks-proxy-agent-6.2.0-beta.0.tgz", "_integrity": "sha512-vJVDGsyaBh7cP8BfynQV1sSqiZ045FkNTyaWLz1g4Ut3sCIuO52sxK0ix8yvqf5n0teDyY1Pw4NRclRiuGTV+w==", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"debug": "^4.3.3", "socks": "^2.6.2", "agent-base": "^6.0.2", "cacheable-lookup": "~6.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "latest", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "finepack": "latest", "raw-body": "latest", "standard": "latest", "typescript": "latest", "@types/node": "latest", "nano-staged": "latest", "ts-standard": "latest", "@types/debug": "latest", "@commitlint/cli": "latest", "git-authors-cli": "latest", "simple-git-hooks": "latest", "standard-version": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "standard-markdown": "latest", "conventional-github-releaser": "latest", "@commitlint/config-conventional": "latest"}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.2.0-beta.0_1644709688071_0.3590181911403465", "host": "s3://npm-registry-packages"}}, "6.2.0-beta.1": {"name": "socks-proxy-agent", "version": "6.2.0-beta.1", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.2.0-beta.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "a77a36d41bb52cd1414dbd9fb637bab1d9008330", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.2.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-OakYR1qSDW9Ksgy7kHp5zdx/LXP3sv2tMq+FQ7VWpz/bZITwC1flAFAEL2oAG1Ry8vA0vvWARazG6GWrPS4FAA==", "signatures": [{"sig": "MEQCIENVx+r5GXe/CdsAudzpfahXDbPcKkBshSd1fsFuZ9iNAiB9Xe8itt6K6xtNPyY+csuIfHuhDW454x5eGQGf3XRqPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC8JLCRA9TVsSAnZWagAAcpQP/0iv85oNqEhJd/HuD0ef\neVSPT/zhr1paqq7/kgBkWk3R2UJ7Yoclheay+03E1UAm59Xg5luJrtsrfX15\ntGs7aeezsvZoYERULFPcSaL1SL9fhkBLeng4mc6zkxsvhLV6itDJqg2Efis6\n73Osm35yIPS7GxK6V93gGH+TvhjXoqGw3MOtsbidtmUVuseHomc5oIud7Ezr\naxK0w6CWJHi9K/02qrLa6k7sb8JsP4Y9L2c3NATI65j2POrYwMLUK5CGxCYX\n24MENohJwFlXQhwiRQhmPBM+e0SBK2N5AXvxzB5C2hyj1/LrrWIZFy1si9Sb\nOf4s2+JNI8kAHGkGRmmnNzHkSwFrk2VZthKT37d+UHeDLW57GGc0y95eueWQ\n0M/vXV161twc5QMx5IszlEN/Cdgo1QOrplPKsWf2Q01Plv/sFRx8AQ31nfsV\nwN5At1+Q8qZNH6kgZV7ytYNSQWkE/HbTAK8XjpExZdbzMqHQ2Rl82ac9eQQt\njTRNa5KNpDJGo/8ka6suLpq5SF+6IXW+nXD2eT/oGOi519fHC3MDoYfFd7ce\njDoe+/+IuQtvxu6X6h9fgGnMDrLGEvKsZMbs39almyginZ75jFsrnbfH+h3i\nOaG1raKr0N/xgfYAl4F3XodF6k0amAxE69xP/hfYY+onaumr+RHvgwYzhO9s\nycXh\r\n=8XUz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:socks-proxy-agent-6.2.0-beta.1.tgz", "engines": {"node": ">= 10"}, "scripts": {"lint": "ts-standard", "test": "mocha --reporter spec", "build": "tsc", "clean": "rimraf node_modules", "update": "ncu -u", "release": "standard-version -a", "prebuild": "<PERSON><PERSON><PERSON> dist", "prerelease": "npm run update:check && npm run contributors", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "release:tags": "git push --follow-tags origin HEAD:master", "update:check": "ncu -- --error-level 2", "release:github": "conventional-github-releaser -p angular"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/Users/<USER>/Projects/node-socks-proxy-agent/socks-proxy-agent-6.2.0-beta.1.tgz", "_integrity": "sha512-OakYR1qSDW9Ksgy7kHp5zdx/LXP3sv2tMq+FQ7VWpz/bZITwC1flAFAEL2oAG1Ry8vA0vvWARazG6GWrPS4FAA==", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"debug": "^4.3.3", "socks": "^2.6.2", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.0.1", "mocha": "latest", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "finepack": "latest", "raw-body": "latest", "standard": "latest", "typescript": "latest", "@types/node": "latest", "nano-staged": "latest", "ts-standard": "latest", "@types/debug": "latest", "@commitlint/cli": "latest", "git-authors-cli": "latest", "cacheable-lookup": "^6.0.4", "simple-git-hooks": "latest", "standard-version": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "standard-markdown": "latest", "conventional-github-releaser": "latest", "@commitlint/config-conventional": "latest"}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.2.0-beta.1_1644937802961_0.49081566458799775", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "socks-proxy-agent", "version": "6.2.0", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "f6b5229cc0cbd6f2f202d9695f09d871e951c85e", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.2.0.tgz", "fileCount": 5, "integrity": "sha512-wWqJhjb32Q6GsrUqzuFkukxb/zzide5quXYcMVpIjxalDBBYy2nqKCFQ/9+Ie4dvOYSQdOk3hUlZSdzZOd3zMQ==", "signatures": [{"sig": "MEUCIQDu1gkZvLN4xDNdZPfnG2gGlshD6PRycQXLGaClgvrnkwIgQMcmYJpg8zi46t73GxT5U4OFTw9cUOR+s0c9ce88W1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXKjcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUchAApBkXNm7YY+6ygSriZpDAnDkGTWAcOTIM4D3YZlsXHJEo8GKT\r\npX8sPpX3eA5fnVT48xzfElmnoiakZf1Lb5TGSFoLV+Guv2qV6W1DoHcfA9w4\r\nkTbIiQKE288jmysW/h8Ogiv1ecr0NCrjxeEq6WT8qi0KogOeBPeIcDmwm086\r\nThZ4noMVjpTcvrTr4n9vURYlILSrZThG8yjYZqozxhM7oxS52kzSR8oyGU+z\r\nymD4tRrf4GUUwt1r5q68C7INUqQxXlVrBeZXEM7C/vSTj7NsgV+FzBC2/lOY\r\nfWQwkAJNS1Jv5iwFRreAqLjzontU4cR90VKX/xCrilxq7dssD9z28LNOJ2XG\r\nihcKoyu4m8jD3IMOkIHVUTqbdSxUBGJ7bkRqDN3/MU7omlGqoNkvrU5K6+Iw\r\nAZbM1yhJTfgGCMrowZH3ld0t9v+bvdCViqEHiFc98SMwvf2BcOvm3+MYqoU6\r\n0jChyQRPUoEVhRSV78v09rw2Mjy+GkwtaeEvgeu2BNyakRaq1ESQ2pNQh7X7\r\nrK8L6hlJY4g2In6Ey/d2fWaXijuRF0MZeircMI41HtoSlSwxJb67DejCHeFX\r\nIA0TeZ2bMcS7AUdCvSB5G4e8yx5kolzfowO8pNHJpxg4X6GC22V+Tb3pxOpY\r\ncea5t0j1qz88r2ZwHf1FxLZoCIMXQLxEZbI=\r\n=Tm5U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "_from": "file:socks-proxy-agent-6.2.0.tgz", "engines": {"node": ">= 10"}, "scripts": {"lint": "ts-standard", "test": "mocha --reporter spec", "build": "tsc", "clean": "rimraf node_modules", "update": "ncu -u", "release": "standard-version -a", "prebuild": "<PERSON><PERSON><PERSON> dist", "prerelease": "npm run update:check && npm run contributors", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "release:tags": "git push --follow-tags origin HEAD:master", "update:check": "ncu -- --error-level 2", "release:github": "conventional-github-releaser -p angular"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "_resolved": "/Users/<USER>/Projects/node-socks-proxy-agent/socks-proxy-agent-6.2.0.tgz", "_integrity": "sha512-wWqJhjb32Q6GsrUqzuFkukxb/zzide5quXYcMVpIjxalDBBYy2nqKCFQ/9+Ie4dvOYSQdOk3hUlZSdzZOd3zMQ==", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "nano-staged": {"*.js": ["prettier-standard"], "*.md": ["standard-markdown"], "package.json": ["finepack"]}, "_nodeVersion": "16.14.2", "dependencies": {"debug": "^4.3.3", "socks": "^2.6.2", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.0.1", "mocha": "latest", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "finepack": "latest", "raw-body": "latest", "standard": "latest", "typescript": "latest", "@types/node": "latest", "nano-staged": "latest", "ts-standard": "latest", "@types/debug": "latest", "@commitlint/cli": "latest", "git-authors-cli": "latest", "cacheable-lookup": "^6.0.4", "simple-git-hooks": "latest", "standard-version": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "standard-markdown": "latest", "conventional-github-releaser": "latest", "@commitlint/config-conventional": "latest"}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.2.0_1650239708139_0.21035474401567145", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "socks-proxy-agent", "version": "6.2.1", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@6.2.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "2687a31f9d7185e38d530bef1944fe1f1496d6ce", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-6.2.1.tgz", "fileCount": 5, "integrity": "sha512-a6KW9G+6B3nWZ1yB8G7pJwL3ggLy1uTzKAgCb7ttblwqdz9fMGJUuTy3uFzEP48FAs9FLILlmzDlE2JJhVQaXQ==", "signatures": [{"sig": "MEQCIF3//uYK4ESAHexDD+h+pvWLsotXCUOJrQbSHq6LNNdOAiAbW+eQK14z2gZKWXZvinToi/0fixOc+OCpcvwV2h9eXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilfmXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhRA//SXlBqrlNrDQcxTWEiy77tM+d/wNBN8S5mXZOMnVogzNPvE2j\r\n2W6NSmrlDkWGHlYxzQFD+C6wBDGcR+2/iBcjU8rUXWPeQZOgUchmCX491PdQ\r\n5/ubuEj36tL7KGSOn7E+nbkne5Cj9EHt8PnqeWKVpolAs0Ox2f9xnRZMuCyc\r\nt4lTtBWzx1m0DBEBPXiKQ+7I26Tjh580OwUtpFpAyHAiQoe7EPwUW7yARZzs\r\nyySzExHVfvbcgGCt2SCdWfM34Wb5pVm+5Ye9HUopzgk2c2ThuaIePP04GBUG\r\nbqR6k4tTqXaFnlUfZfy9n7t+SykHl/YLICN4qpsx1fWqOrgSS9ixaxqidJQF\r\nb7mmnWBjMABYw3Ex23/8x0anKr5xilEKISk75CbpKiK7BIHlW/aLNLrDrWnW\r\nexu7YDUYzflSmY/WCwOBzqTPAwIcJHxD6hUPevMIu84SxKkZYYNeOjq9gT/W\r\neyi0lMQUMmYIyGPQklDXYF6x2Ga8UXmf1sqraVTJ+16L/ruNUrWFchVdwbEl\r\n2E7IGD7omkLGBcpZarlTTgWhNKD9uIPBNG5sZbN4hAFyzqKmGKfA2BBQZ3aA\r\nOJphSDEC0ilFRY0XS0vrZI+yeKFunjx783oztWlM969TebLxk987Fl0zuSni\r\nSwz036I6kRx9J1f8ex2W7VChZuUNvsig+II=\r\n=JdQM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 10"}, "gitHead": "73b75cfd4d1b3117f9ded2556d685b969c05bd69", "scripts": {"lint": "ts-standard", "test": "mocha --reporter spec", "build": "tsc", "clean": "rimraf node_modules", "update": "ncu -u", "release": "standard-version -a", "prebuild": "<PERSON><PERSON><PERSON> dist", "prerelease": "npm run update:check && npm run contributors", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "release:tags": "git push --follow-tags origin HEAD:master", "update:check": "ncu -- --error-level 2", "prepublishOnly": "npm run build", "release:github": "conventional-github-releaser -p angular"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "nano-staged": {"*.js": ["prettier-standard"], "*.md": ["standard-markdown"], "package.json": ["finepack"]}, "_nodeVersion": "16.15.0", "dependencies": {"debug": "^4.3.3", "socks": "^2.6.2", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "latest", "mocha": "9", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "finepack": "latest", "raw-body": "latest", "standard": "latest", "typescript": "latest", "@types/node": "latest", "nano-staged": "latest", "ts-standard": "latest", "@types/debug": "latest", "@commitlint/cli": "latest", "git-authors-cli": "latest", "cacheable-lookup": "^6.0.4", "simple-git-hooks": "latest", "standard-version": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "standard-markdown": "latest", "conventional-github-releaser": "latest", "@commitlint/config-conventional": "latest"}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_6.2.1_1653995926818_0.8380211579517447", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "socks-proxy-agent", "version": "7.0.0", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@7.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/node-socks-proxy-agent#readme", "bugs": {"url": "https://github.com/TooTallNate/node-socks-proxy-agent/issues"}, "dist": {"shasum": "dc069ecf34436621acb41e3efa66ca1b5fed15b6", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-Fgl0YPZ902wEsAyiQ+idGd1A7rSFx/ayC1CQVMw5P+EQx2V0SgpGtf6OKFhVjPflPUl9YMmEOnmfjCdMUsygww==", "signatures": [{"sig": "MEUCICdq3QI0MsbGpVyq6arH7ZPsw59y5ZpzNYFDX1iS5yezAiEA1HjojkfuGExST/2KIwLxj0t3XfPeQIP71mlJChfi7mQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiliNNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVzA/9Gkzof53BnK7JxsZ45+Rydik0o37kAYWrBQfYO2qUAnIn7iL0\r\nKyRslZaAdYC6v+Xw28bc32O/HdiGqD0uQok1K06jQyVaRJqc4AI8vRwoSmIM\r\nRJ0VA9XiKGLILYa/XNB5YxS+b4gadPXl4jpfzizP9vj8TelwXUPq8YpmHhws\r\nPLS8QclqkBkgU0gg4lxO64hTKbiDiql2HNF948P6v33B9x+Si8xb1dPh9tX4\r\nm9OVTRmXifmdkhxxNwjh/xZyhCQdFcON7YQ0H6REbrFUXg9gJEactWJ8cwEt\r\n2lph7sAeER9TcrZ0TKA9YsTzb16gIQOG2oCH77zGW6QpHcAdku03PXXl+C/9\r\nhKsugtJgjIBR3uhjiy0zmyjWYv8xCOpshrS2SlONFz+ru0kVeihNVu7mYLv1\r\nCDHeGMDIfIztCzEvWrDrHByZL35HhkNPMVeZ1dC41ffngZmCgEcik4dhmiCC\r\nuRcG26RJyxxGSNrDnGMYRKnBFCfztXFr4CEQ+kyrbdLYcUfs02oRac0xZ9+9\r\nZScRCjBXabTV7swMBFQHOsziC1pspIvD2CdZJxcsY1B1jLdspqyv2NpTfOo0\r\nTno8wkw+NEuRPGAexZi2q4AscNrDoiktbiMy5LLjVMgZ8SGSkB1SLsSifzwq\r\nLHKdseVR7zaS8V5syUIawqvQQm5xyDCAoi4=\r\n=FSCR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "engines": {"node": ">= 10"}, "gitHead": "c178516b85c618f308bb4fd0002878c9dc5d9824", "scripts": {"lint": "ts-standard", "test": "mocha --reporter spec", "build": "tsc", "clean": "rimraf node_modules", "update": "ncu -u", "release": "standard-version -a", "prebuild": "<PERSON><PERSON><PERSON> dist", "prerelease": "npm run update:check && npm run contributors", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "contributors": "(git-authors-cli && finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "release:tags": "git push --follow-tags origin HEAD:master", "update:check": "ncu -- --error-level 2", "prepublishOnly": "npm run build", "release:github": "conventional-github-releaser -p angular"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "kikobeats", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git://github.com/TooTallNate/node-socks-proxy-agent.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "nano-staged": {"*.js": ["prettier-standard"], "*.md": ["standard-markdown"], "package.json": ["finepack"]}, "_nodeVersion": "16.15.0", "dependencies": {"debug": "^4.3.3", "socks": "^2.6.2", "agent-base": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "latest", "mocha": "9", "rimraf": "latest", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "finepack": "latest", "raw-body": "latest", "standard": "latest", "typescript": "latest", "@types/node": "latest", "nano-staged": "latest", "ts-standard": "latest", "@types/debug": "latest", "@commitlint/cli": "latest", "git-authors-cli": "latest", "cacheable-lookup": "latest", "simple-git-hooks": "latest", "standard-version": "latest", "npm-check-updates": "latest", "prettier-standard": "latest", "standard-markdown": "latest", "conventional-github-releaser": "latest", "@commitlint/config-conventional": "latest"}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_7.0.0_1654006604913_0.30649122435796805", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "socks-proxy-agent", "version": "8.0.0", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@8.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "ee1f3e6fcf10a970f5b93ce206503d30daf6ec94", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.0.tgz", "fileCount": 7, "integrity": "sha512-5tX6GGsT0q6nXjBd/fDtIXxdX9OOOaZjdo5jvtA8fODcP/4Fa2BH+8zfEgxdtyGN4fRVfZcxsSu8vkJILbrD5g==", "signatures": [{"sig": "MEUCIEFmJozxpba6LLKYtJa+9MQBbgLWCvA0zTw76uwMK+TMAiEAup7BxEtXTEUVkpjfaHc3Hlusmy+qd1pa+rtI0RzN/7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmof0g//VCJWW/aGSP/jL5T9V2tJC8uCSQHwcJDNgoeNrLyTRX894shl\r\n/E5Nhl849LpY87wnq/giGDIACHNvUJtgY6LsXsG9nE4g3fFMUj6mwHSi9+7p\r\nwosf2BdAZpk2y+ShRJpPnU2JEVRUNGWgZTJmyPM2T29IXLgu6WKfaAwWHYFP\r\n9H+yD2iZ9NZ5WGBUYAxU5PAyEPmUVRO0gJfpW8+LiO9gh9VVuTCXj/5zlTcD\r\nWeDDPATsXYc+g0/fvxugyQbIxAjaj+0K8v7ETbZRSzFx/pln8I8LXbxCKym4\r\npdYaGsYo1Fot6uVL2ZTLozytAMjuG09yPVRvKXNX4l11gap5Vz+y5qmCP9F6\r\ncaRjM0/nwWon7g1kHzW3KyP29YXCkKEcLXHoCflVi1ZHEVz0KFd1g7iFKXOB\r\ncHFWVTe+jZEY2/30nxdxSb+Of6eHtWfFX9IGluBLvY1chZHiUM70xXO8ykao\r\nFoheML7GGKH1rBdUw7dZqGVjIkDKwyLxV1YDxUS/Q+ZWbJ91ucGR9TGYMpI0\r\n9Y0SHTPQ1hoaEJWggYKKcs67dkFxT/6He06mdhx4Klz0kTJMBh7G2vRYQZHg\r\nY92SGzKIDLrwSskA1Kx7OJAWUJ3UuZrVAyIlBTVcg0FtNXlX5E+REP/JRPtL\r\nK37RZ14/mynvbxSfoThhvk8Z03cPsNWxi6g=\r\n=FBkj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:socks-proxy-agent-8.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/bcf514c307adaf9aff3a909e65dfe472/socks-proxy-agent-8.0.0.tgz", "_integrity": "sha512-5tX6GGsT0q6nXjBd/fDtIXxdX9OOOaZjdo5jvtA8fODcP/4Fa2BH+8zfEgxdtyGN4fRVfZcxsSu8vkJILbrD5g==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/socks-proxy-agent"}, "_npmVersion": "9.6.4", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "socks": "^2.7.1", "agent-base": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.1.0", "jest": "^29.5.0", "proxy": "2.0.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "async-retry": "^1.3.3", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "cacheable-lookup": "^6.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_8.0.0_1683232399695_0.3033674906516193", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "socks-proxy-agent", "version": "8.0.1", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@8.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "ffc5859a66dac89b0c4dab90253b96705f3e7120", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.1.tgz", "fileCount": 7, "integrity": "sha512-59EjPbbgg8U3x62hhKOFVAmySQUcfRQ4C7Q/D5sEHnZTQRrQlNKINks44DMR1gwXp0p4LaVIeccX2KHTTcHVqQ==", "signatures": [{"sig": "MEUCIQDDQLsPor9fytlOlpdFwiuEhxJv84Kzn7spHg4d+3UMlQIgfNH3DTPIPqnlF3lvEITqofQrQjlFrh6dRXK2QEeaQSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23913}, "main": "./dist/index.js", "_from": "file:socks-proxy-agent-8.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/f2f8d45ae926ca1143595c096bf8266d/socks-proxy-agent-8.0.1.tgz", "_integrity": "sha512-59EjPbbgg8U3x62hhKOFVAmySQUcfRQ4C7Q/D5sEHnZTQRrQlNKINks44DMR1gwXp0p4LaVIeccX2KHTTcHVqQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/socks-proxy-agent"}, "_npmVersion": "9.6.4", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "socks": "^2.7.1", "agent-base": "^7.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.1.0", "jest": "^29.5.0", "proxy": "2.0.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "cacheable-lookup": "^6.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_8.0.1_1683324252999_0.27986346719846833", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "socks-proxy-agent", "version": "8.0.2", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@8.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "5acbd7be7baf18c46a3f293a840109a430a640ad", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.2.tgz", "fileCount": 7, "integrity": "sha512-8zuqoLv1aP/66PHF5TqwJ7Czm3Yv32urJQHrVyhD7mmA6d61Zv8cIXQYPTWwmg6qlupnPvs/QKDmfa4P/qct2g==", "signatures": [{"sig": "MEUCIBTg5YCSBUdIagasKJtoBUVJIAE5x+Wccz6qiWTwKm6nAiEAzlHV+lHFL/8SAEHZ2bhZ2HVDwMZ35+75LaOGT5RkvVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24066}, "main": "./dist/index.js", "_from": "file:socks-proxy-agent-8.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/e82c65ac3bdbe4941ebd76bf3148b456/socks-proxy-agent-8.0.2.tgz", "_integrity": "sha512-8zuqoLv1aP/66PHF5TqwJ7Czm3Yv32urJQHrVyhD7mmA6d61Zv8cIXQYPTWwmg6qlupnPvs/QKDmfa4P/qct2g==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/socks-proxy-agent"}, "_npmVersion": "9.8.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"debug": "^4.3.4", "socks": "^2.7.1", "agent-base": "^7.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.1.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "cacheable-lookup": "^6.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_8.0.2_1693814982998_0.9858331877342932", "host": "s3://npm-registry-packages"}}, "8.0.3": {"name": "socks-proxy-agent", "version": "8.0.3", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@8.0.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "6b2da3d77364fde6292e810b496cb70440b9b89d", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.3.tgz", "fileCount": 8, "integrity": "sha512-VNegTZKhuGq5vSD6XNKlbqWhyt/40CgoEw8XxD6dhnm8Jq9IEa3nIa4HwnM8XOqU0CdB0BwWVXusqiFXfHB3+A==", "signatures": [{"sig": "MEUCIFlNOLFAnaePqnDrm6flWupQ5oqaMBU2qP+IXWAB8fJaAiEAp8wqVLqJcODQJbNyoeJO3BFoZHpL/VYg3SCgk3iDMJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25074}, "main": "./dist/index.js", "_from": "file:socks-proxy-agent-8.0.3.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/34167cc81be6fb3194a26c7413519cac/socks-proxy-agent-8.0.3.tgz", "_integrity": "sha512-VNegTZKhuGq5vSD6XNKlbqWhyt/40CgoEw8XxD6dhnm8Jq9IEa3nIa4HwnM8XOqU0CdB0BwWVXusqiFXfHB3+A==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/socks-proxy-agent"}, "_npmVersion": "10.2.4", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"debug": "^4.3.4", "socks": "^2.7.1", "agent-base": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.1.0", "jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "cacheable-lookup": "^6.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_8.0.3_1711762301522_0.8331585477897823", "host": "s3://npm-registry-packages"}}, "8.0.4": {"name": "socks-proxy-agent", "version": "8.0.4", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "socks-proxy-agent@8.0.4", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "9071dca17af95f483300316f4b063578fa0db08c", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.4.tgz", "fileCount": 8, "integrity": "sha512-GNAq/eg8Udq2x0eNiFkr9gRg5bA7PXEWagQdeRX4cPSG+X/8V38v637gim9bjFptMk1QWsCTr0ttrJEiXbNnRw==", "signatures": [{"sig": "MEUCIQCVGE9sWVNsvXQxNfnxkVYDLtr6au/T9keiMS8qqpx5xAIgAdieGvxZXNLSv9g8QcnXPcB16VC91E6Vi1CIjwLnaQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24807}, "main": "./dist/index.js", "_from": "file:socks-proxy-agent-8.0.4.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail test/test.ts", "build": "tsc", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/407e2a13e47683d55ba12268a88dc928/socks-proxy-agent-8.0.4.tgz", "_integrity": "sha512-GNAq/eg8Udq2x0eNiFkr9gRg5bA7PXEWagQdeRX4cPSG+X/8V38v637gim9bjFptMk1QWsCTr0ttrJEiXbNnRw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/socks-proxy-agent"}, "_npmVersion": "10.7.0", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"debug": "^4.3.4", "socks": "^2.8.3", "agent-base": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"dns2": "^2.1.0", "jest": "^29.5.0", "proxy": "2.2.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-retry": "^1.3.3", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "cacheable-lookup": "^6.1.0", "@types/async-retry": "^1.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/socks-proxy-agent_8.0.4_1719560029842_0.8412734543732021", "host": "s3://npm-registry-packages"}}, "8.0.5": {"name": "socks-proxy-agent", "version": "8.0.5", "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "main": "./dist/index.js", "types": "./dist/index.d.ts", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/socks-proxy-agent"}, "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}, "devDependencies": {"@types/async-retry": "^1.4.5", "@types/debug": "^4.1.7", "@types/dns2": "^2.0.3", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "async-retry": "^1.3.3", "cacheable-lookup": "^6.1.0", "dns2": "^2.1.0", "jest": "^29.5.0", "socksv5": "github:TooTallNate/socksv5#fix/dstSock-close-event", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "license": "MIT", "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail test/test.ts", "test-e2e": "jest --env node --verbose --bail test/e2e.test.ts", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "socks-proxy-agent@8.0.5", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-HehCEsotFqbPW9sJ8WVYB6UbmIMv7kUUORIF2Nncq4VQvBfNBLibW9YZR5dlYCSUhwcD628pRllm7n+E+YTzJw==", "_resolved": "/tmp/194f1676afcc1969e5abc1427b4edd77/socks-proxy-agent-8.0.5.tgz", "_from": "file:socks-proxy-agent-8.0.5.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-HehCEsotFqbPW9sJ8WVYB6UbmIMv7kUUORIF2Nncq4VQvBfNBLibW9YZR5dlYCSUhwcD628pRllm7n+E+YTzJw==", "shasum": "b9cdb4e7e998509d7659d689ce7697ac21645bee", "tarball": "https://registry.npmjs.org/socks-proxy-agent/-/socks-proxy-agent-8.0.5.tgz", "fileCount": 8, "unpackedSize": 25533, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID7UqPoFGr0msS0TXWbWJkZncREjzHi8Ta62uR8lvZcQAiEAyoc2dOi7QXzcFXMNBR65CTaFB1t4ZHXi7mHqkg0cO60="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/socks-proxy-agent_8.0.5_1733542331346_0.26803429943432455"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-11T23:28:25.950Z", "modified": "2024-12-07T03:32:11.779Z", "0.0.1": "2013-07-11T23:28:27.107Z", "0.0.2": "2013-07-24T21:06:33.133Z", "0.1.0": "2013-11-19T18:21:13.267Z", "0.1.1": "2014-04-09T23:42:58.167Z", "0.1.2": "2014-06-11T22:02:45.776Z", "1.0.0": "2015-02-12T05:23:55.521Z", "1.0.1": "2015-03-02T01:50:25.637Z", "1.0.2": "2015-07-01T18:45:43.347Z", "2.0.0": "2015-07-11T01:04:46.478Z", "2.1.0": "2017-05-25T00:47:19.210Z", "2.1.1": "2017-06-13T19:16:10.198Z", "3.0.0": "2017-06-13T20:26:22.204Z", "3.0.1": "2017-09-18T16:14:00.641Z", "4.0.0": "2018-03-29T08:11:58.344Z", "4.0.1": "2018-04-13T22:33:40.159Z", "4.0.2": "2019-03-09T03:21:03.523Z", "5.0.0": "2020-02-04T19:42:50.012Z", "5.0.1": "2021-06-21T19:53:54.663Z", "6.0.0": "2021-07-04T10:02:50.734Z", "6.1.0": "2021-09-15T14:38:16.706Z", "6.1.1": "2021-11-22T18:23:17.216Z", "6.2.0-beta.0": "2022-02-12T23:48:08.217Z", "6.2.0-beta.1": "2022-02-15T15:10:03.125Z", "6.2.0": "2022-04-17T23:55:08.299Z", "6.2.1": "2022-05-31T11:18:47.009Z", "7.0.0": "2022-05-31T14:16:45.078Z", "8.0.0": "2023-05-04T20:33:19.909Z", "8.0.1": "2023-05-05T22:04:13.129Z", "8.0.2": "2023-09-04T08:09:43.197Z", "8.0.3": "2024-03-30T01:31:41.666Z", "8.0.4": "2024-06-28T07:33:49.989Z", "8.0.5": "2024-12-07T03:32:11.601Z"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["agent", "http", "https", "proxy", "socks", "socks4", "socks4a", "socks5", "socks5h"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/socks-proxy-agent"}, "description": "A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "talm<PERSON>i", "email": "<EMAIL>"}, {"name": "Indospace.io", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "richardka<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><PERSON><EMAIL>"}, {"name": "jigu", "email": "<EMAIL>"}, {"name": "<PERSON>dez", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "8790386+dimita<PERSON><PERSON><PERSON>@users.noreply.github.com"}], "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}, {"name": "kikobeats", "email": "<EMAIL>"}], "readme": "socks-proxy-agent\n================\n### A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS\n\nThis module provides an `http.Agent` implementation that connects to a\nspecified SOCKS proxy server, and can be used with the built-in `http`\nand `https` modules.\n\nIt can also be used in conjunction with the `ws` module to establish a WebSocket\nconnection over a SOCKS proxy. See the \"Examples\" section below.\n\nExamples\n--------\n\n```ts\nimport https from 'https';\nimport { SocksProxyAgent } from 'socks-proxy-agent';\n\nconst agent = new SocksProxyAgent(\n\t'socks://your-name%40gmail.com:<EMAIL>'\n);\n\nhttps.get('https://ipinfo.io', { agent }, (res) => {\n\tconsole.log(res.headers);\n\tres.pipe(process.stdout);\n});\n```\n\n#### `ws` WebSocket connection example\n\n```ts\nimport WebSocket from 'ws';\nimport { SocksProxyAgent } from 'socks-proxy-agent';\n\nconst agent = new SocksProxyAgent(\n\t'socks://your-name%40gmail.com:<EMAIL>'\n);\n\nvar socket = new WebSocket('ws://echo.websocket.events', { agent });\n\nsocket.on('open', function () {\n\tconsole.log('\"open\" event!');\n\tsocket.send('hello world');\n});\n\nsocket.on('message', function (data, flags) {\n\tconsole.log('\"message\" event! %j %j', data, flags);\n\tsocket.close();\n});\n```", "readmeFilename": "README.md", "users": {"5long": true, "majgis": true, "keenwon": true, "tzq1011": true, "bangbang93": true}}