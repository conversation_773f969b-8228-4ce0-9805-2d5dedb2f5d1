{"_id": "http-proxy-agent", "_rev": "45-1ed11d659ad67f3206d8ecc41563f68c", "name": "http-proxy-agent", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "dist-tags": {"latest": "7.0.2"}, "versions": {"0.0.1": {"name": "http-proxy-agent", "version": "0.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "_id": "http-proxy-agent@0.0.1", "dist": {"shasum": "522142b817eae78bceb90eec48b081669ae241d8", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.0.1.tgz", "integrity": "sha512-p3I1IGKH+JwAwNbyM7iCTrDUfnoJ7NpweCZdNwisQw9iZA1P7o8ctxcGh9HwlerrJ95OPCqY1U4nGvotSAJb2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBd/LvRe5QgXzQggmkK7TiAfgiU/xt53HITdCtQZOooYAiEAm0l8elGaQCyFAplAVUWSMOPi8mbpEjO2X03Kjrq6ZfM="}]}, "_from": ".", "_npmVersion": "1.2.32", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "http-proxy-agent", "version": "0.0.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "_id": "http-proxy-agent@0.0.2", "dist": {"shasum": "c432a2b3ac25a25ee05de7c788698c022d4583b3", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.0.2.tgz", "integrity": "sha512-/0Ed8sI1inqfoYwYptAag2foA5DKlojylMfL3eIrIcQ1r+bPmJSKYLzUqI2F5C/GyW4+0ADwbJjJy+VuVBzaNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKWc08Vb4Kgpi4LwQkb9scDSeO+qykU8YT5kkcCcwmzAIhAKmiyNUsr9hFWzcQ2Fw0acGUcEUdkN8gqKTwD3pju8KT"}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "http-proxy-agent", "version": "0.1.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~0.0.1"}, "devDependencies": {"mocha": "~1.12.0"}, "_id": "http-proxy-agent@0.1.0", "dist": {"shasum": "a60927c4b9e82b1a9a343374857ca55b63760faf", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.1.0.tgz", "integrity": "sha512-u8H29tNnbXPHy8iD+1p98UG3ZQXVwdE32iiFaT1bKyFBRP/8l8qmjBcbLUlfblIHQ4f7Or2d7Wx/mCA0OMaf9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXme1n/f0RiDoJShxIq2F9RMl8je5fMEnUwWCJZBGVqAIhAJLq+rl/QGFhGz5T2o5exrOiT/8Mw3LjezkTjTWL6GXT"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "http-proxy-agent", "version": "0.2.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.0"}, "_id": "http-proxy-agent@0.2.0", "dist": {"shasum": "99fd25c67e4630fa7a9704c32bc68ac89081f01f", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.0.tgz", "integrity": "sha512-8iuoW3GyGi2VplIcssyVJWzgbfgFp8fjTzrrTXh78KWc3AlzeeTHc18//gZU5SuiklpCCoMJ74krtvM67XFWSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ0d5n7PDbRdY9ydEldVU+nOVxVjtczd/8oi2ETfzK5AIhAObPbPHbXQhjrO0hs596soN0dkVXxpC+6vfris1xkMEu"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "http-proxy-agent", "version": "0.2.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.0"}, "_id": "http-proxy-agent@0.2.1", "dist": {"shasum": "68ddbb7aa679e52f33ab3ba263560b4b0cc41a10", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.1.tgz", "integrity": "sha512-6sCDMkaC0w2bWPA/y1Etfnoz6ghwuX/+XiCsahery1naxoPBHFgLkE0upfPXo+iLH/QDnG45tAihWPqrUT2PxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvmp8U8x+GXsPtWpQqNhPxPpHtP2A5z0rh/gV+pxc9JAiBc8oRVNwUoG0gINxNb8YyI0lI+G2c3rXhxPYi5/FJDtQ=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "http-proxy-agent", "version": "0.2.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.0"}, "homepage": "https://github.com/TooTallNate/node-http-proxy-agent", "_id": "http-proxy-agent@0.2.2", "dist": {"shasum": "bcbe5c4357b3fa6f9b05c94315db36ea583e2447", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.2.tgz", "integrity": "sha512-hbcltiZemC8PRoN1cq4EzDHJEMyrSulBlgW0UshyWmDh/y5A6rgbPDGas8ngBweZa5jZB+W0tn0yt0GERQHMCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGGaR28r1nuPeB0CQ88PmwePGW0cr4s//AqH+Gy6LvQiAiB/4XK/C78GGepjHwAKQm4qEkTxEhzEg3yeeartIYV4jw=="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "http-proxy-agent", "version": "0.2.3", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.0"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.0"}, "homepage": "https://github.com/TooTallNate/node-http-proxy-agent", "_id": "http-proxy-agent@0.2.3", "dist": {"shasum": "09e42c875fe8c723b6999ec855cef71a4dfbf454", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.3.tgz", "integrity": "sha512-UEyaXruHAutwN+dq3r1bqjyOyLYlZbNwrAwmamXF2c9yO6QgnkNwGGm8ZMflt68GXTVjbfbW/dSYVq7cO8renQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICZW8fEyg0jlOuFMTYsGoc09uyTXz64gFsvqWQfw6jRAAiEA2wXo1zJEY+4sz/zvg3nz/aZGnZQfhvGXjODGd5afjAg="}]}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "http-proxy-agent", "version": "0.2.4", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.0", "debug": "~0.7.4"}, "devDependencies": {"mocha": "~1.12.0", "proxy": "~0.2.0"}, "homepage": "https://github.com/TooTallNate/node-http-proxy-agent", "_id": "http-proxy-agent@0.2.4", "dist": {"shasum": "4530c79459caf2708db680ac78ad51cb4685a5bc", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.4.tgz", "integrity": "sha512-IGsgFqXDjn7yEWhBluRWHIx0KUSTDdItcgDHXz6fNcrTd3/gZApcIThROv0/GWeQHI7lKlR5NfqLRjFssdXzrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRhXdvIvZq0SCyetgWpNNR5T6DUcxhQd79Vs+AEBVzewIhAPuPl68gL2J3mL/j2EdZCKmxXAKOTNCCNoPyw8ZHlIum"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.5": {"name": "http-proxy-agent", "version": "0.2.5", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.1", "debug": "~0.8.0"}, "devDependencies": {"mocha": "~1.18.2", "proxy": "~0.2.3"}, "homepage": "https://github.com/TooTallNate/node-http-proxy-agent", "_id": "http-proxy-agent@0.2.5", "dist": {"shasum": "4a8e8b5091cdc3edcf490488e0349f57869678fd", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.5.tgz", "integrity": "sha512-Eoe1SclPnZVWNSHTPspB/QW7FGIuMFLn3F3FjR+LD+HLWfXAHTu7QaPIwMHtiRKO7Sj0UbffK3xDU/Ae8BPd0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDT/edF3fWD4NfRfppV9vug2OM+azND7pr3Rwu/kbNO6QIgWjrfEOkVTmY/TX9+oCFOdZQc0g5lMaeckG+Qv5JQuIU="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}}, "0.2.6": {"name": "http-proxy-agent", "version": "0.2.6", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "~1.2.1", "debug": "~1.0.0"}, "devDependencies": {"mocha": "~1.18.2", "proxy": "~0.2.3"}, "homepage": "https://github.com/TooTallNate/node-http-proxy-agent", "_id": "http-proxy-agent@0.2.6", "_shasum": "d4a0a2350a75d4fb5102299e8f8c41f625873caa", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "d4a0a2350a75d4fb5102299e8f8c41f625873caa", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.6.tgz", "integrity": "sha512-vv+VXfRDIj1QFiqEVUaIeGpKI+yagm5tUaLX1GRTiAmm92Yro/acxiwwAJZfSLYY55GxwTrF47nPxE6kTLfMAQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGsIHBJe2fft/Rzf5F/8DkaAHPgf9xDZWCWSu0+LQSK2AiAJ4bScgwjmcYOgoWos8sUD4DNh8/Vy9fAvChIOs+Czow=="}]}, "directories": {}}, "0.2.7": {"name": "http-proxy-agent", "version": "0.2.7", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "~1.0.1", "extend": "3", "debug": "2"}, "devDependencies": {"mocha": "2", "proxy": "~0.2.3"}, "gitHead": "c77058f8476bfa1040f9918f78a5a37231e9f78c", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@0.2.7", "_shasum": "e17fda65f0902d952ce7921e62c7ff8862655a5e", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "e17fda65f0902d952ce7921e62c7ff8862655a5e", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-0.2.7.tgz", "integrity": "sha512-9W3grrlsrW2kRGNRbGkBNVFx4voQS1H1TxWR60MVHKQ+rw+kRtA9JXVGQiiDgYsp315Ex5HPk+3it4lBNyk4WA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA7eOT5XkEYlZM7KnrILZXFoNn7Eop0JJMWBy2QBK511AiB8WyE5tXythe8KU+Q2R17ykm2R9CY+g3/ecW+LWi8E9A=="}]}, "directories": {}}, "1.0.0": {"name": "http-proxy-agent", "version": "1.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "http-proxy-agent.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "2", "extend": "3", "debug": "2"}, "devDependencies": {"mocha": "2", "proxy": "~0.2.3"}, "gitHead": "c2a1e575ebf7226251b55194855e739ef319a1cb", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@1.0.0", "_shasum": "cc1ce38e453bf984a0f7702d2dd59c73d081284a", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.6", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "cc1ce38e453bf984a0f7702d2dd59c73d081284a", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-1.0.0.tgz", "integrity": "sha512-6YMslTZtuupu4irnNBi1bM6dG0UqHBHqObHQn3awavmNXe9CGkmw7KZ68EyAnJk3yBlLpbLwux5+bY1lneDFmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEkWQoN+xQIQD93L/1RdJNXC+wppGkgcGLiFBFQ7pf/4AiEAtRinX8+QOTtzbWjEtRIzWf64/tYtZAGN5RsC4PDpt18="}]}, "directories": {}}, "2.0.0": {"name": "http-proxy-agent", "version": "2.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "4", "debug": "2"}, "devDependencies": {"mocha": "3", "proxy": "~0.2.3"}, "gitHead": "8681658f207a30054c7f8e7da6a929bdd3047687", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@2.0.0", "_shasum": "46482a2f0523a4d6082551709f469cb3e4a85ff4", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.10.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"shasum": "46482a2f0523a4d6082551709f469cb3e4a85ff4", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-2.0.0.tgz", "integrity": "sha512-bhiiWyyhDnBtpu7TdA6SbfYB3rs3QaposYq0HXRz13EsuF4hXcC2O0n613lNZREZ9mV5QulhZ5R4NSbFz2nowg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1FkQocP0k8xoGceM+qPCQtZ+uYluFC7eRyOUHwQmSegIgI8/5gN+aBGs6UvNJI1Rc1PdyjhpoVEZnSVFLTLD+xWs="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent-2.0.0.tgz_1498585951606_0.6513557175640017"}, "directories": {}}, "2.1.0": {"name": "http-proxy-agent", "version": "2.1.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "4", "debug": "3.1.0"}, "devDependencies": {"mocha": "3", "proxy": "~0.2.3"}, "engines": {"node": ">= 4.5.0"}, "gitHead": "65307ac8fe4e6ce1a2685d21ec4affa4c2a0a30d", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@2.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.5.0", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qwHbBLV7WviBl0rQsOzH6o5lwyOIvwp/BdFnvVxXORldu5TmjFfjzBcWUWS5kWAZhmv+JtiDhSuQCp4sBfbIgg==", "shasum": "e4821beef5b2142a2026bd73926fe537631c5405", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-2.1.0.tgz", "fileCount": 8, "unpackedSize": 20854, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEfwepzuGIVHhvzBgrsDQUwWZQ1kXbfkiJOzyDbnX1phAiAkxxZyCk7KzZu2+JYL6aTjvyYe4fzqmZi6P34D8gf4UA=="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_2.1.0_1520120979376_0.982711299283987"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "http-proxy-agent", "version": "3.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./index.js", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "5", "debug": "4"}, "devDependencies": {"mocha": "3", "proxy": "1"}, "engines": {"node": ">= 6"}, "gitHead": "18b10c807fda63744b08c971f0dcce1620672fc5", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@3.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-uGuJaBWQWDQCJI5ip0d/VTYZW0nRrlLWXA4A7P1jrsa+f77rW2yXz315oBt6zGCF6l8C2tlMxY7ffULCj+5FhA==", "shasum": "598f42dc815949a11e2c6dbfdf24cd8a4c165327", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-3.0.0.tgz", "fileCount": 3, "unpackedSize": 7162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8APuCRA9TVsSAnZWagAA61AP/jAgKtiqf1dQZOFw28c0\n0Box15cE0xr8oR+PKdGrzKTZ2JKE3HFF3Ak8/USlxXlLIiQ/yQuY2uqH/O0Q\nlZKwOOOR3blKyYs+7ivEgVFCnxm1nesYiYgOYM+wchijHD5EyTKZe6FUpJeF\n1/3Qav4gFXGLthlrxr0QhBrtfMolZPok6Xl5PXKZG0IkyYb1JxNM42ixZB/j\nYfzR2Y5/s5Tkf/B77Gh6YjDrMC87QJB8FQnMyP42XFvpHe4TAzmKkKJSw/Bo\nzUBkFwfzAMzb/wGPZxxUD2D8iwBiSf7ahtE1/69LclzUZrTrLqbbuRLbXAhQ\nf4GUDr7Ks7ZPSDX3cpQnmHCOECo4P9nxl3YvEYDbq9sN8YjEpc9msHmTnvEp\nIQ0u8TV/fngsfKNjm/FZ2Z8meM2HYD2Qox2YA+o9mU29oGbHsiEI5j/J1HQd\n3RnOMGIU9n53sVwPE20fCkrsvWS6hvRDDO/xZ+hEDNgfXjSnofU6dxx6eBAE\nl5DJXJ9NtPeUDU8zLzyhoY+SaMKvYLWlcsHH4T3G2frZnc+YvGQh95I6YTYj\nIURKERALwWbTRyHxRZbqJVmIc99fXhzk4rakVSUF/RD1SRutB48m0kzvS5nU\n4YzevScOR16c1WkYCRetjsozi1IWYfBQoAh6TJNxKZg90gTta4EXTpBSAiND\nymjh\r\n=qCvJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGxyhLglNLibrCXTZhQSLsjV+rgFuDC9zWGnp0FcIZT2AiEAoLemEwxaIUp3mRK90jleLGb3hfW1HLYDrEGy9ACCoV4="}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_3.0.0_1576010734363_0.4646106516724142"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "http-proxy-agent", "version": "4.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "dist/index", "typings": "dist/index", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"agent-base": "6", "debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 6"}, "gitHead": "ef87c815ce99b9a00ca396662a40897ac8d86ebe", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@4.0.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-GX0FA6+IcDf4Oxc/FBWgYj4zKgo/DnZrksaG9jyuQLExs6xlX+uI5lcA8ymM3JaZTRrF/4s2UX19wJolyo7OBA==", "shasum": "6b74d332e1934a1107b97e97de4a00e267c790fe", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.0.tgz", "fileCount": 8, "unpackedSize": 15444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKgKBCRA9TVsSAnZWagAAspkQAInHRDnNGOiuo4DvVhJc\nl8l8W74JSqBN+AAYUHfPLBd/gECoMu19TWUBp/0u9JSb23JDCLyZElwQH1yZ\nS5jHqH6WKUpvH353oH99iUy0m3DO4N4Cvlk4mEgVguaxBQwtJgoAdq8GbJd5\njePao85/+D0Ybx+g/uHJYKTC8yCKAw7DJnPoVUgsLv85mQKiQUS7s0zt9PR2\nf9iZS+j869phg8pN5E6Qo7RtbYwk2LFbQ1jFbsPEGAoVIXkMmw2Oc2LNoobR\nYkljYDOJB2OFMFW+kj6vqO0vkbrOBDlkj/iWJGbYksnWvbGUDZr3Tc34HK78\nGPis/YzK+3dyzDSOqj4YUItuc5CpQZl0kpzp4nLHP6IRG2x89TSvljoEJyLI\nQe+QMBhKQXmS/q6knAkSiwY6wNdIdhZxFd3pogwK7/li7YHRqczB2GX+g9e/\ns+vUCMDBumwrQf6eus1AaaRD5CqNmQzxch/0bneKC75ZeoEXsgjH/nnL8O4h\n55oXQhkcqy+Lq49cqWpJ1zlpc8gIf1wFZNpsLf+KqKNA3T9laHFq/vnP8VAx\nZwix1FVYer1xWZUj4E9/RcyvfQOeIBgLJvpb/gzC/21zD4DKhbCwcLghncf+\nZhUwb2uL3VGm29oPoSd1dBm4FAClWpPixwQS/fTPeWasS/nOCWc0OwLVXYu6\nnQos\r\n=M8Sk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDi7A2VpVHgorXigU9pObGkRESDNCecKLr0WzyyaGJGAAIhAJ0dSDK8MZKC6tdQVJmfzTSBmGkwWEuKNZFhjdZoI6Py"}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_4.0.0_1579811456596_0.8577857415122601"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "http-proxy-agent", "version": "4.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/node": "^12.12.11", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 6"}, "gitHead": "d41fd2093cc0ef2314fd5a6fd42a5eb36d728c08", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@4.0.1", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==", "shasum": "8a8c8ef7f5932ccf953c296ca8291b95aa74aa3a", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "fileCount": 8, "unpackedSize": 17062, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeP0sqCRA9TVsSAnZWagAA4aIP/2vNJJ547LqmfvKYieV9\naauIFnOO7w3xD9RcG4c6R6Qr7Iq/OegqE/DRyrIWJkOHV/2RiVds/tG7csuq\ntaCcDGKTPa9P5jTjr//GtNRGcewWvnnTzBCOMQ9JJnYZHQwvSwgY6aCiwPEn\nIBnEOvMfgwpf6cEbPmfvyM22o7HvvSeXA53eAM/ix0jrYcnPk57elLPu+6il\nCPmjCEMjLrFZOUxPWxRIHtRTIiwsn7D48lWNM+HCkPdO4v+iuuICbmNaFW41\ndsRr3S/XFz8UqtIyBKU4OMSsLwc8GiLMPLZamBQgO+yZTxblpauRLV5RCX/F\nxZUyKr7rGu56w1GEEilSRkaH/y/nVdQ75pPNg1TfmsPEJYwxHx1am0sSdXZo\n1fZ78xpvHqG40D+vNdrKCrE7sjejJd6+6tSLBYWUwi4RCb72vkhOs1zHwJT8\nO2iqfkj8/mLVot5CNGqlTIQ4ovNUhI5LmjDZZZMVZWC2o2lldBe1VED0uIbz\npanEeCA6dcymH1FM4teuLRp9hUChL8SJSq2ObdP1g915LGTDrnDDCeoVPWeE\nFdiE0ctiL/HpGeVmtxG8VTPU2ahpGL/VBBeBP6VqnapkXw0y1u+SiJOb5mZH\nLB6V/F/vG3+ygxoarI/3XSRpi3VWZw5XhptTkPh+MUoiNwXBW1qORzdAW8uE\nwFMK\r\n=zGOA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNhGHlz/yaRmo2IfqU7VHGyLRqRqm3xf3d1wifUlFXagIhAPXG23vOOUi9mKTdh4TIGZrmhpbNXan/cb9THsvCXgqf"}]}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_4.0.1_1581206314112_0.7457055156138721"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "http-proxy-agent", "version": "5.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "test": "mocha", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-http-proxy-agent.git"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-http-proxy-agent/issues"}, "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "devDependencies": {"@types/debug": "4", "@types/node": "^12.19.2", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.2", "proxy": "1", "rimraf": "^3.0.0", "typescript": "^4.4.3"}, "engines": {"node": ">= 6"}, "gitHead": "5368fa899134a9b0d7d49cc6917c1eecabe7aeb0", "homepage": "https://github.com/TooTallNate/node-http-proxy-agent#readme", "_id": "http-proxy-agent@5.0.0", "_nodeVersion": "12.22.6", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==", "shasum": "5129800203520d434f142bc78ff3c170800f2b43", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz", "fileCount": 8, "unpackedSize": 17084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2ttKCRA9TVsSAnZWagAA7z4P/0eQaTAhdbMZa/SqMuLO\nW2ZkLGodNKTfKsY8NQmtnxOIkXTxAFoHAnxpXFQC+tn9ss1PsSXMbhF1Hw5g\nx4tC3Y1bpAia2e07/K8ZFKcczl77Fs0XWupa4D3CxTMISGrH39D5UzummxT1\n37gL2l6kU3oOOlG1o79LT2YWYy/a9dayVf0AY3fTO3kATRsExB6aILnLrlJU\nxOIZxev/o2sCjA3g52CW0P1+bXA2d0o5GzM2ua7g7PvzIut90crCSs/bCEz/\nLt8HXWsMZzb/h6uPhDy6WhAwOatJyOaOoJQtP6hQsm23brn6fcWgBwnKVEZ5\nxlEsyvzdjAH+oZLMt19UMK3Wsr0MFe62zb9xo3iZc3+TjKfUGxSOPFzyFNHl\npXzb40mNSvpnqJMSxRMZQ8C342EULSqvbXEQIbvZCtIzxD/PrsrihZUhn5Ib\n5sTD4XwF7NvNxqH5DfOxhZ1gvgrDKPdxPyLva5y8ZyQwTdutNucYZmDsKG6/\nr/ioYp04gOGiaTT5HXTG3Vwd1OQOsI8m7qsdf8wG6L684v1s8O1zff9Uy887\n9WhQvlJ23//J4C7qJLK+mXvzTJXjIL17nocf/viVgNjqDcbPcKN/NvgBjier\nZSL7cFIY8NKxTxVd0Nl2mPVUmpuzfkCWZQJ3fefBMkEEOfKs6IUBD2mgUmM6\nr78a\r\n=PORT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF3UsOQpVrBKtPPwLPfF0kjTmHG0jvMuM4YUqHMbWbsOAiA6kMpRZy6XfljC5TVMASmSZIbB3AEDYwAWjNUZeX20oQ=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_5.0.0_1632357349840_0.04943823300760486"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "http-proxy-agent", "version": "6.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.0", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "async-listen": "^2.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.0.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@6.0.0", "_integrity": "sha512-tmlr1P183cfJxzneOlOnQrv4hMkOBcTow1rjlT3tnaJbJGlz4xnZuOEAvHFfKqX5RhvIxkCABPQHo3tQEj3ouw==", "_resolved": "/tmp/88b9c7ca209573081add7f8537601542/http-proxy-agent-6.0.0.tgz", "_from": "file:http-proxy-agent-6.0.0.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-tmlr1P183cfJxzneOlOnQrv4hMkOBcTow1rjlT3tnaJbJGlz4xnZuOEAvHFfKqX5RhvIxkCABPQHo3tQEj3ouw==", "shasum": "be482ebcfea1d9c13e8a756821f813eb1cefca6d", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-6.0.0.tgz", "fileCount": 8, "unpackedSize": 20608, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8ZaokKmuGs9hCYyLGS+gpcQJgSpdpVK/ytLz3LUDPIAiEAv1pcYEZuLLRUCtzAS8vR5wZVQ1OXoWJ2LMCj9oedDI4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSrw/+IsZmWGQR6llxj1VAFjiuJX+1P8PLgBGetJKZCt3U0YJGQEz1\r\nThrvHw/ioQxSeI6bjAfr+XUAvylCwK6par6ZE+VJOCuIV0nRL/33CwlwyCEY\r\nGETmdVYjOse7qNUvz1MroRuRix8sE0ta0tPAja/clz4JU8D4scnimIGxBGXH\r\npeUhST6E56h7wtNo1aoYO146U2aTQfDRyneOwQWuXWCOvcTKAJmNlEcobWzo\r\nTuyK8K/ocevCI7JizH50e+RcZaQFivbYD81aiAjgB08sPvlH6b2E8S7xpLuc\r\nvaKhBeAOEWpTz656EcShyicrmjTmo+rrem3aS9aQYYUfbXhA7Bf30dTagY4w\r\nyeqC0DHDTwh52EXmRRwK0jvRSmZrtAbHuJUUDXlvhgAFuPEzsJtkIly9SGEX\r\nuPOmu+GfNiX8fQWMLUK7DtUzsPYQhLJ+/cJoxoS2MmfrLJo8dKzR97GDKhAd\r\n8wV5td2B8LZX4Ku7rItV+q3n11pap22xw4VZ8kmqJeXC01jKn3GS6nnn7g4R\r\n6LPGVdd8wuQncSJ6J/+oO/imKljX1MjyV46W2c9zibeDBUt+Jby6kvZY4b83\r\nxLhTTZ4uSVXeqJjSY2b7uZbn+O7eiegOJ1qH1JMjgIOe+XAS3ROJzIy1oOqK\r\niGwwgzD3Jl61WhEHHx49BkgjMDy8TNdIvEc=\r\n=kyW3\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_6.0.0_1683232399624_0.9371146031907209"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "http-proxy-agent", "version": "6.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.1", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^2.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.0.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@6.0.1", "_integrity": "sha512-rD8wrfJHbnVll9lkIpQH3vDbKON1Ssciggwydom/r89HLBXEqdMhL6wx7QF5WePDPSr0OdoztdXoojbrXadG5Q==", "_resolved": "/tmp/e970e79099b84e23b747b1020aeda2ec/http-proxy-agent-6.0.1.tgz", "_from": "file:http-proxy-agent-6.0.1.tgz", "_nodeVersion": "20.1.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-rD8wrfJHbnVll9lkIpQH3vDbKON1Ssciggwydom/r89HLBXEqdMhL6wx7QF5WePDPSr0OdoztdXoojbrXadG5Q==", "shasum": "c18aa52daa625a9bd00243f57252fd44e86ff40b", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-6.0.1.tgz", "fileCount": 8, "unpackedSize": 20608, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC4fCzLvbJcRULgsjkY83XBzTeFd86KIPpnQFgIdrHFjAiB0e5eJJtLJHU7htpsXmXFHecSo2iUCy1vJVYTc+5pXMA=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_6.0.1_1683324250313_0.373710115225349"}, "_hasShrinkwrap": false}, "6.1.0": {"name": "http-proxy-agent", "version": "6.1.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.0.2", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^2.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@6.1.0", "_integrity": "sha512-75t5ACHLOMnz/KsDAS4BdHx4J0sneT/HW+Sz070NR+n7RZ7SmYXYn2FXq6D0XwQid8hYgRVf6HZJrYuGzaEqtw==", "_resolved": "/tmp/b2f9de727bb07dd01ab1a2497a387623/http-proxy-agent-6.1.0.tgz", "_from": "file:http-proxy-agent-6.1.0.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-75t5ACHLOMnz/KsDAS4BdHx4J0sneT/HW+Sz070NR+n7RZ7SmYXYn2FXq6D0XwQid8hYgRVf6HZJrYuGzaEqtw==", "shasum": "9bbaebd7d5afc8fae04a5820932b487405b95978", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-6.1.0.tgz", "fileCount": 8, "unpackedSize": 23582, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpLVsdswsNsNP6xI50h/HO3yBOVCb3B6M4nlSuws1xCQIgdLgEM6KBd1V6qBPPjtvUs+vhEOVoeaDSUtrgs3NFpKs="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_6.1.0_1684438283925_0.260327118123723"}, "_hasShrinkwrap": false}, "6.1.1": {"name": "http-proxy-agent", "version": "6.1.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@6.1.1", "_integrity": "sha512-JRCz+4Whs6yrrIoIlrH+ZTmhrRwtMnmOHsHn8GFEn9O2sVfSE+DAZ3oyyGIKF8tjJEeSJmP89j7aTjVsSqsU0g==", "_resolved": "/tmp/a0047c74f042ce1e4df9a52f9c2ad128/http-proxy-agent-6.1.1.tgz", "_from": "file:http-proxy-agent-6.1.1.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-JRCz+4Whs6yrrIoIlrH+ZTmhrRwtMnmOHsHn8GFEn9O2sVfSE+DAZ3oyyGIKF8tjJEeSJmP89j7aTjVsSqsU0g==", "shasum": "dc04f1a84e09511740cfbd984a56f86cc42e4277", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-6.1.1.tgz", "fileCount": 8, "unpackedSize": 24870, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICz7YfFfpFWELmH5OVQdQRZ0xhLodUvWJJG84ddo/0OAAiBovSLknFpKllJSLwrn/mAIqptJmsKjWmDvz/FyVZs93Q=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_6.1.1_1684966199036_0.2878500002091551"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "http-proxy-agent", "version": "7.0.0", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@7.0.0", "_integrity": "sha512-+ZT+iBxVUQ1asugqnD6oWoRiS25AkjNfG085dKJGtGxkdwLQrMKU5wJr2bOOFAXzKcTuqq+7fZlTMgG3SRfIYQ==", "_resolved": "/tmp/d6c99fbc5a8751a29a1f9995257f4545/http-proxy-agent-7.0.0.tgz", "_from": "file:http-proxy-agent-7.0.0.tgz", "_nodeVersion": "20.2.0", "_npmVersion": "9.6.6", "dist": {"integrity": "sha512-+ZT+iBxVUQ1asugqnD6oWoRiS25AkjNfG085dKJGtGxkdwLQrMKU5wJr2bOOFAXzKcTuqq+7fZlTMgG3SRfIYQ==", "shasum": "e9096c5afd071a3fce56e6252bb321583c124673", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.0.tgz", "fileCount": 8, "unpackedSize": 24333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHOI+CxNtA64e3QFFPJtanm/1m81b07qZNuNAsOgbLZnAiBRemIAo3qrF/zzszRd3to+6w/8m8QDG70jhfjSklBTOg=="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_7.0.0_1684974179484_0.4744648603175172"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "http-proxy-agent", "version": "7.0.1", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "tsconfig": "0.0.0", "proxy": "2.1.1"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@7.0.1", "_integrity": "sha512-My1KCEPs6A0hb4qCVzYp8iEvA8j8YqcvXLZZH8C9OFuTYpYjHE7N2dtG3mRl1HMD4+VGXpF3XcDVcxGBT7yDZQ==", "_resolved": "/tmp/a2b91acc74bb84ec3e68edb6ef2bd13a/http-proxy-agent-7.0.1.tgz", "_from": "file:http-proxy-agent-7.0.1.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-My1KCEPs6A0hb4qCVzYp8iEvA8j8YqcvXLZZH8C9OFuTYpYjHE7N2dtG3mRl1HMD4+VGXpF3XcDVcxGBT7yDZQ==", "shasum": "f1c7df4bd6c30ba90f2c713fd4b60d3989d4b3d9", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.1.tgz", "fileCount": 8, "unpackedSize": 23393, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICYcHDTGEcOyYwpAq4PesusOrTbqb3DJiPqdHQnm2i1TAiEA/GV4ppTNqBOY5YoYEipNvhtWRHLsDg5JnsrKmXbiqjs="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_7.0.1_1707762278063_0.7135507732265147"}, "_hasShrinkwrap": false}, "7.0.2": {"name": "http-proxy-agent", "version": "7.0.2", "description": "An HTTP(s) proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "keywords": ["http", "proxy", "endpoint", "agent"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.1.1", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_id": "http-proxy-agent@7.0.2", "_integrity": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==", "_resolved": "/tmp/42ba4e26e53ed178ce9214ff542db94a/http-proxy-agent-7.0.2.tgz", "_from": "file:http-proxy-agent-7.0.2.tgz", "_nodeVersion": "20.11.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==", "shasum": "9a8b1f246866c028509486585f62b8f2c18c270e", "tarball": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "fileCount": 8, "unpackedSize": 23348, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBi+r32oZ7TAb8M+r8gRNyTMLi4SMGeNoZOcOaBcXWBJAiEAgEJSlR5tDp/tFXfa6gKR+uqSbvjxJ3CwHII/T6GqKuk="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/http-proxy-agent_7.0.2_1708024460368_0.681475820381463"}, "_hasShrinkwrap": false}}, "readme": "http-proxy-agent\n================\n### An HTTP(s) proxy `http.Agent` implementation for HTTP\n\nThis module provides an `http.Agent` implementation that connects to a specified\nHTTP or HTTPS proxy server, and can be used with the built-in `http` module.\n\n__Note:__ For HTTP proxy usage with the `https` module, check out\n[`https-proxy-agent`](../https-proxy-agent).\n\n\nExample\n-------\n\n```ts\nimport * as http from 'http';\nimport { HttpProxyAgent } from 'http-proxy-agent';\n\nconst agent = new HttpProxyAgent('http://************:3128');\n\nhttp.get('http://nodejs.org/api/', { agent }, (res) => {\n  console.log('\"response\" event!', res.headers);\n  res.pipe(process.stdout);\n});\n```\n\nAPI\n---\n\n### new HttpProxyAgent(proxy: string | URL, options?: HttpProxyAgentOptions)\n\nThe `HttpProxyAgent` class implements an `http.Agent` subclass that connects\nto the specified \"HTTP(s) proxy server\" in order to proxy HTTP requests.\n\nThe `proxy` argument is the URL for the proxy server.\n\nThe `options` argument accepts the usual `http.Agent` constructor options, and\nsome additional properties:\n\n * `headers` - Object containing additional headers to send to the proxy server\n   in each request. This may also be a function that returns a headers object.\n  \n   **NOTE:** If your proxy does not strip these headers from the request, they\n   will also be sent to the destination server.", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "time": {"modified": "2024-02-15T19:14:21.149Z", "created": "2013-07-09T20:36:57.810Z", "0.0.1": "2013-07-09T20:36:59.134Z", "0.0.2": "2013-07-11T21:05:54.783Z", "0.1.0": "2013-09-03T22:54:53.966Z", "0.2.0": "2013-09-17T00:17:14.847Z", "0.2.1": "2013-10-28T19:37:37.154Z", "0.2.2": "2013-11-16T20:39:12.871Z", "0.2.3": "2013-11-18T19:50:49.096Z", "0.2.4": "2014-01-13T04:32:27.115Z", "0.2.5": "2014-04-09T23:49:19.762Z", "0.2.6": "2014-06-11T21:52:51.873Z", "0.2.7": "2015-07-06T22:42:36.348Z", "1.0.0": "2015-07-11T01:02:50.657Z", "2.0.0": "2017-06-27T17:52:31.712Z", "2.1.0": "2018-03-03T23:49:40.465Z", "3.0.0": "2019-12-10T20:45:34.500Z", "4.0.0": "2020-01-23T20:30:56.749Z", "4.0.1": "2020-02-08T23:58:34.221Z", "5.0.0": "2021-09-23T00:35:50.066Z", "6.0.0": "2023-05-04T20:33:19.763Z", "6.0.1": "2023-05-05T22:04:10.483Z", "6.1.0": "2023-05-18T19:31:24.102Z", "6.1.1": "2023-05-24T22:09:59.193Z", "7.0.0": "2023-05-25T00:22:59.629Z", "7.0.1": "2024-02-12T18:24:38.246Z", "7.0.2": "2024-02-15T19:14:20.532Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/http-proxy-agent"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["http", "proxy", "endpoint", "agent"], "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"itskdk": true, "azevedo": true, "webbot": true, "tsxuehu": true, "ngpvnk": true, "faraoman": true}}