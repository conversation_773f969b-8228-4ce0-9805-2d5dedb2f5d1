{"_id": "env-paths", "_rev": "16-c15f53c35e5c9decdec98191d6c82242", "name": "env-paths", "description": "Get paths for storing things like data, config, cache, etc", "dist-tags": {"latest": "3.0.0"}, "versions": {"0.1.0": {"name": "env-paths", "version": "0.1.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "4cde607a3868e8a999c83a0769bcc01678c06420", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@0.1.0", "_shasum": "53711693afb96bf1c28c0fb48960832411c7e867", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "53711693afb96bf1c28c0fb48960832411c7e867", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-0.1.0.tgz", "integrity": "sha512-YHvRpOTh39+iux1sfLNDFamXCSNS0WdtviJrqL0v2XaZPzj7utXKXjC/Ny5XiVI3H9UCsFJvA1ofaSzxSfFpuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIETGqmaJaox4bc04JPFGFT+DViBcGae17PCs4pe9iPiAAiATumtqDriQIPM1qZofB/uQOgGp9is+y8QtXPMtT8JKFg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/env-paths-0.1.0.tgz_1466544838379_0.35237860563211143"}, "directories": {}}, "0.2.0": {"name": "env-paths", "version": "0.2.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "7c516d0755ab2eb9212f4327e047afe036b5bfd2", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@0.2.0", "_shasum": "47d5cd53befe41a00f3fe0ef5cfb67868b0d264f", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "47d5cd53befe41a00f3fe0ef5cfb67868b0d264f", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-0.2.0.tgz", "integrity": "sha512-MWsrwQLGlsn/I5PFkXijqfxru5n17Em1umn+vEcz+8SDfuZjR7wCfzRbNMt7RCQEa2mA37ou3fnxYdzkOH7QwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvdvfT3VAQSH6OIs5ehReAjx5a0JcPvZbyG5rKadE1HAiEAwJ0COJ3pMKNYMXSIJW8+mwWlXvRwkUYanlAMo5wkCxY="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/env-paths-0.2.0.tgz_1466764802600_0.5944404494948685"}, "directories": {}}, "0.3.0": {"name": "env-paths", "version": "0.3.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "dfddd546f95e4606b7373e5091ad0b2db24e99fc", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@0.3.0", "_shasum": "685b7fafe4cb9e05f06c98d50c0f820e2d19a3f4", "_from": ".", "_npmVersion": "2.15.5", "_nodeVersion": "4.4.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "685b7fafe4cb9e05f06c98d50c0f820e2d19a3f4", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-0.3.0.tgz", "integrity": "sha512-fiNkmGz+r80socpykKiMtw5H/iYEf6nNvGT9V43nY8LeNcaAOQIQVymjFGnaITbw3FXp3VL2jQpP3uSihXLopw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAW+H5kMnyuGEXpgSwQdpG85Jf7RW1NVc/Ws5VveP7uKAiAteNvwCgxb1ys6Qyt585joXRAqHy9cvBTrXJXQoeObNg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/env-paths-0.3.0.tgz_1467488954503_0.6383171891793609"}, "directories": {}}, "0.3.1": {"name": "env-paths", "version": "0.3.1", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "34f4cd8564a9b40d27b0c55f58e4575dcafa8388", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@0.3.1", "_shasum": "c30ccfcbc30c890943dc08a85582517ef00da463", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c30ccfcbc30c890943dc08a85582517ef00da463", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-0.3.1.tgz", "integrity": "sha512-6FQOivgwgDBwneYq/08gU0oPlR9PfuQHMZlK2k+SgKhJEl/bmSd6zHVZ27PfcAUIxXx+IMh65kT4XahouYclgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCydwauBwrkxelxTzsrbpZAyC4e+jeV6/Wf9tfNzWMHWwIgctSUK7i55iP7M9+oNfJGCoN8rQCkioyZFKk9+hbSgqQ="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/env-paths-0.3.1.tgz_1476763410131_0.81416224129498"}, "directories": {}}, "1.0.0": {"name": "env-paths", "version": "1.0.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "a17e0dd6f678fe472c30951a0d7ae4e73ae67a93", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@1.0.0", "_shasum": "4168133b42bb05c38a35b1ae4397c8298ab369e0", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4168133b42bb05c38a35b1ae4397c8298ab369e0", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-1.0.0.tgz", "integrity": "sha512-+6r/UAzikJWJPcQZpBQS+bVmjAMz2BkDP/N4n2Uz1zz8lyw1IHWUeVdh/85gs0dp5A+z76LOQhCZkR6F88mlUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9aRxAOclAI1frIFjZVVbC1zqNUDhgiVtYZraLexPhxQIgIbsXLD4USH+rmnGhfTzXoh362j+/mNr7N89ab2ovRao="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/env-paths-1.0.0.tgz_1484020726148_0.9191886691842228"}, "directories": {}}, "2.0.0": {"name": "env-paths", "version": "2.0.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "gitHead": "4fdcf1ed964e2ae08ad0c5afe53fe83a0b9eb668", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-13VpSqOO91W3MskXxWJ8x+Y33RKaPT53/HviPp8QcMmEbAJaPFEm8BmMpxCHroJ5rGADqr34Zl6zosBt3F+xAA==", "shasum": "5a71723f3df7ca98113541f6fa972184f2c9611d", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-2.0.0.tgz", "fileCount": 4, "unpackedSize": 5029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb389ZCRA9TVsSAnZWagAAr8wQAIAeHBBCUj73J71LZDRI\ni8QFZKlpziQPw+dNBpnKB7FxqBc3RdX4tbxqEixXBrK9fqg5JAMbYwatklbP\n0OdXAK+nqL19HYK+yWwLSBapQ/3wffVYv6QwD0p3qv0bGXPUodlUpVeKWwob\nMDP3J0qvcjfkcPx38uJ4Vr3kwPNwhl6CUtYTfRbQKJBqwYI2P66jxYLKTS2p\nFyXbek6eRtBNmG7djkSWpTVIshp4oXBJ/G3WHrwYZ1A4N8MONZtGHwzbj7CU\np/wcx7J+RxOlnGRSqBShezSuTvgA1RtZSlVkDSo7phbezwq0xKdlqD6tugz2\nBeqJFGxdAXOFBCYJCIffe272O0zU6DRet0lH4VzccAGg3OM5qlMXRyhN2Bgr\nTuZxQOIpfpb1Fz4XNXZXWagNB3oe/Vle8ocoqp6QDr+sY/z7SRktdXirYPCT\nGfmgmAfI9QsP1vE1w9B7dk3huCUetaYxGp3tAmtqq4innOdKAOlqoP/GPDbN\n33Qkw9Z9Fjg80sQJIDfyVWz7kRphy2/5xRPePoKsB72rNWT9cxWgRlVBclTl\nWuZ1wURBat8TPIk5r2yENe0+sGWsNvbdgmM+2y3RDoeAEOv2LjtyXD5OuR1T\nOmyKKPgiakXwCtLmNyEgaVRIL10dt7De7Hwp9qXDjw+3qvKT6O4S5+5pVW4x\nkl2g\r\n=gb2w\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHABCu8kh8v0dbyB5SVSOFzVs+Vg1l25Zv6uX2ITU0qMAiEAmmDKVThH6vhR0+GUqsO7oc1YFpU87d2YGajO3d64/2o="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/env-paths_2.0.0_1541394264663_0.8773571203858879"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "env-paths", "version": "2.1.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "^1.2.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "f2216b01767941174a3720f3537b78380c68d86a", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@2.1.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UP0khs8YtfSoTOMiDsZMuH2nFXAF3dk2JKfkWZd5j3cRDwsUDPIaV7brRsQmclCTi/h7po/zVs+w10FgJTf9ag==", "shasum": "aa0554965e8d109dd6759b90c1bd3d1cdd76d57b", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-2.1.0.tgz", "fileCount": 5, "unpackedSize": 6066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfVIZCRA9TVsSAnZWagAAYEwP/0fyd0XS2HHwSWv+9hGS\nNCF4BxAJJhHcjr/NM5quohuK7SYr7eqP8FPbf94XZX37WlGjLwywQg7dZ0NY\nQGIZSavCIDrhzSs0x73vnRr3z8OA4crVHGKi/LQExARkQze7NyDYNsR1xos5\n2mIFCntP3FcaOrORk6hXKIOt2VnE1KTznRF9X4hczVE6R5oOmOsMOlW1Dq9p\n8icNhjGEJ3xe1Mg5y0iZjvR3VXOKrYopm35qlbp4S2f74lYT7zF7tG4rPXjj\nC+PrkB5qtHnEWg8RLW2+Efg+R6YxK8mT4EdzQBSBGg0m1OYWzDYcZYcVimMn\nlmfAAq6FvT5GjxYNG0HeBucsO5L3AspTiA6xMxepvzqKJsfQ547kQUtCClIU\nSGJBud334X8/P51bjSQJTIAoJQ1xq3P0euIZEqs1ELdP7XsL7tK61colQpw4\nL5ovvMWxkeE7Cp+yK9s+4QOKWdB6cy/1fvMKWs2AFnbDS2J7ZQBPvIbCZmVq\n8+4HS89SblH+NzolvpD2AD8K7HU3HfV0mNQc/pOwrmT1tB8ax/aQ0wlvPZ+6\nNthsc6LCW31sKeuxjz9qFLIh4M32xdXa6WBmjTgDxzOfGd01HFv50Inl9kEN\nPqDI1OcAL/I548ebaYB6xwBJczeSfvlR6FZMz1b651x2Mno2235McdeWo+dA\nfZvl\r\n=SGrE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCld6ymFroRsFl01/w3g/ebdzXHV4tA2ygz/No8SbVRSgIgC31baE26XAJVwOxButRzTdIZULfgCBR5w6cbBeSf7GQ="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/env-paths_2.1.0_1551716888941_0.6816574351417073"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "env-paths", "version": "2.2.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "d771ce6574cd7e4c449c40fd1cf0078f8b9d07a9", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@2.2.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6u0VYSCo/OW6IoD5WCLLy9JUGARbamfSavcNXry/eu8aHVFei6CD3Sw+VGX5alea1i9pgPHW0mbu6Xj0uBh7gA==", "shasum": "cdca557dc009152917d6166e2febe1f039685e43", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.0.tgz", "fileCount": 5, "unpackedSize": 6607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcodN+CRA9TVsSAnZWagAA4M8QAI1qz93V/vqAyaj+oQ1x\nJmgWDk1eGx7NpMlECCQ+SdEOWQoFhcAOr+AUTHgqRRuC7AdAGiVD0n/c5rMn\nzp/A5/hqSpn+ytneTlYSZnPdy0MmQQsqVjBm8HkgDkxVk35vQPKZ1hCIJqJz\nnu7RuftdbqtFnDCeIFlqMmw2OA0HFpXHjJOQ6WsAcyqUnkt6tmx4pb35bT+O\nO6wGWKB8MY7VegGlUmeWgJKsOfcdQXwgQG7LhdW2BwJWFXwEjNT2o/xBOpPG\nnKQOThu+q+L/uMmZvy3DvispB05repiKwrukAmmqMRNyTvVd3X7zV5ayy+N9\n8Nsb+cJi+s5hiKFa/YbG66mZwNUof2xHjf0v/JueA37so0Bq0N6vxolIPTbY\nGetxnWWjSYPP3hN7WZNPQC12Nmy9zwtQk7hYzZe35uNEp5/qyeLPg+lob0eE\ntWkKMpfVq9uYxO4kkj1n0UsZKeSEsauqB/HQmGmJVA/wKiUIEVN2OByvsVuB\n8DMO7TqsFq5nWR+6rQjiXVSWWqfFOOKGvkvwHC4efEfib35tbf4tsOlqtfTa\nO76R1KIQdd43R5XYbES2F1LRpSF3pyp1rwhz0qOXaT0slGwtAdeDnB/t7Bvv\nW6FmcS0VcaVgvKWiYwwvAAeut2An43keDkUOQ3gwn1MS+ONVDd96hA8C1LFt\nhbha\r\n=gvEA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwQpNUkHETj7fJN94oTKskuKYM4iqbD8zg6Y4rkFuNpQIgFGWtRgfe6iIpVpSLA0E0UtJioHbrqX/7k9oCWYRiqjU="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/env-paths_2.2.0_1554109309508_0.8089674451489355"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "env-paths", "version": "2.2.1", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "62d4ec8cd42f1a419e00856fab949ca8286773f6", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@2.2.1", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==", "shasum": "420399d416ce1fbe9bc0a07c62fa68d67fd0f8f2", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz", "fileCount": 5, "unpackedSize": 10163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRhkLCRA9TVsSAnZWagAAzIQP/2Nmbs5q/06VvU/UEGn8\nZxcOoMA7tGLWbSvcUSAIfmtu74DSqoEyMJJLKLsAKG6CeVxf/wvv+IW98luL\nfLSPrIcUbEKEECKnRvgC1/lJx0H7x8daWFhEn5V/qT3/Tq+YQxxxxV6eBgE2\nSg2Cm0YFYj+4fv13Et/N0jr5lX5RjUiBRwYKisM0X/wTntBmrmWuJ+r3G9yn\nfpButg2CG8FhqS8PIvxFMjpfl5Kzx+4fskZlbzWXjl437YVMnOddVfE4d+4b\ncsl9IiRVfRQun7Mma2krUdsyv7/Cv7MeJcktjgUjOK1IfSsR6DyAw4oJtMMS\nbfOYjv6tRPZA4UPO/c+ZSNMkIvcueZLH+1Pufv2tQ99CgY5ZYz0zWOn7SOQ9\n1NA3URMsoeHs1UDbtQmk5ZPHV3/GuXUBDBAwD5Y1gPnUP44hdsguV6VONJEG\nFjsLltGVjSDdvDgxTaSorSKNk3uqiLxSquX/D0qtMy71mcqJ2dIuYhcpgsFE\n/9w0sQkCKDlaCBCr4dQ9ziWnCr38Jut+rWKQkSXYsWdlFnhBYZlPDYhEl2Bb\ng28SPiHvPLoL7soXHz372VZpl36Kc8kDQHOpiz7UACKqYJifgboyC3+o/LzE\nWb6yHm9Tg04MUrq2MisG3VlCZjc5DuHDSwNFpaCUyxv/NX5n6mEDOxP+FW9e\nJoEn\r\n=qhG9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC58s8SpFZdII3DGJV28/aC5XN31PJjPaBnax5dqZPg3AIgC8/GVwDk0mrXqqsjqNe7O6gSNDqVOFt5XJ/bQPVB6lI="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/env-paths_2.2.1_1615206666547_0.899181881445303"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "env-paths", "version": "3.0.0", "description": "Get paths for storing things like data, config, cache, etc", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "f1729272888f45f6584e74dc4d0af3aecba9e7e8", "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "_id": "env-paths@3.0.0", "_nodeVersion": "14.17.5", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-dtJUTepzMW3Lm/NPxRf3wP4642UWhjL2sQxc+ym2YMj1m/H2zDNQOlezafzkHwn6sMstjHTwG6iQQsctDW/b1A==", "shasum": "2f1e89c2f6dbd3408e1b1711dd82d62e317f58da", "tarball": "https://registry.npmjs.org/env-paths/-/env-paths-3.0.0.tgz", "fileCount": 5, "unpackedSize": 9815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKUgKCRA9TVsSAnZWagAAgqgQAIk5pYJ68JuAFv0mbV4b\n/oNXv4IBA2eEXAqDk4vno9RdwAUEMyD07JThmn/J5jR7FKPImZj6Qh31RRXk\nPdVI8Hgvtt+kdXp/nDhEGjKQlB3xNIyP/QaYSoTd3ZcIITYewvOko0+bZSzo\nX3csXgvBrY4TK5d+NaOIT58zM7DYgIspuC93S/BZCAaPIJhjz+VxwxTYrNET\nvwzzS1ViN72eOIBasxUo0FI6uCg1JnSY1AEJIhkAr1d0Ro/FtdmeqcVcG1Ze\noUUAP4CXFpiptCGhHwpVZg05bOH/pqeNNxbekTtVRmgxUNKm9zmA2S+LfGtu\njGkS9vRLcFNhKSUly5LLDno5K6vQRMZVBVJJ7184VbXohPElGy4kFjf10SZV\nb5+SORQR1j4FHmsllUNURGA/H9eUbzpdaGV9bX+572xTvP53EB6WxVpciLvB\nzexeVL8p6Dcf8XSK6EVfBP18/GgEOQYMYUsd3L6W7XzZFeLTk1cEQ7A0V0SY\nLfdZTOx2uC1WKwaen9ZtJJ4cDojkfkWM9OPwEEGarh3bqmvCgOPRAxADmX1p\nNuqUsPGBlyY6TOeJWBS4NM7SdVNIAIvg7yO68rIDseFbL4n+HFwhJr4eAsjU\nSQMVNPA1EwPd72VvFTx0qUEsEHHnM0cc+Rt9MWOWLqoC6FhWCPz/GS4h5Fyf\nGM4V\r\n=2ZH6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIChqNuhVAeCfpjgoM9BXlxu8lvgnweJgd66MlUdMvp9oAiEAx4bbJStW8xnzj7R1JnNlu+VpEaHdDPL9UumA29JKcDk="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/env-paths_3.0.0_1630095370252_0.24084207963657178"}, "_hasShrinkwrap": false}}, "readme": "# env-paths\n\n> Get paths for storing things like data, config, cache, etc\n\nUses the correct OS-specific paths. Most developers get this wrong.\n\n## Install\n\n```\n$ npm install env-paths\n```\n\n## Usage\n\n```js\nimport envPaths from 'env-paths';\n\nconst paths = envPaths('MyApp');\n\npaths.data;\n//=> '/home/<USER>/.local/share/MyApp-nodejs'\n\npaths.config\n//=> '/home/<USER>/.config/MyApp-nodejs'\n```\n\n## API\n\n### paths = envPaths(name, options?)\n\nNote: It only generates the path strings. It doesn't create the directories for you. You could use [`make-dir`](https://github.com/sindresorhus/make-dir) to create the directories.\n\n#### name\n\nType: `string`\n\nThe name of your project. Used to generate the paths.\n\n#### options\n\nType: `object`\n\n##### suffix\n\nType: `string`\\\nDefault: `'nodejs'`\n\n**Don't use this option unless you really have to!**\n\nSuffix appended to the project name to avoid name conflicts with native\napps. Pass an empty string to disable it.\n\n### paths.data\n\nDirectory for data files.\n\nExample locations (with the default `nodejs` [suffix](#suffix)):\n\n- macOS: `~/Library/Application Support/MyApp-nodejs`\n- Windows: `%LOCALAPPDATA%\\MyApp-nodejs\\Data` (for example, `C:\\Users\\<USER>\\AppData\\Local\\MyApp-nodejs\\Data`)\n- Linux: `~/.local/share/MyApp-nodejs` (or `$XDG_DATA_HOME/MyApp-nodejs`)\n\n### paths.config\n\nDirectory for config files.\n\nExample locations (with the default `nodejs` [suffix](#suffix)):\n\n- macOS: `~/Library/Preferences/MyApp-nodejs`\n- Windows: `%APPDATA%\\MyApp-nodejs\\Config` (for example, `C:\\Users\\<USER>\\AppData\\Roaming\\MyApp-nodejs\\Config`)\n- Linux: `~/.config/MyApp-nodejs` (or `$XDG_CONFIG_HOME/MyApp-nodejs`)\n\n### paths.cache\n\nDirectory for non-essential data files.\n\nExample locations (with the default `nodejs` [suffix](#suffix)):\n\n- macOS: `~/Library/Caches/MyApp-nodejs`\n- Windows: `%LOCALAPPDATA%\\MyApp-nodejs\\Cache` (for example, `C:\\Users\\<USER>\\AppData\\Local\\MyApp-nodejs\\Cache`)\n- Linux: `~/.cache/MyApp-nodejs` (or `$XDG_CACHE_HOME/MyApp-nodejs`)\n\n### paths.log\n\nDirectory for log files.\n\nExample locations (with the default `nodejs` [suffix](#suffix)):\n\n- macOS: `~/Library/Logs/MyApp-nodejs`\n- Windows: `%LOCALAPPDATA%\\MyApp-nodejs\\Log` (for example, `C:\\Users\\<USER>\\AppData\\Local\\MyApp-nodejs\\Log`)\n- Linux: `~/.local/state/MyApp-nodejs` (or `$XDG_STATE_HOME/MyApp-nodejs`)\n\n### paths.temp\n\nDirectory for temporary files.\n\nExample locations (with the default `nodejs` [suffix](#suffix)):\n\n- macOS: `/var/folders/jf/f2twvvvs5jl_m49tf034ffpw0000gn/T/MyApp-nodejs`\n- Windows: `%LOCALAPPDATA%\\Temp\\MyApp-nodejs` (for example, `C:\\Users\\<USER>\\AppData\\Local\\Temp\\MyApp-nodejs`)\n- Linux: `/tmp/USERNAME/MyApp-nodejs`\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-env-paths?utm_source=npm-env-paths&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-03-04T17:06:22.172Z", "created": "2016-06-21T21:34:01.070Z", "0.1.0": "2016-06-21T21:34:01.070Z", "0.2.0": "2016-06-24T10:40:04.936Z", "0.3.0": "2016-07-02T19:49:16.706Z", "0.3.1": "2016-10-18T04:03:30.370Z", "1.0.0": "2017-01-10T03:58:46.365Z", "2.0.0": "2018-11-05T05:04:24.814Z", "2.1.0": "2019-03-04T16:28:09.051Z", "2.2.0": "2019-04-01T09:01:49.706Z", "2.2.1": "2021-03-08T12:31:06.769Z", "3.0.0": "2021-08-27T20:16:10.424Z"}, "homepage": "https://github.com/sindresorhus/env-paths#readme", "keywords": ["common", "user", "paths", "env", "environment", "directory", "dir", "appdir", "path", "data", "config", "cache", "logs", "temp", "linux", "unix"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/env-paths.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/env-paths/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"rocket0191": true, "davidbwaters": true, "flumpus-dev": true}}