{"_id": "pac-proxy-agent", "_rev": "35-edd76c6c8566db36a9a049ea90422b39", "name": "pac-proxy-agent", "dist-tags": {"latest": "7.2.0"}, "versions": {"0.0.1": {"name": "pac-proxy-agent", "version": "0.0.1", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@0.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "1eda4595bb8e614e834fe13415da1da0d5cbdb82", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-0.0.1.tgz", "integrity": "sha512-rzpiJJ78wwJ2/ZjSsz1r0dr4CRKQOD5eI3wK/X9HVLUNf8vSr2gv5QR0jSz9nS2G8eCLUZ88RSQf4vijuSMr5w==", "signatures": [{"sig": "MEQCIElgcawsmjCe/2mmm1dsz/O4NCyv6lYD5iTwWvf7Im4gAiBdnaJND2ljTdYOBV5H1jXb6c8NqDxZNeaC7HVsvg0Yaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "dependencies": {"debug": "~0.7.4", "extend": "~1.2.1", "get-uri": "~0.1.0", "agent-base": "~1.0.1", "proxy-agent": "~1.0.0", "pac-resolver": "~0.0.2", "stream-to-array": "~1.0.0"}, "devDependencies": {"mocha": "~1.16.2"}}, "0.0.2": {"name": "pac-proxy-agent", "version": "0.0.2", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@0.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "a3517a1695b7fe4a6a0b2d9b966015e23a6acf13", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-0.0.2.tgz", "integrity": "sha512-oK2loP3O+M8gyVka2NRs6KhTLvPS0X/5mFhY6+rpCqgiN6O1pSmuqhGNAA8cO+AyLPWuwm8dCcU039DL8YkGpw==", "signatures": [{"sig": "MEQCIEslziJRfI2lbCAgYl0CQ3wGwfuc4vSmR1sQzi2Giq9oAiB35U6WW+DAnb3tTsepmUAKJf4OyJwM1PCWvQSlWKiMPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "dependencies": {"debug": "~0.7.4", "extend": "~1.2.1", "get-uri": "~0.1.0", "agent-base": "~1.0.1", "proxy-agent": "1", "pac-resolver": "~0.0.2", "stream-to-array": "~1.0.0"}, "devDependencies": {"mocha": "~1.16.2"}}, "0.1.0": {"name": "pac-proxy-agent", "version": "0.1.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@0.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "f9162c257e0ebac129d45868ea6545693a0495df", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-0.1.0.tgz", "integrity": "sha512-pu8tiHcGYg8oPcuFnXbzNCRJ/ZaXeS52wSyrgm7YN8Z6COoWVjgmppVaW/1GQeTOPwCuTlj2ZdiYDsOSfMUgOQ==", "signatures": [{"sig": "MEUCIF51R8RgQBkdJNoI+5D0mOq1J9KFx7fYyj7aEr73bOkyAiEAncfsVMgHzJGJoOaLyR22v6WzKx6UYTfMP6grRtPDYes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "dependencies": {"debug": "~0.7.4", "extend": "~1.2.1", "get-uri": "~0.1.0", "agent-base": "~1.0.1", "proxy-agent": "1", "pac-resolver": "~1.1.0", "stream-to-array": "~1.0.0"}, "devDependencies": {"mocha": "~1.16.2"}}, "0.1.1": {"name": "pac-proxy-agent", "version": "0.1.1", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@0.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "82f031a9459f2e65086c92b79dfcbbd47680f964", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-0.1.1.tgz", "integrity": "sha512-J4uw3Kn5tmiHr+ZAoY3ZxsQcNAoYc8phIWxfbz8TaSgzBycBuPSMhFk9aUuECRSTCA27cX+mv+mBHPk/WgnB5Q==", "signatures": [{"sig": "MEUCIQDBScD/GseH78mu1pWOfOGRWw5vancVfvImjhHTOobuJgIgTi0RaxB/Wf5cF2CXMYnpe+I9MKo0fEh0vtcyX104vjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "dependencies": {"debug": "~0.7.4", "extend": "~1.2.1", "get-uri": "~0.1.0", "agent-base": "~1.0.1", "proxy-agent": "1", "pac-resolver": "~1.1.0", "stream-to-array": "~1.0.0"}, "devDependencies": {"mocha": "~1.16.2"}}, "0.1.2": {"name": "pac-proxy-agent", "version": "0.1.2", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@0.1.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "a1410f139c17872e1dded5c99e3bdff860f3537c", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-0.1.2.tgz", "integrity": "sha512-dFfYsHJP8685HaRm1ODZxpRI++USxCYSgoLcfGnFpO4ZBajoi7GN1yoW56JSjUFFTHpVz/xiNpewPF9Qq5wR/A==", "signatures": [{"sig": "MEUCIQCXTq7PhcWIkYgRqLksi7a1XcENqXXG7NoErQ+aUmq9DAIgQ7UR9NAWekYJFo4Q/H7OKpqQkNmWy7aCt9D0wi+us3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "dependencies": {"debug": "~0.8.0", "extend": "~1.2.1", "get-uri": "~0.1.0", "agent-base": "~1.0.1", "proxy-agent": "1", "pac-resolver": "~1.2.1", "stream-to-array": "~1.0.0"}, "devDependencies": {"mocha": "~1.16.2"}}, "0.2.0": {"name": "pac-proxy-agent", "version": "0.2.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@0.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "ad902909d92f4fe7cc2e5f59f5bf5061bcfa71b2", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-0.2.0.tgz", "integrity": "sha512-n7a4CFNSiKfr0Kr2XbJxUEQOab5JMJk3si0SNEjKdKAorg15dW4PZWpKZ+J5rr0S91TbuyLnl37vRS0zU7K4cg==", "signatures": [{"sig": "MEUCIQDq5cZl+FfraagUI+aWg/t/rCGUD0rRVrn8Xda4LtacfQIgPt0hGIwQx2CRvR83NwNIWzM9NyO+nMwRVuMXiiSxqRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ad902909d92f4fe7cc2e5f59f5bf5061bcfa71b2", "gitHead": "93e5b1b1edf73e43d12e8a9c3845d928a0539440", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "2.1.3", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "0.10.32", "dependencies": {"debug": "2", "extend": "~1.2.1", "get-uri": "~0.1.0", "agent-base": "~1.0.1", "proxy-agent": "1", "pac-resolver": "~1.2.1", "stream-to-array": "~1.0.0"}, "devDependencies": {"mocha": "~1.16.2"}}, "1.0.0": {"name": "pac-proxy-agent", "version": "1.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@1.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "dcd5b746581367430a236e88eacfd4e5b8d068a5", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-1.0.0.tgz", "integrity": "sha512-BNVUY4KdGN2h9hYgv/olKh7VSApcYy70V0jIIOy+qofipZi3f4mDsyoK8NemCkSCZdNjEgaDkVtGK1IQNLEhvQ==", "signatures": [{"sig": "MEUCIHEnjnLQb4K8/9aH7pd8r3T/dCvmRS+9sJIJp3aJPLeiAiEAmnEFTLx7wfPN9pjl86SjQdj7s34b47Aho/Tnea7A0mo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "dcd5b746581367430a236e88eacfd4e5b8d068a5", "gitHead": "19d7d99027cd78375dbc60762d0565f7596eec20", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"debug": "2", "extend": "3", "get-uri": "1", "agent-base": "2", "pac-resolver": "~1.2.1", "http-proxy-agent": "1", "stream-to-buffer": "0.1.0", "https-proxy-agent": "1", "socks-proxy-agent": "2"}, "devDependencies": {"mocha": "2", "proxy": "0.2.3", "socksv5": "0.0.6"}}, "1.1.0": {"name": "pac-proxy-agent", "version": "1.1.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@1.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "34a385dfdf61d2f0ecace08858c745d3e791fd4d", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-1.1.0.tgz", "integrity": "sha512-QBELCWyLYPgE2Gj+4wUEiMscHrQ8nRPBzYItQNOHWavwBt25ohZHQC4qnd5IszdVVrFbLsQ+dPkm6eqdjJAmwQ==", "signatures": [{"sig": "MEUCIQCP0BVgZgnuudf8hwLw/awLC3ZpkFz1Ju8t/7+9SBZgHwIgOdaGJHrAEzzPN6DuksH/cGBy9CcVuXjR2BwesM/2xao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "f6a8158b49ec950b0ed7bbf2d3b8884eb1fb1ac1", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"debug": "2", "extend": "3", "get-uri": "2", "raw-body": "2", "agent-base": "2", "pac-resolver": "~2.0.0", "http-proxy-agent": "1", "https-proxy-agent": "1", "socks-proxy-agent": "2"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "0.2.3", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent-1.1.0.tgz_1497237608122_0.9256804746109992", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "pac-proxy-agent", "version": "2.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@2.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "beb17cd2b06a20b379d57e1b2e2c29be0dfe5f9a", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-2.0.0.tgz", "integrity": "sha512-t57UiJpi5mFLTvjheC1SNSwIhml3+ElNOj69iRrydtQXZJr8VIFYSDtyPi/3ZysA62kD2dmww6pDlzk0VaONZg==", "signatures": [{"sig": "MEUCIQCwSJ8sKFlByE/Mc12sPQAn37slH9U0u0IciaXSp/DybQIgD9N+7d2vrCnIcm1Kf9969kq7D6Ld9YBvFN6nrYw3+Us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "e677df6559120a9e3cef7a20ef71ccdf1b425536", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"debug": "^2.6.8", "get-uri": "^2.0.0", "raw-body": "^2.2.0", "agent-base": "^2.1.1", "pac-resolver": "^3.0.0", "http-proxy-agent": "^1.0.0", "https-proxy-agent": "^1.0.0", "socks-proxy-agent": "^3.0.0"}, "devDependencies": {"mocha": "^3.4.2", "proxy": "0.2.3", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent-2.0.0.tgz_1497386330878_0.12291873176582158", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "pac-proxy-agent", "version": "2.0.1", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@2.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "d8fc332669883e0c89e5505acc29ccc4ad825819", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-AH9jgQ2yKjGTqFmbGArwNQfqI8YSRzeaBI6eaF85TOHFh4frYEaUuj5Zmv5b63hh4c2IpW2oKijeeA2QE3StBQ==", "signatures": [{"sig": "MEQCIA6pvE2MPSoi9F6ujYU5JKnqXuO0rZyd4z0ETuqh11ArAiBmFfBg2GcTExSPrBmLQCBgMPQNXF1obA2Ex+QOZmpHTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25045}, "main": "index.js", "gitHead": "54bd0eae74e792844cd5538e89c82281fb703ee6", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "get-uri": "^2.0.0", "raw-body": "^2.2.0", "agent-base": "^4.2.0", "pac-resolver": "^3.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_2.0.1_1523472085076_0.2566180422192994", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "pac-proxy-agent", "version": "2.0.2", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@2.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "90d9f6730ab0f4d2607dcdcd4d3d641aa26c3896", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-2.0.2.tgz", "fileCount": 8, "integrity": "sha512-cDNAN1Ehjbf5EHkNY5qnRhGPUCp6SnpyVof5fRzN800QV1Y2OkzbH9rmjZkbBRa8igof903yOnjIl6z0SlAhxA==", "signatures": [{"sig": "MEUCIAtDbFXj2rsyjuckt1PpyiPa1VKnUGom9TrcSKRCnUj7AiEAu/iZpoyY+MVWxb2/GbYdxSh4ZRS4e1H42fUYqyGQjIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25045}, "main": "index.js", "gitHead": "069029809c22b90c994ab085e151b50edc3c00e4", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "get-uri": "^2.0.0", "raw-body": "^2.2.0", "agent-base": "^4.2.0", "pac-resolver": "^3.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.0.5", "proxy": "0.2.4", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_2.0.2_1523509355233_0.4337797111812578", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "pac-proxy-agent", "version": "3.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@3.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "11d578b72a164ad74bf9d5bac9ff462a38282432", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-AOUX9jES/EkQX2zRz0AW7lSx9jD//hQS8wFXBvcnd/J2Py9KaMJMqV/LPqJssj1tgGufotb2mmopGPR15ODv1Q==", "signatures": [{"sig": "MEQCIDTckmGJNBz719Y4MiTkPmYP75YiuacYBG/j0T2qwjRbAiBB/uKVVWtp2oJDX7XlcIDHNGYAXdGxxTJte7vBGETG3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa0+MmCRA9TVsSAnZWagAA7WAP/3X2C/zKq1zFXZne4dI4\nnOLzARmagWjA7wwISMUvj4S4eexkjvIO9gPAgqiY6xBVnSQGKG8+BM/pFyZS\ngLSUj+lkbgwQmIUqg1SUBILT0Dv8m7lxVFmE1Mry/Hr55NhtWXnrKJvf5AEl\nQQPv6bMYx7R6IjBEd1iTW9m27tG9iev8qYFTLf6GgsIczAsSAiDoH7p9eHLB\nBoGA3OJ1Bw07/9lCb1PEyYS5AlJpOwS0rnaBjOFy9ArYIaAoOd7OMhoAeCf7\nMQ/0GUKYTqT+ssMDa/xFn8dQxONq4raOC30MCBThsdH0X3QzP1oRrpaFTH4L\nXz76nqRTJaF0MONFLSjmSg8oCYdXVDG3o2/3c486giiU3NKn/qedK3gy5frP\n5MGxQl3UBL4C2tw9qOpepH+VPv3vgC63ACZP9/8L2Ou5zK4keNkvRKnrVIxa\nPd4tIlsioTjoiReLHTJIKmmaX4AFipLNZVZIDBcN6jq9NrH3fdU0O4YEsLEo\nPais84ZfS18UohV661ua2ZT793v+jUNnY9Co+AqNpA8W1Ue7VqFZ8RwdykvI\nZ6ECIOSBSJVMrsp95SgPBYyD8iVtAVCuzreYVEtIPRq4nEiCLuRNfNrb7tax\n8ISh492ph3LEykkewv+LSyq2LReUPgEmYH1TH0NRhSBUcz5mDzVjOnz5dWFH\n3xxm\r\n=7yld\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2332cbb7103d66d0a94f625b31bfa89e507d98b2", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"debug": "^3.1.0", "get-uri": "^2.0.0", "raw-body": "^2.2.0", "agent-base": "^4.2.0", "pac-resolver": "^3.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.1", "socks-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.1.0", "proxy": "0.2.4", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_3.0.0_1523835686287_0.029970466845282706", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "pac-proxy-agent", "version": "3.0.1", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@3.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "115b1e58f92576cac2eba718593ca7b0e37de2ad", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-44DUg21G/liUZ48dJpUSjZnFfZro/0K5JTyFYLBcmh9+T6Ooi4/i4efwUiEy0+4oQusCBqWdhv16XohIj1GqnQ==", "signatures": [{"sig": "MEQCIF2ASqXImoi6iwA3MsiWM+njRJEXWwhiGGH/nv23fTgpAiBHkaldqVbuY3LN6W6HtCXMpRwmS4oWy5Oi5Pdw7qa9nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrlz2CRA9TVsSAnZWagAAJ34QAIfsJ45/LaKs3FN5zBMP\nBpKBQBml1G+YnFvAw4h5HKbDwxLGD4Jw3beJrZpWtRSLlokZXK/GOFIMNF55\nEB1dkuA49XZVxVYSqVuutI55p5NaGFajggcZJwLCabACZqyrGIugFHwq1MGW\nVsT+w3lIO0eAuoCjr8E2ORIdls5F7PkmwIwV+s+HBuz9426m14/QZAtD3a+u\nOk25QNx9fHoiKJdOu2Fc0+lNtVXqY2pH2iuldzjGwO9X1w7BMuC01TO5eb74\ncQAd2gsSy2eXCoJA9TkBq9i+vzQsVeIMRSm8rNIqcDGFaf1MpJpMwYyAmjgF\nKmce7sq85OcBtKqJLNf83XqIDwxNf6jdFLnuwOKQUIYwrwQIkwnlS7D89ZPe\nWU/aTKY4i6kVHnIWYMHIF68wTfsfdVo2cVqQCfR/o00FvHXa0Rf1Q0XwTWYY\nJcrvIsc/22OzAgqMgjKmqcr4hqP1jRNhihB7WulvWquWRNbvU8xXmobStu1z\n14HWvsvdQ+tgjhFa1AVS8EU2Q0hnMso8GLqkI5EdB/Qa21FdY95VcSgIoAxC\nmTaJoDouMy1VvCdnT8NkYbuf1hTB4YTJjIHdcBuQfnDHGawPPXx6dp9mUEqB\nJIdnrJWNFzq/jEnYJ7osCVDcK4dM6kqeae+axP5MTN7bi/twpeknMY93erwh\nNAGZ\r\n=iicx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "b633cba9e448e799685d7f5cecb51c8caa02d506", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"debug": "^4.1.1", "get-uri": "^2.0.0", "raw-body": "^2.2.0", "agent-base": "^4.2.0", "pac-resolver": "^3.0.0", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^3.0.0", "socks-proxy-agent": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "6", "proxy": "^1.0.1", "socksv5": "0.0.6"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_3.0.1_1571708150328_0.5673410640083076", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "pac-proxy-agent", "version": "4.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@4.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "3c54510e71fc9e59eac76b424ba571b47909820a", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-aXNcjuDpGiC4NUrJWdYXrmIhqxdBtfwfGds0fk4CaqOqyfNxU58BfsCjQd5Ss5RR80+ZuZOwdzxnZ5N9H+Ealw==", "signatures": [{"sig": "MEQCICL/7TUMOrIm74cingrdWXyhyeJAO1eAkG1gxE0+Y6ANAiBTGNhJh0smh6OVSqckFwB1IXxqKQvXJ14qH09SqWYSrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSyQrCRA9TVsSAnZWagAAMrwP/AybVXWAsVxi2thARigR\nawCtOi+BTIhStyHGbviJzFpC2Ibb5+TUekp31cVYol8VMxBkIYntjTefcwyG\n17uQSpx0IDdNsPMvYFsxIGgwKlh1fEr0cjK3PyioRbPJMvOCGLtDbbUAypbS\nPiz4wfBEpPtjYwfeWTySVnQsSJoGy3hf1nAhDJ3Pj+THRLPnayEV/Uy5P2j/\n/6j98ORS6eoFAa+oaReyH9zHufX7mCkOYrUqb8aDBf+HpoxP7B8m50DUoudI\nW3PAUxQbBcHSHNc5ZvNce8YeNTe/o9NoIDIy0g0OGxWl4hcFNnuU7zUXok/d\nmr+6qd8mjKYrgnGGMKop0cKpLzbgMw5V4NAc+sz2ztxXXB8QtKYV/2YIFHCh\n1Ju0mk46Ba78qt4c2vA29A8VhikUWOA5m6BlLcsfWten/IG22dACYBECZGVR\nzE5e8GfZxeAPro27PA76FMdHvT590X/7UrZrOpJUXy9BFj+YtRwdQbPM1ZX+\nGHGx++TJBArFHCZLEsT2564+PVpR6XGsbU/iOX0RiB8ZCdIePofTr/EVmJzM\nBtNpZQNCYMAbVVbj4S8KRxgAH1pT514s9CypMyblinYnnon4IvCn0AvaIAuD\nqMWjKKZWrk59HIr5GYwrvH2KhcUZPfa37pNvhiLX+av0rYb5eV7XRcKVJ2Sz\nEDJA\r\n=8z04\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "49bb28ab7d92a81162e71d18273076bdc0d9e3a0", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"debug": "4", "get-uri": "3", "raw-body": "^2.2.0", "agent-base": "6", "pac-resolver": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "5", "socks-proxy-agent": "5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "socksv5": "0.0.6", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_4.0.0_1581982763464_0.6660209317406549", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "pac-proxy-agent", "version": "4.1.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@4.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "66883eeabadc915fc5e95457324cb0f0ac78defb", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-4.1.0.tgz", "fileCount": 8, "integrity": "sha512-ejNgYm2HTXSIYX9eFlkvqFp8hyJ374uDf0Zq5YUAifiSh1D6fo+iBivQZirGvVv8dCYUsLhmLBRhlAYvBKI5+Q==", "signatures": [{"sig": "MEQCIDqNvYHFSLHAA0akx2CFsc9BsMiQSvcv1USK38UB8b6RAiA8+vSKd10DypfOGNkgRZ4g3/awROXai3YU+wwS/90guw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeSyRrCRA9TVsSAnZWagAA5MUP/1E8jhhPn5NnMXRo9wZ5\nBMpFvQizBviUR7sLT15JE01tNV2af4pdN7je+G5WrTKQHlQuEdAPpNfXwtgA\nyCmdgZCqBPrJ/hryCYS67bri9UUFFRtuOd5pyCo3MpJKqaLZrKLiFfYq/h9D\ndL624fE46YLpCVoud8HqMfxVToxANjhIIyM+a2dusp+FkKW+KkN85hWmm6zT\nNd9qco2SfvMCTE4Q9BT/vuYuulO4kxr450u6lnNZlBmzxsFFfEHiB9Hxhfma\nV0lQ8rW5U3aYji+bKc+h8mRJ76r9RHlw+Zc31cppgezy1BeeLT0OV92fx42M\n8MWal5mEWLefs15+FvsbaqV/EXvAq0PqjAtPqpsc73WiMaNbk12XbAGIWISX\nJPGUgiaIkVoZyG3knr+9R85zXLPSd0gj8/G04XZQDZV07cLw4pzl4AUzN95l\nETE++cgcj40yd7snEfQJ2Lf1tM23060a8YIqc2f7PPiKd3aepYq3MDjMWgA/\n9pTVx8wwggVdTNqiZhX8nM5nTMsS44eA0p5qIy5xxMYpMfKYwjzHX299hCv5\nIY2nako7FbNak6+jDqjDevmxUIaERzvvX4ACp6RY/3s+BKYuvWD6xKHwwOE+\n4KNCQV4h6kaJaeuF6ac78ufJKZ2p0Nbpw8aWXmghCF4f+32Ze7M9AFbnGOgb\nKXjO\r\n=ot5X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "c20aa55b8d56850d44c58459e17cb6744a5a25f4", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"debug": "4", "get-uri": "3", "raw-body": "^2.2.0", "agent-base": "6", "pac-resolver": "^4.1.0", "http-proxy-agent": "^4.0.1", "@tootallnate/once": "1", "https-proxy-agent": "5", "socks-proxy-agent": "5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "socksv5": "0.0.6", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_4.1.0_1581982826984_0.7006393748625548", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "pac-proxy-agent", "version": "5.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@5.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-pac-proxy-agent", "bugs": {"url": "https://github.com/TooTallNate/node-pac-proxy-agent/issues"}, "dist": {"shasum": "b718f76475a6a5415c2efbe256c1c971c84f635e", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-CcFG3ZtnxO8McDigozwE3AqAw15zDvGH+OjXO4kzf7IkEKkQ4gxQ+3sdF50WmhQ4P/bVusXcqNE2S3XrNURwzQ==", "signatures": [{"sig": "MEUCIEoBe4U74h0U4U5dQP63sIPTEiLF9KkkMJKCAncgVWlyAiEAuI4QSC0okiqnhnF0BkW6KeL5HTr/yq3S6vVHRCdlieE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7LkcCRA9TVsSAnZWagAAFAYP/3rQ9OsS76LfqqyBE2zA\nwesZ4ZbWMSbij3RdBSzR5pW9Xhd2nezrRrvrDa+OQGjqQbj1rZJD7bBDmhsb\nGVpglyzA1WLt8wExa47XaB3vA1KHpkXzdq1K8sfnp1gBtYq1RnfnGasoZjDi\nEPLRMzufSIzniG7bZliriIKWH4OCV1EihiYb9q442uzcPxvNdY8jVG/GABUI\nxa6Esd65ascbEBUXNLM0yCauy88TjLZxEzrCLp17jsT2bQMeIdHCRIfJ0OOg\nTOlQzPtGJJEOV1Jmk4YFnYo6iN3wzH+xZoqqL/6lqlprLxK6qHgy9vjcK+QL\n/4FU6SoXlCV7x7PRs8F9fb5wW0kiR2xgNaP2FtJYaIXKowwpcu3q0F8qxjvz\nMgH58ixPpdT3kMgq5wkw9O/p0JhbE/gjdI9pdQMmQPa3Ef/JIAXSiIpN75hg\noTZvX1HFx6UPpsE5nWjzy/8E8QVgdkrffOWMs6PsmX2HlOSgA5XOAeasPSMp\nxDoNryvzgcmVOxIQVn+x9wLnbMO0G6QQ3XdMgCRdNw21REAAQaQPX7tomvfP\n7NtQssC0l0NB65R7svLB5JQM+xsi4mpVXkmWaM0roMXR8PMAibbF6udlmV10\nzCqLhX+DBRubwMDRGOJ/wl+N5GzlqV4IxI4k9IcDZ1PYvaHbZMe/HafmsxAV\nG/6F\r\n=SwEa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "4aa197f51d57997278a081062a73e7b75f3c9e0e", "scripts": {"test": "mocha --reporter spec", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-pac-proxy-agent.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "14.17.3", "dependencies": {"debug": "4", "get-uri": "3", "raw-body": "^2.2.0", "agent-base": "6", "pac-resolver": "^5.0.0", "http-proxy-agent": "^4.0.1", "@tootallnate/once": "1", "https-proxy-agent": "5", "socks-proxy-agent": "5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^6.2.2", "proxy": "1", "eslint": "5.16.0", "rimraf": "^3.0.0", "socksv5": "0.0.6", "typescript": "^3.5.3", "@types/node": "^12.12.11", "@types/debug": "4", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_5.0.0_1626126620029_0.7983133785292131", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "pac-proxy-agent", "version": "6.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@6.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "87cbb8353288c66e57df3a1aa60c14feae1065d8", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-7Hg6R8RFuzeOw4pnYgZQQ3TtdV3KkfkJ4pEVRY2aLnIhPPobQni4xCxZYaVYsNa1YVpke+vSaivLANsY29Ag7Q==", "signatures": [{"sig": "MEUCIQCSe5XCLyV4bJ1gFYIJvk6kf2ETFqi+JDI+QfCFzHHUxQIgPR6snNj+0FOJflYpyESvV3TEJkkhn2YwifklBrk46jM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZqQ//foN6eJc6+u6LC+GELtpS5Jluk3Z8QHlTzOMaq0v7awIJSO50\r\nreJHGXlMmG2wMBxjXxd0xw3HXAlgTRnN0LVHhF8OhUhPYgrslHKyq/Jlcdrt\r\ngUx1arK+5/yRLwdvy+3YNGb0bDJW8DvfQJZFtDKEfiUhpK72aunocoCRK1UI\r\nqsjIZX5+1wX7lkiaXGVAAhTQ05Ibn5MNFjPKtHo3WBDLxgzIR9dhuPUGwi+W\r\nYGLvZSaXq018VgYh8NhgvGlUNEtYlcpKsLOmhzBJzdswbIZur1JjRQWwCSJy\r\nhc9dkYXB75z4f5Wssi83EXhzgN38y24xVgUVjfGTBt0nZdeslbBoMR9X3qk6\r\n29MwlZla8uVaiQ8J3fRPYADFGCndGu1UpU7d29uiBjloq7YVzcV54HMyy93U\r\nlQ6IBskbObp4LH83sqcydbadGtQ1qZx7igXSpWOUaclmljdUcR2ROwQjw7dt\r\np5rR9b5r2WIcjZ8dogmffyXpvuIjrTJaa6dH6bmb9mp8VpOxWsp6Am1cAsdg\r\nJg/dCYJp4q/QfYLa9HM3D1s6jnA5Cr3SXnvuw27galxZUzng9jcMvADQU/+y\r\nxbvr9OSmMLcJEaVebFUlkLpa0jXJmJnC5/5hIihJixQbnvRwCYKKOM2WOg6o\r\nslQgu5kkzhF58T/zHApn9Dx+F1XrLiBWkTA=\r\n=CRlA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-6.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/3aade296ffc5de7129d1da132f3290c7/pac-proxy-agent-6.0.0.tgz", "_integrity": "sha512-7Hg6R8RFuzeOw4pnYgZQQ3TtdV3KkfkJ4pEVRY2aLnIhPPobQni4xCxZYaVYsNa1YVpke+vSaivLANsY29Ag7Q==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.6.4", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.0", "agent-base": "^7.0.0", "pac-resolver": "^6.0.0", "http-proxy-agent": "^6.0.0", "https-proxy-agent": "^6.0.0", "socks-proxy-agent": "^8.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.0", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_6.0.0_1683232399672_0.28582036474903627", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "pac-proxy-agent", "version": "6.0.1", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@6.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "4332cfa631b418b245cba04564792aa56513ecad", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-6.0.1.tgz", "fileCount": 7, "integrity": "sha512-NVFKwuaHnN2ysRyKjUr5Y/0O6pK9SRw5yMn2KQUPgoHZVAnys7/E7wRl7Pc7TIc7svc4+TIULer3vZQajlyjVg==", "signatures": [{"sig": "MEYCIQCp23RUYqz8RkHJso0mMg6qxI3TiEjfgrarMzh6UASweAIhAKPJPHowFrixBJJ7SxL0OoqcPyLVLHQSoy5lUQ8v3ylt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32213}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-6.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/6e6e67db51246912791a0722af10cd71/pac-proxy-agent-6.0.1.tgz", "_integrity": "sha512-NVFKwuaHnN2ysRyKjUr5Y/0O6pK9SRw5yMn2KQUPgoHZVAnys7/E7wRl7Pc7TIc7svc4+TIULer3vZQajlyjVg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.6.4", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.1", "pac-resolver": "^6.0.1", "http-proxy-agent": "^6.0.1", "https-proxy-agent": "^6.1.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.1", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_6.0.1_1683324250256_0.7122248761087737", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "pac-proxy-agent", "version": "6.0.2", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@6.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "503d49a142d58e49165fdd4198ad799d3afb86fe", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-6.0.2.tgz", "fileCount": 7, "integrity": "sha512-jh3apaSHpjtB6IfbiTBgt4svoyC18TvAo4N4hm/BPMK78W4mPt46JbeZm6yIXURX+8sMohURtLp3dDDFSHNk/Q==", "signatures": [{"sig": "MEUCIDno9C02Do6Z9/E1rRZXyyLcFhbjAhpgalJ9kWj20U8pAiEAtlCmtdma/oNKygES+GAnAiyQOLwAdcny8DjJR518ls4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33097}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-6.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/be4a0c4da49622d2b65279a656b957f4/pac-proxy-agent-6.0.2.tgz", "_integrity": "sha512-jh3apaSHpjtB6IfbiTBgt4svoyC18TvAo4N4hm/BPMK78W4mPt46JbeZm6yIXURX+8sMohURtLp3dDDFSHNk/Q==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.6.4", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.1", "pac-resolver": "^6.0.1", "http-proxy-agent": "^6.0.1", "https-proxy-agent": "^6.1.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.0.1", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_6.0.2_1683623020628_0.3253859180980865", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "pac-proxy-agent", "version": "6.0.3", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@6.0.3", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "61042187093b67aa7dd05b41e4ec7c241a27c428", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-6.0.3.tgz", "fileCount": 7, "integrity": "sha512-5Hr1KgPDoc21Vn3rsXBirwwDnF/iac1jN/zkpsOYruyT+ZgsUhUOgVwq3v9+ukjZd/yGm/0nzO1fDfl7rkGoHQ==", "signatures": [{"sig": "MEUCIEVh9A+8u0Va5m4GRYo71LkWqu6a2i6vVxegNLFJtnYLAiEAh9KHPZoDDxyRf+8nqN+Pr3wPUwDbGxTTZsRBisaTjzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33197}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-6.0.3.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/1d8e9e017edd684066ea7b8a965b36bf/pac-proxy-agent-6.0.3.tgz", "_integrity": "sha512-5Hr1KgPDoc21Vn3rsXBirwwDnF/iac1jN/zkpsOYruyT+ZgsUhUOgVwq3v9+ukjZd/yGm/0nzO1fDfl7rkGoHQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.6.6", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.2", "pac-resolver": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_6.0.3_1684974180012_0.32636966582762295", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "pac-proxy-agent", "version": "6.0.4", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@6.0.4", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "f90d066808974cd6813dfcdac69a2aa483b18ba1", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-6.0.4.tgz", "fileCount": 7, "integrity": "sha512-FbJYeusBOZNe6bmrC2/+r/HljwExryon16lNKEU82gWiwIPMCEktUPSEAcTkO9K3jd/YPGuX/azZel1ltmo6nQ==", "signatures": [{"sig": "MEUCIHZoC7Zo/a419k0Aoaey41dbFx4LNjItf9O8gbbeOIGOAiEAuUVZGuc4BdcLPGGtUUX65fSxRwwR29oXVh4g9NGeQEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33197}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-6.0.4.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/fcc9e1f1a36376904b038ed4c8823538/pac-proxy-agent-6.0.4.tgz", "_integrity": "sha512-FbJYeusBOZNe6bmrC2/+r/HljwExryon16lNKEU82gWiwIPMCEktUPSEAcTkO9K3jd/YPGuX/azZel1ltmo6nQ==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.7.2", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.2", "pac-resolver": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "socks-proxy-agent": "^8.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_6.0.4_1689276867863_0.09376453045233757", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "pac-proxy-agent", "version": "7.0.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@7.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "db42120c64292685dafaf2bd921e223c56bfb13b", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-t4tRAMx0uphnZrio0S0Jw9zg3oDbz1zVhQ/Vy18FjLfP1XOLNUEjaVxYCYRI6NS+BsMBXKIzV6cTLOkO9AtywA==", "signatures": [{"sig": "MEUCIDJi9EDcjVP5V3qUumZJ29b1rs2sW4gQe48O7f/5wx6FAiEAziGby12LUjUFmU1iGtRr9Tpode7vT7ygxE7uafJhM9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33723}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-7.0.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/9cdbf26c3b68ece72218293c0ff8e67d/pac-proxy-agent-7.0.0.tgz", "_integrity": "sha512-t4tRAMx0uphnZrio0S0Jw9zg3oDbz1zVhQ/Vy18FjLfP1XOLNUEjaVxYCYRI6NS+BsMBXKIzV6cTLOkO9AtywA==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.7.2", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.4.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.2", "pac-resolver": "^7.0.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.0", "socks-proxy-agent": "^8.0.1", "@tootallnate/quickjs-emscripten": "^0.23.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_7.0.0_1689671061686_0.8077977900885593", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "pac-proxy-agent", "version": "7.0.1", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@7.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "6b9ddc002ec3ff0ba5fdf4a8a21d363bcc612d75", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.0.1.tgz", "fileCount": 7, "integrity": "sha512-ASV8yU4LLKBAjqIPMbrgtaKIvxQri/yh2OpI+S6hVa9JRkUI3Y3NPFbfngDtY7oFtSMD3w31Xns89mDa3Feo5A==", "signatures": [{"sig": "MEUCIQDzFpdMvAMgdd6cE54jpd1ItiWZOVr8o2mPbUTDkjxe9gIgA3EP/oTX5bTmNirzcvg8IXyHyAFYBCZeHXIyVcmRnhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33803}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-7.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/94971bf3a298f4127d1e1131f029361a/pac-proxy-agent-7.0.1.tgz", "_integrity": "sha512-ASV8yU4LLKBAjqIPMbrgtaKIvxQri/yh2OpI+S6hVa9JRkUI3Y3NPFbfngDtY7oFtSMD3w31Xns89mDa3Feo5A==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "9.8.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.2", "pac-resolver": "^7.0.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "socks-proxy-agent": "^8.0.2", "@tootallnate/quickjs-emscripten": "^0.23.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.1.1", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_7.0.1_1693814986458_0.7525038975261062", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "pac-proxy-agent", "version": "7.0.2", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@7.0.2", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "0fb02496bd9fb8ae7eb11cfd98386daaac442f58", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.0.2.tgz", "fileCount": 8, "integrity": "sha512-BFi3vZnO9X5Qt6NRz7ZOaPja3ic0PhlsmCRYLOpN11+mWBCR6XJDqW5RF3j8jm4WGGQZtBA+bTfxYzeKW73eHg==", "signatures": [{"sig": "MEQCIChAM+TVvVJWcMctT4C9MjwgVG6/kCG7n9Dk0/d/eGzaAiAeb9+iavyJrMI64oPPjvK7YLx8mQc05DK/74IBPMM0aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33450}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-7.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/f2c1650412d8e7a5d4e91ddb6ba57e25/pac-proxy-agent-7.0.2.tgz", "_integrity": "sha512-BFi3vZnO9X5Qt6NRz7ZOaPja3ic0PhlsmCRYLOpN11+mWBCR6XJDqW5RF3j8jm4WGGQZtBA+bTfxYzeKW73eHg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "10.7.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.0.2", "pac-resolver": "^7.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.5", "socks-proxy-agent": "^8.0.4", "@tootallnate/quickjs-emscripten": "^0.23.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.2.0", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_7.0.2_1719560031511_0.28192099613912136", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "pac-proxy-agent", "version": "7.1.0", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pac-proxy-agent@7.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "da7c3b5c4cccc6655aaafb701ae140fb23f15df2", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.1.0.tgz", "fileCount": 8, "integrity": "sha512-Z5FnLVVZSnX7WjBg0mhDtydeRZ1xMcATZThjySQUHqr+0ksP8kqaw23fNKkaaN/Z8gwLUs/W7xdl0I75eP2Xyw==", "signatures": [{"sig": "MEYCIQCdU/drkuKlWFlRdm6QMqnTWOCZYv0wabsHq/RolXm37QIhAOFrS8ZmabiEqZT+7E6EE6CrftEm60nylThtrxYjZugA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33649}, "main": "./dist/index.js", "_from": "file:pac-proxy-agent-7.1.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/08cc68051836789d1391d318260a1984/pac-proxy-agent-7.1.0.tgz", "_integrity": "sha512-Z5FnLVVZSnX7WjBg0mhDtydeRZ1xMcATZThjySQUHqr+0ksP8kqaw23fNKkaaN/Z8gwLUs/W7xdl0I75eP2Xyw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/pac-proxy-agent"}, "_npmVersion": "10.8.2", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "directories": {}, "_nodeVersion": "20.18.1", "dependencies": {"debug": "^4.3.4", "get-uri": "^6.0.1", "agent-base": "^7.1.2", "pac-resolver": "^7.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "socks-proxy-agent": "^8.0.5", "@tootallnate/quickjs-emscripten": "^0.23.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "proxy": "2.2.0", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/pac-proxy-agent_7.1.0_1733542332969_0.5327862096077036", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "pac-proxy-agent", "version": "7.2.0", "description": "A PAC file proxy `http.Agent` implementation for HTTP", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-proxy-agent"}, "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "dependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.1.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "pac-resolver": "^7.0.1", "socks-proxy-agent": "^8.0.5"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "async-listen": "^3.0.0", "jest": "^29.5.0", "socksv5": "0.0.6", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "proxy": "2.2.0", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "pac-proxy-agent@7.2.0", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-TEB8ESquiLMc0lV8vcd5Ql/JAKAoyzHFXaStwjkzpOpC5Yv+pIzLfHvjTSdf3vpa2bMiUQrg9i6276yn8666aA==", "_resolved": "/tmp/87c944cead38116ae55f1b4de6f78ace/pac-proxy-agent-7.2.0.tgz", "_from": "file:pac-proxy-agent-7.2.0.tgz", "_nodeVersion": "20.18.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-TEB8ESquiLMc0lV8vcd5Ql/JAKAoyzHFXaStwjkzpOpC5Yv+pIzLfHvjTSdf3vpa2bMiUQrg9i6276yn8666aA==", "shasum": "9cfaf33ff25da36f6147a20844230ec92c06e5df", "tarball": "https://registry.npmjs.org/pac-proxy-agent/-/pac-proxy-agent-7.2.0.tgz", "fileCount": 8, "unpackedSize": 33599, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCemRFUMqM7LSE7RjUx4SEY6St+kdstyBtF+1sHHR/aJgIhAPb0lAxWZfjsNkGEoCaReu6Zrvavj9qFOX3qsEa2n0gM"}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/pac-proxy-agent_7.2.0_1739907512991_0.42895927841494474"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-01-13T06:20:21.850Z", "modified": "2025-02-18T19:38:33.410Z", "0.0.1": "2014-01-13T06:20:21.850Z", "0.0.2": "2014-01-25T18:40:16.221Z", "0.1.0": "2014-01-26T01:23:04.857Z", "0.1.1": "2014-02-04T07:08:34.989Z", "0.1.2": "2014-04-04T19:05:25.494Z", "0.2.0": "2014-11-11T00:33:23.016Z", "1.0.0": "2015-07-11T01:30:05.458Z", "1.1.0": "2017-06-12T03:20:09.085Z", "2.0.0": "2017-06-13T20:38:50.982Z", "2.0.1": "2018-04-11T18:41:25.172Z", "2.0.2": "2018-04-12T05:02:35.308Z", "3.0.0": "2018-04-15T23:41:26.396Z", "3.0.1": "2019-10-22T01:35:50.446Z", "4.0.0": "2020-02-17T23:39:23.564Z", "4.1.0": "2020-02-17T23:40:27.128Z", "5.0.0": "2021-07-12T21:50:20.175Z", "6.0.0": "2023-05-04T20:33:19.821Z", "6.0.1": "2023-05-05T22:04:10.395Z", "6.0.2": "2023-05-09T09:03:40.786Z", "6.0.3": "2023-05-25T00:23:00.204Z", "6.0.4": "2023-07-13T19:34:28.040Z", "7.0.0": "2023-07-18T09:04:21.921Z", "7.0.1": "2023-09-04T08:09:46.614Z", "7.0.2": "2024-06-28T07:33:51.672Z", "7.1.0": "2024-12-07T03:32:13.251Z", "7.2.0": "2025-02-18T19:38:33.261Z"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["pac", "proxy", "agent", "http", "https", "socks", "request", "access"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/pac-proxy-agent"}, "description": "A PAC file proxy `http.Agent` implementation for HTTP", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "readme": "pac-proxy-agent\n===============\n### A [PAC file][pac-wikipedia] proxy `http.Agent` implementation for HTTP and HTTPS\n\nThis module provides an `http.Agent` implementation that retreives the specified\n[PAC proxy file][pac-wikipedia] and uses it to resolve which HTTP, HTTPS, or\nSOCKS proxy, or if a direct connection should be used to connect to the\nHTTP endpoint.\n\nIt is designed to be be used with the built-in `http` and `https` modules.\n\nExample\n-------\n\n```ts\nimport * as http from 'http';\nimport { PacProxyAgent } from 'pac-proxy-agent';\n\nconst agent = new PacProxyAgent('pac+https://cloudup.com/ceGH2yZ0Bjp+');\n\nhttp.get('http://nodejs.org/api/', { agent }, (res) => {\n  console.log('\"response\" event!', res.headers);\n  res.pipe(process.stdout);\n});\n```\n\n[pac-wikipedia]: http://wikipedia.org/wiki/Proxy_auto-config\n", "readmeFilename": "README.md", "users": {"alexyan": true, "yanghcc": true}}