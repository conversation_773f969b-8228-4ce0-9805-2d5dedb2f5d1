{"_id": "mitt", "_rev": "43-e0248c8894e5660c4740661d91be1084", "name": "mitt", "dist-tags": {"next": "2.0.1", "latest": "3.0.1"}, "versions": {"1.0.0": {"name": "mitt", "version": "1.0.0", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.0.0", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "httsp://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "432ee2b18a91e24a17d0e7ef21848189c8f850de", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.0.0.tgz", "integrity": "sha512-UurauSjZxqD2vKXh2P7aX+6GvhRENI01eRtmd/prCfwleN4eYv6ISfefDvOpzzncjt01FL9guZQnG/PJ3clGBQ==", "signatures": [{"sig": "MEUCIHl7TuVrsgQCsQkzGdfE2kaOfixJRJofVvmRK+iztp2aAiEAlmZgYF5PFEnjb1tmEIwYkDgwoWsLAoOCW2miDJaaEKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/mitt.js", "_from": ".", "babel": {"presets": ["es2015", "stage-0"]}, "files": ["src", "dist"], "_shasum": "432ee2b18a91e24a17d0e7ef21848189c8f850de", "amdName": "mitt", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "656c8c381a222294aaebf520ef70a3d449687b9f", "scripts": {"docs": "documentation readme src/index.js --section API -q", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "eslint src test && mocha --compilers js:babel-register test/**/*.js", "build": "npm-run-all clean -p rollup:* -p minify:* -s docs size", "clean": "rimraf dist && mkdirp dist", "release": "npm run build -s && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map", "rollup:cjs": "rollup -c rollup.config.js -m -f cjs -n $npm_package_amdName $npm_package_jsnext_main -o $npm_package_main", "rollup:umd": "rollup -c rollup.config.js -m -f umd -n $npm_package_amdName $npm_package_jsnext_main -o $npm_package_umd_main"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Tiny ~200b functional event emitter / pubsub.", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "6.3.1", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^3.13.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "pretty-bytes": "^4.0.2", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mitt-1.0.0.tgz_1484449093391_0.9418104530777782", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.1": {"name": "mitt", "version": "1.0.1", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.0.1", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "dd7f850967acc0b13420ac0e3e1ff69a3b1b029c", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.0.1.tgz", "integrity": "sha512-ZEi5LQpYnEYMJTP8/lsN2QQw7+pDuGmALS4j8YILoKePw0tmTc3uzRbNb0HyNFt8Iix0iWm0aDv0rGN3kUk8Yw==", "signatures": [{"sig": "MEUCIQDPf5leTo9ehhXleMZ3ufIS/OEi0Lp3gTyp9zSlM8w1/QIgXK5tbVq96hRCp43MvGLbxTVGuS57LJqAlby3lW6PJbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/mitt.js", "_from": ".", "files": ["src", "dist"], "_shasum": "dd7f850967acc0b13420ac0e3e1ff69a3b1b029c", "amdName": "mitt", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "afb0bff7762386bfae2d42f4310d51196dd5f61a", "scripts": {"docs": "documentation readme src/index.js --section API -q", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "eslint src test && mocha --compilers js:babel-register test/**/*.js", "build": "npm-run-all clean -p rollup:* -p minify:* -s docs size", "clean": "rimraf dist && mkdirp dist", "release": "npm run build -s && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map", "rollup:cjs": "rollup -c rollup.config.js -m -f cjs -n $npm_package_amdName $npm_package_jsnext_main -o $npm_package_main", "rollup:umd": "rollup -c rollup.config.js -m -f umd -n $npm_package_amdName $npm_package_jsnext_main -o $npm_package_umd_main"}, "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Tiny ~200b functional event emitter / pubsub.", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "6.9.4", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^3.13.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "babel-register": "^6.9.0", "pretty-bytes-cli": "^2.0.0", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mitt-1.0.1.tgz_1485110089266_0.9653485915623605", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "mitt", "version": "1.1.0", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.1.0", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "22f0d57e2fedd39620a62bb41b7cdd93667d3c41", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.1.0.tgz", "integrity": "sha512-qTyVnnO5mquyVSs63nqMhuoAUJhj7h9Hg0s2NlOk3AwOKTD7zlDpRdEe7U2kkU/YpouBbBuNnc2KiMMlZnR+Bg==", "signatures": [{"sig": "MEUCIQDDxhq6OYAb2CTSCLH5ptNpj9w/X+QzqVOzCBOy7b9HhAIgTBxbXJmrQjuqfr9PLdoG7NkUQ5/X30D1Q8poCUKSz8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/mitt.js", "_from": ".", "files": ["src", "dist", "mitt.d.ts"], "_shasum": "22f0d57e2fedd39620a62bb41b7cdd93667d3c41", "amdName": "mitt", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "f147246b7528904ec28702f43e11bd8ba946d0a6", "scripts": {"docs": "documentation readme src/index.js --section API -q", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "eslint src test && mocha --compilers js:babel-register test/**/*.js", "build": "npm-run-all clean -p rollup:* -p minify:* -s docs size", "clean": "rimraf dist && mkdirp dist", "release": "npm run build -s && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map", "rollup:cjs": "rollup -c rollup.config.js -m -f cjs -n $npm_package_amdName $npm_package_jsnext_main -o $npm_package_main", "rollup:umd": "rollup -c rollup.config.js -m -f umd -n $npm_package_amdName $npm_package_jsnext_main -o $npm_package_umd_main"}, "typings": "./mitt.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "6.9.4", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"]}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^3.13.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "babel-register": "^6.9.0", "pretty-bytes-cli": "^2.0.0", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mitt-1.1.0.tgz_1488058238959_0.5917764422483742", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.1": {"name": "mitt", "version": "1.1.1", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.1.1", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "14881478496dfa56750ea41af13a7ecb5b69a7c2", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.1.1.tgz", "integrity": "sha512-E1OGBCfxtDclOVEQp7c43aTwEhwHr4SXUgWACN5wf00JQ60Gd1W8wWL0UE7DWn5SldHhxKfZ9KwYoqBZ26KL+Q==", "signatures": [{"sig": "MEUCIGD/UPY7CJcoHedq4odqk4T2y5s0mdJtI5bXMnnVBQ0WAiEA7uws2jWHPeYS9RfY88hIuj+G98kdWmZYf5ILm5EVlE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/mitt.js", "_from": ".", "files": ["src", "dist", "mitt.d.ts"], "module": "dist/mitt.es.js", "_shasum": "14881478496dfa56750ea41af13a7ecb5b69a7c2", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "fcc6cce7d5f25d021b7c1eda8320c2697b08984c", "scripts": {"bump": "standard-version", "docs": "documentation readme src/index.js --section API -q", "lint": "eslint src test", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "flow && npm run lint && npm run testonly", "build": "npm-run-all --silent clean -p rollup -p minify:* -s docs size", "clean": "<PERSON><PERSON><PERSON> dist", "rollup": "rollup -c", "release": "npm run build -s && npm run bump && git push --follow-tags origin master && npm publish", "testonly": "mocha --compilers js:babel-register test/**/*.js", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map"}, "typings": "./mitt.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "src/index.js", "_nodeVersion": "6.9.4", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"]}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^3.13.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "flow-bin": "^0.38.0", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "babel-register": "^6.9.0", "pretty-bytes-cli": "^2.0.0", "standard-version": "^4.0.0", "rollup-plugin-flow": "^1.1.1", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1", "babel-plugin-transform-flow-strip-types": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/mitt-1.1.1.tgz_1492219564935_0.824361682171002", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "mitt", "version": "1.1.2", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.1.2", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "380e61480d6a615b660f07abb60d51e0a4e4bed6", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.1.2.tgz", "integrity": "sha512-3btxP0O9iGADGWAkteQ8mzDtEspZqu4I32y4GZYCV5BrwtzdcRpF4dQgNdJadCrbBx7Lu6Sq9AVrerMHR0Hkmw==", "signatures": [{"sig": "MEQCIHxouAhB4aYUQ2nTFgZsLqacu/+Qg4LWpKzeSwnGTOZuAiBL30AbBMPL1khTQ7WYqdT0Blh5C2Yp8ai/BG2fPpOKgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/mitt.js", "_from": ".", "files": ["src", "dist", "mitt.d.ts"], "module": "dist/mitt.es.js", "_shasum": "380e61480d6a615b660f07abb60d51e0a4e4bed6", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "325d81838ee908dc0165dc1658e94c2eb4aee98a", "scripts": {"bump": "standard-version", "docs": "documentation readme src/index.js --section API -q", "lint": "eslint src test", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "flow && npm run lint && npm run testonly", "build": "npm-run-all --silent clean -p rollup -p minify:* -s docs size", "clean": "<PERSON><PERSON><PERSON> dist", "rollup": "rollup -c", "release": "npm run build -s && npm run bump && git push --follow-tags origin master && npm publish", "testonly": "mocha --compilers js:babel-register test/**/*.js", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map"}, "typings": "./mitt.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.es.js", "_nodeVersion": "6.9.4", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"]}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^3.13.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "flow-bin": "^0.38.0", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "babel-register": "^6.9.0", "pretty-bytes-cli": "^2.0.0", "standard-version": "^4.0.0", "rollup-plugin-flow": "^1.1.1", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1", "babel-plugin-transform-flow-strip-types": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/mitt-1.1.2.tgz_1492450039621_0.48842037306167185", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.3": {"name": "mitt", "version": "1.1.3", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.1.3", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "528c506238a05dce11cd914a741ea2cc332da9b8", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.1.3.tgz", "integrity": "sha512-mUDCnVNsAi+eD6qA0HkRkwYczbLHJ49z17BGe2PYRhZL4wpZUFZGJHU7/5tmvohoma+Hdn0Vh/oJTiPEmgSruA==", "signatures": [{"sig": "MEYCIQDin0mKf3B5LuPcFQNL4ICse9BTkBsdZuHnQLqTPOxP6wIhALL2E5GmKvbk6oDQxNpVBYGODPTnaEYg1vsmFR1qnnY8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/mitt.js", "files": ["src", "dist", "mitt.d.ts"], "module": "dist/mitt.es.js", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "533fee0d8b21f2a8520e2bae735ba36c74476ba7", "scripts": {"bump": "standard-version", "docs": "documentation readme src/index.js --section API -q", "lint": "eslint src test", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "flow && npm run lint && npm run testonly", "build": "npm-run-all --silent clean -p rollup -p minify:* -s docs size", "clean": "<PERSON><PERSON><PERSON> dist", "rollup": "rollup -c", "release": "npm run build -s && npm run bump && git push --follow-tags origin master && npm publish", "testonly": "mocha --require reify/node --require flow-remove-types/register test/**/*.js", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map"}, "typings": "./mitt.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.es.js", "_nodeVersion": "8.7.0", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"]}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.2.0", "reify": "^0.8.3", "sinon": "^1.17.4", "eslint": "^3.13.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "flow-bin": "^0.38.0", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "pretty-bytes-cli": "^2.0.0", "standard-version": "^4.0.0", "flow-remove-types": "^1.2.0", "rollup-plugin-flow": "^1.1.1", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1", "babel-plugin-transform-flow-strip-types": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/mitt-1.1.3.tgz_1512616696219_0.7952767270617187", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "mitt", "version": "1.2.0", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@1.2.0", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "cb24e6569c806e31bd4e3995787fe38a04fdf90d", "tarball": "https://registry.npmjs.org/mitt/-/mitt-1.2.0.tgz", "fileCount": 11, "integrity": "sha512-r6lj77KlwqLhIUku9UWYes7KJtsczvolZkzp8hbaDPPaE24OmWl5s539Mytlj22siEQKosZ26qCBgda2PKwoJw==", "signatures": [{"sig": "MEQCIBeMP6xH8Es25feqKwR+J3GpcaEecZrHtAtfJ5bVhHpAAiA02npzg/CcBkOMvZ1Zu+xmn5STfwdKkQ+L0XkTvE+dBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdreLaCRA9TVsSAnZWagAATrAQAJs9SKfiWuC9sPeYLCyo\n00F585jaNsN21+3XMidp4H1mBNaQB9lwSuB/NXBez+dWrrB0k+g3mFxHNFeH\n8Bjdd2xETeqivbrQdu62LElhQcz2j+EUpPfIgGmrvTp4UwVvQKGMG/2+S9xL\n7c2LmLDuPrI1RfNW9eMHI86YAhO0hNt1gjTek0v9eIDTXHmyfcVs/OPnSB+4\ndl1GToe4xwZnME9Ryw5zCwEl3dxgLNIBdideR4AzVwHlmruKd8+A+5ryt6vK\nS7mtftqk9WG+GOu5TBOJmWaLkeasOv1c2EU8mw22mqgYb1n0ReuY2D74smX/\nuw741vd5fLuCxIOaDQX726zfni87ITMEQiIgg3XDshsJBh2GZ6bN/CxSS150\nW1Z5ph34YYiiqGU2l3xiCtFOW1/rN9NOUqSYR+liYeMXnE32MQiqfDhVus/j\nEg9gBG+U1OX9tNiMmHDwAjSafGIYb4P/18ROGjqVRKDvIZOuEkLjaA0nitwb\nQNihkMP1h6OpdKAnLciDe/8xR+qRi2kD49D59pC7hRuBYzGvh0AsrW8IX3J/\nnZIbjprCFadvMSpdJlk2ONQ7KbfkES7iitfSLYUFjXtJNUNhLY3xeA1yO0Ai\nptee6A8NiqGuzSuXMDuNnHLb5mYFWE0kpOqH0grHxcq+TYJ6K6Bf7rLIyHcD\nbcyM\r\n=nMn+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/mitt.js", "babel": {"plugins": ["transform-flow-strip-types"], "presets": ["es2015", "stage-0"]}, "module": "dist/mitt.es.js", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "a4e20b1849fa327b87da55786999fd8c550a0dfa", "scripts": {"bump": "standard-version", "docs": "documentation readme src/index.js --section API -q", "lint": "eslint src test", "size": "echo \"Gzipped Size: $(strip-json-comments --no-whitespace $npm_package_main | gzip-size | pretty-bytes)\"", "test": "flow && npm run lint && npm run testonly", "build": "npm-run-all --silent clean -p rollup -p minify:* -s docs size", "clean": "<PERSON><PERSON><PERSON> dist", "rollup": "rollup -c", "release": "npm run build -s && npm run bump && git push --follow-tags origin master && npm publish", "testonly": "mocha --require esm --require flow-remove-types/register test/**/*.js", "minify:cjs": "uglifyjs $npm_package_main -cm toplevel -o $npm_package_main -p relative --in-source-map ${npm_package_main}.map --source-map ${npm_package_main}.map", "minify:umd": "uglifyjs $npm_package_umd_main -cm -o $npm_package_umd_main -p relative --in-source-map ${npm_package_umd_main}.map --source-map ${npm_package_umd_main}.map"}, "typings": "./mitt.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.es.js", "_nodeVersion": "10.16.0", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"]}, "parser": "babel-es<PERSON>", "extends": "eslint:recommended", "globals": {"expect": true}}, "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^3.13.1", "rimraf": "^2.5.2", "rollup": "^0.41.4", "flow-bin": "^0.38.0", "uglify-js": "^2.6.2", "babel-core": "^6.9.1", "sinon-chai": "^2.8.0", "npm-run-all": "^2.1.1", "babel-eslint": "^7.1.1", "documentation": "^4.0.0-beta4", "gzip-size-cli": "^1.0.0", "pretty-bytes-cli": "^2.0.0", "standard-version": "^4.0.0", "flow-remove-types": "^1.2.0", "rollup-plugin-flow": "^1.1.1", "babel-preset-es2015": "^6.9.0", "rollup-plugin-buble": "^0.15.0", "babel-preset-stage-0": "^6.5.0", "strip-json-comments-cli": "^1.0.1", "babel-plugin-transform-flow-strip-types": "^6.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/mitt_1.2.0_1571676890401_0.08130507661521613", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "mitt", "version": "2.0.0", "keywords": ["events", "eventemitter", "pubsub"], "license": "MIT", "_id": "mitt@2.0.0", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "233674e78d1e69c8122d001ad73c985bd3d2599b", "tarball": "https://registry.npmjs.org/mitt/-/mitt-2.0.0.tgz", "fileCount": 12, "integrity": "sha512-MxK9wErRoeNSklOfRT0vYLGQ2edgiG/fooAsbzP2AbS3WiNGDq0XvcaoIQ9ixKGDtowvVtqgCC8h7Y2hAbMzHw==", "signatures": [{"sig": "MEYCIQCb3ehCbVtquQws/AwcahDEMads6dTuxRAckk5huRUAaQIhAId4nG7DgTRgjbfBqFPOSh0E2ZSVwrjBQyLzXfrgDS/L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY/WCRA9TVsSAnZWagAAfakQAIRCaLND1cMhQ25AqNj3\nRPaAWb6j5HlEZobuuLbIOLUFb9mY4yDXMelKDG2TM+d91SWBfsJtV+96ACQ/\nxrjWp974EuihTjcpIjVRrIJKRHLcrhQ8+wDg0g4lYy0BuF3rfKEiH0iWXaAB\ndqDvO/pefkzlkWJF5aVOnYcRJqEyFEiomj2goAt9i9PC0X2WUEFKH8rYw4kR\nbw4XOVZ8fRO8oPy4jsjsByZ5YaNLYfwTu0geKWYdGvtdJL+WLyOS6zqKG0qw\nvIpMt1sKgu2J7p6yCsQg7cRz1anVyAhX8TggNayKOi1ARSJ70h8Zng2PBfT5\nK9w3PtlgBw299o6fBGsve9fkyTh6CwAiOnw4da/mLVoKNOqxPS+x/LgtOkSm\nT3wIwCJrPTWTPMe6i2XtJK2ebKqxYZ6eI8CX9tlNKVKGwBFsew9XaLmMyDub\nlrGtks/nKjewca+HT4f5HB1NTxBxz4dSprnWeyugeblnWHuJMDqjhhnLXl02\nKGb1HIbNfmpZtErWY9bKnopFgUeqoYMWu5QE5pc7Q/hYPPzNeIiJA/I/OxmA\nRXEIJEU9x8eEJ6A5AZW8YS/AjHNjyrt3DTdQSAW8Ulb0YJZUqIpPiFv95LRv\nRYM0eCNpUg0GXFh3r01JXzFOg0hVEUKLOIb6wNULbuI4vpkkJcM7+OZ1csTl\num0C\r\n=tJot\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/mitt.js", "module": "dist/mitt.es.js", "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/BqsX9NT.png\" width=\"300\" height=\"300\" alt=\"mitt\">\n  <br>\n  <a href=\"https://www.npmjs.org/package/mitt\"><img src=\"https://img.shields.io/npm/v/mitt.svg?style=flat\" alt=\"npm\"></a> <a href=\"https://travis-ci.org/developit/mitt\"><img src=\"https://travis-ci.org/developit/mitt.svg?branch=master\" alt=\"travis\"></a> <a href=\"https://david-dm.org/developit/mitt\"><img src=\"https://david-dm.org/developit/mitt/status.svg\" alt=\"dependencies Status\"></a> <a href=\"https://unpkg.com/mitt/dist/mitt.umd.js\"><img src=\"http://img.badgesize.io/https://unpkg.com/mitt/dist/mitt.js?compression=gzip\" alt=\"gzip size\"></a>\n  \n</p>\n\n# Mitt\n\n> Tiny 200b functional event emitter / pubsub.\n\n-   **Microscopic:** weighs less than 200 bytes gzipped\n-   **Useful:** a wildcard `\"*\"` event type listens to all events\n-   **Familiar:** same names & ideas as [Node's EventEmitter](https://nodejs.org/api/events.html#events_class_eventemitter)\n-   **Functional:** methods don't rely on `this`\n-   **Great Name:** somehow [mitt](https://npm.im/mitt) wasn't taken\n\nMitt was made for the browser, but works in any JavaScript runtime. It has no dependencies and supports IE9+.\n\n## Table of Contents\n\n-   [Install](#install)\n-   [Usage](#usage)\n-   [Examples & Demos](#examples--demos)\n-   [API](#api)\n-   [Contribute](#contribute)\n-   [License](#license)\n\n## Install\n\nThis project uses [node](http://nodejs.org) and [npm](https://npmjs.com). Go check them out if you don't have them locally installed.\n\n```sh\n$ npm install --save mitt\n```\n\nThen with a module bundler like [rollup](http://rollupjs.org/) or [webpack](https://webpack.js.org/), use as you would anything else:\n\n```javascript\n// using ES6 modules\nimport mitt from 'mitt'\n\n// using CommonJS modules\nvar mitt = require('mitt')\n```\n\nThe [UMD](https://github.com/umdjs/umd) build is also available on [unpkg](https://unpkg.com):\n\n```html\n<script src=\"https://unpkg.com/mitt/dist/mitt.umd.js\"></script>\n```\n\nYou can find the library on `window.mitt`.\n\n## Usage\n\n```js\nimport mitt from 'mitt'\n\nconst emitter = mitt()\n\n// listen to an event\nemitter.on('foo', e => console.log('foo', e) )\n\n// listen to all events\nemitter.on('*', (type, e) => console.log(type, e) )\n\n// fire an event\nemitter.emit('foo', { a: 'b' })\n\n// working with handler references:\nfunction onFoo() {}\nemitter.on('foo', onFoo)   // listen\nemitter.off('foo', onFoo)  // unlisten\n```\n\n### Typescript\n\n```ts\nimport mitt from 'mitt';\nconst emitter: mitt.Emitter = mitt();\n```\n\n## Examples & Demos\n\n<a href=\"http://codepen.io/developit/pen/rjMEwW?editors=0110\">\n  <b>Preact + Mitt Codepen Demo</b>\n  <br>\n  <img src=\"https://i.imgur.com/CjBgOfJ.png\" width=\"278\" alt=\"preact + mitt preview\">\n</a>\n\n* * *\n\n## API\n\n<!-- Generated by documentation.js. Update this documentation by updating the source code. -->\n\n#### Table of Contents\n\n-   [mitt](#mitt)\n-   [on](#on)\n    -   [Parameters](#parameters)\n-   [off](#off)\n    -   [Parameters](#parameters-1)\n-   [emit](#emit)\n    -   [Parameters](#parameters-2)\n\n### mitt\n\nMitt: Tiny (~200b) functional event emitter / pubsub.\n\nReturns **Mitt** \n\n### on\n\nRegister an event handler for the given type.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** Type of event to listen for, or `\"*\"` for all events\n-   `handler` **[Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)** Function to call in response to given event\n\n### off\n\nRemove an event handler for the given type.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** Type of event to unregister `handler` from, or `\"*\"`\n-   `handler` **[Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)** Handler function to remove\n\n### emit\n\nInvoke all handlers for the given type.\nIf present, `\"*\"` handlers are invoked after type-matched handlers.\n\nNote: Manually firing \"\\*\" handlers is not supported.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** The event type to invoke\n-   `evt` **Any?** Any value (object is recommended and powerful), passed to each handler\n\n## Contribute\n\nFirst off, thanks for taking the time to contribute!\nNow, take a moment to be sure your contributions make sense to everyone else.\n\n### Reporting Issues\n\nFound a problem? Want a new feature? First of all see if your issue or idea has [already been reported](../../issues).\nIf don't, just open a [new clear and descriptive issue](../../issues/new).\n\n### Submitting pull requests\n\nPull requests are the greatest contributions, so be sure they are focused in scope, and do avoid unrelated commits.\n\n-   Fork it!\n-   Clone your fork: `git clone https://github.com/<your-username>/mitt`\n-   Navigate to the newly cloned directory: `cd mitt`\n-   Create a new branch for the new feature: `git checkout -b my-new-feature`\n-   Install the tools necessary for development: `npm install`\n-   Make your changes.\n-   Commit your changes: `git commit -am 'Add some feature'`\n-   Push to the branch: `git push origin my-new-feature`\n-   Submit a pull request with full remarks documenting your changes.\n\n## License\n\n[MIT License](https://opensource.org/licenses/MIT) © [Jason Miller](https://jasonformat.com/)\n", "source": "src/index.ts", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "0ead0bafdee03a0da3ea552d847b712b7825d562", "scripts": {"docs": "documentation readme src/index.ts --section API -q --parse-extension ts", "lint": "eslint src test --ext ts --ext js", "test": "tsc src/index.ts --noEmit && npm run lint && npm run testonly", "build": "npm-run-all --silent clean -p bundle -s docs", "clean": "<PERSON><PERSON><PERSON> dist", "bundle": "microbundle", "release": "npm run -s build -s && npm t && git tag $npm_package_version && git push && git push --tags && npm publish", "testonly": "mocha --require esm --require ts-node/register test/**/*.js"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "esmodules": "dist/mitt.modern.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.es.js", "_nodeVersion": "12.16.0", "eslintConfig": {"env": {"es6": true, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"], "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/explicit-function-return-type": 0}, "parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "globals": {"expect": true}, "parserOptions": {"sourceType": "module"}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "chai": "^3.5.0", "mocha": "^3.2.0", "sinon": "^1.17.4", "eslint": "^6.5.1", "rimraf": "^2.5.2", "ts-node": "^8.10.1", "sinon-chai": "^2.8.0", "typescript": "^3.9.3", "microbundle": "^0.12.0", "npm-run-all": "^2.1.1", "documentation": "^13.0.0", "@typescript-eslint/parser": "^2.34.0", "@typescript-eslint/eslint-plugin": "^2.34.0"}, "_npmOperationalInternal": {"tmp": "tmp/mitt_2.0.0_1590530005799_0.15743291739677634", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "mitt", "version": "2.0.1", "keywords": ["events", "eventemitter", "emitter", "pubsub"], "license": "MIT", "_id": "mitt@2.0.1", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "9e8a075b4daae82dd91aac155a0ece40ca7cb393", "tarball": "https://registry.npmjs.org/mitt/-/mitt-2.0.1.tgz", "fileCount": 12, "integrity": "sha512-FhuJY+tYHLnPcBHQhbUFzscD5512HumCPE4URXZUgPi3IvOJi4Xva5IIgy3xX56GqCmw++MAm5UURG6kDBYTdg==", "signatures": [{"sig": "MEQCID+tmCV5TlMcb4yiH4VLoY6JC0Bn0LJ5k8qvQQQwOR1gAiBx5WrxVRV5JxrMicmsNjOdVGwdE3bJCVS1zaGtoDqCmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezqbyCRA9TVsSAnZWagAAy1UP/0/N+YKPT3xPvaqSS4NI\nyjw20spx2r9lXHSDMoJG/Ovq/LSHtXg8HlGDTdTLGyPhoteTsKIj5CK53Tb/\nxQWp8y453UyvqHu3LYYELV+cIoM2sO3Anw6C765qWtkbeVKYsOAT7uA7qkl5\nEHw2gtlBSuZ46PquLn63DwL+HuwJRfh1oaGz0RomWQs1WwH5Bu/JMjBqQtKF\ncVEInduXWFRIruKJsE4r+97k/3ApUZAh1Y/MxTjupPlnPVyNRMOO5hlsMKLb\nN3dzqFsgDUPMs2dy0eXf/2NAv7+2/RoPnnDzyDn2UF8FVhYzuD2U9oI5NCqA\n3V5fpglu12zgpLLetym2qI3/vgTiG8QA0ugVrKlFql1AfgtXm0k26yQc5sDj\nIipyPudAU3Vu97jmgptL7Oby8HclBxUK9S8Fzaj7Tw+gFyfYb14EhdNWSL7v\n3oxGHR0KTItxOD4nTymlEnSLZSmZ42M/wPTTnOIhcpZd9ugLXb6bilIUa3bS\nt2sMcF3cFt/r3Jcu4VOq0PIfzvwh1lMuLcdCFNobscMxXG1ByIcbBsqZCOWs\noiazwLenOcfO6rjNuAEhZa+vPCxWKJPgsZU0UjseE4sTQJgCDhlYHIYNE4v8\nq+f4+KVlovck3Daqr1rApLQxFDeA7VTV/U3hledQu9QzijmmK/DJlp47TTXl\nd6aO\r\n=Xkqc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/mitt.js", "module": "dist/mitt.es.js", "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/BqsX9NT.png\" width=\"300\" height=\"300\" alt=\"mitt\">\n  <br>\n  <a href=\"https://www.npmjs.org/package/mitt\"><img src=\"https://img.shields.io/npm/v/mitt.svg\" alt=\"npm\"></a>\n  <img src=\"https://github.com/developit/mitt/workflows/CI/badge.svg\" alt=\"build status\">\n  <a href=\"https://unpkg.com/mitt/dist/mitt.js\"><img src=\"https://img.badgesize.io/https://unpkg.com/mitt/dist/mitt.js?compression=gzip\" alt=\"gzip size\"></a>\n</p>\n\n# Mitt\n\n> Tiny 200b functional event emitter / pubsub.\n\n-   **Microscopic:** weighs less than 200 bytes gzipped\n-   **Useful:** a wildcard `\"*\"` event type listens to all events\n-   **Familiar:** same names & ideas as [<PERSON>de's EventEmitter](https://nodejs.org/api/events.html#events_class_eventemitter)\n-   **Functional:** methods don't rely on `this`\n-   **Great Name:** somehow [mitt](https://npm.im/mitt) wasn't taken\n\nMitt was made for the browser, but works in any JavaScript runtime. It has no dependencies and supports IE9+.\n\n## Table of Contents\n\n-   [Install](#install)\n-   [Usage](#usage)\n-   [Examples & Demos](#examples--demos)\n-   [API](#api)\n-   [Contribute](#contribute)\n-   [License](#license)\n\n## Install\n\nThis project uses [node](http://nodejs.org) and [npm](https://npmjs.com). Go check them out if you don't have them locally installed.\n\n```sh\n$ npm install --save mitt\n```\n\nThen with a module bundler like [rollup](http://rollupjs.org/) or [webpack](https://webpack.js.org/), use as you would anything else:\n\n```javascript\n// using ES6 modules\nimport mitt from 'mitt'\n\n// using CommonJS modules\nvar mitt = require('mitt')\n```\n\nThe [UMD](https://github.com/umdjs/umd) build is also available on [unpkg](https://unpkg.com):\n\n```html\n<script src=\"https://unpkg.com/mitt/dist/mitt.umd.js\"></script>\n```\n\nYou can find the library on `window.mitt`.\n\n## Usage\n\n```js\nimport mitt from 'mitt'\n\nconst emitter = mitt()\n\n// listen to an event\nemitter.on('foo', e => console.log('foo', e) )\n\n// listen to all events\nemitter.on('*', (type, e) => console.log(type, e) )\n\n// fire an event\nemitter.emit('foo', { a: 'b' })\n\n// working with handler references:\nfunction onFoo() {}\nemitter.on('foo', onFoo)   // listen\nemitter.off('foo', onFoo)  // unlisten\n```\n\n### Typescript\n\n```ts\nimport mitt from 'mitt';\nconst emitter: mitt.Emitter = mitt();\n```\n\n## Examples & Demos\n\n<a href=\"http://codepen.io/developit/pen/rjMEwW?editors=0110\">\n  <b>Preact + Mitt Codepen Demo</b>\n  <br>\n  <img src=\"https://i.imgur.com/CjBgOfJ.png\" width=\"278\" alt=\"preact + mitt preview\">\n</a>\n\n* * *\n\n## API\n\n<!-- Generated by documentation.js. Update this documentation by updating the source code. -->\n\n#### Table of Contents\n\n-   [mitt](#mitt)\n-   [on](#on)\n    -   [Parameters](#parameters)\n-   [off](#off)\n    -   [Parameters](#parameters-1)\n-   [emit](#emit)\n    -   [Parameters](#parameters-2)\n\n### mitt\n\nMitt: Tiny (~200b) functional event emitter / pubsub.\n\nReturns **Mitt** \n\n### on\n\nRegister an event handler for the given type.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** Type of event to listen for, or `\"*\"` for all events\n-   `handler` **[Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)** Function to call in response to given event\n\n### off\n\nRemove an event handler for the given type.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** Type of event to unregister `handler` from, or `\"*\"`\n-   `handler` **[Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)** Handler function to remove\n\n### emit\n\nInvoke all handlers for the given type.\nIf present, `\"*\"` handlers are invoked after type-matched handlers.\n\nNote: Manually firing \"\\*\" handlers is not supported.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** The event type to invoke\n-   `evt` **Any?** Any value (object is recommended and powerful), passed to each handler\n\n## Contribute\n\nFirst off, thanks for taking the time to contribute!\nNow, take a moment to be sure your contributions make sense to everyone else.\n\n### Reporting Issues\n\nFound a problem? Want a new feature? First of all see if your issue or idea has [already been reported](../../issues).\nIf don't, just open a [new clear and descriptive issue](../../issues/new).\n\n### Submitting pull requests\n\nPull requests are the greatest contributions, so be sure they are focused in scope, and do avoid unrelated commits.\n\n-   Fork it!\n-   Clone your fork: `git clone https://github.com/<your-username>/mitt`\n-   Navigate to the newly cloned directory: `cd mitt`\n-   Create a new branch for the new feature: `git checkout -b my-new-feature`\n-   Install the tools necessary for development: `npm install`\n-   Make your changes.\n-   Commit your changes: `git commit -am 'Add some feature'`\n-   Push to the branch: `git push origin my-new-feature`\n-   Submit a pull request with full remarks documenting your changes.\n\n## License\n\n[MIT License](https://opensource.org/licenses/MIT) © [Jason Miller](https://jasonformat.com/)\n", "source": "src/index.ts", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "59591757df1a563274e56ee1f80a54818834afca", "scripts": {"docs": "documentation readme src/index.ts --section API -q --parse-extension ts", "lint": "eslint src test --ext ts --ext js", "test": "npm-run-all --silent typecheck lint testonly", "build": "npm-run-all --silent clean -p bundle -s docs", "clean": "<PERSON><PERSON><PERSON> dist", "bundle": "microbundle", "release": "npm run -s build -s && npm t && git commit -m $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "testonly": "mocha --require esm test/**/*.js", "typecheck": "tsc **/*.ts --noEmit"}, "typings": "dist/index.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "esmodules": "dist/mitt.modern.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.es.js", "_nodeVersion": "12.16.0", "eslintConfig": {"env": {"es6": true, "jest": false, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"], "jest/valid-expect": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-module-boundary-types": 0}, "parser": "@typescript-eslint/parser", "extends": ["developit", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "globals": {"expect": true}, "parserOptions": {"sourceType": "module"}}, "eslintIgnore": ["dist"], "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"esm": "^3.2.25", "chai": "^4.2.0", "mocha": "^7.2.0", "sinon": "^9.0.2", "eslint": "^7.1.0", "rimraf": "^3.0.2", "ts-node": "^8.10.1", "sinon-chai": "^3.5.0", "typescript": "^3.9.3", "@types/chai": "^4.2.11", "microbundle": "^0.12.0", "npm-run-all": "^4.1.5", "@types/mocha": "^7.0.2", "@types/sinon": "^9.0.4", "documentation": "^13.0.0", "@types/sinon-chai": "^3.2.4", "eslint-config-developit": "^1.2.0", "@typescript-eslint/parser": "^3.0.1", "@typescript-eslint/eslint-plugin": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mitt_2.0.1_1590601458172_0.028297092586270445", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "mitt", "version": "2.1.0", "keywords": ["events", "eventemitter", "emitter", "pubsub"], "license": "MIT", "_id": "mitt@2.1.0", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "f740577c23176c6205b121b2973514eade1b2230", "tarball": "https://registry.npmjs.org/mitt/-/mitt-2.1.0.tgz", "fileCount": 12, "integrity": "sha512-ILj2TpLiysu2wkBbWjAmww7TkZb65aiQO+DkVdUTBpBXq+MHYiETENkKFMtsJZX1Lf4pe4QOrTSjIfUwN5lRdg==", "signatures": [{"sig": "MEUCIEA8Io1HdrSaudB7gl3P/EV5fU3zVGQgKkoMc0oJRJQnAiEAxwbLi8KRfJQfebyvGLKU83LC871uKq2uee3FAZPojS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDxopCRA9TVsSAnZWagAA7isP/0mDaj3n6bSW7PdY3iZM\nSqKRu2KdGrNTrL9nh0YCeAx6nCm0eTnX4OhKcXUxEjyPQ5GyeyyGnToqDgto\ngo54oViCFRbeUBlRPlB5JbkaPM66uli2kFbLiOu+rgHgEf4axmbQJbcBaSI+\nb95aH0FdlAAYfx8Pf0UiBK32cj3In36PcKlXCDVg4adjG20DSFUPX38dvcPK\nWUg4wwnBUSWIvBr/gyR3l7F9W/rrGDnfMwjD+VnSEb1948BQgbXq/p1mo1QC\nPhORLxBqP/P+upodBpm9rBSKHVi++oGjiyNxHTbo+aCQtvfu6XapCIj71Ved\nlGMlcoivZ+K0PPj1AyebdWWzZ2b1PDXL3gdyWrKpDZV6yZ8450MasTNLmzpP\n6EqpozW/HuSjkD1Y7rrBu/g/AT767YA/5cLVSsk1Y3eCrlm9PZqWBeScKifh\nAHK71mFdH4H2/5o/lKlTGlr2H6UBACOjZjo5Y2Le5wiLPNTwvg9G7S0xgosL\nm8SjXgEj90GnlaQHqgx6iUgyEYXSELfsaic8B/RT58xvOpaCR9k5N6oB/X/N\n0AePKOpeAve0zeCX+jX1HZOXJ0mcu194GYFL8WvTe+VCcd61oV/qYNt3tU8y\n7vMhwsPib3q83/IhYJcXGvlzoN4D/C6aVJKk1p4pOc9Z2tvK4IFjLYVWi46d\nFrej\r\n=hR1o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/mitt.js", "mocha": {"spec": ["test/*_test.ts"], "require": ["ts-node/register", "esm"], "extension": ["ts"]}, "module": "dist/mitt.es.js", "source": "src/index.ts", "authors": ["<PERSON> <<EMAIL>>"], "gitHead": "22c5dcba10736aecb1f39ee88d9f85278108c988", "scripts": {"docs": "documentation readme src/index.ts --section API -q --parse-extension ts", "lint": "eslint src test --ext ts --ext js", "test": "npm-run-all --silent typecheck lint mocha test-types", "build": "npm-run-all --silent clean -p bundle -s docs", "clean": "<PERSON><PERSON><PERSON> dist", "mocha": "mocha test", "bundle": "microbundle", "release": "npm run -s build -s && npm t && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "typecheck": "tsc --noEmit", "test-types": "tsc test/test-types-compilation.ts --noEmit"}, "typings": "index.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "esmodules": "dist/mitt.modern.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.es.js", "_nodeVersion": "12.14.0", "eslintConfig": {"env": {"es6": true, "jest": false, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"], "jest/valid-expect": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-module-boundary-types": 0}, "parser": "@typescript-eslint/parser", "extends": ["developit", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "globals": {"expect": true}, "parserOptions": {"sourceType": "module"}}, "eslintIgnore": ["dist", "index.d.ts"], "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "chai": "^4.2.0", "mocha": "^8.0.1", "sinon": "^9.0.2", "eslint": "^7.1.0", "rimraf": "^3.0.2", "ts-node": "^8.10.2", "sinon-chai": "^3.5.0", "typescript": "^3.9.3", "@types/chai": "^4.2.11", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "@types/mocha": "^7.0.2", "@types/sinon": "^9.0.4", "documentation": "^13.0.0", "@types/sinon-chai": "^3.2.4", "eslint-config-developit": "^1.2.0", "@typescript-eslint/parser": "^3.0.1", "@typescript-eslint/eslint-plugin": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mitt_2.1.0_1594825257372_0.9462805626018445", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "mitt", "version": "3.0.0", "keywords": ["events", "eventemitter", "emitter", "pubsub"], "license": "MIT", "_id": "mitt@3.0.0", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "69ef9bd5c80ff6f57473e8d89326d01c414be0bd", "tarball": "https://registry.npmjs.org/mitt/-/mitt-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-7dX2/10ITVyqh4aOSVI9gdape+t9l2/8QxHrFmUXu4EEUpdlxl6RudZUPZoc+zuY2hk1j7XxVroIVIan/pD/SQ==", "signatures": [{"sig": "MEUCIQCpb8l3BWIruFcltzzjSsHaSmuXCwsILzZ5peIm/uPS2gIgK2pnIEXENr9S+wPM7Zt0KfHaRZwPn2a/QjtCgqtzBu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg053NCRA9TVsSAnZWagAAkO4QAKRfoduN4rRTfbrB7sJd\niYz2NcxRB5kT9dAIOUplnwFc6N4pjvsG1ZXWJKG6Ffs1/XCHid0VXeDpOqww\ntMSsoMucPNjkezjGWL4NtLgrVIzogSG6hm49ekXt91Kvgbt8YNrNh4Sn2P0h\ng7dV6W4OMEdFfarJwdPj8sUAtpQeQWbPE25vDJSurKmHOUaWTp5Xx1JUmwgv\nmR+oYo6D2UdVOlMnSHph+1xxAoCim+XfsiksrlNuUDrefpXlFZH8YXPCP473\nWtVN0hx959R7KanoKKjAj4tHrYCImB8GIMbibsSWsqUpd4gnUuv+2DFdsVJu\n5/tLrpPJfalXtLQNi3PQJfXMgtavn4QFptA7d6b1ADpnYEdkQziRvBXb0aIC\n7t22PghpGKcbmfawdXIDUWt0ksRhh2am3qLEVA89/Cm3N0epq8rViQLyXHMZ\n64nSRuJzHYcq983g6BslF8WAwNpMkVr21dxDgugvFUzS9i9i1QswOd2qAcB+\nrufrmtWvKt1Y2FpbO+HbDfMpO1Y8B4Ct8nISR3PwJsRnTIXl/8JWK8nSdoEc\nFxTzgNJeRTVRW6FRJjbcYCNldW+hQwd/YeaCdqt5EYfTeRhUsbN2B62dSL4A\nF1Stna4MpMNbd1xnxM1HSqwQnh2POHqQRMDi+CtIyDL4pKiLe/H+jsLmf7Ab\nV5Ne\r\n=mtnu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/mitt.js", "mocha": {"spec": ["test/*_test.ts"], "require": ["ts-node/register", "esm"], "extension": ["ts"]}, "module": "dist/mitt.mjs", "source": "src/index.ts", "authors": ["<PERSON> <<EMAIL>>"], "exports": {"import": "./dist/mitt.mjs", "default": "./dist/mitt.mjs", "require": "./dist/mitt.js"}, "gitHead": "13905bd4009e83f1876f2d31419e23cdbf4f9b48", "scripts": {"docs": "documentation readme src/index.ts --section API -q --parse-extension ts", "lint": "eslint src test --ext ts --ext js", "test": "npm-run-all --silent typecheck lint mocha test-types", "build": "npm-run-all --silent clean -p bundle -s docs", "clean": "<PERSON><PERSON><PERSON> dist", "mocha": "mocha test", "bundle": "microbundle -f es,cjs,umd", "release": "npm run -s build -s && npm t && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "typecheck": "tsc --noEmit", "test-types": "tsc test/test-types-compilation.ts --noEmit --strict"}, "typings": "index.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.mjs", "_nodeVersion": "12.19.0", "eslintConfig": {"env": {"es6": true, "jest": false, "mocha": true, "browser": true}, "rules": {"semi": [2, "always"], "jest/valid-expect": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-empty-function": 0, "@typescript-eslint/no-non-null-assertion": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-module-boundary-types": 0}, "parser": "@typescript-eslint/parser", "extends": ["developit", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "globals": {"expect": true}, "parserOptions": {"sourceType": "module"}}, "eslintIgnore": ["dist", "index.d.ts"], "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "chai": "^4.2.0", "mocha": "^8.0.1", "sinon": "^9.0.2", "eslint": "^7.1.0", "rimraf": "^3.0.2", "ts-node": "^8.10.2", "sinon-chai": "^3.5.0", "typescript": "^3.9.7", "@types/chai": "^4.2.11", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "@types/mocha": "^7.0.2", "@types/sinon": "^9.0.4", "documentation": "^13.0.0", "@types/sinon-chai": "^3.2.4", "eslint-config-developit": "^1.2.0", "@typescript-eslint/parser": "^3.0.1", "@typescript-eslint/eslint-plugin": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/mitt_3.0.0_1624481229034_0.19376983114278534", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "mitt", "version": "3.0.1", "keywords": ["events", "eventemitter", "emitter", "pubsub"], "license": "MIT", "_id": "mitt@3.0.1", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "homepage": "https://github.com/developit/mitt", "bugs": {"url": "https://github.com/developit/mitt/issues"}, "dist": {"shasum": "ea36cf0cc30403601ae074c8f77b7092cdab36d1", "tarball": "https://registry.npmjs.org/mitt/-/mitt-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==", "signatures": [{"sig": "MEQCIFRo6xXmkkEA/oHRCzY8TOy4hKsBElWLSDw7sCYYPj5zAiBUOLWUN9R6a2x82jY5Zu/yWUORvB20JLdNdqnbR6LacA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26444}, "main": "dist/mitt.js", "mocha": {"spec": ["test/*_test.ts"], "require": ["ts-node/register", "esm"], "extension": ["ts"]}, "module": "dist/mitt.mjs", "source": "src/index.ts", "authors": ["<PERSON> <<EMAIL>>"], "exports": {"types": "./index.d.ts", "import": "./dist/mitt.mjs", "module": "./dist/mitt.mjs", "default": "./dist/mitt.mjs", "require": "./dist/mitt.js"}, "gitHead": "b240473b5707857ba2c6a8e6d707c28d1e39da49", "scripts": {"docs": "documentation readme src/index.ts --section API -q --parse-extension ts", "lint": "eslint src test --ext ts --ext js", "test": "npm-run-all --silent typecheck lint mocha test-types", "build": "npm-run-all --silent clean -p bundle -s docs", "clean": "<PERSON><PERSON><PERSON> dist", "mocha": "mocha test", "bundle": "microbundle -f es,cjs,umd", "release": "npm run -s build -s && npm t && git commit -am $npm_package_version && git tag $npm_package_version && git push && git push --tags && npm publish", "typecheck": "tsc --noEmit", "test-types": "tsc test/test-types-compilation.ts --noEmit --strict"}, "typings": "index.d.ts", "_npmUser": {"name": "developit", "email": "<EMAIL>"}, "prettier": {"singleQuote": true, "trailingComma": "none"}, "umd:main": "dist/mitt.umd.js", "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "_npmVersion": "9.7.2", "description": "Tiny 200b functional Event Emitter / pubsub.", "directories": {}, "jsnext:main": "dist/mitt.mjs", "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "^3.2.25", "chai": "^4.2.0", "mocha": "^8.0.1", "sinon": "^9.0.2", "eslint": "^7.32.0", "rimraf": "^3.0.2", "ts-node": "^10.9.1", "prettier": "^2.8.8", "sinon-chai": "^3.5.0", "typescript": "^4.9.5", "@types/chai": "^4.2.11", "microbundle": "^0.12.3", "npm-run-all": "^4.1.5", "@types/mocha": "^7.0.2", "@types/sinon": "^9.0.4", "documentation": "^14.0.2", "@types/sinon-chai": "^3.2.4", "eslint-plugin-compat": "^4.1.4", "eslint-config-developit": "^1.2.0", "@typescript-eslint/parser": "^5.61.0", "@typescript-eslint/eslint-plugin": "^5.61.0"}, "_npmOperationalInternal": {"tmp": "tmp/mitt_3.0.1_1688491907478_0.383738120109016", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2017-01-15T02:58:15.545Z", "modified": "2025-01-17T02:10:03.017Z", "1.0.0": "2017-01-15T02:58:15.545Z", "1.0.1": "2017-01-22T18:34:49.505Z", "1.1.0": "2017-02-25T21:30:40.867Z", "1.1.1": "2017-04-15T01:26:06.818Z", "1.1.2": "2017-04-17T17:27:19.858Z", "1.1.4": "2017-12-07T03:05:56.594Z", "1.1.3": "2017-12-07T03:18:17.140Z", "1.2.1": "2019-10-21T16:52:27.941Z", "1.2.0": "2019-10-21T16:54:50.525Z", "2.0.0": "2020-05-26T21:53:25.921Z", "2.0.1": "2020-05-27T17:44:18.373Z", "2.1.0": "2020-07-15T15:00:57.508Z", "3.0.0": "2021-06-23T20:47:09.180Z", "3.0.1": "2023-07-04T17:31:47.638Z"}, "bugs": {"url": "https://github.com/developit/mitt/issues"}, "license": "MIT", "homepage": "https://github.com/developit/mitt", "keywords": ["events", "eventemitter", "emitter", "pubsub"], "repository": {"url": "git+https://github.com/developit/mitt.git", "type": "git"}, "description": "Tiny 200b functional Event Emitter / pubsub.", "maintainers": [{"name": "developit", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n  <img src=\"https://i.imgur.com/BqsX9NT.png\" width=\"300\" height=\"300\" alt=\"mitt\">\n  <br>\n  <a href=\"https://www.npmjs.org/package/mitt\"><img src=\"https://img.shields.io/npm/v/mitt.svg\" alt=\"npm\"></a>\n  <img src=\"https://github.com/developit/mitt/workflows/CI/badge.svg\" alt=\"build status\">\n  <a href=\"https://unpkg.com/mitt/dist/mitt.js\"><img src=\"https://img.badgesize.io/https://unpkg.com/mitt/dist/mitt.js?compression=gzip\" alt=\"gzip size\"></a>\n</p>\n\n# Mitt\n\n> Tiny 200b functional event emitter / pubsub.\n\n-   **Microscopic:** weighs less than 200 bytes gzipped\n-   **Useful:** a wildcard `\"*\"` event type listens to all events\n-   **Familiar:** same names & ideas as [<PERSON>de's EventEmitter](https://nodejs.org/api/events.html#events_class_eventemitter)\n-   **Functional:** methods don't rely on `this`\n-   **Great Name:** somehow [mitt](https://npm.im/mitt) wasn't taken\n\nMitt was made for the browser, but works in any JavaScript runtime. It has no dependencies and supports IE9+.\n\n## Table of Contents\n\n-   [Install](#install)\n-   [Usage](#usage)\n-   [Examples & Demos](#examples--demos)\n-   [API](#api)\n-   [Contribute](#contribute)\n-   [License](#license)\n\n## Install\n\nThis project uses [node](http://nodejs.org) and [npm](https://npmjs.com). Go check them out if you don't have them locally installed.\n\n```sh\n$ npm install --save mitt\n```\n\nThen with a module bundler like [rollup](http://rollupjs.org/) or [webpack](https://webpack.js.org/), use as you would anything else:\n\n```javascript\n// using ES6 modules\nimport mitt from 'mitt'\n\n// using CommonJS modules\nvar mitt = require('mitt')\n```\n\nThe [UMD](https://github.com/umdjs/umd) build is also available on [unpkg](https://unpkg.com):\n\n```html\n<script src=\"https://unpkg.com/mitt/dist/mitt.umd.js\"></script>\n```\n\nYou can find the library on `window.mitt`.\n\n## Usage\n\n```js\nimport mitt from 'mitt'\n\nconst emitter = mitt()\n\n// listen to an event\nemitter.on('foo', e => console.log('foo', e) )\n\n// listen to all events\nemitter.on('*', (type, e) => console.log(type, e) )\n\n// fire an event\nemitter.emit('foo', { a: 'b' })\n\n// clearing all events\nemitter.all.clear()\n\n// working with handler references:\nfunction onFoo() {}\nemitter.on('foo', onFoo)   // listen\nemitter.off('foo', onFoo)  // unlisten\n```\n\n### Typescript\n\nSet `\"strict\": true` in your tsconfig.json to get improved type inference for `mitt` instance methods.\n\n```ts\nimport mitt from 'mitt';\n\ntype Events = {\n  foo: string;\n  bar?: number;\n};\n\nconst emitter = mitt<Events>(); // inferred as Emitter<Events>\n\nemitter.on('foo', (e) => {}); // 'e' has inferred type 'string'\n\nemitter.emit('foo', 42); // Error: Argument of type 'number' is not assignable to parameter of type 'string'. (2345)\n```\n\nAlternatively, you can use the provided `Emitter` type:\n\n```ts\nimport mitt, { Emitter } from 'mitt';\n\ntype Events = {\n  foo: string;\n  bar?: number;\n};\n\nconst emitter: Emitter<Events> = mitt<Events>();\n```\n\n## Examples & Demos\n\n<a href=\"http://codepen.io/developit/pen/rjMEwW?editors=0110\">\n  <b>Preact + Mitt Codepen Demo</b>\n  <br>\n  <img src=\"https://i.imgur.com/CjBgOfJ.png\" width=\"278\" alt=\"preact + mitt preview\">\n</a>\n\n* * *\n\n## API\n\n<!-- Generated by documentation.js. Update this documentation by updating the source code. -->\n\n#### Table of Contents\n\n-   [mitt](#mitt)\n-   [all](#all)\n-   [on](#on)\n    -   [Parameters](#parameters)\n-   [off](#off)\n    -   [Parameters](#parameters-1)\n-   [emit](#emit)\n    -   [Parameters](#parameters-2)\n\n### mitt\n\nMitt: Tiny (~200b) functional event emitter / pubsub.\n\nReturns **Mitt** \n\n### all\n\nA Map of event names to registered handler functions.\n\n### on\n\nRegister an event handler for the given type.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** Type of event to listen for, or `'*'` for all events\n-   `handler` **[Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)** Function to call in response to given event\n\n### off\n\nRemove an event handler for the given type.\nIf `handler` is omitted, all handlers of the given type are removed.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** Type of event to unregister `handler` from, or `'*'`\n-   `handler` **[Function](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Statements/function)?** Handler function to remove\n\n### emit\n\nInvoke all handlers for the given type.\nIf present, `'*'` handlers are invoked after type-matched handlers.\n\nNote: Manually firing '\\*' handlers is not supported.\n\n#### Parameters\n\n-   `type` **([string](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String) \\| [symbol](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Symbol))** The event type to invoke\n-   `evt` **Any?** Any value (object is recommended and powerful), passed to each handler\n\n## Contribute\n\nFirst off, thanks for taking the time to contribute!\nNow, take a moment to be sure your contributions make sense to everyone else.\n\n### Reporting Issues\n\nFound a problem? Want a new feature? First of all see if your issue or idea has [already been reported](../../issues).\nIf don't, just open a [new clear and descriptive issue](../../issues/new).\n\n### Submitting pull requests\n\nPull requests are the greatest contributions, so be sure they are focused in scope, and do avoid unrelated commits.\n\n-   Fork it!\n-   Clone your fork: `git clone https://github.com/<your-username>/mitt`\n-   Navigate to the newly cloned directory: `cd mitt`\n-   Create a new branch for the new feature: `git checkout -b my-new-feature`\n-   Install the tools necessary for development: `npm install`\n-   Make your changes.\n-   Commit your changes: `git commit -am 'Add some feature'`\n-   Push to the branch: `git push origin my-new-feature`\n-   Submit a pull request with full remarks documenting your changes.\n\n## License\n\n[MIT License](https://opensource.org/licenses/MIT) © [Jason Miller](https://jasonformat.com/)\n", "readmeFilename": "README.md", "users": {"ash": true, "ahme-t": true, "andrej": true, "emyann": true, "joakin": true, "monjer": true, "samobo": true, "drewigg": true, "ahmed-ab": true, "arturomc": true, "faeliaso": true, "mhaidarh": true, "rochejul": true, "twinraven": true, "lius971125": true, "quocnguyen": true, "ta2edchimp": true, "yexiyue666": true, "soenkekluth": true, "ahmedelgabri": true, "marianoviola": true, "charlespeters": true, "shanewholloway": true, "daniel-zahariev": true, "jkabore": true}}