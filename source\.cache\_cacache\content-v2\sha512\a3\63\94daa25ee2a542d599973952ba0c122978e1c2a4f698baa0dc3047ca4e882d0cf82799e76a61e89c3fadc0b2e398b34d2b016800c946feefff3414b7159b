{"_id": "<PERSON><PERSON><PERSON><PERSON>", "_rev": "85-3c47e0e59c8ebb65d86f681bea8d49e2", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "CLI arguments parser. Native port of python's argparse.", "dist-tags": {"latest": "2.0.1"}, "versions": {"0.1.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.0", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "dependencies": {"underscore": "1.3.1", "underscore.string": "2.1.1"}, "engines": {"node": ">= 0.6.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "argparse@0.1.0", "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "141038690326bb1fe60c3aa2fd32e60252cdf3ea", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.0.tgz", "integrity": "sha512-uy8SkrmCXhLnMYAWAQPEbylYL/brWv1TyCGkhv1DyQWnPKAyayCTmPEjlQBCLJeB2mu65gd3kl0vkbH+VFYYMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFG7TSGdd5vNrNubl73KgXCF3OL29pmct700bQgudrsQIhAJAQZGX8UgGBBeDfqR3yzDVooelfRLfu00lC69Riweik"}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.1", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "dependencies": {"underscore": "1.3.1", "underscore.string": "2.1.1"}, "engines": {"node": ">= 0.6.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "argparse@0.1.1", "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "7898577c1e2a249f3e550cc48209acc1d939026e", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.1.tgz", "integrity": "sha512-MRI093BmrdlmcQZQlNI0NWhPumyyU5EjotK9p2ZJ6zMkeJRfry/SVNS7+CO7VciW78GUupoEd8quweUJGWCmHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdBO+eDyMlySNjH80Z7R0vos8sDWzm3bAT3p1TtsOO6AIhAI4k0v6s0N6RTjaNGkkMyvKnwSpsrFo6uFEof4D19qjf"}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.2", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "1.3.1", "underscore.string": "2.1.1"}, "devDependencies": {"mocha": "1.0.1"}, "engines": {"node": ">= 0.6.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "argparse@0.1.2", "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.12", "_nodeVersion": "v0.6.14", "_defaultsLoaded": true, "dist": {"shasum": "dab8a7925b091e24683e17f8bcf7d9a4bf00204a", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.2.tgz", "integrity": "sha512-2daqhObgjuPCbTyX05wB+3oF0EXUO3T5iQ7megZMRACcq3BeR/9uzPGT7ZzNlyPwoJRnZqQQOCdEFOUjojuP3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBeBaekbVEolKFKkU/GtGuFJDNWRuYCuIeWvYqym8JOyAiEA1StnUxgSP03IiAxxqlyGwkAW+SC81eN/JKeKv1iEcnM="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.3", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.3", "dist": {"shasum": "e1a6a760f372b4082080da00b3ed13b46d42badf", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.3.tgz", "integrity": "sha512-9qXH2gJwulikAek2VB+IPQCwbp95UIjQvInstpQJ4cWNUkR9aDUrvVWkX0TtVRFF1sZHLW3kexeugi72DE6KwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC09SktjU3AXEk3K2+nBGezdaJ6dFDnUlJshsSQda647QIhAMNzmfAaaE1QHua1kURNhAhWIsXdw/poHA0fdjIohkCc"}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.4", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.4", "dist": {"shasum": "72ca942eb911bf18a011144361f3607c71ab4cfe", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.4.tgz", "integrity": "sha512-efi55L63AYDvcNJSA3QXr2XMu9m2UBRcUo5PjOjw/3Z6OP3QLxe4S2c4FrR/hchJeSyCnApagiIZM5IWC4+KXQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDZC9r74AFBsp5uDPTgaMuC2NaMyhywfxhv/eO1JOYEzAiEArFFYYqgIs/zkmsBOJCKsLlN2J0Ll3yWn+gSjXoCrc4g="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.5": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.5", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.5", "dist": {"shasum": "bcb6f43cedd06bedc4d0e97c833f97595e16f7e5", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.5.tgz", "integrity": "sha512-AWLGdm7n/VyLApINK+5MWWkyDmwTUpUobc5I6ckWW1Y3tl05EZzhx5aue1zxpewanZGHTk9fOaarbI9ATY2MfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF6H4iRjSu27SWGrqLubwCjsHCvNQANdn+5VZYMklpcZAiAPNP360PKFa4c3iqidw913FP2A8eM7FTYPXJ7aj+yXKA=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.6": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.6", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.6", "dist": {"shasum": "434d4ceaf77275e117d396293dcb2977c31b40ec", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.6.tgz", "integrity": "sha512-uW+Wd+dY1JKw+h5dBe2eeHPSxEufaxH1rZoKnJ4Cq2YZ2kYrkcKkkrc5n85I1ab1egusyeJorwFOCJ4lGqYteQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB2/Q9V0vc1Vr84mP+eEkvMxtMOJXuNLh+Y8CfHDZTF3AiBNTbtQ7FAuhMtqQAUp+B3VB/bjLAWlFZj7dpfKqqu1fA=="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.7": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.7", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.7", "dist": {"shasum": "eb6f77628d196ce9a56dd36abed78ae25f3b01f0", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.7.tgz", "integrity": "sha512-VMBE7gm/QWKwXKmsNdxvot/nbgOGU1d+D9Dz9g/lT0upagN1c8l46SJ9ey2YwU7t0V3jThSgH+YeUn4Y5Raw+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKbXLK7q1Zx7OrWna+IzDt7L4N/7dz0rTgrhRnf2WtAQIhAJ9/9gEpOhBkqitryYIqFWqsqvZOiyU87wCiS6ZovgwU"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.8": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.8", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.8", "dist": {"shasum": "fafa8e68ba36528ea538a8745c33f384d1b6daf1", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.8.tgz", "integrity": "sha512-sYQyYdSbK8QXUtuLWpCd4qKHCXBFld8eN6uEXa32q91RoE937efV+5sRTaCTcvcvN3K38IXF4TLE+LsYN8mWYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFF4BrbnPCGcZnncinTH09b05csCvc8LyyWsqdkoTplyAiEAimRgL48EDBjofRFX4jmQQKVVCK2y0SliWxMlfWpfJ6k="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.9": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.9", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.9", "dist": {"shasum": "9c5d6da174c219dd796271ef46a8b633be298c07", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.9.tgz", "integrity": "sha512-XqEAgr02DN0UDd+CR6iJhUi6qJla4N2AUqOyAn+/BkrSOhLQAoCTe7MOaeBcpO0vv/AFMEksGy33hqF4hyijvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICCF6d3oRrcCkIxXl2kdD6/IEazr+LQHZyoGnCpt73gWAiEAwKxazsLGrTvsm0pg7QhwDv8zMVp0vYn+DanJcdNaxR4="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.10": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.10", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.3.3", "underscore.string": "~2.1.1"}, "devDependencies": {"mocha": "~1.2.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.10", "dist": {"shasum": "ab77c1177fbab43831941be108dc6c1222e4980d", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.10.tgz", "integrity": "sha512-sbJS7zmzD3Q4Ggj6ZHZ2K4k7qp/Ycskz42xWjSSlyr8DSS5ISezKoHBYDAWqUnFZyCETLnunrVdMxsSU9HSWFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnFl0/iU2dbgX8vnHtlbBo40OYFDsJ9hW9YHPxHIOQiAIhAOCG3myiw5vKg5ggBknIrrqrxF2u/Hng0H5osXCJCmRf"}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.11": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.11", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.4.3", "underscore.string": "~2.3.1"}, "devDependencies": {"mocha": "~1.8.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.11", "dist": {"shasum": "c61031628629a24935badc7156714981d86ec819", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.11.tgz", "integrity": "sha512-Kqp2jpl2aFHoOZ6ejErEmFDA5IkhJoDSj/KvCyxd4kIRVVYr/I1R+PeukiWa0n0BtwisBioybafnjdSH3kRytg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAHzEXTPMVZWDDhS4inkje61IqYJs6k7Q3ZgMidDWn39AiEA5LohKSHdBSfnq3tgM40Wun4hyUBti6J8RruqKQjpdks="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.12": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.12", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.4.3", "underscore.string": "~2.3.1"}, "devDependencies": {"mocha": "~1.8.1"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.12", "dist": {"shasum": "987e5bd68ce0b82c213e1552fd6a46e11652617e", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.12.tgz", "integrity": "sha512-dUKfteg/ImDCDhNLR72qTjDX9QkGcF7BqwSgHftAoFYC+fUPf8+HPY5p86T80FTw9UeLWm7pIWMi2c8CGihluA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBvV8UnkNgXCIEzAAElkzMplSEsybXMNZFVs8/gdDexWAiBGjBEvWjdu5RfWzemwaDwDJBUqX3skrjA5hrgsd5B8yw=="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.13": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.13", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.4.3", "underscore.string": "~2.3.1"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.13", "dist": {"shasum": "2abd72e43d0a34d9049141e9c255ba816409f407", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.13.tgz", "integrity": "sha512-Re<PERSON>lu5K59viP2YOPehdoXeaGwIRTW4SHj9yB7OnRzlaiaz5NffYCH60QhsQ3i2YLR78sEEWmjfLivU45iqc6qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9D9oB7/yo7iqjQ5AC6gv3LoA2TXJM3JyXin5VU741UAiAcfsBtp0WwIAYYymeKqPe0f4mUE50dz1l3UH2gOJ+swg=="}]}, "_from": "https://github.com/nodeca/argparse/tarball/0.1.13", "_resolved": "https://github.com/nodeca/argparse/tarball/0.1.13", "_npmVersion": "1.2.14", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.14": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.14", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.4.3", "underscore.string": "~2.3.1"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "argpar<PERSON>@0.1.14", "dist": {"shasum": "9d927aad6d8d12649b1a0bbb519590b26d465eb6", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.14.tgz", "integrity": "sha512-hKuN9pGNqOFR2ryKGY1GhF8jXY5obWWYihGTomL1TL5QvzLyBPU/R7gNjQNfWsjpmIBPrNNWRBC+nhk/B7+1Xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKX2/1y3U5lo5L6Bn+O1Rfvlojdnob78BxaVjYoTrdlQIgRnGizoHx5y2ZWqkfKo4WkjRaazd/ySoiyojpGaHOmoo="}]}, "_from": "https://github.com/nodeca/argparse/tarball/0.1.14", "_resolved": "https://github.com/nodeca/argparse/tarball/0.1.14", "_npmVersion": "1.2.18", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.15": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.15", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/argparse/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.4.3", "underscore.string": "~2.3.1"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "argparse@0.1.15", "dist": {"shasum": "28a1f72c43113e763220e5708414301c8840f0a1", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.15.tgz", "integrity": "sha512-TRa9y0lEvcbbWexD1ad7GzAtdEQs9Ecr8D3WuPl/ng9h36TbrSoMPsGbCXZFo7pD9yUVRE6WElvAqEhG1jW6VA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEcphEWcMwMxhUug8HmdArLb6eh9stGwiBP7XTOQw5raAiAkgFnsLuTUQOC5byOsNzhOImxCN4WwHgNoLGgr2GFLew=="}]}, "_from": "https://github.com/nodeca/argparse/tarball/0.1.15", "_resolved": "https://github.com/nodeca/argparse/tarball/0.1.15", "_npmVersion": "1.2.18", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.1.16": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "0.1.16", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"underscore": "~1.7.0", "underscore.string": "~2.4.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "9c32eb1405d5d4b5686087d95bac010774979659", "_id": "argparse@0.1.16", "_shasum": "cfd01e0fbba3d6caed049fbd758d40f65196f57c", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "cfd01e0fbba3d6caed049fbd758d40f65196f57c", "tarball": "https://registry.npmjs.org/argparse/-/argparse-0.1.16.tgz", "integrity": "sha512-LjmC2dNpdn2L4UzyoaIr11ELYoLn37ZFy9zObrQFHsSuOepeUEMKnM8w5KL4Tnrp2gy88rRuQt6Ky8Bjml+Baw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAPGqItCxo63vt9mtp7Vf7lq/VEs2k9CMg4gjsQcAVCBAiEAkdTYJkJhXBscjGRVvV0fKJflQAupXHAxmv9EPh8gefc="}]}, "directories": {}}, "1.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.0", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"lodash": "^3.2.0", "sprintf-js": "~1.0.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "ffc165b39332bbd85474adb9afccad7d080aac1c", "_id": "argparse@1.0.0", "_shasum": "4408c1504b038a2f75436a04a9bcf9b77f2aef84", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "4408c1504b038a2f75436a04a9bcf9b77f2aef84", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.0.tgz", "integrity": "sha512-QmCSgXJPiE2TwUmw/3Nh4twP/DbAeX4OAvXpVzs11VskORDT0r7N+rEXHym6mxyDbTfDp2D0lo0sbODG7fQJOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQP7MssTBZ8F//bxencS/Ye5dkOR5+rPyYses+qb/qeAiAhiW+9hE+VQpfcTjaJ4zHjkyBScfuXm9QoGrV+2vQdIA=="}]}, "directories": {}}, "1.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.1", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"lodash": "~3.2", "sprintf-js": "~1.0.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "84b31478e66f3352fa62f3f884ccdf97352d33b5", "_id": "argparse@1.0.1", "_shasum": "cb1010b8559920fc8aee521eb9e80e4851790923", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "cb1010b8559920fc8aee521eb9e80e4851790923", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.1.tgz", "integrity": "sha512-ewswOcNNPOBTl0RJh//vwaLnXJN0sFBuzixpHyUcqgr/5NWfp/aJuvCL8t8R8gh3zfmmBO+tfmHoADPp3/Y6kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyuKUQVXDUuo+ArMjOgiW/7ayl/0WcJvkKklyAiNKj4AIhAIN057SrpwaFCybWUJXoQsxMKFCNiTzi+utXoSddt9jq"}]}, "directories": {}}, "1.0.2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.2", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/argparse.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "dependencies": {"lodash": ">= 3.2.0 < 4.0.0", "sprintf-js": "~1.0.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "990f1b5332e70dd3c1c437d2f4077a2b63ac9674", "_id": "argparse@1.0.2", "_shasum": "bcfae39059656d1973d0b9e6a1a74154b5a9a136", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "bcfae39059656d1973d0b9e6a1a74154b5a9a136", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.2.tgz", "integrity": "sha512-WefjOipMi7bdo1OGv7BIg5imUuuWxtHp7SH8HbhNIovxCzv70uk8RILrr9VYd7SQEdqHzY6j5BqaktR4HCu9vQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQfrGmlbRLtfvFeGPPjRjCfEAY9L3pkex+tvqH9LA1TgIhAP7aO1GTeNEk7DjzzhhtcePvL0idHr8x/lv3KB2Q330F"}]}, "directories": {}}, "1.0.3": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.3", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"lodash": ">= 3.2.0 < 4.0.0", "sprintf-js": "~1.0.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "e46e471f113ba31074c0d0c156f93fd7a618b27c", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "_id": "argparse@1.0.3", "_shasum": "14389deeb0c28fc4cda9405b9f532a4e3785ce84", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "14389deeb0c28fc4cda9405b9f532a4e3785ce84", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.3.tgz", "integrity": "sha512-W3ljhM0j9lduyssD8+dPR0P0HWCSCGrflhsgIVFzNQpx8TDQGN/dcqcb0hOuOWsf0TNZ8OIVgKXC7Rb2vpUp+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICaQwCZM3Fvyt4Tuysvxyvll0eRLf2RDlSWsaPsekYCaAiBlFnItxkDEcSXlzZtsO11+Y02khn3a/E7rLUZBlGUIFw=="}]}, "directories": {}}, "1.0.4": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.4", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"lodash": ">= 4.0.0 < 5.0.0", "sprintf-js": "~1.0.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "26e177bd9c164cbe4906bafb32b968eeebd2f2f2", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "_id": "argparse@1.0.4", "_shasum": "2b12247b933001971addcbfe4e67d20fd395bbf4", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "2b12247b933001971addcbfe4e67d20fd395bbf4", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.4.tgz", "integrity": "sha512-nChgTocTucFvebsRskyrPUK/9GVYovJHGuQDVEKQw5wNhrhDlrKBQ7tjHsMmHr5qf45hUsxLkhDiGuX6g4/+LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDQvTgVGOf0T7/mhBbsHelzrva5CpXHi8JDpiGZp8z6tAiEAgyryODs3/MRhDaw3ozElr3x21GHnFRmOiH98SF+1UOk="}]}, "directories": {}}, "1.0.5": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.5", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "lib/"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"sprintf-js": "~1.0.2"}, "devDependencies": {"mocha": "*"}, "gitHead": "994fc0120a51cd462cdd84f9aa211d357a82a60d", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "_id": "argparse@1.0.5", "_shasum": "8e1ff9ab91d1662d2dce2954d8511f448ccade42", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "8e1ff9ab91d1662d2dce2954d8511f448ccade42", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.5.tgz", "integrity": "sha512-lg1655jTvB6JfmOVDcNYg44ork84Zhq4XnynEEisxLCJoE9xp+Qsp5uCxgenINqNvV3Y1CL30oOcKUZ7VF/1WQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDcWi+IOxYY72uGb0UkFFfhOnZU2TM8Owwip3kM1Q0QdAiAsPCnOjnc0pwVRW0rC8kzRNkIQkmioGPLYbSDVp5wH0A=="}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/argparse-1.0.5.tgz_1454677471203_0.5495436238124967"}, "directories": {}}, "1.0.6": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.6", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "lib/"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"sprintf-js": "~1.0.2"}, "devDependencies": {"eslint": "2.0.0-rc.0", "eslint-plugin-nodeca": "~1.0.3", "mocha": "*", "ndoc": "^3.1.0"}, "gitHead": "f8810fdae9ab4eb68283ba4c5f62baeaf0c0719d", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "_id": "argparse@1.0.6", "_shasum": "ada3c46ade64695906efbb7a0a337f619abb4694", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "ada3c46ade64695906efbb7a0a337f619abb4694", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.6.tgz", "integrity": "sha512-IZhPu0hZjybbS+i6nDMAcD2DGDoWXCSiL4GIkRJ0Djt77XcZNCUY9lX6AH/I7U4p/hRYl2SqahKYvFKAF47HBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZn7w5kMOsSePvaI8VaOql7ntDq4xJz8oFaMAlgm0ozwIgddY6pk6hZHJls66R5rN53XjGL/Oo1+68GvuulRKV/2Y="}]}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/argparse-1.0.6.tgz_1454752276282_0.7111219766084105"}, "directories": {}}, "1.0.7": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.7", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "lib/"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"sprintf-js": "~1.0.2"}, "devDependencies": {"eslint": "2.0.0-rc.0", "eslint-plugin-nodeca": "~1.0.3", "mocha": "*", "ndoc": "^3.1.0"}, "gitHead": "2d243d3062affb01f49f5f00e8ca8464a15e9374", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "_id": "argparse@1.0.7", "_shasum": "c289506480557810f14a8bc62d7a06f63ed7f951", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "c289506480557810f14a8bc62d7a06f63ed7f951", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.7.tgz", "integrity": "sha512-svfNOD2ovohwmmIi/Y1NtBuc53G8RhPLeIMBwB0uTK8pXV1yu0+ZVzj+XHFDFWAPYflfOvPLQxjUB8WpnObhjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPIsNp5GZ5igmgGnl3mDuAvgT7ZuD3L5ULDtgwi5+inAiEAlEK+AZ4Lz6vfCtVtluGCDbMy4n996+uwYprtEdivdxI="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/argparse-1.0.7.tgz_1458226887302_0.8524546672124416"}, "directories": {}}, "1.0.8": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.8", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "homepage": "https://github.com/nodeca/argparse", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "lib/"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"istanbul": "^0.4.5", "sprintf-js": "~1.0.2"}, "devDependencies": {"eslint": "^2.0.0", "eslint-plugin-nodeca": "~1.0.3", "mocha": "^3.1.0", "ndoc": "^5.0.0"}, "gitHead": "3f2a5d4e5d8deae97bb047f82d4445fb806c32d2", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "_id": "argparse@1.0.8", "_shasum": "e5bd891bbdbbd725a94aa9827b25c0323fc5e8ee", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "e5bd891bbdbbd725a94aa9827b25c0323fc5e8ee", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.8.tgz", "integrity": "sha512-gYMHx71tcNKDKyEXc8uioXhdovUejWfMdVjHceQuLIRHJhwswfxqX/XFt648vQOa0cDKee4tAHQ5Rr+IcJv4lw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAdpJtOIlj3mQLmi9i+qtNvfNSU/FMLAc6JhBZ2gq7QHAiEA0x97oJ13/q9aO7FpJd6d3PEmuS4W3ONzIKli4ny6jxM="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/argparse-1.0.8.tgz_1475176131444_0.5720982311759144"}, "directories": {}}, "1.0.9": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.9", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "lib/"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"sprintf-js": "~1.0.2"}, "devDependencies": {"eslint": "^2.13.1", "istanbul": "^0.4.5", "mocha": "^3.1.0", "ndoc": "^5.0.1"}, "gitHead": "acb39f2d726b90d2eadf9e6574a938d6250ad248", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "homepage": "https://github.com/nodeca/argparse#readme", "_id": "argparse@1.0.9", "_shasum": "73d83bc263f86e97f8cc4f6bae1b0e90a7d22c86", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "73d83bc263f86e97f8cc4f6bae1b0e90a7d22c86", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.9.tgz", "integrity": "sha512-iK7YPKV+GsvihPUTKcM3hh2gq47zSFCpVDv/Ay2O9mzuD7dfvLV4vhms4XcjZvv4VRgXuGLMEts51IlTjS11/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkTN/JrCOzP4bB1TItnIaVnjqesjlQR+isO9vyuDQ2NAIhAIQEq7jDm56j71Er0XhZZj/oHaut70hgslr6wS4wNQXl"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/argparse-1.0.9.tgz_1475177461025_0.33920647646300495"}, "directories": {}}, "1.0.10": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Very powerful CLI arguments parser. Native port of argparse - python's options parsing library", "version": "1.0.10", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "files": ["index.js", "lib/"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"test": "make test"}, "dependencies": {"sprintf-js": "~1.0.2"}, "devDependencies": {"eslint": "^2.13.1", "istanbul": "^0.4.5", "mocha": "^3.1.0", "ndoc": "^5.0.1"}, "gitHead": "ea45e14bad13b9e4a10af28f11fb7e731079ab72", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "homepage": "https://github.com/nodeca/argparse#readme", "_id": "argparse@1.0.10", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "shasum": "bcd6791ea5ae09725e17e5ad988134cd40b3d911", "tarball": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "fileCount": 27, "unpackedSize": 116446, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGBSlWYqJJS8ly2A5x23cEqG4hBghOCO+4VmmjZBC4dwIhAMvtmzy1dCR3dTOg8Gp0+gqBp9fCBeqO6fyG9uUEmdyJ"}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/argparse_1.0.10_1518704641025_0.2567322588736727"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "CLI arguments parser. Native port of python's argparse.", "version": "2.0.0", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "main": "argparse.js", "license": "Python-2.0", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha", "coverage": "npm run test && nyc report --reporter html"}, "devDependencies": {"@babel/eslint-parser": "^7.11.0", "@babel/plugin-syntax-class-properties": "^7.10.4", "eslint": "^7.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0"}, "gitHead": "f1a60eebdc487708085398da7e3fbcddd396d7b4", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "homepage": "https://github.com/nodeca/argparse#readme", "_id": "argparse@2.0.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-mEKF1/WpTsblaqx7NIkcsTxwDzvJuGH5sdUqDNcJS+vXCWe+yM/o4cs/Q2/GFESAYg+O7UouEmz+iBqmKofI/Q==", "shasum": "b082dc7fc442b093e4c9941db63b9db9d7305f78", "tarball": "https://registry.npmjs.org/argparse/-/argparse-2.0.0.tgz", "fileCount": 7, "unpackedSize": 171322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNqJQCRA9TVsSAnZWagAAIhwP/29pX0HOj8vfc2uuKTsf\nlvMpRsOpN2pzER//kmiIDzFF1gCS8msmxuhRAeHctWLytXj9QaywHCtpmiwo\n49mGK8n6JEanYqjk4bq5PAHGowtoQ3G0v6CbYcKeFXKxoBVED94pwoyqmsjT\nZQWiHzlodgaOKfwFH7rmn137baQS5iDz3LnS4WDfXu8O32C8HwFUtw8MPXJn\nfBDzT4fmdelMQj9X6SAGZJCsO/Js4/uf1PdABN5UPVats9Oe8lUe68fyfgml\nPViKNT8yGl+BCjRxbJjHqyMDfX1yacZ49ZAxIO37lSxfgivOiWtJlWiv39UR\nZAM//roQVZVirMTWyb8SrNcFl87X4CbL9Ssof5C+N+qu5RqvM1HMTmznyR1t\nYEShpHjYSFMmWElnYmAfDd0kztitEZeI75cAkONJCfeMrpWo9qLKcnZLFBtk\nHQAFYChg0umMVtAMsxvMAodZy/4HJz8CuMv8uCXF/mhzdsnLXbTvMgQ2rKiy\nS5KPqfUJyI9c87b2S7AGKsEDt7w7aiUcqkcvArW/ArhbFPO/b/RrNpngpf3O\n1QqytK/LPHZe5fGMXwX/eul+KmoVZN7PnRiSptCI/e9cAvuZeRMlgoaLB8Ba\nHUya2pbIYsNha7U/uh5GBIeiTvqE9uKiG9Hpnv/eV7ajGsg6Mzz8IsqPsyKC\npUMG\r\n=Qynn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9VBQr5qwokjykFKXqKEGqHzhk6Y+LpQ1x9JFL65VA0QIgFyJd5NrsVYY86eWhYMWD8ExTDKJ7MWhmKdhts2sJS6A="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/argparse_2.0.0_1597416015913_0.18687702563277964"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "CLI arguments parser. Native port of python's argparse.", "version": "2.0.1", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "main": "argparse.js", "license": "Python-2.0", "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "scripts": {"lint": "eslint .", "test": "npm run lint && nyc mocha", "coverage": "npm run test && nyc report --reporter html"}, "devDependencies": {"@babel/eslint-parser": "^7.11.0", "@babel/plugin-syntax-class-properties": "^7.10.4", "eslint": "^7.5.0", "mocha": "^8.0.1", "nyc": "^15.1.0"}, "gitHead": "e373563876f1c049826c21bd1c9388f6a5737a3e", "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "homepage": "https://github.com/nodeca/argparse#readme", "_id": "argparse@2.0.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "shasum": "246f50f3ca78a3240f6c997e8a9bd1eac49e4b38", "tarball": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "fileCount": 7, "unpackedSize": 171548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSXOzCRA9TVsSAnZWagAA3aMP/jSc//Kv/EcAJDf9zguA\nei+dmXoFh5ZMOG8jwM5LOodJfjMcprSvNbWpFEb+d1JxX8k1ukrtTdZCt2NS\nfL7obXK0Rmze8oxM47ebqXgcIuaFwEFtbINNbdhqbxHajF7KDv68VuIQ3/R7\nJE0GtBn7pmsUWyffI4hXIHDiT6kS7X8N/XG36x09szB6aT7VI7tjvchlmKPS\nvG3X0Z3amXad43bPHjiGLVtmlUZ+DVKCr+F7C+NPwjLavDku38IWbQiL4aB6\niNmLwhp/68X08syenskp95Wob+WV9m4up5sWoN77lHw+j8ow2+7KKmZNfqB8\noCpkCh435x170x1pIbcUSlS2xTwxc7FUmNKAYyC01nSn8VBmqDuuhIyK0CmD\nJzGKBzdtRiyIYFezILEgrzFWFfDoiRCf7C7/EdGSif1u7YmhvQQzipxMlj0V\nCNjtKypRajTTznQE2P1x4VoIsGdh21odHwl/942jBte+7cCTe6q0hadfPWrg\nxV2W8+ouTlRym7MelaXImgthPljAQj9iBm2nruRiCy1fr6NYwf1n3Taw4vDI\ni96UEEoZI8B/BluR1B6r0roSC6XkS00iR9S/4p3OcKa6vzm0wKf4A/NLudpR\nyjB0qXIkGH+VYQX+qEzqBgOKnrbAibLI516zo/zdMecm5YU9zUh+MH9Wmujt\n0eJK\r\n=EMEE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH1TwnHWb5mQxIvhpMQIDK9CZjEvWm3r54fpVDzPwwqnAiB15fTGo5Qy7GVLldJRMSv/zb6ZVlip2AKfS46m6fpqjA=="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/argparse_2.0.1_1598649266472_0.755290261850194"}, "_hasShrinkwrap": false}}, "readme": "argparse\n========\n\n[![Build Status](https://secure.travis-ci.org/nodeca/argparse.svg?branch=master)](http://travis-ci.org/nodeca/argparse)\n[![NPM version](https://img.shields.io/npm/v/argparse.svg)](https://www.npmjs.org/package/argparse)\n\nCLI arguments parser for node.js, with [sub-commands](https://docs.python.org/3.9/library/argparse.html#sub-commands) support. Port of python's [argparse](http://docs.python.org/dev/library/argparse.html) (version [3.9.0](https://github.com/python/cpython/blob/v3.9.0rc1/Lib/argparse.py)).\n\n**Difference with original.**\n\n- JS has no keyword arguments support.\n  -  Pass options instead: `new ArgumentParser({ description: 'example', add_help: true })`.\n- JS has no python's types `int`, `float`, ...\n  - Use string-typed names: `.add_argument('-b', { type: 'int', help: 'help' })`.\n- `%r` format specifier uses `require('util').inspect()`.\n\nMore details in [doc](./doc).\n\n\nExample\n-------\n\n`test.js` file:\n\n```javascript\n#!/usr/bin/env node\n'use strict';\n\nconst { ArgumentParser } = require('argparse');\nconst { version } = require('./package.json');\n\nconst parser = new ArgumentParser({\n  description: 'Argparse example'\n});\n\nparser.add_argument('-v', '--version', { action: 'version', version });\nparser.add_argument('-f', '--foo', { help: 'foo bar' });\nparser.add_argument('-b', '--bar', { help: 'bar foo' });\nparser.add_argument('--baz', { help: 'baz bar' });\n\nconsole.dir(parser.parse_args());\n```\n\nDisplay help:\n\n```\n$ ./test.js -h\nusage: test.js [-h] [-v] [-f FOO] [-b BAR] [--baz BAZ]\n\nArgparse example\n\noptional arguments:\n  -h, --help         show this help message and exit\n  -v, --version      show program's version number and exit\n  -f FOO, --foo FOO  foo bar\n  -b BAR, --bar BAR  bar foo\n  --baz BAZ          baz bar\n```\n\nParse arguments:\n\n```\n$ ./test.js -f=3 --bar=4 --baz 5\n{ foo: '3', bar: '4', baz: '5' }\n```\n\n\nAPI docs\n--------\n\nSince this is a port with minimal divergence, there's no separate documentation.\nUse original one instead, with notes about difference.\n\n1. [Original doc](https://docs.python.org/3.9/library/argparse.html).\n2. [Original tutorial](https://docs.python.org/3.9/howto/argparse.html).\n3. [Difference with python](./doc).\n\n\nargparse for enterprise\n-----------------------\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of argparse and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-argparse?utm_source=npm-argparse&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "time": {"modified": "2023-10-21T01:41:33.987Z", "created": "2012-05-16T20:16:27.775Z", "0.1.0": "2012-05-16T20:16:30.136Z", "0.1.1": "2012-05-23T06:20:01.344Z", "0.1.2": "2012-05-29T10:00:31.477Z", "0.1.3": "2012-07-04T11:20:25.948Z", "0.1.4": "2012-07-30T07:33:17.434Z", "0.1.5": "2012-09-03T12:52:32.986Z", "0.1.6": "2012-09-09T09:26:01.487Z", "0.1.7": "2012-10-14T12:14:16.299Z", "0.1.8": "2012-12-02T19:28:07.466Z", "0.1.9": "2012-12-27T05:42:28.541Z", "0.1.10": "2012-12-30T05:26:54.477Z", "0.1.11": "2013-02-07T14:20:03.293Z", "0.1.12": "2013-02-10T10:48:44.580Z", "0.1.13": "2013-04-08T11:55:47.978Z", "0.1.14": "2013-05-12T02:28:54.390Z", "0.1.15": "2013-05-12T14:11:18.146Z", "0.1.16": "2014-12-01T18:23:38.631Z", "1.0.0": "2015-02-19T07:23:51.940Z", "1.0.1": "2015-02-20T06:48:21.420Z", "1.0.2": "2015-03-22T03:51:34.445Z", "1.0.3": "2015-10-27T14:16:16.145Z", "1.0.4": "2016-01-17T07:58:21.779Z", "1.0.5": "2016-02-05T13:04:32.133Z", "1.0.6": "2016-02-06T09:51:19.935Z", "1.0.7": "2016-03-17T15:01:29.698Z", "1.0.8": "2016-09-29T19:08:53.638Z", "1.0.9": "2016-09-29T19:31:03.257Z", "1.0.10": "2018-02-15T14:24:01.840Z", "2.0.0": "2020-08-14T14:40:16.042Z", "2.0.1": "2020-08-28T21:14:26.713Z"}, "repository": {"type": "git", "url": "git+https://github.com/nodeca/argparse.git"}, "users": {"fgribreau": true, "39dotyt": true, "monsterkodi": true, "cycomachead": true, "tunnckocore": true, "nketchum": true, "aureooms": true, "monolithed": true, "52u": true, "jktravis": true, "i-erokhin": true, "carlosvillademor": true, "hyokosdeveloper": true, "boto": true, "hugojosefson": true, "lixulun": true, "jiangc": true, "thehatchcloud": true, "strawhat": true, "shavyg2": true, "bglasser": true, "flumpus-dev": true}, "homepage": "https://github.com/nodeca/argparse#readme", "keywords": ["cli", "parser", "<PERSON><PERSON><PERSON><PERSON>", "option", "args"], "bugs": {"url": "https://github.com/nodeca/argparse/issues"}, "license": "Python-2.0", "readmeFilename": "README.md"}